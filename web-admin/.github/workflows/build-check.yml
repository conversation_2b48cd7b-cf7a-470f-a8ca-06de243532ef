name: Build Check

on:
  push:
    branches:
      - feature/**
      - development
  workflow_dispatch:
    inputs:
      reason:
        description: 'Motivo'
        required: false

env:
  GITHUB_SHA: ${{ github.sha }}
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID_DEV }}
  GKE_CLUSTER: ${{ secrets.K8S_CLUSTER_DEV }}
  GKE_ZONE: ${{ secrets.K8S_ZONE_DEV }}
  GH_USERNAME: ${{ secrets.GH_USERNAME }}
  GH_TOKEN: ${{ secrets.GH_TOKEN }}

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v0
        with:
          project_id: ${{ env.PROJECT_ID }}
          service_account_key: ${{ secrets.GCP_SERVICE_ACCOUNT_DEV }}
          export_default_credentials: true

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Auth
        run: |-
          gcloud --quiet auth configure-docker
          gcloud container clusters get-credentials --zone=${{ env.GKE_ZONE }} ${{ env.GKE_CLUSTER }}

      - name: Login to DockerHub Registry
        run: echo ${{ secrets.DOCKER_PASSWORD }} | docker login -u ${{ secrets.DOCKER_LOGIN }} --password-stdin

      - name: Build
        uses: docker/build-push-action@v2
        with:
          context: .
          build-args: |
            GH_USERNAME=${{ env.GH_USERNAME }}
            GH_TOKEN=${{ env.GH_TOKEN }}
          push: false
          cache-from: type=gha
          cache-to: type=gha,mode=max
