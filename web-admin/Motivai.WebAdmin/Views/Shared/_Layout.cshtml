@using Motivai.SharedKernel.Helpers;
@{
    var tinymceApiKey = ConfigurationHelper.GetValue("TINYMCE_API_KEY");
}
<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Painel Administrativo</title>
    <base href="/" />
    <meta content="IE=Edge" http-equiv="X-UA-Compatible">
    <meta charset="utf-8">
    <meta content="width=device-width, minimum-scale=1.0, maximum-scale=1.0" name="viewport">
    <meta content="marketing, promotion, incentive, loyalty, rewards" name="keywords">
    <meta name="theme-color" content="#4b0f94">
    <link rel="icon" type="image/png" href="/assets/img/favicon/android-chrome-192x192.png" sizes="192x192">
    <link rel="icon" type="image/png" href="/assets/img/favicon/favicon-32x32.png" sizes="32x32">
    <link rel="icon" type="image/png" href="/assets/img/favicon-16x16.png" sizes="16x16">
    <link rel="apple-touch-icon" sizes="114x114" href="/assets/img/favicon/apple-touch-icon-114x114.png">
    <meta name="msapplication-TileColor" content="#00293b">
    <meta name="msapplication-TileImage" content="/assets/img/favicon/mstile-144x144.png">
    <link rel="stylesheet" href="~/dist/vendor.css" asp-append-version="true" />
</head>
<body>
    @RenderBody()
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
    <script src="https://cloud.tinymce.com/stable/tinymce.min.js?apiKey=@tinymceApiKey"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.5.0/Chart.bundle.min.js"></script>
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-40BCS41M43"></script>
    <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'G-40BCS41M43');
    </script>
    @RenderSection("Scripts", false)
</body>
</html>
