import { Inject, Injectable } from '@angular/core';
import { URLSearchParams, Http, Headers, RequestOptionsArgs, ResponseContentType } from '@angular/http';
import { Observable } from 'rxjs/Observable';
import 'rxjs/add/operator/timeoutWith';
import 'rxjs/add/operator/map';

import { AppConfig, APP_CONFIG } from '../settings/appconfig';
import { AuthStore } from '../auth/auth.store';

@Injectable()
export class ApiService {
  private _baseUrl: string;

  constructor(@Inject(APP_CONFIG) app: AppConfig, private _authStore: AuthStore, private _http: Http) {
    this._baseUrl = app.baseUrl;
  }

  public postWithoutToken(path: string, data: any): Observable<any> {
    path = this.verifyPath(path);
    const headers = new Headers();
    this.addJsonHeader(headers);
    return this._http.post(`${this._baseUrl}${path}`, data, { headers: headers }).catch(err => this.handleError(err));
  }

  public get(path: string, params?: any, timeout: number = 40000): Observable<any> {
    path = this.verifyPath(path);
    const headers = new Headers();
    this.addAuthorizationHeader(headers);
    const queryParams = new URLSearchParams();
    if (params) {
      for (const key in params) {
        queryParams.set(key, params[key]);
      }
    }
    return this._http
      .get(`${this._baseUrl}${path}`, { headers: headers, params: queryParams })
      .timeoutWith(
        timeout,
        this.observableForError('O tempo de espera da resposta excedeu, por favor, tente novamente.')
      )
      .map(this.handleApiReturn)
      .catch(err => this.handleError(err));
  }

  public patch(path: string, params?: any, data?: any, timeout: number = 40000): Observable<any> {
    path = this.verifyPath(path);
    const headers = new Headers();
    this.addAuthorizationHeader(headers);
    const queryParams = new URLSearchParams();
    if (params) {
      for (const key in params) {
        queryParams.set(key, params[key]);
      }
    }
    return this._http
      .patch(`${this._baseUrl}${path}`, data, { headers: headers, params: queryParams })
      .timeoutWith(
        timeout,
        this.observableForError('O tempo de espera da resposta excedeu, por favor, tente novamente.')
      )
      .map(this.handleApiReturn)
      .catch(err => this.handleError(err));
  }

  public getFile(path: string, params?: any, timeout: number = 10000): Observable<any> {
    path = this.verifyPath(path);
    const headers = new Headers();
    this.addAuthorizationHeader(headers);
    const queryParams = new URLSearchParams();
    if (params) {
      for (const key in params) {
        queryParams.set(key, params[key]);
      }
    }

    return this._http
      .get(`${path}`, { headers: headers, params: queryParams, responseType: ResponseContentType.Blob as ResponseContentType.Json})
      .timeoutWith(timeout, this.observableForError('O tempo de espera da resposta excedeu, por favor, tente novamente.'))
      .map(this.handleApiReturn)
      .catch(this.handleError);
  }

  public post(path: string, data: any, timeout: number = 10000): Observable<any> {
    return this.postWithHeaders(path, data, timeout);
  }

  public postWithStaticContent(path, data, timeout: number = 10000): Observable<any> {
    return this.postWithHeaders(path, data, timeout, { 'gp-dynamic-content': '1'} );
  }

  public postWithHeaders(path: string, data: any, timeout: number = 10000, customHeaders: object = null): Observable<any> {
    path = this.verifyPath(path);
    const headers = new Headers();
    this.addJsonHeader(headers);
    this.addAuthorizationHeader(headers);
    if (customHeaders) {
      Object.keys(customHeaders).forEach(key => {
        if (customHeaders[key]) {
          headers.append(key, customHeaders[key]);
        }
      })
    }
    return this._http
      .post(`${this._baseUrl}${path}`, data, { headers: headers })
      .timeoutWith(
        timeout,
        this.observableForError('O tempo de espera da resposta excedeu, por favor, tente novamente.')
      )
      .map(this.handleApiReturn)
      .catch(err => this.handleError(err));
  }

  public put(path: string, data?: any, timeout: number = 10000): Observable<any> {
    return this.putWithHeaders(path, data, timeout, null);
  }

  public putWithStaticContent(path, data, timeout: number = 10000): Observable<any> {
    return this.putWithHeaders(path, data, timeout, { 'gp-dynamic-content': '1' });
  }

  public putWithHeaders(path: string, data?: any, timeout: number = 10000, customHeaders: object = null): Observable<any> {
    path = this.verifyPath(path);
    const headers = new Headers();
    this.addJsonHeader(headers);
    this.addAuthorizationHeader(headers);
    if (customHeaders) {
      Object.keys(customHeaders).forEach(key => {
        if (customHeaders[key]) {
          headers.append(key, customHeaders[key]);
        }
      })
    }
    return this._http
      .put(`${this._baseUrl}${path}`, data || {}, { headers: headers })
      .timeoutWith(
        timeout,
        this.observableForError('O tempo de espera da resposta excedeu, por favor, tente novamente.')
      )
      .map(this.handleApiReturn)
      .catch(err => this.handleError(err));
  }

  public delete(path: string, timeout: number = 5000): Observable<any> {
    path = this.verifyPath(path);
    const headers = new Headers();
    this.addJsonHeader(headers);
    this.addAuthorizationHeader(headers);
    return this._http
      .delete(`${this._baseUrl}${path}`, { headers: headers })
      .timeoutWith(
        timeout,
        this.observableForError('O tempo de espera da resposta excedeu, por favor, tente novamente.')
      )
      .map(this.handleApiReturn)
      .catch(err => this.handleError(err));
  }

  public deleteByParam(path: string, params: any = null, timeout: number = 5000): Observable<any> {
    path = this.verifyPath(path);
    const headers = new Headers();
    this.addAuthorizationHeader(headers);
    const queryParams = new URLSearchParams();
    if (params) {
      for (const key in params) {
        queryParams.set(key, params[key]);
      }
    }
    return this._http
      .delete(`${this._baseUrl}${path}`, { headers: headers, params: queryParams })
      .timeoutWith(
        timeout,
        this.observableForError('O tempo de espera da resposta excedeu, por favor, tente novamente.')
      )
      .map(this.handleApiReturn)
      .catch(err => this.handleError(err));
  }

  private handleApiReturn(resp: Response | any) {
    if (resp.ok) {
      let apiReturn: any;
      try {
        apiReturn = resp.json();
      } catch (ex) {
        throw new Error(
          'Não foi possível processar sua requisição. Se o erro persistir, por favor, contate o suporte do sistema.'
        );
      }
      if (apiReturn.success === true) {
        return apiReturn.return;
      } else if (apiReturn.success === false) {
        if (apiReturn.error) {
          throw new Error(apiReturn.error);
        } else {
          throw new Error('Ocorreu um erro durante a operação, por favor, tente novamente.');
        }
      }
    }
    return resp;
  }

  private verifyPath(path: string): string {
    if (path && path.charAt(0) != '/') {
      return '/' + path;
    }
    return path;
  }

  private addJsonHeader(headers: Headers): any {
    headers.append('Content-Type', 'application/json');
  }

  private addAuthorizationHeader(headers: Headers): any {
    headers.append('access_token', this._authStore.getToken());
  }

  private handleError(error: any) {
    let errorMessage: string;
    if (error instanceof Response || error.status > 0) {
      if (error.status == 200) {
        return error;
      } else if (error.status == 401) {
        errorMessage = 'Efetue o login para acessar o recurso.';
        this.destroySession();
      } else if (error.status === 403) {
        errorMessage =
          'Você não tem permissão para realizar esta operação, por favor, contate o administrador do sistema.';
      } else if (error.status == 400) {
        errorMessage = error.text();
      } else if (error.status === 404) {
        errorMessage = 'O recurso solicitado não foi encontrado.';
      } else if (error.status === 503 || error.status === 504) {
        errorMessage =
          'Não foi possível efetuar a conexão com o servidor, por favor, verifique sua conexão e atualize a página.';
      } else if (error.status == 500) {
        errorMessage = 'Ocorreu um erro ao efetuar a requisição, se o erro persistir, contate o administrador.';
      } else {
        let body = { error: null };
        error.json().then(resp => (body = resp));
        const err = body.error || JSON.stringify(body);
        errorMessage = `${error.status} - ${error.statusText || ''} ${err}`;
      }
    } else if (error instanceof Error) {
      errorMessage = error.message;
    } else if (error) {
      errorMessage = error.message ? error.message : error.status ? `${error.status} - ${error.statusText}` : error;
    } else {
      errorMessage = 'Ocorreu um erro ao efetuar a requisição, se o erro persistir, contate o administrador.';
    }
    return Observable.throw(errorMessage);
  }

  private observableForError(message: string): any {
    return Observable.throw(message);
  }

  private destroySession() {
    if (this._authStore) this._authStore.logoff();
    sessionStorage.clear();
    location.href = '/login';
  }
}
