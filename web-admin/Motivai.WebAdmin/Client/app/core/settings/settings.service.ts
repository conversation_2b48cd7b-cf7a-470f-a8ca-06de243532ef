import { Injectable, Inject } from '@angular/core';

import { AppConfig, APP_CONFIG } from './appconfig';
import { AuthStore } from '../auth/auth.store';
import { LayoutType } from '../auth/layout-type';

@Injectable()
export class SettingsService {
    private user: any;
    public app: AppConfig;
    private _layout: any;

    constructor(@Inject(APP_CONFIG) app: AppConfig, private as: AuthStore) {
        // User Settings
        // -----------------------------------
        this.user = {
            name: '<PERSON><PERSON><PERSON>',
            job: 'ng-developer',
            picture: 'assets/img/user/01.jpg'
        };

        // App Settings
        // -----------------------------------
        this.app = app;

        // Layout Settings
        // -----------------------------------
        this._layout = {
            isFixed: true,
            isCollapsed: false,
            isBoxed: false,
            isRTL: false,
            horizontal: false,
            isFloat: false,
            asideHover: false,
            theme: null,
            asideScrollbar: false,
            isCollapsedText: false,
            useFullLayout: false,
            hiddenFooter: false,
            offsidebarOpen: false,
            asideToggled: false,
            viewAnimation: 'ng-fadeInUp'
        };

    }

    get layout() {
        return this._layout;
    }

    getAppSetting(name) {
        return name ? this.app[name] : this.app;
    }

    getUserSetting(name) {
        return name ? this.user[name] : this.user;
    }

    getLayoutSetting(name) {
        return name ? this._layout[name] : this._layout;
    }

    setAppSetting(name, value) {
        if (typeof this.app[name] !== 'undefined') {
            this.app[name] = value;
        }
    }

    setUserSetting(name, value) {
        if (typeof this.user[name] !== 'undefined') {
            this.user[name] = value;
        }
    }

    setLayoutSetting(name, value) {
        if (typeof this._layout[name] !== 'undefined') {
            return this._layout[name] = value;
        }
    }

    toggleLayoutSetting(name) {
        return this.setLayoutSetting(name, !this.getLayoutSetting(name));
    }

}
