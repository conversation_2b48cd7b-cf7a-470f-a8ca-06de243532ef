import { Injectable } from '@angular/core';

declare var gtag: Function;
declare var dataLayer: any;

@Injectable()
export class TrackerService {
  public sendPageView(page: string) {
    if (gtag) {
      gtag('event', 'page_view', {
        page_title: document.title,
        page_location: window.location.href,
        page_path: page
      });
    } else if (dataLayer) {
      dataLayer.push({
        event: 'page_view',
        page_title: document.title,
        page_location: window.location.href,
        page_path: page
      });
    }
  }

  public sendEvent(eventCategory: string, eventAction: string, eventLabel: string, eventValue: number | string = 1) {
    console.debug(eventCategory, eventAction, eventLabel, eventValue);
    if (gtag) {
      gtag('event', 'custom_event', {
        event_category: eventCategory,
        event_action: eventAction,
        event_label: eventLabel,
        value: eventValue || 1
      });
    } else if (dataLayer) {
      dataLayer.push({
        event: 'custom_event',
        event_category: eventCategory,
        event_action: eventAction,
        event_label: eventLabel,
        value: eventValue || 1
      });
    }
  }

  sendCustomEvent(eventName: string = 'custom_event', params: any = {}) {
    console.debug(eventName, params);
    if (gtag) {
      gtag('event', eventName, params);
    } else if (dataLayer) {
      params.event = eventName;
      dataLayer.push(params);
    }
  }

  public registerOutgoing(url: string) {
    this.sendEvent('Outbound Link', 'click', url);
  }
}
