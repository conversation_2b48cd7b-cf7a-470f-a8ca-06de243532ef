import { Injectable } from '@angular/core';
import { Observable } from "rxjs/Observable";

import { ApiService } from "../api/api.service";

@Injectable()
export class UsersService {
  constructor(private _api: ApiService) {}

  public searchByBusinessTypeAndName(businessType: string, document: string | undefined, name: string | undefined): Observable<Array<any>> {
    return this._api.get(`/api/users/${businessType}`, { document: document, name: name });
  }

  public getMainAddress(campaignId: string, userId: string): Observable<any> {
    return this._api.get(`/api/users/${userId}/campaigns/${campaignId}/addresses/main`);
  }

  public authenticateByPlatformSso(campaignId: string, userId: string, login: any): Observable<any> {
    return this._api.post(`/api/users/${userId}/campaigns/${campaignId}/sso/authentication`, login, 20000);
  }
}
