import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { AuthStore } from '../auth/auth.store';

@Injectable()
export class NavigationService {
	constructor(private _route: Router, private _authStore: AuthStore) { }

	public redirectToPath(path: string) {
		this._route.navigate(['/', path]);
	}

	public redirectToPaths(paths: Array<string>) {
		this._route.navigate(['/'].concat(paths));
	}

	public goToLogin() {
		this.redirectToPath('login');
	}

	public goToHome() {
		this.redirectToPath('home');
	}

	public goToHomeIf(permissionPredicate: (authStore: AuthStore) => boolean): boolean {
		if (!permissionPredicate)
			return true;

		if (permissionPredicate(this._authStore)) {
			this.goToHome();
			return true;
		}
		return false;
	}
}
