import { Injectable } from '@angular/core';

const KEY_STORE = 'tmpgp';

@Injectable()
export class DataStore {
	private _data: any = {};

	constructor() {
		this.loadDataFromStorage();
	}

	private storeDataToStorage() {
		sessionStorage.setItem(KEY_STORE, JSON.stringify(this._data));
	}

	private loadDataFromStorage() {
		try {
			let content = sessionStorage.getItem(KEY_STORE);
			if (content)
				this._data = JSON.parse(content);
		} catch (ex) {
			this._data = null;
		}
		if (!this._data)
			this._data = {};
	}

	public forceStore() {
		this.storeDataToStorage();
	}

	public set(key: string, value: any) {
		this._data[key] = value;
	}

	public get(key: string): any {
		if (!this._data)
			this.loadDataFromStorage();
		return this._data[key];
	}

	public remove(key: string) {
		this._data[key] = null;
	}
}
