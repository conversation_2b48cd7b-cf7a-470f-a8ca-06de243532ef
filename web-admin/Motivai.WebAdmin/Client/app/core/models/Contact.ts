import { Address } from './Address';

export class Contact {
	id: string;
	mobilePhoneDdd: string;
	mobilePhone: string;
	formatedMobilePhone: string;
	homePhoneDdd: string;
	homePhone: string;
	commercialPhoneDdd: string;
	commercialPhone: string;
	formatedCommercialPhone: string;

	talkTo: string;
	email: string;
	cep: string;
	address: string;
	number: string;
	complement: string;
	neighborhood: string;
	city: string;
	state: string;
}