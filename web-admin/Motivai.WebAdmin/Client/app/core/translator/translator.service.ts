import { Injectable } from '@angular/core';
import { TranslateService } from 'ng2-translate/ng2-translate';

@Injectable()
export class TranslatorService {
    defaultLanguage: string = 'pt_BR';
    availablelangs: any;

    constructor(private translate: TranslateService) {
        translate.setDefaultLang(this.defaultLanguage);
        this.availablelangs = [
            { code: 'pt_BR', text: 'Português' },
        ];
        this.useLanguage(this.defaultLanguage);
    }

    useLanguage(lang: string = this.defaultLanguage) {
        this.translate.use(lang);
    }

    getAvailableLanguages() {
        return this.availablelangs;
    }

}
