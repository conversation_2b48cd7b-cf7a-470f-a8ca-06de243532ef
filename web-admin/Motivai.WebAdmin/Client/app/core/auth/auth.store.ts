import { Injectable, EventEmitter } from '@angular/core';
import { JwtHelper } from 'angular2-jwt';
import { Observable } from 'rxjs/Observable';
import { ApprovalFeature } from './approval-feature';

import { Principal } from './profile';
import { RolePermissions } from './role-permissions';

const STG_KEY = 'USR-DT';
const TKN_KEY = 'USR-TK';
const APPROVAL_FEATURE_KEY = 'APPROVAL_FEATURE';
@Injectable()
export class AuthStore {
  private jwtHelper = new JwtHelper();
  private _loggedUser: Principal;
  private _roleAcl: RolePermissions;

  private userAuthenticated = false;
  private authenticationEmitter = new EventEmitter<boolean>();

  constructor() {
    this.tryLoadProfileFromStorage();
  }

  get loggedUser(): Principal {
    if (this._loggedUser == null) {
      this.tryLoadProfileFromStorage();
    }
    return this._loggedUser;
  }

  set setLayout(layout: string) {
    this._loggedUser.setLayout(layout);
  }

  set acl(acl: string[]) {
    this._loggedUser.acl = acl;
    this.createRoleFromAcl();
  }

  get role(): RolePermissions {
    if (!this._roleAcl) this.createRoleFromAcl();
    return this._roleAcl;
  }

  private tryLoadProfileFromStorage() {
    const token = sessionStorage.getItem(TKN_KEY);
    const user = sessionStorage.getItem(STG_KEY);
    if (token) {
      this.createSession(token);
    }
    if (user && this._loggedUser) {
      const rawUser = JSON.parse(user);
      this._loggedUser.acl = rawUser._acl;
      this.createRoleFromAcl();

      const approvalFeature = sessionStorage.getItem(APPROVAL_FEATURE_KEY);
      if (approvalFeature) {
        const approvalFeatureObject = JSON.parse(approvalFeature);
        this._loggedUser.setApprovalFeature(new ApprovalFeature(approvalFeatureObject['campaignId'], approvalFeatureObject['feature'], approvalFeatureObject['contextId']));
      }

      this.updateSession();
    }
  }

  private storeProfile() {
    this.authenticationEmitter.emit(this.userAuthenticated);
    sessionStorage.setItem(STG_KEY, JSON.stringify(this._loggedUser));
    sessionStorage.setItem(TKN_KEY, this._loggedUser.token);

    if (this.loggedUser.hasApprovalFeature) {
      sessionStorage.setItem(APPROVAL_FEATURE_KEY, JSON.stringify({
        campaignId: this.loggedUser.approvalFeature.campaignId,
        feature: this.loggedUser.approvalFeature.feature,
        contextId: this.loggedUser.approvalFeature.contextId
      }));
    }
  }

  private createRoleFromAcl() {
    this._roleAcl = RolePermissions.createFromAcl(this._loggedUser.acl);
  }

  public get asObservable(): Observable<boolean> {
    return this.authenticationEmitter.asObservable();
  }

  public logoff() {
    this.userAuthenticated = false;
    this._loggedUser = null;
    this.authenticationEmitter.emit(false);
    sessionStorage.clear();
  }

  public isExpired(jwt: any) {
    return false; // new Date().getTime() > jwt.exp * 1000;
  }

  public createSession(token: string): boolean {
    let jwt = null;
    try {
      jwt = this.jwtHelper.decodeToken(token);
      if (jwt == null || this.isExpired(jwt)) {
        this.logoff();
        return false;
      }
    } catch (ex) {
      this.logoff();
      return false;
    }
    this._loggedUser = new Principal(token, jwt);
    this.storeProfile();
    this.userAuthenticated = true;
    return this.userAuthenticated;
  }

  public updateSession() {
    this.storeProfile();
  }

  public isUserAuthenticated() {
    return this.userAuthenticated;
  }

  public hasPermissionTo(permissionCode: string): boolean {
    if (!this._loggedUser) return false;
    return this._loggedUser.hasPermission(permissionCode);
  }

  public hasAtLeastOnePermission(permissionCodes: Array<string>): boolean {
    if (!this._loggedUser) return false;
    return this._loggedUser.hasAnyPermission(permissionCodes);
  }

  public getToken(): string {
    return this._loggedUser.token;
  }
  get hasApprovalFeature(): boolean {
    return this._loggedUser && this._loggedUser.hasApprovalFeature;
  }

  public setApprovalFeature(approvalFeature: ApprovalFeature) {
    this._loggedUser.setApprovalFeature(approvalFeature);
    this.updateSession();
  }
}
