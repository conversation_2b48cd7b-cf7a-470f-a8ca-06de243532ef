import { ROOT_BU } from "../constants/constants-value";
import { ApprovalFeature } from "./approval-feature";

export class Principal {
  private _id: string;
  private _name: string;
  private _role: string;
  private _token: string;
  private _layout: string;
  private _startComponent: string;
  private _buId: string;
  private _acl: string[];
  private _approvalFeature: ApprovalFeature;

  constructor(token: string, jwt: any) {
    this._token = token;
    this._id = jwt.id;
    this._buId = jwt.bu;
    this._name = jwt.name;
    this._role = jwt.role;
    this._layout = jwt.layout;
    this._startComponent = jwt.startComponent;
  }

  get id(): string {
    return this._id;
  }

  get name(): string {
    return this._name;
  }

  get role(): string {
    return this._role;
  }

  get token(): string {
    return this._token;
  }

  set acl(acl: string[]) {
    this._acl = acl;
  }

  get acl(): Array<string> {
    return this._acl;
  }

  get layout(): string {
    return this._layout;
  }

  get startComponent(): string {
    return this._startComponent;
  }

  get approvalFeature(): ApprovalFeature {
    return this._approvalFeature;
  }

  get isRootBu(): boolean {
    return this._buId == ROOT_BU;
  }

  get hasApprovalFeature(): boolean {
    return this._approvalFeature && this._approvalFeature.feature && !!this._approvalFeature.contextId;
  }

  setLayout(layout: string) {
    this._layout = layout;
  }

  setApprovalFeature(approvalFeature: ApprovalFeature) {
    this._approvalFeature = approvalFeature;
  }

  // TODO: ordenar as permission para acelerar as buscas
  hasPermission(permission: string): boolean {
    if (this._acl) {
      return this._acl.indexOf(permission) >= 0;
    }
    return false;
  }

  hasAnyPermission(permissions: Array<string>): boolean {
    if (this._acl && permissions && permissions.length) {
      return !!this._acl.find(p => permissions.indexOf(p) >= 0);
    }
    return false;
  }
}
