import { Base64 } from 'js-base64';

export class ApprovalFeature {
    private _contextId: string;
    private _campaignId: string;
    private _feature: string;

    constructor(campaignId: string,  feature: string, contextId: string) {
        this._campaignId = campaignId;
        this._feature = feature;
        this._contextId = contextId;
    }

    static resolveToken(token: Base64): ApprovalFeature {
        if (!token)
            return null;

        const decodedToken: string[] = Base64.decode(token).split("|");
        return new ApprovalFeature(decodedToken[0], decodedToken[1], decodedToken[2]);
    }

    get campaignId(): string {
        return this._campaignId;
    }

    get contextId(): string {
        return this._contextId;
    }

    get feature(): string {
        return this._feature;
    }

    setEncryptedIds(encrypted) {
        this._campaignId = encrypted.campaignId;
        this._contextId = encrypted.contextId;
    }
}
