import { Injectable } from '@angular/core';
import { Observable } from 'rxjs/Observable';
import 'rxjs/add/observable/of';
import 'rxjs/add/operator/map';
import 'rxjs/add/operator/toPromise';

import { ApiService } from '../api/api.service';
import { AuthStore } from './auth.store';
import { User, UserMfa } from './user';
import { ApprovalFeature } from './approval-feature';

@Injectable()
export class AuthService {
  constructor(private _api: ApiService, private _authStore: AuthStore) {
  }

  private createSession(token: string): Observable<boolean> {

    if (!this._authStore.createSession(token))
      return Observable.of(false);
    return this._api.get('/api/auth/acl')
      .do(
        acl => {
          this._authStore.acl = acl;
          this._authStore.updateSession();
        },
        err => {
          console.error('err:', err);
          throw new Error('Ocorreu um erro ao carregar as permissões do usuário.');
        }
      )
      .map(acl => this._authStore.isUserAuthenticated());
  }

  private encryptIds(campaignId: string, contextId: string): Observable<any> {
    return this._api.post('/api/auth/encrypt', { campaignId: campaignId, contextId: contextId });
  }

  authenticate(user: User): Observable<string> {
    this._authStore.logoff();
    return this._api.postWithoutToken('/login/authenticate', user)
      .map(resp => resp.text());
  }

  authenticateWithoutMFA(user: User): Observable<boolean> {
    this._authStore.logoff();
    return this._api.postWithoutToken('/login/rpa/authenticate', user)
      .map(resp => resp.text())
      .switchMap(response => this.createSession(response));
  }

  validateMfa(user: UserMfa): Observable<boolean> {
    this._authStore.logoff();
    return this._api.postWithoutToken('/login/authentication/mfa/validate', user)
      .map(resp => resp.text())
      .switchMap(response => this.createSession(response));
  }

  resolveUrlToken(urlToken: string): Observable<any> {
    const approvalFeature = ApprovalFeature.resolveToken(urlToken);
    return this.encryptIds(approvalFeature.campaignId, approvalFeature.contextId)
      .map(encryptedIds => {
        approvalFeature.setEncryptedIds(encryptedIds);
        this._authStore.setApprovalFeature(approvalFeature);
        return approvalFeature;
      });
  }

  loadPermission(): Promise<Array<string>> {
    return this._api.get('/api/auth/acl').toPromise();
  }

  updatePassword(currentPassword: string, newPassword: string, confirmPassword: string): Observable<boolean> {
    return this._api.put('/api/auth/password', { currentPassword: currentPassword, newPassword: newPassword, confirmation: confirmPassword });
  }
}
