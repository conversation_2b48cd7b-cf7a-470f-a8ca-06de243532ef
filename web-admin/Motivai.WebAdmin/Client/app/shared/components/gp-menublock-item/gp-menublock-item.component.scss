.menublock-item {
  margin-top: 0.5em;
  container-type: inline-size;
  transition: all 0.3s ease;
}

.panel {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: none;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  transition: all 0.3s ease;
}

.panel:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.panel-body.xs {
  padding: 8px 6px 4px 6px;
  border-radius: 12px;
}
.panel-body.sm {
  padding: 10px 12px 5px 12px;
  border-radius: 14px;
}
.panel-body.md {
  padding: 12px 14px 6px 14px;
  border-radius: 16px;
}
.panel-body.lg {
  padding: 14px 16px 8px 16px;
  border-radius: 18px;
}

a {
  display: block;
  text-decoration: none;
  transition: all 0.3s ease;
}

a:hover {
  transform: scale(1.02);
}

a em {
  display: block;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease;
}

.item-text {
  display: block;
  padding-top: 3px;
  height: 32px;
  line-height: 1.1em;
  font-weight: 600;
  font-size: clamp(10px, 2.5vw, 18px);
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  hyphens: auto;
  color: #2d3748;
  transition: all 0.3s ease;
  letter-spacing: 0.3px;
}

a:hover .item-text {
  color: #4a5568;
}

.xs .item-text {
  font-size: clamp(8px, 2vw, 12px);
}
.sm .item-text {
  font-size: clamp(10px, 2.2vw, 14px);
}
.md .item-text {
  font-size: clamp(12px, 2.5vw, 16px);
}
.lg .item-text {
  font-size: clamp(14px, 3vw, 18px);
}

@container (max-width: 140px) {
  .item-text {
    font-size: clamp(8px, 1.8vw, 12px) !important;
    line-height: 1.0em;
  }
}

@container (min-width: 141px) and (max-width: 180px) {
  .item-text {
    font-size: clamp(10px, 2.2vw, 14px) !important;
    line-height: 1.05em;
  }
}

@container (min-width: 181px) {
  .item-text {
    font-size: clamp(12px, 2.5vw, 16px) !important;
    line-height: 1.1em;
  }
}
