.menublock-item {
  margin-top: 0.5em;
  container-type: inline-size;
}
.panel-body.xs {
  padding: 6px 3px 2px 5px;
}
.panel-body.sm {
  padding: 8px 10px 3px 10px;
}
.panel-body.md {
  padding: 10px 10px 4px 10px;
}
.panel-body.lg {
  padding: 12px 10px 5px 10px;
}
a em { display: block; }

.item-text {
  display: block;
  padding-top: 3px;
  height: 32px;
  line-height: 1.1em;
  font-weight: bold;
  font-size: clamp(10px, 2.5vw, 18px);
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  hyphens: auto;
}

.xs .item-text {
  font-size: clamp(8px, 2vw, 12px);
}
.sm .item-text {
  font-size: clamp(10px, 2.2vw, 14px);
}
.md .item-text {
  font-size: clamp(12px, 2.5vw, 16px);
}
.lg .item-text {
  font-size: clamp(14px, 3vw, 18px);
}

@container (max-width: 140px) {
  .item-text {
    font-size: clamp(8px, 1.8vw, 12px) !important;
    line-height: 1.0em;
  }
}

@container (min-width: 141px) and (max-width: 180px) {
  .item-text {
    font-size: clamp(10px, 2.2vw, 14px) !important;
    line-height: 1.05em;
  }
}

@container (min-width: 181px) {
  .item-text {
    font-size: clamp(12px, 2.5vw, 16px) !important;
    line-height: 1.1em;
  }
}
