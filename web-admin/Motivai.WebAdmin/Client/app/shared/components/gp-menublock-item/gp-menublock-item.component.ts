import { OnInit } from '@angular/core';
import { Component, Input } from '@angular/core';

@Component({
  selector: 'gp-menublock-item',
  template: `
    <div class="menublock-item" grid="4 3 2 2">
      <div class="panel b">
        <div [class]="'panel-body text-center ' + size">
          <a [class]="'link-unstyled ' + textClass">
            <em [class]="iconClass"></em>
            <span class="item-text">{{text}}</span>
          </a>
        </div>
      </div>
    </div>
  `,
  styleUrls: [ 'gp-menublock-item.component.scss' ]
})
export class GpMenublockItemComponent implements OnInit {
  @Input() icon: string = '';
  @Input() text: string = '';
  @Input() size: string = 'md';

  @Input() color: string = 'text-primary';

  textClass: string = '';
  iconClass: string = 'fa fa-';

  ngOnInit() {
    let iconSize = '3';
    let marginBottom = 'sm';
    if (this.size) {
      switch (this.size) {
        case 'lg':
          iconSize = '5';
          marginBottom = 'md';
          break;
        case 'md':
          iconSize = '4';
          marginBottom = 'sm';
          break;
        case 'xs':
          iconSize = '2';
          marginBottom = 'xs';
          break;
        case 'sm':
          marginBottom = 'xs';
          break;
      }
    }
    if (!this.icon) {
      this.icon = '';
    }
    this.iconClass = `fa fa-${iconSize}x fa-${this.icon} mb-${marginBottom}`;
    this.textClass = this.textClass.concat(this.color);
  }
}
