import { Router } from '@angular/router';
import { Component, OnInit, ViewChild, EventEmitter, Output } from '@angular/core';
const screenfull = require('screenfull');
const browser = require('jquery.browser');
declare var $: any;

import { SettingsService } from '../../core/settings/settings.service';
import { MenuService } from '../../core/menu/menu.service';
import { AuthStore } from '../../core/auth/auth.store';
import { LayoutType } from '../../core/auth/layout-type';

@Component({
    selector: 'app-header',
    templateUrl: './header.component.html',
    styleUrls: ['./header.component.scss']
})
export class HeaderComponent implements OnInit {
    navCollapsed = true; // for horizontal layout
    menuItems: any = []; // for horizontal layout

    @Output('setMenuState') setMenuState: EventEmitter<any> = new EventEmitter<any>();

    isNavSearchVisible: boolean;
    @ViewChild('fsbutton') fsbutton;  // the fullscreen button

    constructor(private _authStore: AuthStore, private _route: Router, menu: MenuService,
                public settings: SettingsService) {
        // show only a few items on demo
        this.menuItems = menu.getMenu().slice(0, 4); // for horizontal layout
    }

    ngOnInit() {
        this.isNavSearchVisible = false;
        if (browser.msie) { // Not supported under IE
            this.fsbutton.nativeElement.style.display = 'none';
        }
    }

    logoff(event) {
        event.preventDefault();
        this._authStore.logoff();
        this._route.navigate(['/login']);
    }

    openNavSearch(event) {
        event.preventDefault();
        event.stopPropagation();
        this.setNavSearchVisible(true);
    }

    setNavSearchVisible(stat: boolean) {
        this.isNavSearchVisible = stat;
    }

    getNavSearchVisible() {
        return this.isNavSearchVisible;
    }

    toggleCollapsedSideabar() {
        this.settings.layout.isCollapsed = !this.settings.layout.isCollapsed;
        this.setMenuState.emit(this.settings.layout.isCollapsed)
    }

    isCollapsedText() {
        return this.settings.layout.isCollapsedText;
    }

    get noSideBar() {
       return this._authStore.loggedUser.layout === LayoutType.NO_SIDEBAR || this.isApprovalFeature;
    }

    get isApprovalFeature() {
        return this._authStore.loggedUser && this._authStore.loggedUser.layout === LayoutType.APPROVAL_FEATURE;
    }
}
