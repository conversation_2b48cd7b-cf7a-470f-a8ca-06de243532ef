<!-- START Top Navbar-->
<nav class="navbar topnavbar" role="navigation">
    <!-- START navbar header-->
    <div class="navbar-header">
        <button class="navbar-toggle collapsed" type="button" (click)="navCollapsed = !navCollapsed">
            <span class="sr-only">Toggle navigation</span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
        </button>
        <a class="navbar-brand" routerLink="/">
            <div class="brand-logo">
                <img class="img-responsive" src="/assets/img/logo.png" alt="App Logo" />
            </div>
            <div class="brand-logo-collapsed">
                <img class="img-responsive" src="/assets/img/logo-single.png" alt="App Logo" />
            </div>
        </a>
    </div>
    <!-- END navbar header-->
    <!-- START Nav wrapper-->
    <div class="collapse navbar-collapse" [collapse]="navCollapsed">
        <!-- Navbar Menu -->
        <ul class="nav navbar-nav">
            <li *ngFor="let item of menuItems" [ngClass]="{'dropdown': item.submenu}" dropdown>
                <a *ngIf="!item.heading && !item.submenu" [routerLink]="item.link" [title]="item.text">
                    <span>{{(item.translate | translate) || item.text}}</span>
                </a>
                <a dropdownToggle *ngIf="item.submenu">
                    <span>{{(item.translate | translate) || item.text}}</span>
                </a>
                <!-- START Dropdown menu-->
                <ul class="dropdown-menu animated fadeInUp">
                    <li *ngFor="let subitem of item.submenu">
                        <a [routerLink]="subitem.link" [title]="subitem.text">
                            <span>{{(subitem.translate | translate) || subitem.text}}</span>
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
        <!-- End Navbar Menu-->
        <!-- START Right Navbar-->
        <ul class="nav navbar-nav navbar-right">
            <!-- START lock screen-->
            <li>
                <a title="Lock screen" [routerLink]="'/lock'">
                    <em class="icon-lock"></em>
                </a>
            </li>
            <!-- END lock screen-->
            <!-- Search icon-->
            <li>
                <a (click)="openNavSearch($event)">
                    <em class="icon-magnifier"></em>
                </a>
            </li>
            <!-- START Alert menu-->
            <li class="dropdown dropdown-list" dropdown>
                <a dropdownToggle>
                    <em class="icon-bell"></em>
                    <div class="label label-danger">11</div>
                </a>
                <!-- START Dropdown menu-->
                <ul dropdownMenu class="dropdown-menu animated flipInX">
                    <li>
                        <!-- START list group-->
                        <div class="list-group">
                            <!-- list item-->
                            <a class="list-group-item">
                                <div class="media-box">
                                    <div class="pull-left">
                                        <em class="fa fa-twitter fa-2x text-info"></em>
                                    </div>
                                    <div class="media-box-body clearfix">
                                        <p class="m0">New followers</p>
                                        <p class="m0 text-muted">
                                            <small>1 new follower</small>
                                        </p>
                                    </div>
                                </div>
                            </a>
                            <!-- list item-->
                            <a class="list-group-item">
                                <div class="media-box">
                                    <div class="pull-left">
                                        <em class="fa fa-envelope fa-2x text-warning"></em>
                                    </div>
                                    <div class="media-box-body clearfix">
                                        <p class="m0">New e-mails</p>
                                        <p class="m0 text-muted">
                                            <small>You have 10 new emails</small>
                                        </p>
                                    </div>
                                </div>
                            </a>
                            <!-- list item-->
                            <a class="list-group-item">
                                <div class="media-box">
                                    <div class="pull-left">
                                        <em class="fa fa-tasks fa-2x text-success"></em>
                                    </div>
                                    <div class="media-box-body clearfix">
                                        <p class="m0">Pending Tasks</p>
                                        <p class="m0 text-muted">
                                            <small>11 pending task</small>
                                        </p>
                                    </div>
                                </div>
                            </a>
                            <!-- last list item-->
                            <a class="list-group-item">
                                <small translate="topbar.notification.MORE">More notifications</small>
                                <span class="label label-danger pull-right">14</span>
                            </a>
                        </div>
                        <!-- END list group-->
                    </li>
                </ul>
                <!-- END Dropdown menu-->
            </li>
            <!-- END Alert menu-->
        </ul>
        <!-- END Right Navbar-->
    </div>
    <!-- END Nav wrapper-->
    <app-navsearch [visible]="getNavSearchVisible()" (onclose)="setNavSearchVisible(false)"></app-navsearch>
</nav>
<!-- END Top Navbar-->
