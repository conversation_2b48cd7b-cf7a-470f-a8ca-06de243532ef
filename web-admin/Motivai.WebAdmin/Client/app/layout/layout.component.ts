import { Component, OnInit, HostBinding } from '@angular/core';
import { SettingsService } from '../core/settings/settings.service';
import { AuthStore } from '../core/auth/auth.store';
import { LayoutType } from './../core/auth/layout-type';
import { Router, RouteConfigLoadStart, RouteConfigLoadEnd } from '@angular/router';

@Component({
    selector: 'app-layout',
    templateUrl: './layout.component.html',
    styleUrls: ['./layout.component.scss']
})
export class LayoutComponent implements OnInit {
    loadingRouteConfig: boolean = false;

    constructor(private router: Router, public settings: SettingsService, private as: AuthStore) { }

    ngOnInit() {
        this.router.events.subscribe(event => {
            if (event instanceof RouteConfigLoadStart) {
                this.loadingRouteConfig = true;
            } else if (event instanceof RouteConfigLoadEnd) {
                this.loadingRouteConfig = false;
            }
        });

        if (this.as) {
            this.settings.setLayoutSetting('isCollapsed', this.as.loggedUser.layout === LayoutType.NO_SIDEBAR || this.isApprovalFeature);
        }
    }

    get isApprovalFeature() {
        return !!this.as.loggedUser && this.as.loggedUser.layout === LayoutType.APPROVAL_FEATURE;
    }
}
