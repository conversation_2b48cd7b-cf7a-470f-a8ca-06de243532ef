<div class="wrapper">
    <!-- top navbar-->
    <app-header class="topnavbar-wrapper" (setMenuState)="setMenuState($event)"></app-header>
    <!-- sidebar-->
    <app-sidebar *ngIf="!isApprovalFeature" class="aside"></app-sidebar>
    <!-- Main section-->
    <section>
        <!-- Page content-->
        <div class="content-wrapper">
            <div class="app-spinner router-spinner" *ngIf="loadingRouteConfig">
                <i class="fa fa-spinner fa-spin fa-5x" aria-hidden="true"></i>
            </div>
            <router-outlet></router-outlet>
        </div>
    </section>
    <!-- Page footer-->
    <footer app-footer></footer>
</div>
