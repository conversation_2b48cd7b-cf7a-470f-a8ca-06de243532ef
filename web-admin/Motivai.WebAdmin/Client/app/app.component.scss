.font-awesome {
  font-family: 'FontAwesome', 'Source Sans Pro';
}

.input-uppercase {
  text-transform: uppercase
}

.table-hover > tbody > tr:hover {
  background-color: #e6e6e6;
}

.modal.gp-modal {
  margin-top: 40px;
  background-color: rgba(0, 0, 0, 0.5);
}

.menublock .active .menublock-item .panel.b {
  border-color: #FE4A00;
  box-shadow: 0px 0px 3px #FE4A00;
  -webkit-box-shadow: 0px 0px 3px #FE4A00;
}

.ico-action {
  cursor: pointer;
}
.cursor-pointer {
  cursor: pointer;
  color: #59d4ab;
}

.icon-action:hover, .icon-action:active {
  color: #59d4ab;
}

.pink-color {
  color: #3d1171;
}

ul.tooltip-list {
  padding-left: 1em;
}

div.alert {
  margin: 1em 0 1em 0;
}


.fixed-switch {
  max-width: 150px;
  padding-left: 0;
  padding-right: 0;
}

.no-horizontal-padding {
  padding-left: 0;
  padding-right: 0;
}

.no-border {
  border: 0px;
}

.tab-no-border .tab-content {
  border: 0px;
}

.no-shadow {
  box-shadow: none;
}

.no-bottom {
  margin-bottom: 0px;
}

/* Alinhas os botões com os inputs */
.col-with-button, .top-p27 {
  padding-top: 27px;
}

.top-m1 {
  margin-top: 1em;
}

.top-m5 {
  margin-top: 5em;
}

.bottom-m1 {
  margin-bottom: 1em;
}

.left-m1 {
  margin-left: 1em;
}

.right-m1 {
  margin-right: 1em;
}

.right-m2 {
  margin-right: 2em;
}

.top-p0 {
  padding-top: 0;
}

.top-p05 {
  padding-top: 0.5em;
}

.top-p1 {
  padding-top: 1em;
}

.top-p2 {
  padding-top: 2em;
}

.bottom-p0 {
  padding-bottom: 0;
}

.bottom-p1 {
  padding-left: 1em;
}

.left-p1 {
  padding-left: 1em;
}

.right-p1 {
  padding-right: 1em;
}


.banner-desktop {
  width: 100%;
  max-height: 400px;
}

.banner-mobile {
  width: 100%;
  max-width: 500px;
  max-height: 600px;
}

.text-right {
  text-align: right;
}

.clear {
  clear: both;
}

.overflow-hidden {
  overflow: hidden;
}

.import-box-actions {
  min-height: 40px;
}

.import-link-action {
  display: inline-block;
  min-width: 30%;
  max-width: 50%;
  text-decoration: none !important;
}

// arvore de menus

tree div.tree {
  order: 1px solid #ccc;
  border-radius: 5px;
  width: 100%;
  padding: 1em 0;
}

.node-content-wrapper {
  padding: 2px 5px;
  min-height: 30px;
  min-width: 150px;
  width: 100%;
  border: 1px solid #ccc;
  border-radius: 5px;
}

.node-drop-slot {
  padding: 2px 5px;
  min-height: 5px;
  min-width: 150px;
  width: 100%;
  background-color: #e0ffde;
  margin: 3px;
}
