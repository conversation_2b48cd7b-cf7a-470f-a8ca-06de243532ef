.modern-login-wrapper {
  min-height: 100vh;
  // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background: linear-gradient(135deg, #4c0f94 0%, #ffad8c 100%);
  // background: linear-gradient(135deg, #dec2ff 20%, #ffaf8c 150%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.login-container {
  width: 100%;
  max-width: 450px;
  position: relative;
  z-index: 1;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transition: all 0.3s ease;
}

.login-card:hover {
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.3);
}

.logo-section {
  background: linear-gradient(135deg, #3d1171 0%, #5d9cec 100%);
  background: linear-gradient(135deg, #4c0f94 40%, #FE4A00 100%);
  padding: 40px 20px;
  text-align: center;
  position: relative;
}

.logo-section::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(135deg, transparent 49%, rgba(255, 255, 255, 0.1) 50%, transparent 51%);
}

.logo-container {
  display: inline-block;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.logo {
  width: 120px;
  height: auto;
  filter: brightness(0) invert(1);
  transition: all 0.3s ease;
}

.logo:hover {
  transform: scale(1.05);
}

.form-section {
  padding: 40px 30px;
}

.welcome-text {
  text-align: center;
  margin-bottom: 30px;
}

.welcome-text h2 {
  color: #2c3e50;
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 10px 0;
  letter-spacing: -0.5px;
}

.welcome-text p {
  color: #7f8c8d;
  font-size: 16px;
  margin: 0;
  line-height: 1.5;
}

.modern-form {
  margin-top: 30px;
}

.input-group {
  width: 100%;
  margin-bottom: 25px;
}

.input-wrapper {
  position: relative;
  margin-bottom: 5px;
}

.input-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #bdc3c7;
  font-size: 16px;
  z-index: 2;
  transition: all 0.3s ease;
}

.modern-input {
  width: 100%;
  padding: 15px 15px 15px 45px;
  border: 2px solid #ecf0f1;
  border-radius: 12px;
  font-size: 16px;
  background: #fff;
  transition: all 0.3s ease;
  outline: none;
  box-sizing: border-box;
}

.modern-input:focus {
  border-color: #5d9cec;
  box-shadow: 0 0 0 3px rgba(61, 17, 113, 0.1);
}

.modern-input:focus+.floating-label,
.modern-input:not(:placeholder-shown)+.floating-label {
  transform: translateY(-25px) scale(0.85);
  color: #5d9cec;
}

.modern-input:focus~.input-icon {
  color: #5d9cec;
}

.floating-label {
  position: absolute;
  left: 45px;
  top: 50%;
  transform: translateY(-50%);
  color: #bdc3c7;
  font-size: 16px;
  pointer-events: none;
  transition: all 0.3s ease;
  background: #fff;
  padding: 0 5px;
  z-index: 1;
}

.error-message {
  display: flex;
  align-items: center;
  color: #e74c3c;
  font-size: 14px;
  margin-top: 5px;
  animation: slideIn 0.3s ease;
}

.error-message i {
  margin-right: 5px;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.captcha-section {
  margin: 25px 0;
  display: flex;
  justify-content: center;
}

.button-section {
  margin-top: 30px;
}

.button-section gp-spinner-button {
  width: 100%;
}

.button-section gp-spinner-button button {
  width: 100%;
  padding: 15px 20px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  border: none;
  background: linear-gradient(135deg, #3d1171 0%, #5d9cec 100%);
  color: white;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(61, 17, 113, 0.3);
}

.button-section gp-spinner-button button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(61, 17, 113, 0.4);
}

.button-section gp-spinner-button button:active {
  transform: translateY(0);
}

.button-section gp-spinner-button button:disabled {
  opacity: 0.7;
  transform: none;
  cursor: not-allowed;
}

.modern-footer {
  margin-top: 30px;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
}

.alert-section {
  margin-top: 20px;
}

@media (max-width: 768px) {
  .modern-login-wrapper {
    padding: 10px;
  }

  .login-container {
    max-width: 100%;
  }

  .form-section {
    padding: 30px 20px;
  }

  .welcome-text h2 {
    font-size: 24px;
  }

  .logo {
    width: 100px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .modern-login-wrapper {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .login-card {
    background: rgba(30, 30, 30, 0.95);
  }

  .welcome-text h2 {
    color: #ecf0f1;
  }

  .welcome-text p {
    color: #bdc3c7;
  }

  .modern-input {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    color: #ecf0f1;
  }

  .floating-label {
    background: transparent;
  }
}
