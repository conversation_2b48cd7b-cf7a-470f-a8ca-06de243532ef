import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { RecaptchaComponent } from 'ng-recaptcha';

import { GpAlertComponent } from '../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { SettingsService } from '../../core/settings/settings.service';
import { AuthService } from '../../core/auth/auth.service';
import { User, UserMfa } from '../../core/auth/user';
import { MenuController } from '../menu-controller';
import { AuthStore } from '../../core/auth/auth.store';
import { StartComponentType } from '../../core/auth/start-component';
import { AdminRoutingModule } from '../admin/admin-routing.module';

enum LOGIN_STEP { AUTHENTICATION, SECURITY_CODE_CONFIRMATION }

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent implements OnInit {
  @ViewChild('loginAlert') loginAlert: GpAlertComponent;
  @ViewChild('reCaptcha') reCaptcha: RecaptchaComponent;
  @Input('skipSecurityValidation') skipSecurityValidation: boolean = false;

  valForm: FormGroup;
  loading = false;

  loginStep: LOGIN_STEP = LOGIN_STEP.AUTHENTICATION;
  user = new User();
  userMfa = new UserMfa();
  mfaRetryCount: number = 0;
  recaptchaToken: any = null;
  urlToken: string;

  constructor(public settings: SettingsService, fb: FormBuilder, private _authService: AuthService,
    private _route: Router, private _activatedRoute: ActivatedRoute, private _menuCtrl: MenuController,
    private _authStore: AuthStore) {
    this.user = new User();
    this.valForm = fb.group({
      'login': [this.user.login, Validators.required],
      'password': [this.user.password, Validators.required]
    });
  }

  get isAuthentication() {
    return this.loginStep != LOGIN_STEP.SECURITY_CODE_CONFIRMATION;
  }

  get isConfirmation() {
    return this.loginStep == LOGIN_STEP.SECURITY_CODE_CONFIRMATION;
  }

  ngOnInit(): void {
    this._activatedRoute.queryParams.subscribe(params => {
      if (params['token']) {
        this.urlToken = params['token'];
      }
    });
  }

  submitForm(event, value: any) {
    event.preventDefault();
    for (let c in this.valForm.controls) {
      this.valForm.controls[c].markAsTouched();
    }

    if (!this.valForm.value) {
      this.loginAlert.showError('Preencha os campos para continuar.');
      return;
    }

    this.user = value;

    if (!this.skipSecurityValidation) {
      if (!this.recaptchaToken) {
        this.loginAlert.showError('Preencha o reCaptcha para prosseguir.');
        return;
      }

      this.user.recaptchaToken = this.recaptchaToken;
      this.authenticateUser();
    } else {
      this.authenticateUserWithoutMFA();
    }
  }

  private handleError(error: any) {
    this.recaptchaToken = null;
    if (this.reCaptcha) {
      this.reCaptcha.reset();
    }
    this.loginAlert.handleAndShowError(error);
    if (error == 'Sessão expirada.') {
      this.clear();
    }
  }

  private clear() {
    this.user = new User();
    this.userMfa = new UserMfa();
    this.mfaRetryCount = 0;
    this.showLoginForm();
  }

  private showLoginForm() {
    this.loginStep = LOGIN_STEP.AUTHENTICATION;
  }

  private showSecurityCodeForm() {
    this.loginStep = LOGIN_STEP.SECURITY_CODE_CONFIRMATION;
  }

  resolvedCaptcha(response: any) {
    this.recaptchaToken = response;
  }

  authenticateUserWithoutMFA() {
    this.loginAlert.clear();

    this.loading = true;
    this._authService.authenticateWithoutMFA(this.user)
      .subscribe(
        logged => {
          if (logged) {
            if (this.urlToken) {
              this.resolveTokenAndRedirect();
            } else {
              this._menuCtrl.reloadMenu();
              this.resolveRouteByStartComponent();
            }
          } else {
            this.loading = false;
            this.handleError('Usuário e/ou senha inválidos.');
          }
        },
        (err: any) => this.handleLoginError(err)
      );
  }

  authenticateUser() {
    this.loginAlert.clear();

    this.loading = true;
    this._authService.authenticate(this.user)
      .subscribe(
        sessionId => {
          this.loading = false;
          if (sessionId) {
            this.userMfa.sessionId = sessionId;
            this.showSecurityCodeForm();
          } else {
            this.handleError('Usuário e/ou senha inválidos.');
          }
        },
        (err: any) => this.handleLoginError(err)
      );
  }

  private handleLoginError(err: any) {
    if (err.status == 401) {
      this.handleError('Usuário e/ou senha inválidos.');
    } else if (err.status == 500) {
      this.handleError('Ocorreu um erro durante a autenticação.');
    } else {
      if (err.text)
        err.text().then(text => this.handleError(text));

      else
        this.handleError(err);
    }
    this.loading = false;
  }

  validateUserMfa() {
    this.loginAlert.clear();

    this.loading = true;
    this.userMfa.recaptchaToken = this.recaptchaToken;
    this.mfaRetryCount += 1;
    this._authService.validateMfa(this.userMfa)
      .subscribe(
        logged => {
          if (logged) {
            if (this.urlToken) {
              this.resolveTokenAndRedirect();
            } else {
              this._menuCtrl.reloadMenu();
              this.resolveRouteByStartComponent();
            }
          } else {
            this.loading = false;
            this.handleError('Código de segurança inválido.');
            if (this.mfaRetryCount > 3) {
              this.clear();
            }
          }
        },
        (err: any) => {
          if (err.status == 401) {
            this.handleError('Código de segurança inválido.');
          } else if (err.status == 500) {
            this.handleError('Ocorreu um erro durante a autenticação.');
          } else {
            if (err.text)
              err.text().then(text => this.handleError(text));
            else
              this.handleError(err);
          }
          if (this.mfaRetryCount > 3) {
            this.clear();
          }
          this.loading = false;
        }
      );
  }

  private resolveTokenAndRedirect() {
    this._authService.resolveUrlToken(this.urlToken)
      .subscribe(
        approvalFeature => {
          this._menuCtrl.reloadByApprovalFeature();
          this._route.navigate(AdminRoutingModule.resolveFeatureRoute(approvalFeature.campaignId, approvalFeature.feature));
        }
      );
  }

  private resolveRouteByStartComponent() {
    switch (this._authStore.loggedUser.startComponent) {
      case StartComponentType.DASHBOARD: return this._route.navigate(['/']);
      case StartComponentType.CAMPAIGNS_LIST: return this._route.navigate(['/campanha']);
      default: return this._route.navigate(['/']);
    }
  }

}


