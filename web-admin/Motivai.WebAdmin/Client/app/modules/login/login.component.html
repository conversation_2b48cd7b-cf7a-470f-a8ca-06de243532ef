<div class="modern-login-wrapper">
	<div class="login-container">
		<div class="login-card">
			<div class="logo-section">
				<div class="logo-container">
					<img class="logo" src="/assets/img/logo.png" alt="Motivai Logo"/>
				</div>
			</div>

			<div class="form-section">
				<div *ngIf="isAuthentication" class="auth-form">
					<div class="welcome-text">
						<h2>Bem-vindo de volta!</h2>
						<p>Entre com suas credenciais para acessar sua conta</p>
					</div>

					<form [formGroup]="valForm" class="modern-form" role="form" name="loginForm" novalidate=""
						(submit)="submitForm($event, valForm.value)">

						<div class="input-group">
							<div class="input-wrapper">
								<i class="fa fa-user input-icon"></i>
								<input class="modern-input" type="text" name="login" placeholder="" autocomplete="off" required="required" formControlName="login" />
								<label class="floating-label">Usuário</label>
							</div>
							<span class="error-message" *ngIf="valForm.controls['login'].hasError('required') && (valForm.controls['login'].dirty || valForm.controls['login'].touched)">
								<i class="fa fa-exclamation-circle"></i> Login é obrigatório
							</span>
						</div>

						<div class="input-group">
							<div class="input-wrapper">
								<i class="fa fa-lock input-icon"></i>
								<input class="modern-input" type="password" name="password" placeholder="" required="required" formControlName="password" />
								<label class="floating-label">Senha</label>
							</div>
							<span class="error-message" *ngIf="valForm.controls['password'].hasError('required') && (valForm.controls['password'].dirty || valForm.controls['password'].touched)">
								<i class="fa fa-exclamation-circle"></i> Senha é obrigatória
							</span>
						</div>

						<div class="captcha-section" *ngIf="!skipSecurityValidation">
							<re-captcha #reCaptcha (resolved)="resolvedCaptcha($event)" size="normal">
							</re-captcha>
						</div>

						<div class="button-section">
							<gp-spinner-button type="submit" text="Entrar" [actionPrimary]="true" size="lg" icon="sign-in"
								[block]="true" [loading]="loading" loadingText="Aguarde">
							</gp-spinner-button>
						</div>
					</form>
				</div>

				<div *ngIf="isConfirmation" class="mfa-form">
					<div class="welcome-text">
						<h2>Verificação de Segurança</h2>
						<p>Digite o código de segurança enviado para você</p>
					</div>

					<form class="modern-form">
						<div class="input-group">
							<div class="input-wrapper">
								<i class="fa fa-shield input-icon"></i>
								<input class="modern-input" type="text" name="securityCode" placeholder="" autocomplete="off"
									required="required" [(ngModel)]="userMfa.securityCode" />
								<label class="floating-label">Código de Segurança</label>
							</div>
						</div>

						<div class="button-section">
							<gp-spinner-button text="Confirmar" [actionPrimary]="true" size="lg" icon="check"
								[block]="true" [loading]="loading" loadingText="Aguarde" (click)="validateUserMfa()">
							</gp-spinner-button>
						</div>
					</form>
				</div>

				<div class="alert-section">
					<gp-alert #loginAlert></gp-alert>
				</div>
			</div>
		</div>

		<footer app-footer class="modern-footer"></footer>
	</div>
</div>
