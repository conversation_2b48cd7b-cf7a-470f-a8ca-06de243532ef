import { Injectable } from '@angular/core';
import { ApiService } from '../../../../core/api/api.service';
import { Observable } from 'rxjs/Observable';

@Injectable()
export class B2bUsersService {
    constructor(private _api: ApiService) {}

    save(user: any): Observable<any> {
        if (user.id) {
            return this._api.put(`/api/usersb2bportal/${user.id}`, user, 20000);
        }

        return this._api.post(`/api/usersb2bportal`, user, 20000);
    }

    resetPassword(userId: string): Observable<any> {
        return this._api.put(`/api/usersb2bportal/${userId}/password/reset`, null, 20000);
    }

    findById(userId: string): Observable<any> {
        return this._api.get(`/api/usersb2bportal/${userId}`, null, 20000);
    }

    find(document: string, skip: number, limit: number): Observable<any[]> {
        const params: any = {};
        if (document) params.document = document;
        if (skip) params.skip = skip;
        if (limit) params.limit = limit;
        return this._api.get(`/api/usersb2bportal`, params, 20000);
    }
}
