import { Component, OnInit, ViewChild } from '@angular/core';
import { TabsetComponent } from 'ng2-bootstrap';
import { B2bUsersListComponent } from './users-list/b2b-users-list.component';
import { B2bUsersEditComponent } from './users-edit/b2b-users-edit.component';
import { AuthStore } from '../../../../core/auth/auth.store';
import { PERMISSION_B2B_USER_EDIT } from '../../../../core/auth/access-points';

@Component({
    selector: 'b2b-users',
    templateUrl: './b2b-users.component.html'
})
export class B2bUsersComponent implements OnInit {
    userId: string = '';
    
    @ViewChild('tabs') tabs: TabsetComponent;
    @ViewChild('listComponent') listComponent: B2bUsersListComponent;
    @ViewChild('editComponent') editComponent: B2bUsersEditComponent;

    constructor(private _auth: AuthStore) { }
    get canEditUser(){
        return this._auth.hasPermissionTo(PERMISSION_B2B_USER_EDIT);
    }
    ngOnInit(): void { }

    refresh() {
        this.listComponent.find();
        this.editComponent.clear();
        this.userId = '';
    }

    edit($event: any) {
        this.userId = $event;
        this.tabs.tabs[1].active = true;
    }
}
