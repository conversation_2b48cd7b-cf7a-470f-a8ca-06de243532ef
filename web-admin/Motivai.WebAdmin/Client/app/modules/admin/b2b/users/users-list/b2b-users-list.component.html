<gp-card [first]="true" title="Pesquisa">
	<gp-form-row>
		<gp-form-col cols="12 6 6">
			<gp-simple-input label="Documento (CPF/CNPJ)">
				<input type="text" class="form-control" [(ngModel)]="filter.document" />
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 3 3">
			<gp-spinner-button text="Pesquisar" [search]="true" [loading]="loading" marginTop="26px" (click)="find()"></gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
	<gp-alert #alert [overlay]="true"></gp-alert>
</gp-card>
<gp-card title="Resultados encontrados">
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<gp-grid name="grid" [rows]="users" [columns]="[ 'Documento', 'Nome', 'Tipo' ]" [fields]="[ 'document', 'name', 'typeDescription' ]" 
				[showActive]="true" [showPagination]="true" [pageSize]="limit" [showTotalPages]="false"
				[showEdit]="true" [showDelete]="false" [loading]="loading" (onEdit)="edit($event)" (onPageChanged)="pageChanged($event)">
			</gp-grid>
		</gp-form-col>
	</gp-form-row>
</gp-card>