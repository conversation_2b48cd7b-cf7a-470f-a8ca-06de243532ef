<form #f="ngForm" novalidate>
<gp-card title="Dados do usuário" [first]="true">
	<gp-form-row>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Documento (CPF/CNPJ)" [required]="true" errorMessage="Documento é obrigatório">
				<gp-input-mask name="document" [onlyInteger]="true" [thousandsSeparator]="false" required [(ngModel)]="user.document"></gp-input-mask>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 8 8">
			<gp-simple-input label="Nome" [required]="true" errorMessage="Nome é obrigatório">
				<input type="text" name="name" class="form-control" required [(ngModel)]="user.name" />
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Login" [required]="true" errorMessage="Login é obrigatório">
				<input type="text" name="login" class="form-control" required [(ngModel)]="user.login" />
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 8 8">
			<gp-simple-input label="Email" [required]="true" errorMessage="Digite um email válido!">
				<input type="text" name="email" class="form-control" pattern="^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$" required [(ngModel)]="user.email" />
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Celular (DDD + Número)">
				<gp-input-mask name="mobilePhone" mask="00000000000" [(ngModel)]="user.mobilePhone"></gp-input-mask>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Tipo de usuário" [required]="true" errorMessage="Tipo é obrigatório">
				<select class="form-control" name="type" [(ngModel)]="user.type">
					<option value="">Selecione</option>
					<option value="Seller">Vendedor</option>
					<option value="Representative">Representante</option>
				</select>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4">
			<label>Ativo</label>
			<div>
				<gp-switch name="active" [(ngModel)]="user.active"></gp-switch>
			</div>
		</gp-form-col>
	</gp-form-row>
	<spinner [overlay]="true" [show]="loadingScreen"></spinner>
</gp-card>

<gp-card title="Campanhas relacionadas">
	<gp-form-row>
		<gp-form-col cols="12 6 6">
			<gp-simple-input label="Campanha">
				<ng-select name="campaignId" name="selectedCampaign" [items]="campaigns" [(ngModel)]="selectedCampaign"></ng-select>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 3 3">
			<gp-spinner-button type="button" [pink]="true" text="Vincular" icon="plus" marginTop="26px" (click)="linkCampaign()"></gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<gp-grid name="grid" [rows]="user.campaigns" [columns]="[ 'Descrição' ]" [fields]="[ 'name' ]"
				[showActive]="false" [showPagination]="false" [showTotalPages]="false"
				[showEdit]="false" [showDelete]="true" (onDelete)="unlinkCampaign($event)">
			</gp-grid>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card title="Fábricas/Distribuidoras relacionadas">
	<gp-form-row>
		<gp-form-col cols="12 6 6">
			<gp-simple-input label="Fábrica/Distribuidora">
				<ng-select name="factoryId" [items]="factories" [(ngModel)]="factory.selectedFactory"></ng-select>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 2 2">
			<gp-simple-input label="CEP De">
				<gp-input-mask name="cepFrom" mask="00000-000" [cleanMask]="true" [(ngModel)]="factory.cepFrom"></gp-input-mask>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 2 2">
			<gp-simple-input label="CEP Até">
				<gp-input-mask name="cepTo" mask="00000-000" [cleanMask]="true" [(ngModel)]="factory.cepTo"></gp-input-mask>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 2 2">
			<gp-spinner-button type="button" [pink]="true" text="Vincular" icon="plus" marginTop="26px" (click)="linkFactory()"></gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<gp-grid name="grid" [rows]="user.factories" [columns]="[ 'Nome', 'CEP De', 'Cep Até' ]" [fields]="[ 'name', 'cepFrom', 'cepTo' ]"
				[showActive]="false" [showPagination]="false" [showTotalPages]="false"
				[showEdit]="false" [showDelete]="true" (onDelete)="unlinkFactory($event)">
			</gp-grid>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card *ngIf="canEditB2bUser">
	<div class="row">
		<div grid="12 12 12">
			<gp-spinner-button type="button" text="Salvar usuário" [pink]="true" icon="send" (click)="save()" pull="right" [loading]="loading" [disabled]="!f.valid"></gp-spinner-button>
			<gp-spinner-button type="button" bootstrapClass="primary" text="Resetar Senha" (click)="resetPassword()" *ngIf="enableResetPassword" pull="right" marginRight="5px" [loading]="loading"></gp-spinner-button>
			<gp-spinner-button type="button" [actionSecondary]="true" text="Novo" (click)="clear()" pull="right" marginRight="5px" [loading]="loading"></gp-spinner-button>
		</div>
	</div>
	<gp-alert #alert [overlay]="true"></gp-alert>
</gp-card>
</form>
