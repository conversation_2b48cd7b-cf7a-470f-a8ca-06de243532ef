import { Component, OnInit, ViewChild, Input } from '@angular/core';
import { NgForm } from '@angular/forms';

import { CampaignService } from '../../../campaigns/campaign.service';
import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { FactoryService } from '../../factories/factory.service';
import { B2bUsersService } from '../b2b-users.service';
import { AuthStore } from '../../../../../core/auth/auth.store';
import { PERMISSION_B2B_USER_EDIT } from '../../../../../core/auth/access-points';

@Component({
    selector: 'b2b-users-edit',
    templateUrl: './b2b-users-edit.component.html'
})
export class B2bUsersEditComponent implements OnInit {
    private _userId: string;
    @Input() set userId(v: string) {
        if (v) {
            this._userId = v;
            this.findUser();
        }
    }

    user: any = {};
    campaigns: any[] = [];
    selectedCampaign: any[] = [];
    factory: any = {};
    factories: any[] = [];
    loadingCampaigns: boolean = false;
    loadingFactories: boolean = false;
    loadingUser: boolean = false;
    loading: boolean = false;

    @ViewChild('alert') alert: GpAlertComponent;
    @ViewChild('f') form: NgForm;

    constructor(private _campaignService: CampaignService, private _factoryService: FactoryService,
        private _b2bUsersService: B2bUsersService, private _auth: AuthStore) { }

    get canEditB2bUser() {
        return this._auth.hasPermissionTo(PERMISSION_B2B_USER_EDIT);
    }
    ngOnInit(): void {
        this.loadCampaigns();
        this.loadFactories();
    }

    clear() {
        this.user = {};
        this.selectedCampaign = [];
        this.factory = {};
        this._userId = '';

        Object.keys(this.form.controls).forEach(key => {
            this.form.controls[key].reset();
        });
    }

    loadCampaigns() {
        this.loadingCampaigns = true;
        this._campaignService.getAll(null, null, 'B2B', 0, 2000).subscribe(
            campaigns => {
                if (campaigns) {
                    this.campaigns = campaigns.map(campaign => ({ id: campaign.id, text: campaign.name }));
                }
                this.loadingCampaigns = false;
            },
            err => {
                this.alert.showError(err, true);
                this.loadingCampaigns = false;
            }
        );
    }

    loadFactories() {
        this.loadingFactories = true;
        this._factoryService.search(null, 0, 2000, true, null).subscribe(
            factories => {
                if (factories) {
                    this.factories = factories.map(factory => ({ id: factory.id, text: `${factory.type === 'Factory' ? '[F]' : '[D]'} ${factory.name}` }));
                }
                this.loadingFactories = false;
            },
            err => {
                this.alert.showError(err, true);
                this.loadingFactories = false;
            }
        );
    }

    findUser() {
        if (this._userId) {
            this.loadingUser = true;
            this._b2bUsersService.findById(this._userId).subscribe(
                user => {
                    this.user = user || {};
                    this.loadingUser = false;
                },
                err => {
                    this.alert.showError(err, true);
                    this.loadingUser = false;
                }
            );
        }
    }

    save() {
        if (this.canEditB2bUser) {
            this.loading = true;
            this._b2bUsersService.save(this.user).subscribe(
                response => {
                    if (response) {
                        if (!this.user.id) this.user = response;
                        if (this.user.generatedPassword) {
                            this.alert.showSuccess(`Dados do usuário salvos com sucesso. Anote a senha, ela não será exibida novamente: ${this.user.generatedPassword}!`);
                            this.user.generatedPassword = '';
                        } else {
                            this.alert.showSuccess('Dados do usuário salvos com sucesso!');
                        }
                    } else {
                        this.alert.showError('Ocorreu um erro ao salvar os dados do usuário. Tente novamente!');
                    }
                    this.loading = false;
                },
                err => {
                    this.alert.showError(err, true);
                    this.loading = false;
                }
            );
        }
        if (!this.canEditB2bUser) return this.alert.showInfo('O usuario logado não possue permissão, Por favor entre em contato com o admistrador');
    }

    resetPassword() {
        if (this.canEditB2bUser) {
            this.loading = true;
            this._b2bUsersService.resetPassword(this._userId).subscribe(
                password => {
                    if (password) {
                        this.alert.showSuccess(`Senha gerada com sucesso. Anote, pois não será mostrada novamente: ${password}`);
                    } else {
                        this.alert.showError('Ocorreu um erro ao gerar a nova seha');
                    }
                    this.loading = false;
                },
                err => {
                    this.alert.showError(err, true);
                    this.loading = false;
                }
            );
        }
        if (!this.canEditB2bUser) return this.alert.showInfo('O usuario logado não possue permissão, Por favor entre em contato com o admistrador');
    }

    linkCampaign() {
        if (this.selectedCampaign && this.selectedCampaign.length) {
            const selectedCampaign = this.selectedCampaign[0];
            if (!this.user) this.user = {};
            if (!this.user.campaigns) this.user.campaigns = [];

            const campaignExists = this.user.campaigns.find(c => c.campaignId === selectedCampaign.id);
            if (campaignExists) {
                return this.alert.showError('A campanha selecionada já está vinculada');
            }

            this.user.campaigns.push({ campaignId: selectedCampaign.id, name: selectedCampaign.text });
        }
    }

    unlinkCampaign($event: any) {
        if ($event) {
            if (!this.user) return;
            if (!this.user.campaigns) return;

            const campaign = this.user.campaigns.find(c => c.campaignId === $event.campaignId);
            if (!campaign) return;

            this.user.campaigns.splice(this.user.campaigns.indexOf(campaign), 1);
        }
    }

    linkFactory() {
        if (this.factory && this.factory.selectedFactory && this.factory.selectedFactory.length) {
            const selectedFactory = this.factory.selectedFactory[0];
            if (!this.user) this.user = {};
            if (!this.user.factories) this.user.factories = [];

            const factoryExists = this.user.factories.find(f => f.factoryId === selectedFactory.id);
            if (factoryExists) {
                return this.alert.showError('A fábrica/distribuidora selecionada já está vinculada');
            }
            if (this.factory.cepFrom) {
                if (this.factory.cepFrom.length == 0) {
                    this.factory.cepFrom = null;
                } else if (this.factory.cepFrom.length != 8) {
                    this.alert.showError('CEP De inválido.');
                    return;
                }
            }
            if (this.factory.cepTo) {
                if (this.factory.cepTo.length == 0) {
                    this.factory.cepTo = null;
                } else if (this.factory.cepTo.length != 8) {
                    this.alert.showError('CEP Até inválido.');
                    return;
                }
            }
            if (this.factory.cepFrom && this.factory.cepTo) {
                const parsedCepFrom = parseInt(this.factory.cepFrom, 10);
                const parsedCepTo = parseInt(this.factory.cepTo, 10);
                if (parsedCepFrom > parsedCepTo) {
                    this.alert.showError('CEP De precisa ser menor que CEP Até');
                    return;
                }
            }

            this.user.factories.push({
                factoryId: selectedFactory.id,
                name: selectedFactory.text,
                cepFrom: this.factory.cepFrom,
                cepTo: this.factory.cepTo
            });

            this.factory.cepFrom = '';
            this.factory.cepTo = '';
        }
    }

    unlinkFactory($event: any) {
        if ($event) {
            if (!this.user) this.user = {};
            if (!this.user.factories) this.user.factories = [];

            const factory = this.user.factories.find(f => f.factoryId === $event.factoryId);
            if (!factory) return;

            this.user.factories.splice(this.user.factories.indexOf(factory), 1);
        }
    }

    get loadingScreen(): boolean {
        return this.loadingUser || this.loadingCampaigns || this.loadingFactories;
    }

    get enableResetPassword() {
        if (this._userId) return true;
        return false;
    }

}
