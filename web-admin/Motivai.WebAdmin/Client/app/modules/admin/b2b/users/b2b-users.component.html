<div class="content-heading">Usuários B2B
	<small>Gerenciamento de usuários do portal B2B</small>
</div>
<tabset #tabs class="bg-white p0 tab-no-border" [justified]="true">
	<tab (select)="refresh()">
		<ng-template tabHeading>Busca</ng-template>
		<b2b-users-list #listComponent (onedit)="edit($event)"></b2b-users-list>
	</tab>
	<tab *ngIf="canEditUser">
		<ng-template tabHeading >Cadastro</ng-template>
		<b2b-users-edit #editComponent [userId]="userId"></b2b-users-edit>
	</tab>
</tabset>