import { Component, OnInit, ViewChild, Output, EventEmitter } from '@angular/core';
import { B2bUsersService } from '../b2b-users.service';
import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { AuthStore } from '../../../../../core/auth/auth.store';
import { PERMISSION_B2B_USER_LIST } from '../../../../../core/auth/access-points';

@Component({
    selector: 'b2b-users-list',
    templateUrl: './b2b-users-list.component.html'
})
export class B2bUsersListComponent implements OnInit {
    filter: any = {};
    users: any[] = [];
    skip: number = 0;
    limit: number = 100;
    loading: boolean = false;

    @ViewChild('alert') alert: GpAlertComponent;
    @Output('onedit') onedit: EventEmitter<any> = new EventEmitter();

    constructor(private _b2bUsersService: B2bUsersService, private _auth: AuthStore) { }

    get canListB2bUser(){
        return this._auth.hasPermissionTo(PERMISSION_B2B_USER_LIST);
    }


    ngOnInit(): void {
        this.find();
    }

    find() {
        if (this.canListB2bUser) {
            this.loading = true;
            this._b2bUsersService.find(this.filter.document, this.skip, this.limit).subscribe(
                users => {
                    this.users = users || [];
                    this.loading = false;
                },
                err => {
                    this.alert.showError(err, true);
                    this.loading = false;
                }
            );
        }
        if (!this.canListB2bUser) return this.alert.showInfo('O usuario logado não possue permissão, Por favor entre em contato com o admistrador');

    }

    pageChanged($event: any) {
        if ($event) {
            this.skip = $event.skip;
            this.limit = $event.limit;
            this.find();
        }
    }

    edit($event) {
        if ($event) {
            this.onedit.emit($event.id);
        }
    }
}
