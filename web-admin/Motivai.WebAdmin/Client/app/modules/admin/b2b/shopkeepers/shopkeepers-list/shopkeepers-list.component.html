<gp-card [first]="true" title="Pesquisa">
	<gp-form-row>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Nome">
				<input type="text" class="form-control" [(ngModel)]="filters.name" />
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Documento">
				<input type="text" class="form-control" [(ngModel)]="filters.document" />
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4">
			<gp-spinner-button text="Pesquisar" [search]="true" [loading]="loading" [inline]="true" (click)="find()"></gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
	<gp-alert #alert [overlay]="true"></gp-alert>
</gp-card>

<gp-card title="Resultados encontrados">
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<gp-grid [columns]="[ 'Documento', 'Nome', 'Email' ]" [fields]="[ 'document', 'name', 'email' ]" [rows]="shopkeepers" 
				[loading]="loading" [showEdit]="true" [showDelete]="false" [showPagination]="true" 
				[showTotalPages]="false" [showActive]="true" [pageSize]="limit"
				(onEdit)="editShopkeeper($event)" (onPageChanged)="pageChanged($event)">
			</gp-grid>
		</gp-form-col>
	</gp-form-row>
</gp-card>