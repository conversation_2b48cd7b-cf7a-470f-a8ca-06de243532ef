import { Component, OnInit, Input, ViewChild } from '@angular/core';
import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { NgForm, FormBuilder, Validators, FormGroup } from '@angular/forms';
import { CustomValidators } from 'ng2-validation';
import { REGEX_CEP, REGEX_CNPJ, REGEX_CELLPHONE, REGEX_TELEPHONE } from '../../../../../shared/formatters/regex';
import { CampaignService } from '../../../campaigns/campaign.service';
import { CorreiosService } from '../../../../../core/services/correios.service';
import { B2BShopkeeperService } from '../B2bShopkeeperService';
import { AuthStore } from '../../../../../core/auth/auth.store';
import { PERMISSION_SHOPKEEPERS_LIST, PERMISSION_SHOPKEEPERS_EDIT } from '../../../../../core/auth/access-points';

@Component({
    selector: 'shopkeepers-edit',
    templateUrl: './shopkeepers-edit.component.html'
})
export class ShopkeepersEditComponent implements OnInit {
    private _shopkeeperId: string;
    @Input() set shopkeeperId(v: string) {
        if (v) {
            this._shopkeeperId = v;
            this.findById();
        }
    }
    get shopkeeperId() {
        return this._shopkeeperId;
    }

    selectedCamp: any = {
        campaign: [],
        active: false
    };
    loading: boolean = false;
    loadingShopkeeper: boolean = false;
    loadingCampaigns: boolean = false;
    loadingScreen: boolean = false;
    campaigns: any[] = [];
    shopkeeper: any = {
        address: {},
        campaigns: []
    };

    @ViewChild('alert') alert: GpAlertComponent;
    @ViewChild('f') form: NgForm;

    constructor(private _campaignService: CampaignService, private _correiosService: CorreiosService,
        private _b2bShopkeeperService: B2BShopkeeperService, private _authStore: AuthStore) { }

    ngOnInit(): void {
        this.loadCampaigns();
    }
    get canListShopeekepers() {
        return this._authStore.hasPermissionTo(PERMISSION_SHOPKEEPERS_LIST);
    }
    get canEditShopeekepers() {
        return this._authStore.hasPermissionTo(PERMISSION_SHOPKEEPERS_EDIT);
    }
    findById() {
        if (this.canListShopeekepers) {
            if (this._shopkeeperId) {
                this.loadingScreen = true;
                this._b2bShopkeeperService.findById(this._shopkeeperId).subscribe(
                    shopkeeper => {
                        this.shopkeeper = shopkeeper || {};
                        if (!this.shopkeeper.address) this.shopkeeper.address = {};
                        if (!this.shopkeeper.campaigns) this.shopkeeper.campaigns = [];
                        this.loadingScreen = false;
                    },
                    err => {
                        this.alert.showError(err, true);
                        this.loadingScreen = false;
                    }
                );
            }
        }

        if (!this.canListShopeekepers) return this.alert.showInfo('O usuario logado não contem permissão');
    }

    loadCampaigns() {
      
            this.loadingCampaigns = true;
            this._campaignService.getAll(null, null, 'B2B', 0, 2000).subscribe(
                campaigns => {
                    if (campaigns) {
                        this.campaigns = campaigns.map(campaign => ({ id: campaign.id, text: campaign.name }));
                    }
                    this.loadingCampaigns = false;
                },
                err => {
                    this.alert.showError(err, true);
                    this.loadingCampaigns = false;
                }
            );
        
    }

    clear() {
        this.selectedCamp = [];
        this._shopkeeperId = '';
        this.shopkeeper = {
            campaigns: [],
            address: {}
        };

        Object.keys(this.form.controls).forEach(key => {
            this.form.controls[key].reset();
        });
    }

    linkCampaign() {
        if (this.selectedCamp.campaign && this.selectedCamp.campaign.length) {
            if (!this.shopkeeper) this.shopkeeper = {};
            if (!this.shopkeeper.campaigns) this.shopkeeper.campaigns = [];

            const selectedCampaign = this.selectedCamp.campaign[0];
            const campaingExists = this.shopkeeper.campaigns.find(c => c.campaignId === selectedCampaign.id);
            if (campaingExists) {
                return this.alert.showError('Campanha ja vinculada!');
            }

            this.shopkeeper.campaigns.push({ campaignId: selectedCampaign.id, name: selectedCampaign.text, active: this.selectedCamp.active });
        }
    }

    unLinkCampaing($event: any) {
        if ($event) {
            if (!this.shopkeeper) return;
            if (!this.shopkeeper.campaigns) return;

            const campaign = this.shopkeeper.campaigns.find(c => c.campaignId === $event.campaignId);
            if (!campaign) return;

            this.shopkeeper.campaigns.splice(this.shopkeeper.campaigns.indexOf(campaign), 1);
        }
    }

    public searchAddress() {
        if (!this.shopkeeper) return;
        if (!this.shopkeeper.address) return;
        if (!this.shopkeeper.address.cep) return;
        this.loadingScreen = true;
        this._correiosService.searchAddressByCep(this.shopkeeper.address.cep).subscribe(
            address => {
                if (!address) {
                    this.shopkeeper.address = {};
                } else {
                    this.shopkeeper.address.street = address.address;
                    this.shopkeeper.address.neighborhood = address.neighborhood;
                    this.shopkeeper.address.city = address.city;
                    this.shopkeeper.address.state = address.state;
                }
                this.loadingScreen = false;
            },
            err => {
                console.log(err);
                this.loadingScreen = false;
            }
        );
    }

    save() {
        if (this.canEditShopeekepers) {
            this.loading = true;
            this._b2bShopkeeperService.save(this.shopkeeper).subscribe(
                response => {
                    if (response) {
                        if (!this.shopkeeper.id) this.shopkeeper = response;
                        this.alert.showSuccess(`Dados do lojista salvos com sucesso!`);
                    } else {
                        this.alert.showError('Ocorreu um erro ao salvar os dados do lojista. Tente novamente!');
                    }
                    this.loading = false;
                },
                err => {
                    this.alert.showError(err, true);
                    this.loading = false;
                }
            );
        }
        if (!this.canEditShopeekepers) return this.alert.showInfo('O usuario logado não contem permissão');

    }

}
