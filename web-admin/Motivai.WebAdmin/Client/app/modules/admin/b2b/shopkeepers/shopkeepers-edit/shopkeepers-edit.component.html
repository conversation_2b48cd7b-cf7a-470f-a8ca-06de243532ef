<form #f="ngForm" novalidate>
    <gp-card title="Dados do Lojista" [first]="true">
        <gp-form-row>
            <gp-form-col cols="12 4 4">
                <gp-simple-input label="Documento (CPF/CNPJ)" [required]="true" errorMessage="Documento obrigatório!">
                    <gp-input-mask name="document" [onlyInteger]="true" [thousandsSeparator]="false" [(ngModel)]="shopkeeper.document"></gp-input-mask>
                </gp-simple-input>
            </gp-form-col>
            <gp-form-col cols="12 8 8">
                <gp-simple-input label="Nome" [required]="true" errorMessage="Nome é obrigatório!">
                    <input type="text" name="name" class="form-control" required [(ngModel)]="shopkeeper.name" />
                </gp-simple-input>
            </gp-form-col>
        </gp-form-row>
        <gp-form-row>
            <gp-form-col cols="12 4 4">
                <gp-simple-input label="Telefone Principal (DDD + Número)" [required]="true" errorMessage="Telefone obrigatório">
                    <gp-input-mask name="mainPhone" mask="(00)00000-0000" required [(ngModel)]="shopkeeper.mainPhone"></gp-input-mask>
                </gp-simple-input>
            </gp-form-col>
            <gp-form-col cols="12 4 4">
                <gp-simple-input label="Celular (DDD + Número)">
                    <gp-input-mask name="mobilePhone" mask="(00)00000-0000" [(ngModel)]="shopkeeper.mobilePhone"></gp-input-mask>
                </gp-simple-input>
            </gp-form-col>

            <gp-form-col cols="12 4 4">
                <gp-simple-input label="Telefone Empresarial (DDD + Número)">
                    <gp-input-mask name="businessPhone" mask="(00)00000-0000" [(ngModel)]="shopkeeper.businessPhone"></gp-input-mask>
                </gp-simple-input>
            </gp-form-col>
        </gp-form-row>
        <gp-form-row>
            <gp-form-col cols="12 12 12">
                <gp-simple-input label="Email" [required]="true" errorMessage="Digite um email válido">
                    <input type="text" name="email" class="form-control" pattern="^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$" required [(ngModel)]="shopkeeper.email"  />
                </gp-simple-input>
            </gp-form-col>
        </gp-form-row>
    </gp-card>
    <gp-card title="Endereço">
        <spinner [overlay]="true" [show]="loadingScreen"></spinner>
        <gp-form-row>
            <gp-form-col cols="12 2 2">
                <gp-simple-input label="CEP :" [required]="true" errorMessage="CEP Obrigatorio!" >
                    <gp-input-mask name="cep" mask="00000-000" [cleanMask]="true" required [(ngModel)]="shopkeeper.address.cep" (onblur)="searchAddress()"></gp-input-mask>
                </gp-simple-input>
            </gp-form-col>
            <gp-form-col cols="12 4 4">
                <gp-simple-input label="Logradouro" >
                    <input type="text" name="address" class="form-control" [(ngModel)]="shopkeeper.address.street" />
                </gp-simple-input>
            </gp-form-col>
            <gp-form-col cols="12 4 4">
                    <gp-simple-input label="Bairro" >
                        <input type="text" name="neighborhood" class="form-control"  [(ngModel)]="shopkeeper.address.neighborhood" />
                    </gp-simple-input>
                </gp-form-col>
            <gp-form-col cols="12 2 2">
                <gp-simple-input label="Numero :" [required]="true" errorMessage="Numero Obrigatório!">
                    <input type="text" name="number" class="form-control" required [(ngModel)]="shopkeeper.address.number" />
                </gp-simple-input>
            </gp-form-col>
        </gp-form-row>
        <gp-form-row>
            <gp-form-col cols="12 6 6">
                <gp-simple-input label="Cidade :"  >
                    <input type="text" name="city" class="form-control"  [(ngModel)]="shopkeeper.address.city" />
                </gp-simple-input>
            </gp-form-col>
            <gp-form-col cols="12 6 6">
                <gp-simple-input label="Estado :">
                    <input type="text" name="state" class="form-control"  [(ngModel)]="shopkeeper.address.state" />
                </gp-simple-input>
            </gp-form-col>
        </gp-form-row>
        <gp-form-row>
            <gp-form-col cols="12 6 6">
                <gp-simple-input label="Referencia :">
                    <input type="text" name="reference" class="form-control" [(ngModel)]="shopkeeper.address.reference" />
                </gp-simple-input>
            </gp-form-col>
            <gp-form-col cols="12 6 6">
                <gp-simple-input label="Complemento :">
                    <input type="text" name="complement" class="form-control" [(ngModel)]="shopkeeper.address.complement" />
                </gp-simple-input>
            </gp-form-col>
        </gp-form-row>
    </gp-card>
    <gp-card title="Relacionar Campanhas:">
        <gp-form-row>
            <gp-form-col cols="12 8 8">
                <gp-simple-input label="Campanhas">
                    <ng-select name="campaingId" [items]="campaigns" [(ngModel)]="selectedCamp.campaign"></ng-select>
                </gp-simple-input>
            </gp-form-col>
            <gp-form-col cols="12 2 2">
                <label>Ativo</label>
                <div>
                    <gp-switch name="active" [(ngModel)]="selectedCamp.active"></gp-switch>
                </div>
            </gp-form-col>
            <gp-form-col cols="12 2 2">
                <gp-spinner-button type="button" [pink]="true" text="Vincular" icon="plus" marginTop="26px" (click)="linkCampaign()"></gp-spinner-button>
            </gp-form-col>
        </gp-form-row>
        <gp-form-row>
            <gp-form-col cols="12 12 12">
                <gp-grid name="Campaings" [rows]="shopkeeper.campaigns" [columns]="['Descriçao']" [fields]="['name']" [showActive]="true"
                    [showPagination]="false" [showTotalPages]="false" [showEdit]="false" [showDelete]="!shopkeeperId" (onDelete)="unLinkCampaing($event)"></gp-grid>
            </gp-form-col>
        </gp-form-row>
        <gp-alert #alert [overlay]="true"></gp-alert>
    </gp-card>
    <gp-card *ngIf="canEditShopeekepers">
        <gp-form-row>
            <gp-form-col cols="12 12 12">
                <gp-spinner-button type="button" [pink]="true" text="Salvar Lojista" marginRight="5px" pull="right" (click)="save()" [loading]="loading"></gp-spinner-button>

                <gp-spinner-button type="button" [actionSecondary]="true" text="Novo" marginRight="5px" pull="right" (click)="clear()"></gp-spinner-button>
            </gp-form-col>
        </gp-form-row>
    </gp-card>
</form>
