import { Component, OnInit, ViewChild, Output, EventEmitter } from '@angular/core';
import { B2BShopkeeperService } from '../B2bShopkeeperService';
import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { AuthStore } from '../../../../../core/auth/auth.store';
import { PERMISSION_SHOPKEEPERS_LIST, PERMISSION_SHOPKEEPERS_EDIT } from '../../../../../core/auth/access-points';

@Component({
    selector: 'shopkeepers-list',
    templateUrl: './shopkeepers-list.component.html'
})
export class ShopkeepersListComponent implements OnInit {
    filters: any = {};
    loading: boolean = false;
    shopkeepers: any[] = [];
    skip: number = 0;
    limit: number = 100;

    @ViewChild('alert') alert: GpAlertComponent;
    @Output('edit') edit: EventEmitter<any> = new EventEmitter();

    constructor(private _shopkeeperService: B2BShopkeeperService , private _authStore: AuthStore) { }
    
    get canListShopeekepers() {
        return this._authStore.hasPermissionTo(PERMISSION_SHOPKEEPERS_LIST);
    }
    get canEditShopeekepers() {
        return this._authStore.hasPermissionTo(PERMISSION_SHOPKEEPERS_EDIT);
    }
    ngOnInit(): void {
        this.find();
    }

    find() {
        if (this.canListShopeekepers) {
            this.loading = true;
            this._shopkeeperService.find(this.filters.document, this.filters.name, this.skip, this.limit).subscribe(
                shopkeepers => {
                    this.shopkeepers = shopkeepers || [];
                    this.loading = false;
                },
                err => {
                    this.alert.showError(err, true);
                    this.loading = false;
                }
            );
        }
        if (!this.canListShopeekepers)
        return this.alert.showInfo('O usuario nao contem permissão, por favor contate um admistrador do sistema!');
      
    }

    editShopkeeper($event) {
        this.edit.emit($event);
    }

    pageChanged($event) {
        if ($event) {
            this.skip = $event.skip;
            this.limit = $event.limit;
            this.find();
        }
    }
}
