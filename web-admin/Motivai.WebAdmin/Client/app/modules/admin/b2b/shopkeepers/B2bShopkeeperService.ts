import { Injectable } from '@angular/core';
import { ApiService } from '../../../../core/api/api.service';
import { Observable } from 'rxjs/Observable';
@Injectable()
export class B2BShopkeeperService {
    constructor(private _api: ApiService) {}
    save(shopkeeper: any): Observable<any> {
        if (shopkeeper.id) {
            return this._api.put(`/api/shopkeepers/${shopkeeper.id}`, shopkeeper, 20000);
        }

        return this._api.post(`/api/shopkeepers`, shopkeeper, 20000);
    }

    find(document: string, name: string, skip: number, limit: number): Observable<any[]> {
        const params: any = { skip, limit };
        if (document) params.document = document;
        if (name) params.name = name;

        return this._api.get(`/api/shopkeepers`, params, 20000);
    }

    findById(id: string): Observable<any> {
        return this._api.get(`/api/shopkeepers/${id}`, null, 20000);
    }
}
