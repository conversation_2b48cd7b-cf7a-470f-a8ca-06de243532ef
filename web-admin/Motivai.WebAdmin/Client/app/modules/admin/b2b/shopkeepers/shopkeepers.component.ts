import { Component, OnInit, ViewChild } from '@angular/core';
import { TabsetComponent } from '../../../../../../node_modules/ng2-bootstrap';
import { ShopkeepersListComponent } from './shopkeepers-list/shopkeepers-list.component';
import { ShopkeepersEditComponent } from './shopkeepers-edit/shopkeepers-edit.component';
import { AuthStore } from '../../../../core/auth/auth.store';
import { PERMISSION_SHOPKEEPERS_EDIT } from '../../../../core/auth/access-points';


@Component({
    selector: 'shopkeepers',
    templateUrl: './shopkeepers.component.html'
})
export class ShopkeepersComponent implements OnInit {
    shopkeeperId: string = '';

    @ViewChild('tabs') tabs: TabsetComponent;
    @ViewChild('list') list: ShopkeepersListComponent;
    @ViewChild('edit') edit: ShopkeepersEditComponent;

    constructor(private _auth: AuthStore) { }
    
    get canEditShopeekeper(){
        return this._auth.hasPermissionTo(PERMISSION_SHOPKEEPERS_EDIT);
    }
    ngOnInit(): void { }

    onEdit($event) {
        this.shopkeeperId = $event.id;
        this.tabs.tabs[1].active = true;
    }

    refresh() {
        this.shopkeeperId = '';
        this.edit.clear();
        this.list.find();
    }
  
}
