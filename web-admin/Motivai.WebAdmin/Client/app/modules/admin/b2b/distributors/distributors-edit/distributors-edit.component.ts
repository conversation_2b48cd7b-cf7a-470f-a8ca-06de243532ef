import { Region, RegionType } from './../../../regions/regions';
import { Component, OnInit } from '@angular/core';

@Component({
    selector: 'distributors-edit',
    templateUrl: 'distributors-edit.component.html'
})

export class DistributorsEditComponent implements OnInit {

    private factory: any = {};
    private regions: Region[] = [];
    private factoryRegions: any[] = [];
    private regionType = RegionType;
    private selectedRegion: string = '';
    private selectedRegionType: string;
    private loadingRegions: boolean = false;
    private loading: boolean = false;
    private loadingScreen: boolean = false;

    constructor() { }

    ngOnInit() { }
}
