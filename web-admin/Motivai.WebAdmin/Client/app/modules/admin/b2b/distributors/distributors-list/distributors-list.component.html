<gp-card [first]="true" title="Pesquisa">
	<gp-form-row>
		<gp-form-col cols="12 6 6">
			<div class="form-group">
				<input type="text" name="name" class="form-control" placeholder="Nome da Distribuidora" [(ngModel)]="factory.name" />
			</div>
		</gp-form-col>
		<gp-form-col cols="12 6 6">
			<gp-spinner-button text="Pesquisar" buttonClass="bg-primary-dark" icon="search" (click)="getFactories(true)" [loading]="loading"
			 loadingText="Pesquisando">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card title="Registros encontrados">
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<gp-grid #grid name="grid" [rows]="rows" [columns]="[ 'Nome da Distribuidora' ]" [fields]="[ 'name' ]" [showActive]="true" [showPagination]="true"
			 [showTotalPages]="false" [showEdit]="true" [showDelete]="false" [loading]="loading" (onEdit)="editFactory($event)"
			 (onPageChanged)="onPageChanged($event)">
			</gp-grid>
		</gp-form-col>
	</gp-form-row>
</gp-card>