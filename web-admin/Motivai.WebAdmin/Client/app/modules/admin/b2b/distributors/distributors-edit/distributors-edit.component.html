<form #factoryForm="ngForm" (ngSubmit)="factorySubmit()" novalidate>
	<gp-card [first]="true" title="Dados da Distribuidora">
		<gp-form-row>
			<gp-form-col cols="12 3 3">
				<gp-simple-input label="CNPJ">
					<gp-input-mask name="cnpj" mask="00.000.000/0000-00" placeholder="00.000.000/0000-00" [(ngModel)]="factory.cnpj"></gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 9 9">
				<gp-simple-input label="Nome da distribuidora" [required]="true" errorMessage="Nome da distribuidora é obrigatório">
					<input type="text" name="name" class="form-control" required [(ngModel)]="factory.name" />
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="E-mail do responsável" [required]="true" errorMessage="E-mail do responsável é obrigatório">
					<input type="text" name="email" class="form-control" required [(ngModel)]="factory.email" />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Parceiro" [required]="true" errorMessage="Parceiro é obrigatório">
					<gp-partners-select name="partner" [(ngModel)]="factory.companyId" [selected]="factory.companyId" required></gp-partners-select>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<label>Ativo</label>
				<div>
					<label class="switch switch-lg">
						<input type="checkbox" checked="checked" id="active" name="active" [(ngModel)]="factory.active" />
						<span></span>
					</label>
				</div>
			</gp-form-col>
		</gp-form-row>
		<spinner [overlay]="true" [show]="loadingScreen"></spinner>
	</gp-card>

	<gp-card title="Regiões de Atuação">
		<gp-form-row>
			<gp-form-col cols="12 5 5">
				<div class="form-group">
					<label>Abrangência</label>
					<select class="form-control" name="regionType" [(ngModel)]="selectedRegionType" (change)="regionTypeChanged()">
						<option [value]="regionType.Cep.key">{{ regionType.Cep.value }}</option>
						<option [value]="regionType.Neighborhood.key">{{ regionType.Neighborhood.value }}</option>
						<option [value]="regionType.City.key">{{ regionType.City.value }}</option>
						<option [value]="regionType.State.key">{{ regionType.State.value }}</option>
					</select>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 5 5">
				<div class="form-group">
					<label>Região</label>
					<select class="form-control" [disabled]="!selectedRegionType" name="region" [(ngModel)]="selectedRegion">
						<option value="">{{ regionsSelectFirstItem }}</option>
						<option *ngFor="let r of regions" [value]="r.id">{{ r.name }}</option>
					</select>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 2 2">
				<gp-spinner-button type="button" [pink]="true" icon="plus" text="Adicionar" loadingText="Processando" pull="right" [loading]="loading"
				 [disabled]="!isRegionValid" (click)="addRegion()" marginTop="24px">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-grid name="regionsGrid" [rows]="factoryRegions" [columns]="['Abrangência', 'Descrição da Região']" [fields]="['regionTypeDescription', 'name']"
				 [loading]="loadingRegions" [showActive]="false" [showPagination]="false" [showEdit]="false" [showDelete]="true" emptyMessage="Nenhuma região adicionada."
				 (onDelete)="removeRegion($event)">
				</gp-grid>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-alert #regionsAlert></gp-alert>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card title="Representantes Comerciais">
		<gp-form-row>
			<gp-form-col cols="12 3 3">
				<div class="form-group">
					<label>CPF</label>
					<input type="text" class="form-control" />
				</div>
			</gp-form-col>
			<gp-form-col cols="12 7 7">
				<div class="form-group">
					<label>Nome do representante</label>
					<input type="text" class="form-control" />
				</div>
			</gp-form-col>
			<gp-form-col cols="12 2 2">
				<gp-spinner-button type="button" [pink]="true" icon="plus" text="Adicionar" loadingText="Processando" pull="right" [loading]="loading"
				[disabled]="!isRegionValid" (click)="addRegion()" marginTop="24px">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-grid name="representativesGrid" [rows]="[]" [columns]="['CPF', 'Nome do Representante']" [fields]="['cpf', 'name']"
				[loading]="loadingRegions" [showActive]="false" [showPagination]="false" [showEdit]="false" [showDelete]="true" emptyMessage="Nenhum representante adicionada."
				(onDelete)="removeRegion($event)">
				</gp-grid>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card>
		<div class="row">
			<div class="col-md-12">
				<gp-spinner-button type="submit" text="Salvar Distribuidora" size="lg" pull="right" [pink]="true" icon="send" loadingText="Processando"
				 [loading]="loading" [disabled]="!factoryForm.valid">
				</gp-spinner-button>

				<gp-spinner-button text="Novo" bootstrapClass="default" icon="plus" size="lg" pull="right" marginRight="10px" [disabled]="loading"
				 (click)="reset()" [showSpinner]="false">
				</gp-spinner-button>
			</div>
		</div>
		<div class="row">
			<div class="col-md-12">
				<gp-alert #alert></gp-alert>
			</div>
		</div>
	</gp-card>
</form>
