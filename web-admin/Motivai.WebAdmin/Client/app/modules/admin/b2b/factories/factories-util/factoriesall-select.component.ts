import { OnInit, Component, Input, EventEmitter, Output, forwardRef } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR, Validator, FormControl, NG_VALIDATORS } from '@angular/forms';


import { Factory } from '../factory.model';
import { FactoryService } from '../factory.service';


@Component({
  selector: 'gp-factoriesall-select',
  template: `
  <select [disabled]="disabled || loading" [id]="id" class="form-control" [(ngModel)]="value" (change)="onSelect()">
    <option value="">{{ loading ? "Carregando..." : firstItemText }}</option>
    <option *ngFor="let c of factories" [value]="c.id">{{ c.name }}</option>
  </select>
`,
  providers: [{ provide: NG_VALUE_ACCESSOR, useExisting: forwardRef(() => GpFactoriesAllSelectComponent), multi: true }]
})
export class GpFactoriesAllSelectComponent implements OnInit {


  constructor(private factoryService: FactoryService) { }

  @Input() id: string;
  @Input() active: boolean = true;
  @Input() level: number = 1;
  @Input() disabled: boolean = false;
  @Input() emptyMessage: string;
  @Input() selected: string;


  @Input() set factoryId(v: string) {
    this._factoryId = v;
    if (v)
      this.getAllFactories();
    else {
      this.firstItemText = 'Selecione';
      this.factories = [];
      this.value = v;
    }
  }


  @Output('onselect') onselect: EventEmitter<any> = new EventEmitter();

  private onTouched(): any { }
  private propagateChange = (_: any) => { };

  loading: boolean;
  factories: Factory[];
  firstItemText: string = 'Selecione';
  selectedItem?: Factory;


  private _factoryId: string;
  private _value: string = '';

  private valueToSet?: string = undefined;

  ngOnInit() {
    this.getAllFactories();
  }


  get value(): string {
    return this._value;
  }

  set value(v: string) {
    if (v !== this._value) {
      this._value = v;
      this.propagateChange(v);
    }
    this.onTouched();
  }


  writeValue(value: any): void {
    if (value !== this._value) {
      if (this.loading || !this.factories || !this.factories.length) {
        this.valueToSet = value;
      } else {
        this.value = value;
      }
    }
  }

  registerOnChange(fn: any): void {
    this.propagateChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.registerOnTouched = fn;
  }

  onChange(data: string) {
    this.value = data;
  }

  getAllFactories(): void {
    this.loading = true;
    this.factoryService.getAll()
      .subscribe(factories => {
        this.factories = factories;
        this.loading = false;
      });
  }


  onSelect() {
    const item = this.factories.find(x => x.id === this.value);
    this.onselect.emit(item);
  }
}
