<form #factoryForm="ngForm" (ngSubmit)="factorySubmit()" novalidate *ngIf="canView()">
	<gp-card [first]="true" title="Dados da Fábrica">
		<gp-form-row>
			<gp-form-col cols="12 3 3">
				<gp-simple-input label="Tipo">
					<select class="form-control" name="type" [(ngModel)]="factory.type" required>
						<option value="Factory">Fábrica</option>
						<option value="Distributor">Distribuidora</option>
					</select>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 3 3">
				<gp-simple-input label="CNPJ">
					<gp-input-mask name="cnpj" mask="00.000.000/0000-00" placeholder="00.000.000/0000-00" [(ngModel)]="factory.cnpj"></gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 9 9">
				<gp-simple-input label="Nome da fábrica" [required]="true" errorMessage="Nome da fábrica é obrigatório">
					<input type="text" name="name" class="form-control" required [(ngModel)]="factory.name" />
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="E-mail do responsável" [required]="true" errorMessage="E-mail do responsável é obrigatório">
					<input type="text" name="email" class="form-control" required [(ngModel)]="factory.email" />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Parceiro" [required]="true" errorMessage="Parceiro é obrigatório">
					<gp-partners-select name="partner" [(ngModel)]="factory.companyId" [selected]="factory.companyId" required></gp-partners-select>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<label>Ativo</label>
				<div>
					<label class="switch switch-lg">
						<input type="checkbox" checked="checked" id="active" name="active" [(ngModel)]="factory.active" /><span></span>
					</label>
				</div>
			</gp-form-col>
		</gp-form-row>
		<spinner [overlay]="true" [show]="loadingScreen"></spinner>
	</gp-card>

	<gp-card title="Regiões de Atuação">
		<gp-form-row>
			<gp-form-col cols="12 5 5">
				<div class="form-group">
					<label>Abrangência</label>
					<select class="form-control" name="regionType" [(ngModel)]="selectedRegionType" (change)="regionTypeChanged()">
						<option [value]="regionType.Cep.key">{{ regionType.Cep.value }}</option>
						<option [value]="regionType.Neighborhood.key">{{ regionType.Neighborhood.value }}</option>
						<option [value]="regionType.City.key">{{ regionType.City.value }}</option>
						<option [value]="regionType.State.key">{{ regionType.State.value }}</option>
					</select>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 5 5">
				<div class="form-group">
					<label>Região</label>
					<select class="form-control" [disabled]="!selectedRegionType" name="region" [(ngModel)]="selectedRegion">
						<option value="">{{ regionsSelectFirstItem }}</option>
						<option *ngFor="let r of regions" [value]="r.id">{{ r.name }}</option>
					</select>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 2 2">
				<gp-spinner-button type="button" [pink]="true" icon="plus" text="Adicionar" loadingText="Processando" pull="right"
				 [loading]="loading" [disabled]="!isRegionValid" (click)="addRegion()" marginTop="24px">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 12">
				<gp-grid name="regionsGrid" [rows]="factoryRegions" [columns]="['Abrangência', 'Descrição da Região']" [fields]="['regionTypeDescription', 'name']"
				 [loading]="loadingRegions" [showActive]="false" [showPagination]="false" [showEdit]="false" [showDelete]="true"
				 emptyMessage="Nenhuma região adicionada." (onDelete)="removeRegion($event)">
				</gp-grid>
			</gp-form-col>
		</gp-form-row>
		<div class="row">
			<div class="col-md-12">
				<gp-alert #regionsAlert></gp-alert>
			</div>
		</div>
	</gp-card>

	<gp-card title="Token de integração">
		<gp-form-row>
			<gp-form-col cols="12 5 5">
				<div class="form-group">
					<label>Token gerado</label>
					<input type="text" name="integrationToken" class="form-control" readonly [(ngModel)]="factory.integrationToken" />
				</div>
			</gp-form-col>
			<gp-form-col cols="12 2 2">
				<gp-spinner-button type="button" [pink]="true" icon="" text="Gerar Token" (click)="getIntegrationToken()"
				 marginTop="24px">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card>
		<div class="row">
			<div class="col-md-12">
				<gp-spinner-button type="submit" text="Salvar Fábrica" size="lg" pull="right" [pink]="true" icon="send"
				 loadingText="Processando" [loading]="loading" [disabled]="!factoryForm.valid" *ngIf="canCreate() || canEdit()">
				</gp-spinner-button>

				<gp-spinner-button text="Novo" bootstrapClass="default" icon="plus" size="lg" pull="right" marginRight="10px"
				 [disabled]="loading" (click)="reset()" [showSpinner]="false" *ngIf="canCreate()">
				</gp-spinner-button>
			</div>
		</div>
		<div class="row">
			<div class="col-md-12">
				<gp-alert #alert></gp-alert>
			</div>
		</div>
	</gp-card>
</form>
