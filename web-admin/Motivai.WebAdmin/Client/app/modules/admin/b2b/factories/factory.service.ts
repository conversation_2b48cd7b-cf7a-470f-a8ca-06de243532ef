import { Observable } from 'rxjs/Observable';
import { Injectable } from '@angular/core';

import { Factory } from './factory.model';
import { ApiService } from '../../../../core/api/api.service';

@Injectable()
export class FactoryService {
    constructor(private _api: ApiService) { }

    public save(factory: Factory): Observable<Factory> {
        if (factory.id)
            return this._api.put(`/api/factories/${factory.id}`, factory);
        else
            return this._api.post(`/api/factories`, factory);
    }

    public getById(id: string): Observable<Factory> {
        return this._api.get(`/api/factories/${id}`);
    }

    public getByPartnerId(partnerId: string): Observable<Factory[]> {
        return this._api.get(`/api/factories/partner/${partnerId}`);
    }

    public search(name?: string, skip?: number, limit?: number, asc?: boolean, sortby?: string): Observable<Factory[]> {
        let params: any = {};

        if (name) params.name = name;
        if (asc != null) params.asc = asc;
        if (sortby) params.sortBy = sortby;

        params.skip = skip || 0;
        params.limit = limit || 10;

        return this._api.get(`/api/factories`, params);
    }

    public getAll(): Observable<Factory[]> {
        return this._api.get('api/factories/all');
    }
}
