import { Component, OnInit, Input, forwardRef, OnChanges, Output, EventEmitter } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR, Validator, FormControl, NG_VALIDATORS } from '@angular/forms';

import { Factory } from '../factory.model';
import { FactoryService } from '../factory.service';

@Component({
    selector: 'gp-factories-select',
    template: `
    <select [disabled]="disabled || loading" [id]="id" class="form-control" [(ngModel)]="value" (change)="onSelect()">
      <option value="">{{ loading ? "Carregando..." : firstItemText }}</option>
      <option *ngFor="let c of factories" [value]="c.id">{{ c.name }}</option>
    </select>
  `,
    providers: [{ provide: NG_VALUE_ACCESSOR, useExisting: forwardRef(() => GpFactoriesSelectComponent), multi: true }]
})
export class GpFactoriesSelectComponent implements OnInit, ControlValueAccessor {
    @Input() id: string;
    @Input() active: boolean = true;
    @Input() level: number = 1;
    @Input() disabled: boolean = false;
    @Input() emptyMessage: string;
    @Input() selected: string;

    @Input() set partnerId(v: string) {
        this._partnerId = v;
        if (v)
            this.getFactories();
        else {
            this.firstItemText = 'Selecione';
            this.factories = [];
            this.value = v;
        }
    }

    @Output('onselect') onselect: EventEmitter<any> = new EventEmitter();

    loading: boolean;
    factories: Factory[];
    firstItemText: string = 'Selecione';
    selectedItem?: Factory;

    private _partnerId: string;
    private _value: string = '';

    private valueToSet?: string = undefined;

    private onTouched(): any { }
    private propagateChange = (_: any) => { };

    constructor(private factoryService: FactoryService) { }

    get value(): string {
        return this._value;
    }

    set value(v: string) {
        if (v !== this._value) {
            this._value = v;
            this.propagateChange(v);
        }
        this.onTouched();
    }

    ngOnInit() {
        this.getFactories();
    }

    writeValue(value: any): void {
        if (value !== this._value) {
            if (this.loading || !this.factories || !this.factories.length) {
                this.valueToSet = value;
            } else {
                this.value = value;
            }
        }
    }

    registerOnChange(fn: any): void {
        this.propagateChange = fn;
    }

    registerOnTouched(fn: any): void {
        this.registerOnTouched = fn;
    }

    onChange(data: string) {
        this.value = data;
    }

    getFactories(): void {
        this.loading = true;
        this.factoryService.getByPartnerId(this._partnerId)
            .subscribe(factories => {
                this.factories = factories;
                if (factories && factories.length) {
                    this.disabled = false;
                    this.firstItemText = 'Selecione';
                    this.value = '';
                } else if (!factories || !factories.length) {
                    this.firstItemText = this.emptyMessage;
                    this.disabled = true;
                    this.value = '';
                } else if (this.value && this.factories.findIndex(c => c.id === this.value) < 0) {
                    this.value = '';
                }

                if (this.valueToSet) {
                    this.value = this.valueToSet;
                }

                if (this.value) {
                    this.onSelect();
                }
                this.loading = false;
            });
    }

    getAllFactories(): void {
        this.loading = true;
        this.factoryService.getByPartnerId(this._partnerId)
            .subscribe(factories => {
                this.factories = factories;
                if (factories && factories.length) {
                    this.disabled = false;
                    this.firstItemText = 'Selecione';
                    this.value = '';
                } else if (!factories || !factories.length) {
                    this.firstItemText = this.emptyMessage;
                    this.disabled = true;
                    this.value = '';
                } else if (this.value && this.factories.findIndex(c => c.id === this.value) < 0) {
                    this.value = '';
                }

                if (this.valueToSet) {
                    this.value = this.valueToSet;
                }

                if (this.value) {
                    this.onSelect();
                }
                this.loading = false;
            });
    }

    onSelect() {
        const item = this.factories.find(x => x.id === this.value);
        this.onselect.emit(item);
    }
}
