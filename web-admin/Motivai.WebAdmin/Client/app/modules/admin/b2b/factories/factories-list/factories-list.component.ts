import { GpGridComponent } from '../../../../../shared/components/gp-grid/gp-grid.component';
import { PERMISSION_FACTORIES_LIST, PERMISSION_FACTORIES_VIEW, PERMISSION_FACTORIES_UPDATE } from '../../../../../core/auth/access-points';
import { AuthStore } from '../../../../../core/auth/auth.store';
import { EventEmitter, ViewChild } from '@angular/core';
import { FactoryService } from '../factory.service';
import { Factory } from '../factory.model';
import { Component, OnInit, Output } from '@angular/core';

@Component({
    selector: 'factories-list',
    templateUrl: 'factories-list.component.html'
})
export class GpFactoriesListComponent implements OnInit {

    private factory: Factory = new Factory();
    private rows: Factory[] = [];
    private skip: number = 0;
    private limit: number = 10;
    private loading: boolean = false;
    @ViewChild('grid') grid: GpGridComponent;

    @Output('onEdit') onEdit: EventEmitter<any> = new EventEmitter<any>();

    constructor(private _factoryService: FactoryService, private _as: AuthStore) { }

    ngOnInit() {
        this.getFactories();
    }

    public getFactories(resetSearch: boolean = false) {
        if (resetSearch) {
            this.skip = 0;
            this.limit = 10;
            this.grid.resetPagination();
        }
        this.loading = true;
        this._factoryService.search(this.factory.name, this.skip, this.limit, undefined, undefined)
            .subscribe(factories => {
                this.rows = factories;
                if (this.rows) {
                    this.rows.forEach(f => {
                        if (f.type == 'Factory')
                            f.typeDescription = 'Fábrica';
                        else
                            f.typeDescription = 'Distribuidora';
                    });
                }
                this.loading = false;
            }, err => {
                console.log(err);
                this.loading = false;
            });
    }

    private editFactory($event) {
        this.onEdit.emit($event.id);
    }

    private canList() {
        return this._as.hasPermissionTo(PERMISSION_FACTORIES_LIST);
    }

    private canEditOrView() {
        return this._as.hasPermissionTo(PERMISSION_FACTORIES_VIEW) ||
            this._as.hasPermissionTo(PERMISSION_FACTORIES_UPDATE);
    }
}
