import { PERMISSION_FACTORIES_VIEW, PERMISSION_FACTORIES_UPDATE, PERMISSION_FACTORIES_CREATE } from '../../../../../core/auth/access-points';
import { NgForm } from '@angular/forms';
import { ViewChild, Input } from '@angular/core';
import { Component, OnInit } from '@angular/core';
import { CNPJ } from 'cpf_cnpj';
import { v4 as uuid } from 'uuid';
import { Guid } from 'guid-typescript';

import { RegionType, Region } from '../../../regions/regions';
import { Factory } from '../factory.model';
import { AuthStore } from '../../../../../core/auth/auth.store';
import { FactoryService } from '../factory.service';
import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { RegionsService } from '../../../regions/regions.service';

@Component({
    selector: 'factories-edit',
    templateUrl: 'factories-edit.component.html'
})
export class GpFactoriesEditComponent implements OnInit {

    private _factoryId: string;
    @Input() set factoryId(v: string) {
        if (v) {
            this._factoryId = v;
            this.getFactory();
        }
    }

    private factory: Factory = new Factory();
    private regions: Region[] = [];
    private factoryRegions: any[] = [];
    private regionType = RegionType;
    private selectedRegion: string = '';
    private selectedRegionType: string;
    private loadingRegions: boolean = false;
    private loading: boolean = false;
    private loadingScreen: boolean = false;
    @ViewChild('regionsAlert') regionsAlert: GpAlertComponent;
    @ViewChild('alert') alert: GpAlertComponent;
    @ViewChild('factoryForm') factoryForm: NgForm;

    constructor(private _regionService: RegionsService,
        private _factoryService: FactoryService,
        private _as: AuthStore) { }

    ngOnInit() { }

    // Reset form
    public reset() {
        this.factory = new Factory();
        this.regions = [];
        this.factoryRegions = [];
        this.selectedRegion = '';
        this.selectedRegionType = '';

        this.regionsAlert.clear();
        this.alert.clear();
        this.factoryForm.reset();

        Object.keys(this.factoryForm.controls).forEach(key => {
            this.factoryForm.controls[key].reset();
        });
    }

    // Get factory details
    private getFactory() {
        if (this._factoryId) {
            this.loadingScreen = true;
            this._factoryService.getById(this._factoryId)
                .subscribe(factory => {
                    this.factory = factory;
                    this.getRegions(factory.regions);
                    this.loadingScreen = false;
                }, err => {
                    this.handleError(err);
                    this.loadingScreen = false;
                });
        }
    }

    // Get regions details
    private getRegions(regionsIds: string[]) {
        if (regionsIds) {
            this.factoryRegions = [];
            regionsIds.forEach(x => {
                this._regionService.getById(x)
                    .subscribe(region => {
                        this.handleRegionType(region);
                        this.factoryRegions.push(region);
                    }, err => this.handleError(err));
            });
        }
    }

    // Save factory
    private factorySubmit() {
        if (!this.factory.type) {
            this.alert.showWarning('Selecione o tipo.');
            return;
        }
        if (this.factory.cnpj) {
            if (!CNPJ.isValid(this.factory.cnpj)) {
                this.alert.showError('CNPJ inválido! Verifique o valor digitado');
                return;
            }
        }

        this.alert.clear();
        this.loading = true;
        this._factoryService.save(this.factory)
            .subscribe(factory => {
                this.factory = factory;
                this.alert.showSuccess('Dados da fábrica salvos com sucesso.');
                this.loading = false;
            }, err => {
                this.handleError(err);
                this.loading = false;
            });
    }

    // Get regions by region type
    private regionTypeChanged() {
        if (this.selectedRegionType) {
            this.loadingRegions = true;
            this._regionService.search(undefined, this.selectedRegionType, 0, 0, undefined, undefined)
                .subscribe(regions => {
                    this.regions = regions;
                    this.selectedRegion = '';
                    this.loadingRegions = false;
                }, err => {
                    this.handleError(err);
                    this.loadingRegions = false;
                });
        }
    }

    // Regions first item text
    private get regionsSelectFirstItem() {
        if (this.loadingRegions)
            return 'Carregando...';

        if (this.selectedRegionType)
            return 'Selecione';
        else
            return 'Selecione uma abrangência';
    }

    // Validate if we can add a region to the list
    private get isRegionValid() {
        return this.selectedRegion && this.selectedRegionType;
    }

    // Adds a region to the list of region
    private addRegion() {
        this.regionsAlert.clear();
        if (this.selectedRegion) {
            this.loading = true;

            if (!this.factory.regions)
                this.factory.regions = [];

            const regionExist = this.factory.regions.find(x => x === this.selectedRegion);
            const fullRegion = this.regions.find(x => x.id === this.selectedRegion);
            if (!regionExist && fullRegion) {
                this.factory.regions.push(this.selectedRegion);
                this.handleRegionType(fullRegion);
                this.factoryRegions.push(fullRegion);
            } else {
                this.regionsAlert.showError('Região já adicionada a esta fábrica');
            }

            this.loading = false;
        }
    }

    // Remove a region from the list of regions
    private removeRegion($event: any) {
        if ($event) {
            const regionExist = this.factory.regions.find(x => x === $event.id);
            const fullRegion = this.factoryRegions.find(x => x.id === $event.id);
            if (regionExist && fullRegion) {
                this.factory.regions.splice(this.factory.regions.indexOf(regionExist), 1);
                this.factoryRegions.splice(this.factoryRegions.indexOf(fullRegion), 1);
            }
        }
    }

    private handleRegionType(region) {
        switch (region.regionType) {
            case RegionType.Cep.key:
                region.regionTypeDescription = RegionType.Cep.value;
                break;
            case RegionType.City.key:
                region.regionTypeDescription = RegionType.City.value;
                break;
            case RegionType.Neighborhood.key:
                region.regionTypeDescription = RegionType.Neighborhood.value;
                break;
            case RegionType.State.key:
                region.regionTypeDescription = RegionType.State.value;
                break;
        }
    }

    // Handle all errors
    private handleError(err) {
        const msg = err ? (err.message ? err.message : err.toString()) : '';
        this.alert.showError(msg);
    }

    private canView() {
        return this._as.hasPermissionTo(PERMISSION_FACTORIES_VIEW);
    }

    private canEdit() {
        return this._as.hasPermissionTo(PERMISSION_FACTORIES_UPDATE);
    }

    private canCreate() {
        return this._as.hasPermissionTo(PERMISSION_FACTORIES_CREATE);
    }

    private getIntegrationToken() {
        this.factory.integrationToken = Guid.create().toString();
    }
}
