
import { PERMISSION_FACTORIES_CREATE, PERMISSION_FACTORIES_UPDATE, PERMISSION_FACTORIES_VIEW } from '../../../../core/auth/access-points';
import { AuthStore } from '../../../../core/auth/auth.store';
import { TabsetComponent } from 'ng2-bootstrap/tabs';
import { GpFactoriesListComponent } from './factories-list/factories-list.component';
import { GpFactoriesEditComponent } from './factories-edit/factories-edit.component';
import { Component, OnInit, ViewChild } from '@angular/core';

@Component({
    selector: 'factories',
    templateUrl: 'factories.component.html'
})
export class GpFactoriesComponent implements OnInit {

    private _factoryId: string;
    @ViewChild('editComponent') editComponent: GpFactoriesEditComponent;
    @ViewChild('listComponent') listComponent: GpFactoriesListComponent;
    @ViewChild('tabs') tabs: TabsetComponent;

    constructor(private _as: AuthStore) { }

    ngOnInit() { }

    private editFactory($event: any) {
        this._factoryId = $event;
        this.tabs.tabs[1].active = true;
    }

    private clearEditForm() {
        this.editComponent.reset();
        this.listComponent.getFactories();
        this._factoryId = '';
    }

    private canViewOrCreateOrUpdate() {
        return this._as.hasPermissionTo(PERMISSION_FACTORIES_CREATE) ||
            this._as.hasPermissionTo(PERMISSION_FACTORIES_UPDATE) ||
            this._as.hasPermissionTo(PERMISSION_FACTORIES_VIEW);
    }
}
