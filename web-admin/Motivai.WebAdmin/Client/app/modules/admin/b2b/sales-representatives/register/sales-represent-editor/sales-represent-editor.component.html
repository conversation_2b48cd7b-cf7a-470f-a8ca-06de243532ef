<form #srForm="ngForm" (ngSubmit)="save()">
	<gp-card [first]="true" title="Dados do Representante">
		<gp-form-row>
			<gp-form-col cols="12 4 4">
				<gp-simple-input label="CPF*">
					<gp-input-mask name="cpf" mask="000.000.000-00" placeholder="000.000.000-00" [required]="true"
						[(ngModel)]="saleRepresentative.cpf"></gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Nome" [required]="true" errorMessage="Nome do representante é obrigatório">
					<input type="text" name="name" class="form-control" required [(ngModel)]="saleRepresentative.name" />
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 4 4">
				<gp-simple-input label="Celular">
					<gp-input-mask name="cellphone" mask="(00) 00000-0000" [(ngModel)]="saleRepresentative.cellphone"></gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="E-mail" [required]="false">
					<input type="text" name="email" class="form-control" [(ngModel)]="saleRepresentative.email" />
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 4 4" *ngIf="!saleRepresentative.id">
				<gp-input-checkbox text="Gerar usuário (documento será o login e senha)" additionalClass="top-p1"
					name="generateUser" [(ngModel)]="saleRepresentative.generateUser">
				</gp-input-checkbox>
			</gp-form-col>
			<gp-form-col cols="12 6 6" *ngIf="!saleRepresentative?.generateUser">
				<gp-simple-input label="Login">
					<gp-select name="login" [items]="users" [(ngModel)]="saleRepresentative.userAdministrationId">
					</gp-select>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 6" *ngIf="saleRepresentative?.generateUser">
				<gp-simple-input label="Perfil para o usuário">
					<gp-select name="role" [items]="roles" [(ngModel)]="saleRepresentative.rolesId">
					</gp-select>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<label>Ativo</label>
				<div>
					<label class="switch switch-lg">
						<input type="checkbox" checked="checked" id="active" name="active" [(ngModel)]="saleRepresentative.active" /><span></span>
					</label>
				</div>
			</gp-form-col>
		</gp-form-row>
		<spinner [overlay]="true" [show]="loading"></spinner>
	</gp-card>
	<gp-card>
		<gp-form-row>
			<gp-form-col cols="12 3 3">
				<gp-simple-input label="Tipo da Comissão*">
					<select class="form-control" name="comissionType" placeholder="Selecione o tipo de comissão" required [(ngModel)]="saleRepresentative.commissionType">
						<option value="Fixed">Fixo</option>
						<option value="Range">Intervalo de Valor do Pedido</option>
					</select>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 3 3" *ngIf="isFixedCommission">
				<gp-simple-input label="Comissão (%)">
					<gp-input-mask name="commissionValue" [decimais]="3" [onlyDecimal]="true" [(ngModel)]="saleRepresentative.fixedCommissionValue"></gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row *ngIf="isRangeCommission">
			<gp-form-col cols="12 3 3">
				<gp-simple-input label="Valor De">
					<gp-input-mask name="fromValue" [onlyDecimal]="true" [(ngModel)]="rangeCommission.fromValue"></gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 3 3">
				<gp-simple-input label="Valor Até">
					<gp-input-mask name="toValue" [onlyDecimal]="true" [(ngModel)]="rangeCommission.toValue"></gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 3 3">
				<gp-simple-input label="Comissão (%)">
					<gp-input-mask name="commissionValue" [decimais]="3" [onlyDecimal]="true" [(ngModel)]="rangeCommission.commissionValue"></gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 3 3" additionalClasses="top-p2">
				<gp-spinner-button text="Salvar Comissão" [pink]="true" icon="send" [disabled]="sending" loadingText="Salvando" (click)="saveCommission()"></gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row *ngIf="isRangeCommission">
			<gp-form-col cols="12 12">
				<gp-grid name="bvsGrid" [rows]="saleRepresentative.commissionsByRanges" [loading]="loading"
					[columns]="['Valor De', 'Valor Até', 'Comissão (%)']" [fields]="['fromValue', 'toValue', 'commissionValue']"
					[showActive]="false" [showPagination]="false" [showEdit]="true" [showDelete]="true"
					(onEdit)="editCommission($event)" (onDelete)="removeCommission($event)">
				</gp-grid>
			</gp-form-col>
		</gp-form-row>
	</gp-card>
	<gp-card title="Fábricas e Distribuidoras do Representante">
		<gp-form-row>
			<gp-form-col cols="12 4 4">
				<gp-simple-input label="Fábrica/Distribuidora">
					<gp-select name="factory" [items]="availableFactories" [(ngModel)]="selectedFactoryId">
					</gp-select>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 3 3" additionalClasses="top-p2">
				<gp-spinner-button text="Adicionar" pull="right" [pink]="true" icon="plus" (click)="addFactory()">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 12">
				<gp-grid name="factoriesGrid" [rows]="saleRepresentative.factoriesModels"
					[columns]="['Fábrica/Distribuidora']" [fields]="['name']"
					[showActive]="false" [showPagination]="false" [showEdit]="false" [showDelete]="true"
					(onDelete)="removeFactory($event)">
				</gp-grid>
			</gp-form-col>
		</gp-form-row>
	</gp-card>
	<gp-card title="Ramos do Representante">
		<gp-form-row>
			<gp-form-col cols="12 4 4">
				<gp-simple-input label="Ramo">
					<gp-select name="campaign" [items]="availableCampaigns" [(ngModel)]="selectedCampaignId">
					</gp-select>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 3 3" additionalClasses="top-p2">
				<gp-spinner-button text="Adicionar" pull="right" [pink]="true" icon="plus" (click)="addCampaign()">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 12">
				<gp-grid name="campaignsGrid" [rows]="saleRepresentative.campaignsModels"
					[columns]="['Campanha']" [fields]="['name']"
					[showActive]="false" [showPagination]="false" [showEdit]="false" [showDelete]="true"
					(onDelete)="removeCampaign($event)">
				</gp-grid>
			</gp-form-col>
		</gp-form-row>
	</gp-card>
	<gp-card>
		<div class="row">
			<div class="col-md-12">
				<gp-spinner-button type="submit" text="Salvar" pull="right" [pink]="true" icon="send"
					loadingText="Processando" [loading]="sending" [disabled]="srForm.invalid" *ngIf="canSave">
				</gp-spinner-button>
		
				<gp-spinner-button text="Novo" bootstrapClass="default" icon="plus" pull="right" marginRight="10px"
					[disabled]="loading" (click)="reset()" [showSpinner]="false" *ngIf="canCreate">
				</gp-spinner-button>
			</div>
		</div>
		<div class="row">
			<div class="col-md-12">
				<gp-alert #gpAlert></gp-alert>
			</div>
		</div>
	</gp-card>
</form>
