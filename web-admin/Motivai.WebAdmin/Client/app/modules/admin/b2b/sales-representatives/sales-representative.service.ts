import { Injectable } from '@angular/core';

import { ApiService } from '../../../../core/api/api.service';
import { Observable } from 'rxjs/Observable';

@Injectable()
export class SalesRepresentativeService {
	constructor(private _api: ApiService) { }

	getUsers(): Observable<Array<any>> {
    return this._api.get('/api/administrators');
	}

	getRoles(): Observable<Array<any>> {
    return this._api.get('/api/roles');
	}

	getAll(name: any, from: number | null, size: number | null): Observable<Array<any>> {
		return this._api.get('/api/salesrepresentatives', { name: name, from: from, size: size });
	}

	getById(salesRepresentativeId: any): Observable<any> {
		return this._api.get(`/api/salesrepresentatives/${salesRepresentativeId}`);
	}

	save(salesRepresentative: any): Observable<any> {
		if (salesRepresentative.id) {
			return this._api.put(`/api/salesrepresentatives/${salesRepresentative.id}`, salesRepresentative);
		}
		return this._api.post(`/api/salesrepresentatives`, salesRepresentative);
	}

	getLinkedCampaigns(salesRepresentativeId: any): Observable<Array<any>> {
		return this._api.get(`/api/salesrepresentatives/${salesRepresentativeId}/campaigns`);
	}

	getLinkedFactories(salesRepresentativeId: any): Observable<Array<any>> {
		return this._api.get(`/api/salesrepresentatives/${salesRepresentativeId}/factories`);
	}

	getAvailableFactoriesForUser(userId: string): Observable<Array<any>> {
		return this._api.get(`/api/salesrepresentatives/shopkeepers/${userId}/factories`);
	}
}
