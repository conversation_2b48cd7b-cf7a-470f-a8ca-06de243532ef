<!--component html goes here -->
<form #gpForm="ngForm" novalidate>
	<gp-card [first]="true" title="Dados da Tabela Comercial">
		<div class="row">
			<div grid="12 12 12">
				<gp-simple-input label="Descrição" [required]="true" errorMessage="Descrição é obrigatória">
					<input type="text" class="form-control" name="name" required [(ngModel)]="salesRepresentative.name" />
				</gp-simple-input>
			</div>
		</div>
		<div class="row">
			<div grid="12 6">
				<gp-simple-input label="Data de Início" [required]="true" errorMessage="Data de início é obrigatória">
					<my-date-picker name="startDate" [(ngModel)]="salesRepresentative.startDate"
						[options]="dateOptions" locale="pt-br" [required]="true">
					</my-date-picker>
				</gp-simple-input>
			</div>
			<div grid="12 6">
				<gp-simple-input label="Data de Término" [required]="true" errorMessage="Data de término é obrigatória">
					<my-date-picker name="endDate" [(ngModel)]="salesRepresentative.endDate"
						[options]="dateOptions" locale="pt-br" [required]="true">
					</my-date-picker>
				</gp-simple-input>
			</div>
		</div>
		<div class="row">
			<div grid="12 6 6">
				<gp-simple-input label="Tipo de Desconto" [required]="true" errorMessage="Tipo de desconto é obrigatório">
					<select class="form-control" name="discountType" required [(ngModel)]="salesRepresentative.discountType">
						<option value="Value">Valor</option>
						<option value="Percent">Percentual</option>
					</select>
				</gp-simple-input>
			</div>
			<div grid="12 6 6">
				<gp-simple-input [label]="discountAmountLabel + ' de Desconto'" [required]="true" errorMessage="Desconto é obrigatório">
					<gp-input-mask name="amount" required [onlyDecimal]="true" [(ngModel)]="salesRepresentative.discountAmount"></gp-input-mask>
				</gp-simple-input>
			</div>
		</div>
		<div class="row">
			<div grid="12 6 6">
				<label>Ativo</label>
				<div>
					<label class="switch switch-lg">
						<input type="checkbox" checked="checked" id="active" name="active" /><span></span>
					</label>
				</div>
			</div>
		</div>
		<div class="row">
			<div grid="12 12 12">
				<gp-spinner-button type="button" [pink]="true" icon="send" text="Salvar Tabela Comercial" 
					loadingText="Processando" pull="right" [loading]="loading">
				</gp-spinner-button>
				<gp-spinner-button text="Novo" bootstrapClass="default" icon="plus" pull="right" marginRight="10px"
					[disabled]="loading" (click)="reset()" [showSpinner]="false">
				</gp-spinner-button>
			</div>
		</div>
	</gp-card>

	<gp-card title="Filtro de Aplicação">
		<accordion [closeOthers]="true">
			<accordion-group panelClass="b0 mb-sm panel-default">
				<div accordion-heading>
					<label>Por Parceiro</label>
				</div>
				<div class="row">
					<div grid="12 6 6" [group]="true">
						<label>Parceiro:</label>
						<ng-select [allowClear]="true" [items]="campaignPartners" (data)="selectPartner($event)" placeholder="Selecione um parceiro">
						</ng-select>
					</div>
					<div grid="12 6 6" class="top-p2 pull-right">
						<gp-spinner-button type="button" [pink]="true" icon="plus" text="Adicionar" loadingText="Adicionando" pull="right" [loading]="sendingFilter"
							[disabled]="!discountFilter.partnerId" (click)="addPartnerToFilter()">
						</gp-spinner-button>
					</div>
				</div>
				<div class="row">
					<div grid="12">
						<gp-grid name="partnersGrid" [rows]="salesRepresentative.partnersFilters" [columns]="['Parceiro']" [fields]="['partnerName']" [loading]="false"
							[showActive]="false" [showEdit]="true" [showDelete]="true" emptyMessage="Nenhum parceiro no filtro."
							(onEdit)="editPartner($event)" (onDelete)="removePartnerFromFilter($event)">
						</gp-grid>
					</div>
				</div>
			</accordion-group>
	
			<accordion-group panelClass="b0 mb-sm panel-default">
				<div accordion-heading>
					<label>Por Classificação (Departamento, Categoria e/ou Subcategoria)</label>
				</div>
				<div class="row">
					<div grid="12 3 3" [group]="true">
						<gp-simple-input label="Departamento">
							<gp-categories-select id="department" name="department" [active]="true" [level]="1" required [(ngModel)]="discountFilter.departmentId">
							</gp-categories-select>
						</gp-simple-input>
					</div>
					<div grid="12 3 3" [group]="true">
						<gp-simple-input label="Categoria">
							<gp-categories-select id="category" name="category" [active]="true" [level]="2" emptyMessage="Sem categorias"
								[parentId]="discountFilter.departmentId" [disabled]="!discountFilter.departmentId" [(ngModel)]="discountFilter.categoryId">
							</gp-categories-select>
						</gp-simple-input>
					</div>
					<div grid="12 3 3" [group]="true">
						<gp-simple-input label="Subcategoria">
							<gp-categories-select id="category" name="subcategory" [active]="true" [level]="3" emptyMessage="Sem subcategorias"
								[parentId]="discountFilter.categoryId" [disabled]="!discountFilter.categoryId" [(ngModel)]="discountFilter.subcategoryId">
							</gp-categories-select>
						</gp-simple-input>
					</div>
					<div grid="12 3 3" class="top-p2 pull-right">
						<gp-spinner-button type="button" [pink]="true" icon="plus" text="Adicionar" loadingText="Adicionando" pull="right"
							[loading]="sendingFilter" [disabled]="!discountFilter.departmentId" (click)="addDepartmentToFilter()">
						</gp-spinner-button>
					</div>
				</div>
				<div class="row">
					<div grid="12">
						<gp-grid name="departmentsGrid" [rows]="salesRepresentative.categoriesFilters"
							[columns]="['Departamento', 'Categoria', 'Subcategoria']" [fields]="['departmentName', 'categoryName', 'subcategoryName']"
							[loading]="false" [showActive]="false" [showEdit]="false" [showDelete]="true" emptyMessage="Nenhum departamento no filtro."
							(onDelete)="removeDepartmentFromFilter($event)">
						</gp-grid>
					</div>
				</div>
			</accordion-group>
	
			<accordion-group panelClass="b0 mb-sm panel-default">
				<div accordion-heading>
					<label>Por SKU</label>
				</div>
				<div class="row">
					<div grid="12 7" [group]="true">
						<label>Parceiro:</label>
						<ng-select [allowClear]="true" [items]="campaignPartners" (data)="selectPartner($event)" placeholder="Selecione um parceiro">
						</ng-select>
					</div>
					<div grid="12 5" [group]="true">
						<gp-simple-input label="Código SKU">
							<input type="text" class="form-control" name="skuCode" [(ngModel)]="discountFilter.skuCode" (blur)="searchSku()" />
						</gp-simple-input>
					</div>
					<div grid="12 10" [group]="true">
						<input type="text" class="form-control" [value]="product?.name" disabled />
					</div>
					<div grid="12 2" class="pull-right">
						<gp-spinner-button type="button" [pink]="true" icon="plus" text="Adicionar" loadingText="Adicionando" pull="right"
							[loading]="sendingFilter" [disabled]="isSkuInvalid" (click)="addProductToFilter()">
						</gp-spinner-button>
					</div>
				</div>
				<div class="row">
					<div grid="12">
						<gp-grid name="productsGrid" [rows]="salesRepresentative.skusFilters"
							[columns]="['Parceiro', 'Código SKU', 'Produto']" [fields]="['partnerName', 'skuCode', 'productName']"
							[loading]="false" [showActive]="false" [showEdit]="false" [showDelete]="true" emptyMessage="Nenhum produto no filtro."
							(onDelete)="removeProductFromFilter($event)">
						</gp-grid>
					</div>
				</div>
			</accordion-group>
		</accordion>
	</gp-card>
</form>
