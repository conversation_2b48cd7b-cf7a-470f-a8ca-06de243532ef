import { Component } from '@angular/core';
import { IMyDpOptions } from 'mydatepicker';

@Component({
  selector: 'commercial-tables-editor',
  templateUrl: 'commercial-tables-editor.component.html'
})
export class CommercialTablesEditorComponent {
  salesRepresentative: any = {};
  discountFilter = {};

  private dateOptions: IMyDpOptions;

  constructor() { }

  ngOnInit() {
    this.dateOptions = {
      dateFormat: 'dd/mm/yyyy',
      inline: false,
      minYear: 2000,
      maxYear: 2100
    };
  }

  get discountAmountLabel() {
    if (this.salesRepresentative && this.salesRepresentative.discountType == 'Percent')
      return 'Porcentagem';
    return 'Valor';
  }
}
