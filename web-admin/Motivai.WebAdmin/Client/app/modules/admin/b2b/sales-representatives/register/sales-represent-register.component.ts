import { Component, ViewChild } from '@angular/core';
import { TabsetComponent } from 'ng2-bootstrap';

import {PERMISSION_SHOPKEEPERS_LIST, PERMISSION_SHOPKEEPERS_VIEW,  PERMISSION_SHOPKEEPERS_EDIT,  PERMISSION_SALES_REPRESENTATIVES_INS} from '../../../../../core/auth/access-points';
import { AuthStore } from '../../../../../core/auth/auth.store';
import { SalesRepresentListComponent } from './sales-represent-list/sales-represent-list.component';
import { SalesRepresentEditorComponent } from './sales-represent-editor/sales-represent-editor.component';

@Component({
    selector: 'sales-represent-register',
    templateUrl: 'sales-represent-register.component.html'
})
export class SalesRepresentRegisterComponent {
    @ViewChild('tabset') tabset: TabsetComponent;
    @ViewChild('list') listComponent: SalesRepresentListComponent;
    @ViewChild('editor') editorComponent: SalesRepresentEditorComponent;

    constructor(private _authStore: AuthStore) {}

    get canAccessEditor() {
        return this._authStore.hasPermissionTo(PERMISSION_SHOPKEEPERS_VIEW)
        || this._authStore.hasPermissionTo(PERMISSION_SALES_REPRESENTATIVES_INS)
        || this._authStore.hasPermissionTo(PERMISSION_SHOPKEEPERS_EDIT);
    }

    clearEditor() {
        this.editorComponent.reset();
        this.listComponent.loadSalesRepresentantives();
    }

    edit(id) {
        if (this.editorComponent) {
            this.editorComponent.prepareEdition(id);
            this.tabset.tabs[1].active = true;
        }
    }
}
