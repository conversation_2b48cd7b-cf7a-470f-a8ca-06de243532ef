import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

import { AuthStore } from '../../../../core/auth/auth.store';
import { PERMISSION_SALES_REPRESENTATIVES_ORDER_LIST, PERMISSION_SALES_REPRESENTATIVES_ORDER_NEW, PERMISSION_SALES_REPRESENTATIVES_COMMERCIAL_TABLE_LIST, PERMISSION_SHOPKEEPERS_LIST } from '../../../../core/auth/access-points';

@Component({
    selector: 'sales-represent-view',
    templateUrl: 'sales-represent-view.component.html'
})
export class SalesRepresentViewComponent {
    constructor(private _authStore: AuthStore, private _route: Router) { }

    ngOnInit() {
        if (!this.canAccessSalesRepresentatives) {
            if (this.canAccessCommercialTable) {
                this._route.navigate(['/representantes/tabelascomerciais']);
            } else if (this.canAccessSalesRepOrders) {
                this._route.navigate(['/representantes/pedido']);
            } else if (this.canAccessShopkeepers) {
                this._route.navigate(['/representantes/lojas']);
            }
        }
    }

    get canAccessSalesRepresentatives() {
        return this._authStore.hasPermissionTo(PERMISSION_SHOPKEEPERS_LIST);
    }

    get canAccessSalesRepOrders() {
        return this._authStore.hasAtLeastOnePermission([
            PERMISSION_SALES_REPRESENTATIVES_ORDER_LIST,
            PERMISSION_SALES_REPRESENTATIVES_ORDER_NEW
        ]);
    }

    get canAccessCommercialTable() {
        return this._authStore.hasPermissionTo(PERMISSION_SALES_REPRESENTATIVES_COMMERCIAL_TABLE_LIST);
    }

    get canAccessShopkeepers() {
        return this._authStore.hasPermissionTo(PERMISSION_SHOPKEEPERS_LIST);
    }
}
