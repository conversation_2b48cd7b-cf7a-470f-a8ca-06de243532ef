import { Component, OnInit, ViewChild, Input, Output, EventEmitter } from '@angular/core';

import {PERMISSION_SHOPKEEPERS_LIST, PERMISSION_SHOPKEEPERS_EDIT,  PERMISSION_SHOPKEEPERS_VIEW} from '../../../../../../core/auth/access-points';
import { AuthStore } from '../../../../../../core/auth/auth.store';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { SalesRepresentEditorComponent } from '../sales-represent-editor/sales-represent-editor.component';
import { SalesRepresentativeService } from '../../sales-representative.service';

@Component({
    selector: 'sales-represent-list',
    templateUrl: 'sales-represent-list.component.html'
})
export class SalesRepresentListComponent implements OnInit {
    @Output('edit') editEmitter: EventEmitter<string> = new EventEmitter<string>();
    @ViewChild('gpAlert') gpAlert: GpAlertComponent;
    
    filter: any = { name: '' };

    salesRepresentatives: Array<any> = [];
    loading: boolean = false;

    constructor(private _authStore: AuthStore, private _salesRepresentativesService: SalesRepresentativeService) {}

    ngOnInit() {
        if (this.canAccessSalesRepresentatives) {
            this.loadSalesRepresentantives();
        } else {
            this.gpAlert.showError('Você não tem permissão para acessar esta tela, por favor, contate o administrador.');
        }
    }
    
    get canAccessSalesRepresentatives() {
        return this._authStore.hasPermissionTo(PERMISSION_SHOPKEEPERS_LIST);
    }

    private handleError(err) {
        const msg = err ? (err.message ? err.message : err.toString()) : '';
        this.gpAlert.showError(msg);
        this.loading = false;
    }

    get canViewOrEdit() {
        return this._authStore.hasPermissionTo(PERMISSION_SHOPKEEPERS_VIEW)
            || this._authStore.hasPermissionTo(PERMISSION_SHOPKEEPERS_EDIT);
    }

    loadSalesRepresentantives() {
        this.gpAlert.clear();
        this.loading = true;
        this._salesRepresentativesService.getAll(this.filter.name, null, null)
            .subscribe(salesRepresentatives => {
                if (salesRepresentatives) {
                    salesRepresentatives.forEach(s => {
                        s.commissionTypeDesc = s.commissionType == 'Fixed' ? 'Fixa' : 'Intervalor de valor';
                    });
                }
                this.salesRepresentatives = salesRepresentatives;
            },
            err => this.handleError(err),
            () => this.loading = false
        );
    }

    editSalesRepresentative(salesRepresentative: any) {
        if (!salesRepresentative || !salesRepresentative.id) return;
        this.editEmitter.emit(salesRepresentative.id);
    }
}
