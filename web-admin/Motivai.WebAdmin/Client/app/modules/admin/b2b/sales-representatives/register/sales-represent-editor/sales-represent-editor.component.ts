import { Component, OnInit, ViewChild } from '@angular/core';

import { PERMISSION_SALES_REPRESENTATIVES_INS, PERMISSION_SHOPKEEPERS_EDIT } from '../../../../../../core/auth/access-points';
import { AuthStore } from '../../../../../../core/auth/auth.store';
import { SalesRepresentativeService } from '../../sales-representative.service';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignService } from '../../../../campaigns/campaign.service';
import { Item } from '../../../../../../shared/models/item';
import { FactoryService } from '../../../factories/factory.service';

@Component({
  selector: 'sales-represent-editor',
  templateUrl: 'sales-represent-editor.component.html'
})
export class SalesRepresentEditorComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;

  saleRepresentative: any = { active: true };
  rangeCommission: any = {};
  loading: boolean = false;
  sending: boolean = false;

  users: Array<Item> = [];
  roles: Array<Item> = [];

  availableCampaigns: Array<Item> = [];
  selectedCampaignId: string | null = null;

  availableFactories: Array<Item> = [];
  selectedFactoryId: string | null = null;

  constructor(private _authStore: AuthStore, private _salesRepresentativesService: SalesRepresentativeService,
    private _campaignService: CampaignService, private _factoryService: FactoryService) { }

  ngOnInit() {
    this.loadCombos();
  }
  
  get canCreate() {
    return this._authStore.hasPermissionTo(PERMISSION_SALES_REPRESENTATIVES_INS);
  }

  get canSave() {
    return this._authStore.hasPermissionTo(PERMISSION_SALES_REPRESENTATIVES_INS)
      || this._authStore.hasPermissionTo(PERMISSION_SHOPKEEPERS_EDIT);
  }

  get isFixedCommission() {
    return this.saleRepresentative && this.saleRepresentative.commissionType == 'Fixed';
  }

  get isRangeCommission() {
    return this.saleRepresentative && this.saleRepresentative.commissionType == 'Range';
  }

  private handleError(err) {
    const msg = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro durante o processamento da requisição.';
    this.gpAlert.showError(msg);
    this.loading = false;
    this.sending = false;
  }

  private loadCombos() {
    this.loading = true;
    this._salesRepresentativesService.getUsers()
      .subscribe(
        users => {
          if (users) {
            this.users = users.map(u => new Item(u.id, `${u.name} - ${u.login}`));
          } else {
            this.users = [];
          }
        },
        err => this.handleError(err)
      );
    this._salesRepresentativesService.getRoles()
      .subscribe(
        roles => {
          if (roles) {
            this.roles = roles.map(r => new Item(r.id, r.name));
          }
        },
        err => this.handleError(err)
      );
    this._campaignService.getAll(null, null, 'B2B')
      .subscribe(
        campaigns => {
          if (campaigns) {
            this.availableCampaigns = campaigns.map(c => new Item(c.id, c.name));
            this.removeLinkedCampaignsFromList();
          } else {
            this.availableCampaigns = [];
          }
        },
        err => this.handleError(err),
        () => this.loading = false
      );
    this._factoryService.search(undefined, undefined, undefined, true, 'name')
      .subscribe(
        factories => {
          if (factories) {
            this.availableFactories = factories.map(f => {
              let name = (f.type == 'Factory' ? '[F] ' : '[D] ') + f.name;
              return new Item(f.id, name);
            });
          } else {
            this.availableFactories = [];
          }
        },
        err => this.handleError(err),
        () => this.loading = false
      );
  }

  private removeLinkedCampaignsFromList() {
    if (this.saleRepresentative.campaignsModels && this.saleRepresentative.campaignsModels.length) {
      this.availableCampaigns = this.availableCampaigns.filter(c => this.saleRepresentative.campaignsModels.findIndex(cm => cm.id == c.id) < 0);
    }
  }

  public prepareEdition(salesRepresentativeId) {
    this.gpAlert.clear();
    if (!salesRepresentativeId) {
      this.gpAlert.showWarning('ID inválido.');
      return;
    }
    this.loading = true;
    this.loadCombos();
    this._salesRepresentativesService.getById(salesRepresentativeId)
      .subscribe(
        salesRepresentative => {
          if (salesRepresentative) {
            this.saleRepresentative = salesRepresentative;
            this.removeLinkedCampaignsFromList();
          } else {
            this.gpAlert.showWarning('Representante não encontrado.');
          }
        },
        err => this.handleError(err),
        () => this.loading = false
      );
  }

  reset() {
    this.gpAlert.clear();
    this.saleRepresentative = {
      active: true
    };
  }

  addCampaign() {
    this.gpAlert.clear();
    let foundCampaign = this.availableCampaigns.find(c => c.id == this.selectedCampaignId);
    if (foundCampaign) {
      if (!this.saleRepresentative.campaignsModels) {
        this.saleRepresentative.campaignsModels = [];
      }
      
      if (this.saleRepresentative.campaignsModels.find(c => c.id == foundCampaign.id)) {
        this.gpAlert.showWarning('Campanha já está na lista.');
      } else {
        this.saleRepresentative.campaignsModels.push({ id: foundCampaign.id, name: foundCampaign.text });
        this.availableCampaigns = this.availableCampaigns.filter(c => c.id != foundCampaign.id);
      }
    }
    this.selectedCampaignId = null;
  }

  removeCampaign(campaign) {
    this.selectedCampaignId = null;
    if (!campaign) return;
    this.saleRepresentative.campaignsModels = this.saleRepresentative.campaignsModels.filter(c => c.id != campaign.id);

    if (!this.availableCampaigns.find(c => c.id == campaign.id)) {
      this.availableCampaigns = this.availableCampaigns.concat(new Item(campaign.id, campaign.name));
    }
  }

  addFactory() {
    this.gpAlert.clear();
    let foundFactory = this.availableFactories.find(c => c.id == this.selectedFactoryId);
    if (foundFactory) {
      if (!this.saleRepresentative.factoriesModels) {
        this.saleRepresentative.factoriesModels = [];
      }
      
      if (this.saleRepresentative.factoriesModels.find(c => c.id == foundFactory.id)) {
        this.gpAlert.showWarning('Campanha já está na lista.');
      } else {
        this.saleRepresentative.factoriesModels.push({ id: foundFactory.id, name: foundFactory.text });
        this.availableFactories = this.availableFactories.filter(c => c.id != foundFactory.id);
      }
    }
    this.selectedFactoryId = null;
  }

  removeFactory(factory) {
    this.selectedFactoryId = null;
    if (!factory) return;
    this.saleRepresentative.factoriesModels = this.saleRepresentative.factoriesModels.filter(c => c.id != factory.id);

    if (!this.availableFactories.find(c => c.id == factory.id)) {
      this.availableFactories = this.availableFactories.concat(new Item(factory.id, factory.name));
    }
  }

  removeCommission(commission: any) {
    if (!commission) return;
    if (!this.saleRepresentative.commissionsByRanges) return;
    this.saleRepresentative.commissionsByRanges = this.saleRepresentative.commissionsByRanges.filter(c => c != commission);
  }

  editCommission(commission: any) {
    if (!commission) return;
    if (!this.saleRepresentative.commissionsByRanges) return;
    this.rangeCommission = commission;
  }

  saveCommission() {
    this.gpAlert.clear();
    if (!this.rangeCommission.fromValue || !this.rangeCommission.toValue || !this.rangeCommission.commissionValue) {
      this.gpAlert.showWarning('Preencha todos os campos para prosseguir.');
      return;
    }
    try {
      this.rangeCommission.fromValue = parseFloat(this.rangeCommission.fromValue);
      this.rangeCommission.toValue = parseFloat(this.rangeCommission.toValue);
    } catch (ex) {
      this.gpAlert.showWarning('Intervalo de valores inválido.');
      return;
    }
    if (this.rangeCommission.fromValue > this.rangeCommission.toValue) {
      this.gpAlert.showWarning('Valor Até tem que ser maior que Valor De.');
      return;
    } else if (this.rangeCommission.commissionValue <= 0) {
      this.gpAlert.showWarning('Valor da comissão inválido.');
      return;
    }
    if (this.saleRepresentative.commissionsByRanges) {
      let foundRange = this.saleRepresentative.commissionsByRanges.findIndex(c => {
        if (c == this.rangeCommission) return null;
        if (c.fromValue <= this.rangeCommission.fromValue && c.toValue >= this.rangeCommission.fromValue) return c;
        if (c.fromValue <= this.rangeCommission.toValue && c.toValue >= this.rangeCommission.toValue) return c;
        return null;
      });
      if (foundRange >= 0) {
        this.gpAlert.showWarning('Não é permitido sobrepor intervalo de comissões.');
        return;
      }
    } else {
      this.saleRepresentative.commissionsByRanges = [];
    }
    if (!this.rangeCommission.id)
      this.saleRepresentative.commissionsByRanges.push(this.rangeCommission);
    this.rangeCommission = {};
  }

  save() {
    this.sending = true;
    this._salesRepresentativesService.save(this.saleRepresentative)
      .subscribe(
        result => {
          if (result) {
            this.reset();
            this.gpAlert.showSuccess('Representante salvo com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível salvar o representante, por favor, tente novamente.');
          }
        },
        err => this.handleError(err),
        () => this.sending = false
      );
  }
}
