<div class="content-heading">Representantes
	<small>Gerenciamento de Representantes de Distribuidoras e Fábricas</small>
</div>
<!-- <gp-card [noPaddingTop]="true" [noPaddingBottom]="true">
	<div class="row">
		<accordion [closeOthers]="true">
			<accordion-group panelClass="b0 mb-sm panel-default no-shadow" [isOpen]="true">
				<div accordion-heading>
					<label><em class="fa fa-navicon"></em> Features de Representantes</label>
				</div>
				<div class="row menublock text-center">
					<gp-menublock-item size="xs" icon="users" color="text-primary" text="Representantes" routerLinkActive="active"
						[routerLink]="['/representantes/cadastros']" *ngIf="canAccessSalesRepresentatives"></gp-menublock-item>

					<gp-menublock-item size="xs" icon="list" color="text-primary" text="Tabela Comercial" routerLinkActive="active"
						[routerLink]="['/representantes/tabelascomerciais']" *ngIf="canAccessCommercialTable"></gp-menublock-item>

					<gp-menublock-item size="xs" icon="shopping-cart" color="text-primary" text="Pedido" routerLinkActive="active"
						[routerLink]="['/representantes/pedido']" *ngIf="canAccessSalesRepOrders"></gp-menublock-item>

					<gp-menublock-item size="xs" icon="building-o" color="text-primary" text="Lojistas" routerLinkActive="active"
						[routerLink]="['/representantes/lojas']" *ngIf="canAccessShopkeepers"></gp-menublock-item>
				</div>
			</accordion-group>
		</accordion>
	</div>
</gp-card> -->
<router-outlet></router-outlet>