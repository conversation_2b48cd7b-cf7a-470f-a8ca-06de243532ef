<form>
	<gp-card title="Novo pedido" *ngIf="canCreateOrder">
		<gp-form-row>
			<gp-form-col cols="12 4 4">
				<label>Pesquisar por CPF/CNPJ</label>
				<gp-input-mask name="document" [(ngModel)]="search.document" [onlyInteger]="true" placeholder="Pesquise pelo CPF/CNPJ"
					(keypress)="searchUsersOnEnter($event)" title="Pressione ENTER para pesquisar por CPF/CNPJ"></gp-input-mask>
			</gp-form-col>
			<gp-form-col cols="12 4 4">
				<label>Pesquisar por nome</label>
				<input type="text" class="form-control" name="name" [(ngModel)]="search.name" placeholder="Pesquise pelo nome"
					(keypress)="searchUsersOnEnter($event)" title="Pressione ENTER para pesquisar por nome"/>
			</gp-form-col>
			<gp-form-col cols="12 2 2" additionalClasses="top-p2">
				<gp-spinner-button [pink]="true" icon="search" text="Pesquisar" loadingText="Pesquisando"
					[loading]="searchingUser" (click)="searchUsers()"
					title="Pressione a tecla ENTER nos campos de CPF/CNPJ e nome para pesquisar"></gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 8 8">
				<label>Lojistas encontrados</label>
				<gp-select name="user" [items]="users" [(ngModel)]="order.userId" (ngModelChange)="loadFactoriesByUserRegions()"
					[placeholder]="userPlaceholder"></gp-select>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 8 8">
				<gp-simple-input label="Ramo de negócio" [required]="true" errorMessage="Ramo de negócio é obrigatório">
					<gp-select name="campaignId" [items]="campaignsItems" [(ngModel)]="order.campaignId">
					</gp-select>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 8 8">
				<gp-simple-input label="Fábrica/Distribuidora" [required]="true" errorMessage="Fábrica/Distribuidora é obrigatória">
					<gp-select name="factoryId" [items]="factories" [(ngModel)]="order.factoryId" [placeholder]="factoriesPlaceholder">
					</gp-select>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<div class="row">
			<div class="col-md-12">
				<gp-alert #searchAlert></gp-alert>
			</div>
		</div>
	</gp-card>

	<gp-card title="Carrinho de compras" *ngIf="canCreateOrder">
		<gp-form-row>
			<gp-form-col cols="12 3 3">
				<gp-simple-input label="Pesquise o produto">
					<input type="text" class="form-control" name="skucode" [(ngModel)]="search.productFilter" placeholder="SKU ou nome"
						(keypress)="searchProductsOnEnter($event)" title="Pressione ENTER para pesquisar por SKU ou nome do produto" />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 2 2" additionalClasses="top-p2">
				<gp-spinner-button buttonClass="btn-block" [pink]="true" icon="search" text="Pesquisar" loadingText="Pesquisando"
					[loading]="searchingProduct" (click)="searchProducts()"
					title="Pressione ENTER no campo de produto para pesquisar."></gp-spinner-button>
			</gp-form-col>
			<gp-form-col cols="12 6 5">
				<gp-simple-input label="Produtos encontrados">
					<gp-select name="product" [items]="productsItems" [(ngModel)]="skuId" [placeholder]="productPlaceholder">
					</gp-select>
				</gp-simple-input>
			</gp-form-col>
			<div grid="12 2 2" class="top-p1">
				<img *ngIf="selectedProduct?.smallImage" class="product-image" title="Visualizar imagem"
					defaultImage="/assets/img/img-loader.gif" [lazyLoad]="selectedProduct.smallImage" (click)="showLargeImage()" />
			</div>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 3 3">
				<gp-simple-input label="Quantidade">
					<gp-input-mask required name="quantity" [onlyInteger]="true" placeholder="1" [(ngModel)]="selectedProduct.quantity"></gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 3 3">
				<div class="form-group">
					<label>Valor Unit. (R$)</label>
					<div class="input-group">
						<span class="input-group-addon">R$</span>
						<gp-input-mask required name="salePrice" [onlyDecimal]="true" [decimais]="2" placeholder="0,00"
							[(ngModel)]="selectedProduct.salePrice" (keypress)="applyDiscountFromSalePrice($event)"
							title="Pressione ENTER para calcular o desconto"></gp-input-mask>
					</div>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 3 3">
				<div class="form-group">
					<label>Desconto no item (R$)</label>
					<div class="input-group">
						<span class="input-group-addon">R$</span>
						<gp-input-mask required name="discountAmount" [onlyDecimal]="true" [decimais]="2" placeholder="0,00"
							[(ngModel)]="selectedProduct.discountAmount" (keypress)="applyDiscountUsingAmount($event)"
							title="Pressione ENTER para aplicar o desconto"></gp-input-mask>
					</div>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 3 3">
				<div class="form-group">
					<label>Desconto no item (%)</label>
					<div class="input-group">
						<span class="input-group-addon">%</span>
						<gp-input-mask required name="discountPercent" [onlyDecimal]="true" [decimais]="3" placeholder="0"
							[(ngModel)]="selectedProduct.discountPercent" (keypress)="applyDiscountUsingPercent($event)"
							title="Pressione ENTER para aplicar o desconto"></gp-input-mask>
					</div>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 6">
				<gp-spinner-button [pink]="true" icon="plus" text="Adicionar ao carrinho"
					[disabled]="disableButtons" (click)="addProductToCart()"></gp-spinner-button>

				<gp-spinner-button bootstrapClass="danger" icon="trash" text="Cancelar"
					[disabled]="disableButtons" (click)="cancelProduct()"></gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
		<hr />
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<h4>Itens do carrinho</h4>
				<gp-grid name="shoppingCart" [rows]="order?.items" 
					[columns]="['Código', 'Produto', 'Valor unitário', 'Quantidade', 'Desconto (R$)', 'Desconto (%)', 'Total']" 
					[fields]="['skuCode', 'name', 'salePrice', 'quantity', 'discountAmount', 'discountPercent', 'total']"
					[showActive]="false" [showPagination]="false" [showTotalPages]="false"
					[showEdit]="true" [showDelete]="true" (onEdit)="editProduct($event)" (onDelete)="removeProduct($event)">
				</gp-grid>
			</gp-form-col>
		</gp-form-row>
		<hr />
		<gp-form-row>
			<gp-form-col cols="12 4 4">
				<span>Total s/ desconto: <h4>R$ {{(order.total + order.totalDiscount) | number:'1.2'}}</h4></span>
			</gp-form-col>
			<gp-form-col cols="12 4 4">
				<span>Total desconto: <h4>R$ {{order.totalDiscount | number:'1.2'}}</h4></span>
			</gp-form-col>
			<gp-form-col cols="12 4 4">
				<span>Total c/ desconto: <h4>R$ {{order.total | number:'1.2'}}</h4></span>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card>
		<div class="row" *ngIf="canCreateOrder">
			<div class="col-md-12">
				<gp-spinner-button [pink]="true" icon="send" pull="right" marginRight="1em" text="Criar pedido" [disabled]="disableButtons"
					[loading]="sending" loadingText="Processando..." (click)="createOrder()"></gp-spinner-button>

				<gp-spinner-button bootstrapClass="danger" pull="right" icon="trash" marginRight="1em" text="Cancelar pedido"
					[disabled]="disableButtons" (click)="cancelOrder()"></gp-spinner-button>

				<gp-spinner-button bootstrapClass="default	" pull="right" icon="arrow-left" marginRight="1em" text="Voltar"
					[disabled]="disableButtons" [routerLink]="['/representantes/pedido']" ></gp-spinner-button>
			</div>
		</div>
		<div class="row col-md-12">
			<gp-alert #gpAlert></gp-alert>
		</div>
	</gp-card>
</form>

<gp-modal [title]="selectedProduct?.name" #imageModal>
	<div class="row">
		<div class="col-xs-12 text-center">
			<img *ngIf="productImageToShow" defaultImage="/assets/img/img-loader.gif"
				[lazyLoad]="productImageToShow" />
		</div>
	</div>
</gp-modal>
