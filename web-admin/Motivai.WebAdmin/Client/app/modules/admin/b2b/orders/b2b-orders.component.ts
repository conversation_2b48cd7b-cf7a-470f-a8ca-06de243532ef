import { TabsetComponent } from 'ng2-bootstrap/tabs';
import { Component, OnInit, ViewChild } from '@angular/core';

import { B2BOrdersListComponent } from './orders-list/b2b-orders-list.component';
import { B2BOrdersEditComponent } from './orders-detail/b2b-orders-edit.component';

@Component({
    selector: 'b2b-orders',
    templateUrl: 'b2b-orders.component.html'
})
export class B2BOrdersComponent implements OnInit {
    @ViewChild('tabs') tabs: TabsetComponent;
    @ViewChild('list') list: B2BOrdersListComponent;
    @ViewChild('edit') edit: B2BOrdersEditComponent;

    private orderId: string = '';

    constructor() { }

    ngOnInit() {
        this.tabs.tabs[1].disabled = true;
    }

    private prepareEdit($event) {
        if ($event) {
            this.orderId = $event.id;
            this.tabs.tabs[1].disabled = false;
            this.tabs.tabs[1].active = true;
        }
    }

    private clearEditForm() {
        this.orderId = '';
        this.list.findOrders();
        this.tabs.tabs[1].disabled = true;
        this.edit.clear();
    }
}
