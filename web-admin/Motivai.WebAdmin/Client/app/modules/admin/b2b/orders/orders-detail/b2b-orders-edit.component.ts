import { DecimalPipe } from '@angular/common';
import { Component, OnInit, Input, ViewChild } from '@angular/core';
import { B2BOrdersService } from '../b2b-orders.service';
import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';

@Component({
    selector: 'b2b-orders-edit',
    templateUrl: 'b2b-orders-edit.component.html'
})
export class B2BOrdersEditComponent implements OnInit {
    private order: any = {};
    private items: any[] = [];
    private loading: boolean = false;
    private loadingScreen: boolean = false;
    private pipe: DecimalPipe = new DecimalPipe('pt-BR');

    @ViewChild('alert') alert: GpAlertComponent;

    private _orderId: string;
    @Input() set orderId(v: string) {
        if (v) {
            this._orderId = v;
            this.findOrderById();
        }
    }

    constructor(private _ordersService: B2BOrdersService) { }

    ngOnInit() { }

    public clear() {
        this.order = {};
        this.items = [];
        this._orderId = '';
        if (this.alert) this.alert.clear();
    }

    private findOrderById() {
        if (this._orderId) {
            if (this.alert) this.alert.clear();
            this.loadingScreen = true;
            this._ordersService.findOrderById(this._orderId)
                .subscribe(order => {
                    if (order) {
                        this.order = order;
                        if (order.childrenOrders && order.childrenOrders.length) {
                            this.items = order.childrenOrders[0].items || [];

                            // Format currency
                            if (this.items.length > 0) {
                                this.items.forEach(item => {
                                    if (item.unitPrice && item.unitPrice.currency) {
                                        item.unitPrice.currency = this.pipe.transform(item.unitPrice.currency, '1.2-2');
                                    }
                                });
                            }
                        }
                    }
                    this.loadingScreen = false;
                }, err => {
                    const message = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao carregar as informações do pedido';
                    this.alert.showError(message);
                    this.loadingScreen = false;
                });
        }
    }
}
