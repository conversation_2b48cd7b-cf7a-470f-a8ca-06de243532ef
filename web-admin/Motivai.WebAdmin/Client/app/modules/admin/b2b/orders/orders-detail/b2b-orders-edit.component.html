<div *ngIf="!_orderId">
	<gp-card [first]="true">
		<gp-alert type="warning" message="Selecione um pedido para visualizar os detalhes"></gp-alert>
	</gp-card>
</div>
<div *ngIf="_orderId">
	<gp-card [first]="true">
		<h3>Pedido: {{ order.internalOrderNumber }}</h3>
		<hr />
		<div class="row">
			<div class="col-lg-4 col-xs-12 br pv">
				<div class="clearfix">
					<p class="pull-left"><strong>Data do Pedido</strong></p>
					<p class="pull-right mr">{{ order.creationDate | datetimezone:order.timezone }}</p>
				</div>
				<div class="clearfix">
					<p class="pull-left"><strong>Data Local do Pedido</strong></p>
					<p class="pull-right mr">{{ order.creationDate | datetimezone }}</p>
				</div>
				<div class="clearfix">
					<p class="pull-left"><strong>Data de Atualização</strong></p>
					<p class="pull-right mr">{{ order.updateDate | datetimezone }}</p>
				</div>
				<div class="clearfix">
					<p class="pull-left"><strong>Situação</strong></p>
					<p class="pull-right mr" [class.text-primary]="!order.occurredError"><strong>{{ order.statusDescription }}</strong></p>
				</div>
				<div class="clearfix">
					<p class="pull-left"><strong>Total Pedido</strong></p>
					<p class="pull-right mr">
						<span><strong>R$ {{ order.totalAmount?.currency | number:'1.2-2' }}</strong></span>
					</p>
				</div>
			</div>
			<div class="col-lg-4 col-xs-6 br pv" *ngIf="order.shippingAddress">
				<div class="row">
					<div class="col-md-2 text-center visible-md visible-lg">
						<em class="fa fa-truck fa-3x text-muted"></em>
					</div>
					<div class="col-md-10">
						<h4>Entrega</h4>
						<address></address>{{ order.shippingAddress?.addressName }}<br/>
						CEP: {{ order.shippingAddress?.cep | mask:'00000-000' }}<br/>
						{{ order.shippingAddress?.street }}, {{ order.shippingAddress?.number }}<br/>
						{{ order.shippingAddress?.neighborhood }} - {{ order.shippingAddress?.city }}/{{ order.shippingAddress?.state }}
					</div>
				</div>
			</div>
			<div class="col-lg-4 col-xs-6 br pv" *ngIf="order.participant">
				<div class="row">
					<div class="col-md-2 text-center visible-md visible-lg">
						<em class="fa fa-user fa-3x text-muted"></em>
					</div>
					<div class="col-md-10">
						<h4>Lojista</h4>
						<address></address>{{ order.participant?.name }}<br/>
						<strong>CNPJ:</strong> {{ order.participant?.cnpj | document }}<br/>
						<strong>Telefone:</strong> {{ (order.participant?.mainPhone | telephone) || 'Não cadastrado' }}<br/>
						<strong>Celular:</strong> {{ (order.participant?.mobilePhone | telephone) || 'Não cadastrado' }}
					</div>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col-md-12">
				<gp-alert #alert></gp-alert>
			</div>
		</div>
		<spinner [overlay]="true" [show]="loadingScreen"></spinner>
	</gp-card>

	<gp-card title="Itens do pedido">
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-grid name="itemsGrid"
					[rows]="items" 
					[columns]="['Código SKU', 'Produto', 'Quantidade', 'Valor Unitário (R$)']" 
					[fields]="['skuCode', 'name', 'quantity', 'unitPrice.currency']"
					[showActive]="false"
					[showPagination]="false"
					[showTotalPages]="false"
					[showEdit]="false"
					[showDelete]="false"
					[loading]="loading">
				</gp-grid>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card title="Observações" *ngIf="order.observations">
		<p>{{ order.observations }}</p>
	</gp-card>
</div>