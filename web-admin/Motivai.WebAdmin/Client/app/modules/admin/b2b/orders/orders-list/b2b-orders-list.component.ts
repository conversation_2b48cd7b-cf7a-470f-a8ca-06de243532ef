import { Component, OnInit, Output, EventEmitter, ViewChild } from '@angular/core';
import { Router } from '@angular/router';

import { PERMISSION_SALES_REPRESENTATIVES_ORDER_NEW, PERMISSION_SALES_REPRESENTATIVES_ORDER_LIST, PERMISSION_SALES_REPRESENTATIVES_ORDER_VIEW } from '../../../../../core/auth/access-points';
import { AuthStore } from '../../../../../core/auth/auth.store';
import { B2BOrdersService } from '../b2b-orders.service';
import { FormatHelper } from '../../../../../shared/formatters/format-helper';
import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';

@Component({
    selector: 'b2b-orders-list',
    templateUrl: 'b2b-orders-list.component.html'
})
export class B2BOrdersListComponent implements OnInit {
    @Output('onedit') onedit: EventEmitter<any> = new EventEmitter();

    private search: any = {};
    private orders: any[] = [];
    private loading: boolean = false;
    private skip: number = 0;
    private limit: number = 50;

    @ViewChild('alert') alert: GpAlertComponent;
    constructor(private _route: Router, private _authStore: AuthStore, private _ordersService: B2BOrdersService) { }

    ngOnInit() {
        this.findOrders();
    }

    get canCreateOrder() {
        return this._authStore.hasPermissionTo(PERMISSION_SALES_REPRESENTATIVES_ORDER_NEW);
    }

    get canListOrders() {
        return this._authStore.hasPermissionTo(PERMISSION_SALES_REPRESENTATIVES_ORDER_LIST);
    }

    get canViewOrder() {
        return this._authStore.hasPermissionTo(PERMISSION_SALES_REPRESENTATIVES_ORDER_VIEW);
    }

    public findOrders() {
        this.loading = true;
        this.alert.clear();
        this._ordersService.findOrders(this.search.orderDateFrom, this.search.orderDateTo, this.search.status, this.search.orderNumber, this.search.cnpj, this.skip, this.limit)
            .subscribe(orders => {
                this.orders = orders;
                if (this.orders) {
                    this.orders.forEach(order => {
                        order.formattedCreationDate = FormatHelper.formatDateWithTimezone(order.creationDate, order.timezone);
                    });
                }
                this.loading = false;
            }, err => {
                const message = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao buscar os pedidos';
                this.alert.showError(message);
                this.loading = false;
            });
    }

    private prepareEdit($event) {
        if ($event) {
            this.onedit.emit($event);
        }
    }

    private pageChanged($event: any) {
        if ($event) {
            this.skip = $event.skip;
            this.limit = $event.limit;
            this.findOrders();
        }
    }

    copyOrder(order: any) {
        if (!order || !order.id)
            return;
        this._route.navigate(['representantes', 'pedido', order.id, 'clone']);
    }
}
