<gp-card [first]="true">
	<gp-form-row>
		<gp-form-col cols="12 12 12" *ngIf="canCreateOrder">
			<button class="btn btn-default btn-lg btn-main"
				[routerLink]="['/representantes', 'pedido', 'novo']">Novo Pedido <i class="fa fa-plus" style="margin-left: 5px"></i>
			</button>
		</gp-form-col>
	</gp-form-row>
</gp-card>
<gp-card title="Filtros de pedidos" *ngIf="canListOrders">
	<gp-form-row>
		<gp-datepicker cols="12 4 4 4" label="Data do pedido (de)" name="orderDateFrom" [(ngModel)]="search.orderDateFrom"></gp-datepicker>
		<gp-datepicker cols="12 4 4 4" label="Data do pedido (até)" name="orderDateTo" [(ngModel)]="search.orderDateTo"></gp-datepicker>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Status do Pedido">
				<select class="form-control" name="status" [(ngModel)]="search.status">
					<option value=''>Todos</option>
					<option value="Analysis">Em análise</option>
					<option value="Approved">Aprovado</option>
					<option value="Debted">Faturado</option>
					<option value="Refused">Reprovado</option>
				</select>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Número do pedido">
				<input type="text" class="form-control" name="orderNumber" [(ngModel)]="search.orderNumber" />
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="CNPJ do Lojista">
				<gp-input-mask name="cnpj" mask="99.999.999/9999-99" [(ngModel)]="search.cnpj"></gp-input-mask>
			</gp-simple-input>
		</gp-form-col>

		<gp-form-col cols="12 4 4">
			<gp-spinner-button type="button" [search]="true" text="Consultar pedidos"
				marginTop="26px" [loading]="loading" (click)="findOrders()">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
</gp-card>
<gp-card [last]="true" title="Pedidos encontrados">
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<gp-grid name="ordersGrid"
				[rows]="orders"
				[columns]="['Data/Hora do Pedido', 'Número do Pedido', 'Status']"
				[fields]="['formattedCreationDate', 'internalOrderNumber', 'formattedStatus']"
				[showActive]="false" [showPagination]="true" [showTotalPages]="false" [pageSize]="limit"
				[showTooltip]="true" customTooltip="Copiar pedido"
				customIcon="copy" customTitle="Copiar Pedido"
				[showEdit]="canViewOrder" [showCustom]="canCreateOrder" [showDelete]="false"
				[loading]="loading"
				(onEdit)="prepareEdit($event)" (onCustom)="copyOrder($event)" (onPageChanged)="pageChanged($event)">
			</gp-grid>
		</gp-form-col>
	</gp-form-row>
	<div class="row">
		<div class="col-md-12">
			<gp-alert #alert></gp-alert>
		</div>
	</div>
</gp-card>
