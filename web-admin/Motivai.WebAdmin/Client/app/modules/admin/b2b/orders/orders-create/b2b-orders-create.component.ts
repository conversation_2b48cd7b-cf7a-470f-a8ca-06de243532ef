import { Component, OnInit, ViewChild } from '@angular/core';
import { CurrencyPipe } from '@angular/common';

import { CampaignService } from '../../../campaigns/campaign.service';
import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { Item } from '../../../../../shared/models/item';
import { AuthStore } from '../../../../../core/auth/auth.store';
import { SalesRepresentativeService } from '../../sales-representatives/sales-representative.service';
import { UsersService } from '../../../../../core/services/users.service';
import { FormatHelper } from '../../../../../shared/formatters/format-helper';
import { FactoryService } from '../../factories/factory.service';
import { ProductsService } from '../../../products/products.service';
import { GpModalComponent } from '../../../../../shared/components/gp-modal/gp-modal.component';
import { B2BOrdersService } from '../b2b-orders.service';
import { ActivatedRoute, Router } from '@angular/router';
import { PERMISSION_SALES_REPRESENTATIVES_ORDER_NEW } from '../../../../../core/auth/access-points';

@Component({
  selector: 'b2b-orders-create',
  templateUrl: 'b2b-orders-create.component.html',
  styles: [`
    .product-image {
      width: 60px;
      height: 60px;
      border:2px solid #CCC;
      border-radius:5px;
      cursor:pointer;
    }
  `]
})
export class B2BOrdersCreateComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @ViewChild('searchAlert') searchAlert: GpAlertComponent;
  @ViewChild('imageModal') imageModal: GpModalComponent;
  
  search: any = {
    document: '',
    name: ''
  };

  users: Array<Item> = [];
  userPlaceholder: string = 'Pesquise um lojista';
  products: Array<any> = [];
  productsItems: Array<Item> = [];
  productPlaceholder: string = 'Pesquise um produto';
  campaignsItems: Array<any> = [];
  factoriesPlaceholder: string = 'Selecione um lojista';
  factories: Array<any> = [];

  // representante que está fazendo o pedido
  selectedProduct: any = {};
  productImageToShow: string;

  order: any = {};

  loading: boolean = false;
  searchingUser: boolean = false;
  searchingProduct: boolean = false;
  sending: boolean = false;

  constructor(private _authStore: AuthStore, private _usersService: UsersService,
    private _salesRepService: SalesRepresentativeService, private _productsService: ProductsService,
    private _ordersService: B2BOrdersService, private route: ActivatedRoute, private router: Router) {
  }

  ngOnInit() {
    this.newOrder();
    if (!this.canCreateOrder) {
      this.gpAlert.showError('Você não tem permissão para acessar esta tela, por favor, contate o administrador.');
      return;
    }
    this._salesRepService.getLinkedCampaigns(this._authStore.loggedUser.id)
      .subscribe(
        campaigns => {
          if (campaigns && campaigns.length && campaigns.length > 0) {
            this.campaignsItems = campaigns.map(c => new Item(c.id, c.name));
          } else {
            this.gpAlert.showWarning('Nenhum ramo encontrado.');
            this.campaignsItems = [];
          }
        },
        err => this.handleError(err)
      );
  
    if (this.route != null) {
      this.route.params.subscribe((params: any) => {
        let orderToClone = params['id'];
        if (!!orderToClone) {
          this.loading = true;
          this._ordersService.cloneOrder(orderToClone)
            .subscribe(
              order => {
                if (order) {
                  this.order = order;
                  if (this.order.userId) {
                    this.users = [new Item(this.order.userId, this.order.userName)];
                  }
                  this.loadFactoriesByUserRegions();
                  this.totalize();
                } else {
                  this.gpAlert.showWarning('Não foi possível encontrar o pedido para cópia.');
                }
                this.loading = false;
              },
              err => this.handleError(err)
            );
        }
      });
    }
  }

  get canCreateOrder() {
      return this._authStore.hasPermissionTo(PERMISSION_SALES_REPRESENTATIVES_ORDER_NEW);
  }

  get disableButtons() {
    return this.loading || this.searchingUser || this.searchingProduct || this.sending;
  }

  get product() {
    if (this.selectedProduct)
      return this.selectedProduct.skuId;
    return null;
  }

  set product(prod) {
    if (prod && prod.skuId) {
      this.selectedProduct = prod;
    } else {
      this.selectedProduct = {
        unitPrice: 0
      };
    }
    if (!this.selectedProduct.salePrice) {
      this.selectedProduct.salePrice = this.selectedProduct.unitPrice;
      this.selectedProduct.quantity = 1;
      this.selectedProduct.discountAmount = 0;
      this.selectedProduct.discountPercent = 0;
    }
  }

  get skuId() {
    if (this.selectedProduct)
      return this.selectedProduct.skuId;
    return null;
  }

  set skuId(skuId: string) {
    if (this.products && skuId) {
      this.product = this.products.find(p => p.skuId == skuId);
    } else {
      this.product = null;
    }
  }

  private handleError(err) {
    const msg = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro durante o processamento da requisição.';
    this.gpAlert.showError(msg);
    this.loading = false;
    this.searchingUser = false;
    this.searchingProduct = false;
    this.sending = false;
  }

  private isSendEvent(keyEvent: any) {
    return keyEvent && keyEvent.key == 'Enter';
  }

  private newOrder() {
    this.order = {
      campaignId: '',
      userId: '',
      factoryId: '',
      items: [],
      totalDiscount: 0,
      total: 0
    };
    this.search = {
      document: '',
      name: ''
    };
    this.users = [];
    this.userPlaceholder = 'Pesquise um lojista';
    this.products = [];
    this.productsItems = [];
    this.productPlaceholder = 'Pesquise um produto';
    this.campaignsItems = [];
    this.factoriesPlaceholder = 'Selecione um lojista';
    this.factories = [];
  }

  searchUsersOnEnter(event: any) {
    if (this.isSendEvent(event)) {
      this.searchUsers();
    }
  }

  searchUsers() {
    if (!this.search.document && !this.search.name) {
      this.searchAlert.showWarning('Informe o CPF/CNPJ ou nome do lojista para pesquisa.');
      return;
    }
    if (this.search.document) {
      this.search.document = this.search.document.replace(/[.-\/]/g, '');
    } else if (this.search.name.length < 3) {
      this.searchAlert.showWarning('Digite pelo menos 3 no filtro para efetuar pesquisa.');
      return;
    }
    this.searchingUser = true;
    this.userPlaceholder = 'Pesquisando...';
    this.order.userId = '';
    this.gpAlert.clear();
    this._usersService.searchByBusinessTypeAndName('B2B', this.search.document, this.search.name)
      .subscribe(
        users => {
          if (users) {
            this.users = users.map(u => new Item(u.userId, u.name + ' - ' + FormatHelper.formatDocument(u.document)));
            if (this.users && this.users.length == 1 && this.users[0].id) {
              this.order.userId = this.users[0].id;
              this.loadFactoriesByUserRegions();
              this.userPlaceholder = '';
            } else {
              this.userPlaceholder = 'Foram encontrados ' + this.users.length + ' lojistas';
            }
          } else {
            this.users = [];
            this.userPlaceholder = 'Nenhum lojista encontrado pelo filtro informado';
            this.gpAlert.showWarning(this.userPlaceholder);
          }
          this.searchingUser = false;
        },
        err => this.handleError(err)
      );
  }

  loadFactoriesByUserRegions() {
    if (!this.order.userId) {
      return;
    }
    this.factoriesPlaceholder = 'Carregando fábricas que atendam a região do lojista';
    let facId = this.order.factoryId;
    if (this.order.factoryId) {
      this.order.factoryId = null;
    }
    this._salesRepService.getAvailableFactoriesForUser(this.order.userId)
      .subscribe(
        factories => {
          if (factories) {
            this.factories = factories.map(c => new Item(c.id, c.name));
            if (factories.length == 1) {
              this.order.factoryId = factories[0].id;
              this.factoriesPlaceholder = '';
            } else {
              this.factoriesPlaceholder = 'Foram encontradas ' + factories.length + ' fábricas';
              this.order.factoryId = facId;
            }
          } else {
            this.factories = [];
            this.factoriesPlaceholder = 'Nenhuma fábrica encontrada';
            this.gpAlert.showWarning(this.factoriesPlaceholder);
          }
        },
        err => this.handleError(err)
      );
  }

  searchProductsOnEnter(event: any) {
    if (this.isSendEvent(event)) {
      this.searchProducts();
    }
  }

  searchProducts() {
    if (!this.search.productFilter) {
      this.gpAlert.showWarning('Informe o filtro de produto para pesquisa.');
      return;
    }
    if (!this.order.userId) {
      this.gpAlert.showWarning('Selecione o lojista.');
      return;
    }
    if (!this.order.campaignId) {
      this.gpAlert.showWarning('Selecione o ramo.');
      return;
    }
    if (!this.order.factoryId) {
      this.gpAlert.showWarning('Selecione a fábrica.');
      return;
    }
    this.searchingProduct = true;
    this.productPlaceholder = 'Pesquisando...';
    this.gpAlert.clear();
    this._productsService.search(this.order.campaignId, this.order.userId, this.order.factoryId, this.search.productFilter)
      .subscribe(
        products => {
          if (products) {
            this.products = products;
            this.productsItems = products.map(p => new Item(p.skuId, p.name + ' - ' + p.skuCode));
            if (this.productsItems.length == 1) {
              this.product = products[0];
              this.productPlaceholder = '';
            } else {
              this.productPlaceholder = 'Foram encontrados ' + products.length + ' produtos';
            }
          } else {
            this.productsItems = [];
            this.products = [];
            this.productPlaceholder = 'Nenhum produto encontrado pelo filtro informado';
            this.gpAlert.showWarning(this.productPlaceholder);
          }
          this.searchingProduct = false;
        },
        err => this.handleError(err)
      );
  }

  showLargeImage() {
    if (this.selectedProduct) {
      this.productImageToShow = this.selectedProduct.largeImage || this.selectedProduct.smallImagem;
      this.imageModal.show();
    }
  }

  applyDiscountFromSalePrice(event) {
    if (this.isSendEvent(event)) {
      this.gpAlert.clear();
      if (!this.selectedProduct.salePrice) {
        this.gpAlert.showError('Valor unitário do produto inválido.');
      } else if (this.selectedProduct.unitPrice < this.selectedProduct.salePrice) {
        this.selectedProduct.discountAmount = 0;
        this.selectedProduct.discountPercent = 0;
      } else {
        this.selectedProduct.discountAmount = this.selectedProduct.unitPrice - this.selectedProduct.salePrice;
        this.selectedProduct.discountPercent = parseFloat(((this.selectedProduct.discountAmount / this.selectedProduct.unitPrice) * 100.0).toFixed(3));
      }
    }
  }

  applyDiscountUsingAmount() {
    if (this.isSendEvent(event)) {
      this.gpAlert.clear();
      if (!this.selectedProduct.discountAmount) {
        this.gpAlert.showError('Valor de desconto do produto inválido.');
      } else {
        this.selectedProduct.discountPercent = parseFloat(((this.selectedProduct.discountAmount / this.selectedProduct.unitPrice) * 100.0).toFixed(3));
        this.selectedProduct.salePrice = this.selectedProduct.unitPrice - this.selectedProduct.discountAmount;
      }
    }
  }

  applyDiscountUsingPercent() {
    if (this.isSendEvent(event)) {
      this.gpAlert.clear();
      if (!this.selectedProduct.discountPercent) {
        this.gpAlert.showError('Porcentagem de desconto do produto inválida.');
      } else if (this.selectedProduct.discountPercent > 100) {
        this.gpAlert.showError('Porcentagem de desconto do produto não pode ser maior que 100.');
      } else {
        this.selectedProduct.discountAmount = parseFloat(((this.selectedProduct.discountPercent / 100) * this.selectedProduct.unitPrice).toFixed(2));
        this.selectedProduct.salePrice = this.selectedProduct.unitPrice - this.selectedProduct.discountAmount;
      }
    }
  }

  removeProduct(product: any) {
    if (!product || !this.order.items) {
      return;
    }
    this.order.items = this.order.items.filter(p => p.skuId != product.skuId && p.skuCode != product.skuCode);
  }

  editProduct(product: any) {
    if (!product) return;
    this.product = product;
  }

  addProductToCart() {
    if (!this.selectedProduct) {
      this.gpAlert.showWarning('Selecione o produto para adicionar ao carrinho.');
      return;
    }
    if (!this.selectedProduct.quantity) {
      this.gpAlert.showWarning('Informe a quantidade.');
      return;
    }
    if (!this.selectedProduct.salePrice) {
      this.gpAlert.showWarning('Informe o valor unitário de venda do produto.');
      return;
    }
    if (!this.selectedProduct.discountAmount || !this.selectedProduct.discountPercent) {
      this.selectedProduct.discountAmount = 0;
      this.selectedProduct.discountPercent = 0;
    }

    this.selectedProduct.total = this.selectedProduct.quantity * this.selectedProduct.salePrice;

    if (!this.order.items) {
      this.order.items = [];
    }
    
    if (this.order.items.findIndex(p => p.skuId == this.selectedProduct.skuId && p.skuCode == this.selectedProduct.skuCode) < 0) {
      this.order.items.push(this.selectedProduct);
    }
    this.totalize();
    this.products = [];
    this.productsItems = [];
    this.product = null;
    this.search.productFilter = '';
  }

  cancelProduct() {
    if (!this.selectedProduct) {
      return;
    }
    if (confirm('Deseja cancelar a edição do item?')) {
      this.product = null;
      this.search.productFilter = '';
      this.totalize();
    }
  }

  totalize() {
    if (this.order.items && this.order.items.length > 0) {
      let totalDiscount = 0;
      let total = 0;
      for (let i in this.order.items) {
        if (!this.order.items[i].total) {
          this.order.items[i].total = this.order.items[i].quantity * this.order.items[i].salePrice;
        }
        totalDiscount = totalDiscount + (this.order.items[i].discountAmount || 0);
        total = total + (this.order.items[i].total || 0);
      }
      this.order.totalDiscount = totalDiscount;
      this.order.total = total;
    } else {
      this.order.totalDiscount = 0;
      this.order.total = 0;
    }
  }

  createOrder() {
    if (this.selectedProduct && this.selectedProduct.skuId) {
      this.gpAlert.showWarning('Você está editando um item, por favor, cancele-o para prosseguir.');
      return;
    }
    if (!this.order.userId) {
      this.gpAlert.showWarning('Selecione o lojista.');
      return;
    }
    if (!this.order.campaignId) {
      this.gpAlert.showWarning('Selecione o ramo.');
      return;
    }
    if (!this.order.factoryId) {
      this.gpAlert.showWarning('Selecione a fábrica.');
      return;
    }
    if (!this.order.items || !this.order.items.length) {
      this.gpAlert.showWarning('Adicione pelo menos um produto no carrinho.');
      return;
    }
    
    this.order.creationDate = new Date();
    this.order.timezoneOffset = (new Date()).getTimezoneOffset();
    try {
      this.order.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    } catch (ex) {
    }

    this.sending = true;
    this.gpAlert.clear();
    this._ordersService.createOrder(this.order)
      .subscribe(
        result => {
          if (result) {
            this.newOrder();
            this.gpAlert.showSuccess('Pedido criado com sucesso. Número do pedido gerado: ' + result);
          } else {
            this.gpAlert.showWarning('Não foi possível criar o pedido, por favor, tente novamente.');
          }
          this.sending = false;
        },
        err => this.handleError(err)
      );
  }

  cancelOrder() {
    if (confirm('Deseja cancelar o pedido?')) {
      this.newOrder();
    }
  }
}
