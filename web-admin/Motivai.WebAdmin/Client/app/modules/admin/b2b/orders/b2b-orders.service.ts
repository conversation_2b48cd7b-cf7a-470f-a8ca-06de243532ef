import { Observable } from 'rxjs/Observable';
import * as moment from 'moment';
import { Injectable } from '@angular/core';
import { ApiService } from '../../../../core/api/api.service';

@Injectable()
export class B2BOrdersService {
    constructor(private _api: ApiService) { }

    findOrders(orderDateFrom?: Date, orderDateTo?: Date, status?: string, orderNumber?: string, cnpj?: string, skip?: number, limit?: number): Observable<any[]> {
        const query: any = {};
        if (orderDateFrom) query.orderDateFrom = moment(orderDateFrom).format('YYYY-MM-DD');
        if (orderDateTo) query.orderDateTo = moment(orderDateTo).format('YYYY-MM-DD');
        if (status) query.status = status;
        if (orderNumber) query.orderNumber = orderNumber;
        if (cnpj) query.cnpj = cnpj;
        if (skip != null) query.skip = skip;
        if (limit != null) query.limit = limit;
        return this._api.get(`/api/b2b/orders`, query);
    }

    findOrderById(orderId: string): Observable<any> {
        return this._api.get(`/api/b2b/orders/${orderId}`);
    }

    createOrder(order: any): Observable<any> {
        return this._api.post(`/api/b2b/orders`, order, 30000);
    }

    cloneOrder(orderId: any): Observable<any> {
        return this._api.get(`/api/b2b/orders/${orderId}/clone`, 30000);
    }
}
