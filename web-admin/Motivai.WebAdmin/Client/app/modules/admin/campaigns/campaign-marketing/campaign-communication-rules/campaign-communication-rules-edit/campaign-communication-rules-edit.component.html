<form #communicationRulesForm="ngForm" (ngSubmit)="saveCommunicationRules()">
  <gp-alert #alert [overlay]="true"></gp-alert>
  <spinner [overlay]="true" [show]="loading"></spinner>

  <gp-card title="Dados da regra de comunicação" [first]="true">
    <gp-form-row>
      <gp-form-col cols="12 6 6">
        <gp-simple-input label="Nome*">
          <input type="text" class="form-control" name="name" required [(ngModel)]="communicationRule.name" />
        </gp-simple-input>
      </gp-form-col>
      <gp-form-col cols="12 6 6">
        <label>Ativo</label>
        <div>
          <gp-switch name="active" [(ngModel)]="communicationRule.active"></gp-switch>
        </div>
      </gp-form-col>
    </gp-form-row>
    <gp-form-row>
      <gp-form-col cols="12 3 3">
        <gp-simple-input label="Tipo de regra*">
          <select class="form-control" name="ruleType" [(ngModel)]="communicationRule.ruleType">
            <option value="CUSTOM">Customizado</option>
          </select>
        </gp-simple-input>
      </gp-form-col>
      <gp-form-col cols="12 3 3">
        <gp-simple-input label="Periodicidade">
          <select class="form-control" name="periodity" [(ngModel)]="communicationRule.periodity">
            <option value="DAY_OF_THE_WEEK">Dia da semana</option>
            <!--option value="DAY_OF_THE_MONTH">Dia do mês</option>
            <option value="SPECIFIC_DATE">Dia específico</option-->
          </select>
        </gp-simple-input>
      </gp-form-col>
      <gp-form-col cols="12 6 6">
        <label>Recorrente</label>
        <div>
          <gp-switch name="recurrent" [(ngModel)]="communicationRule.recurrent"></gp-switch>
        </div>
      </gp-form-col>
    </gp-form-row>
    <gp-form-row *ngIf="!communicationRule.id">
      <gp-form-col cols="12">
        <gp-spinner-button
          type="button"
          (click)="saveDataCommunicationRules()"
          text="Salvar"
          size="lg"
          pull="right"
          [pink]="true"
          icon="send"
          loadingText="Processando"
          [loading]="loading"
        >
        </gp-spinner-button>
      </gp-form-col>
    </gp-form-row>
  </gp-card>
  <div *ngIf="communicationRule.id">
    <gp-card title="Data de processamento">
      <gp-form-row *ngIf="communicationRule.periodity === 'DAY_OF_THE_WEEK'">
        <gp-form-col cols="12 6 6">
          <gp-simple-input label="Dia da semana">
            <select class="form-control" name="day" [(ngModel)]="day">
              <option value="Sunday">Domingo</option>
              <option value="Monday">Segunda-feira</option>
              <option value="Tuesday">Terça-feira</option>
              <option value="Wednesday">Quarta-feira</option>
              <option value="Thursday">Quinta-feira</option>
              <option value="Friday">Sexta-feira</option>
              <option value="Saturday">Sábado</option>
            </select>
          </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 6 6">
          <gp-simple-input label="Horário">
            <select class="form-control" name="hour" [(ngModel)]="hour">
              <option value="00:00">00:00</option>
              <option value="01:00">01:00</option>
              <option value="02:00">02:00</option>
              <option value="03:00">03:00</option>
              <option value="04:00">04:00</option>
              <option value="05:00">05:00</option>
              <option value="06:00">06:00</option>
              <option value="07:00">07:00</option>
              <option value="08:00">08:00</option>
              <option value="09:00">09:00</option>
              <option value="10:00">10:00</option>
              <option value="11:00">11:00</option>
              <option value="12:00">12:00</option>
              <option value="13:00">13:00</option>
              <option value="14:00">14:00</option>
              <option value="15:00">15:00</option>
              <option value="16:00">16:00</option>
              <option value="17:00">17:00</option>
              <option value="18:00">18:00</option>
              <option value="19:00">19:00</option>
              <option value="20:00">20:00</option>
              <option value="21:00">21:00</option>
              <option value="22:00">22:00</option>
              <option value="23:00">23:00</option>
            </select>
          </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 12 12">
          <gp-spinner-button
            type="button"
            [pink]="true"
            (click)="addToGridAndSave()"
            text="Adicionar"
            size="lg"
            pull="right"
            icon=""
          >
          </gp-spinner-button>
        </gp-form-col>
      </gp-form-row>

      <gp-form-row *ngIf="communicationRule.periodity === 'DAY_OF_THE_MONTH'">
        <gp-form-col cols="12 3 3">
          <gp-simple-input label="Disparar no dia">
            <select class="form-control" name="day" [(ngModel)]="day">
              <option *ngFor="let day of days" [value]="day">{{ day }}</option>
            </select>
          </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 3 3">
          <gp-simple-input label="A partir do mês">
            <select class="form-control" name="day" [(ngModel)]="communicationRule.month">
              <option value="JANUARY">Janeiro</option>
              <option value="JANUARY">Fevereiro</option>
              <option value="JANUARY">Março</option>
              <option value="JANUARY">Abril</option>
              <option value="JANUARY">Maio</option>
              <option value="JANUARY">Junho</option>
              <option value="JANUARY">Julho</option>
              <option value="JANUARY">Agosto</option>
              <option value="JANUARY">Setembro</option>
              <option value="JANUARY">Outubro</option>
              <option value="JANUARY">Novembro</option>
              <option value="JANUARY">Dezembro</option>
            </select>
          </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 6 6">
          <gp-simple-input label="Horário">
            <select class="form-control" name="hour" [(ngModel)]="hour">
              <option value="00:00">00:00</option>
              <option value="01:00">01:00</option>
              <option value="02:00">02:00</option>
              <option value="03:00">03:00</option>
              <option value="04:00">04:00</option>
              <option value="05:00">05:00</option>
              <option value="06:00">06:00</option>
              <option value="07:00">07:00</option>
              <option value="08:00">08:00</option>
              <option value="09:00">09:00</option>
              <option value="10:00">10:00</option>
              <option value="11:00">11:00</option>
              <option value="12:00">12:00</option>
              <option value="13:00">13:00</option>
              <option value="14:00">14:00</option>
              <option value="15:00">15:00</option>
              <option value="16:00">16:00</option>
              <option value="17:00">17:00</option>
              <option value="18:00">18:00</option>
              <option value="19:00">19:00</option>
              <option value="20:00">20:00</option>
              <option value="21:00">21:00</option>
              <option value="22:00">22:00</option>
              <option value="23:00">23:00</option>
            </select>
          </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 12 12">
          <gp-spinner-button
            type="button"
            [pink]="true"
            (click)="addToGridAndSave()"
            text="Adicionar"
            size="lg"
            pull="right"
            icon=""
          >
          </gp-spinner-button>
        </gp-form-col>
      </gp-form-row>

      <gp-form-row *ngIf="communicationRule.periodity === 'SPECIFIC_DATE'">
        <gp-form-col cols="12 3 3">
          <gp-simple-input label="Disparar no dia">
            <select class="form-control" name="day" [(ngModel)]="day">
              <option *ngFor="let day of days" [value]="day">{{ day }}</option>
            </select>
          </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 3 3">
          <gp-simple-input label="No mês">
            <select class="form-control" name="day" [(ngModel)]="communicationRule.month">
              <option value="JANUARY">Janeiro</option>
              <option value="JANUARY">Fevereiro</option>
              <option value="JANUARY">Março</option>
              <option value="JANUARY">Abril</option>
              <option value="JANUARY">Maio</option>
              <option value="JANUARY">Junho</option>
              <option value="JANUARY">Julho</option>
              <option value="JANUARY">Agosto</option>
              <option value="JANUARY">Setembro</option>
              <option value="JANUARY">Outubro</option>
              <option value="JANUARY">Novembro</option>
              <option value="JANUARY">Dezembro</option>
            </select>
          </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 3 3">
          <gp-simple-input label="Do ano">
            <select class="form-control" name="day" [(ngModel)]="communicationRule.year">
              <option value="JANUARY">2020</option>
              <option value="JANUARY">2021</option>
              <option value="JANUARY">2022</option>
              <option value="JANUARY">2023</option>
              <option value="JANUARY">2024</option>
              <option value="JANUARY">2025</option>
            </select>
          </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 3 3">
          <gp-simple-input label="Horário">
            <select class="form-control" name="hour" [(ngModel)]="hour">
              <option value="00:00">00:00</option>
              <option value="01:00">01:00</option>
              <option value="02:00">02:00</option>
              <option value="03:00">03:00</option>
              <option value="04:00">04:00</option>
              <option value="05:00">05:00</option>
              <option value="06:00">06:00</option>
              <option value="07:00">07:00</option>
              <option value="08:00">08:00</option>
              <option value="09:00">09:00</option>
              <option value="10:00">10:00</option>
              <option value="11:00">11:00</option>
              <option value="12:00">12:00</option>
              <option value="13:00">13:00</option>
              <option value="14:00">14:00</option>
              <option value="15:00">15:00</option>
              <option value="16:00">16:00</option>
              <option value="17:00">17:00</option>
              <option value="18:00">18:00</option>
              <option value="19:00">19:00</option>
              <option value="20:00">20:00</option>
              <option value="21:00">21:00</option>
              <option value="22:00">22:00</option>
              <option value="23:00">23:00</option>
            </select>
          </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 12 12">
          <gp-spinner-button
            type="button"
            [pink]="true"
            (click)="addToGridAndSave()"
            text="Adicionar"
            size="lg"
            pull="right"
            icon=""
          >
          </gp-spinner-button>
        </gp-form-col>
      </gp-form-row>

      <gp-form-row>
        <gp-form-col cols="12 12 12">
          <table class="table table-striped table-hover">
            <thead>
              <tr>
                <th>Dia da semana</th>
                <th>Hora</th>
                <th>Processamento</th>
                <th>Ativo</th>
                <th width="130px">Ações</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let item of gridProcessingDates">
                <td>{{ item.day }}</td>
                <td>{{ item.hour }}</td>
                <td>{{ item.status }}</td>
                <td>
                  <div>
                    <gp-switch
                      name="active"
                      [(ngModel)]="item.active"
                      [ngModelOptions]="{ standalone: true }"
                      (onchange)="updateActiveProcessingDate(item)"
                    ></gp-switch>
                  </div>
                </td>
                <td>
                  <button type="button" title="Detalhes" class="btn btn-default btn-sm" (click)="showDetails(item.id)">
                    <i title="Editar" class="fa fa-pencil-square-o grid-edit"></i>
                  </button>
                </td>
              </tr>
              <tr *ngIf="!gridProcessingDates || !gridProcessingDates.length">
                <td [attr.colspan]="3">Não há processamentos.</td>
              </tr>
            </tbody>
          </table>
        </gp-form-col>
      </gp-form-row>
    </gp-card>

    <gp-card title="Publico Alvo">
      <gp-form-row>
        <gp-form-col cols="12 4 4">
          <gp-simple-input label="Selecione" [required]="true">
            <gp-select (change)="handleTargetAudience($event)" name="targerAudience" [items]="targerAudiences" 
              [allowClear]="false" [(ngModel)]="targetAudience">
            </gp-select>
          </gp-simple-input>
        </gp-form-col>
      </gp-form-row>

      <gp-form-row *ngIf="isTargetAudienceExternalParticipants">
        <gp-form-col cols="12 12 12">
          <p>
            1. O formato do arquivo deve ser <u>[.csv]</u>, com as colunas separadas por [;].
          </p>
          <p>
            2. O cabeçalho deve ser formado por: nome e email, como está sendo feito na imagem de exemplo.
          </p>
          <ul>
            <li><strong>Nome:</strong> nome do destinatário</li>
            <li><strong>Email:</strong> e-mail do destinatário</li>
          </ul>
        </gp-form-col>
      </gp-form-row>
  
      <gp-form-row *ngIf="isTargetAudienceExternalParticipants">
        <gp-form-col cols="12 12 12">
          <img src="/assets/img/imports/modelo_planilha_importacao_emails_regua_relacionamento.png" style="width: 400px;" />
          <br /><br />
        </gp-form-col>
      </gp-form-row>
  

      <gp-form-row *ngIf="isTargetAudienceExternalParticipants">
        <gp-form-col cols="12 12 12">
          <gp-fileupload-default-folder
            name="emailExternalParticipants"
            [csv]="true"
            [campaignId]="campaignId"
            (uploaded)="setEmailFileUrl($event.result)"
            (error)="onUploadError($event)"
            (uploaded)="onUploadComplete($event)"
          >
          </gp-fileupload-default-folder>
        </gp-form-col>
      </gp-form-row>

      <gp-form-row *ngIf="!isTargetAudienceExternalParticipants">
        <campaign-participants-group-selector
          cols="12 4"
          [(ngModel)]="communicationRule.participantsGroups"
          [multiple]="true"
          [name]="participantsGroups"
        ></campaign-participants-group-selector>
      </gp-form-row>
    </gp-card>

    <gp-card title="E-mail">
      <gp-form-row>
        <gp-form-col cols="12 6 6">
          <gp-simple-input label="Assunto">
            <input type="text" class="form-control" name="emailsubject" [(ngModel)]="communicationRule.email.subject" />
          </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 6 6">
          <label>Ativo</label>
          <div>
            <gp-switch name="emailactive" [(ngModel)]="communicationRule.email.active"></gp-switch>
          </div>
        </gp-form-col>
      </gp-form-row>

      <gp-form-row>
        <div class="col-xs-12">
          <div class="top-m1 text-help">
            <span>Textos dinâmicos disponíveis:</span>
            <ul>
              <li><strong>@NOMEPARTICIPANTE</strong>: nome do participante</li>
              <li *ngIf="!isTargetAudienceExternalParticipants"><strong>@DOCUMENTOPARTICIPANTE</strong>: CPF/CNPJ do participante</li>
            </ul>
          </div>
        </div>
      </gp-form-row>

      <gp-form-row>
        <gp-form-col cols="12 12 12">
          <label>Template de envio</label>
          <gp-editor id="emailtemplate" name="emailtemplate" [(ngModel)]="communicationRule.email.template">
          </gp-editor>
        </gp-form-col>
      </gp-form-row>
    </gp-card>

    <gp-card title="SMS" *ngIf="!isTargetAudienceExternalParticipants">
      <gp-form-row>
        <gp-form-col cols="12 6 6">
          <label>Ativo</label>
          <div>
            <gp-switch name="smsactive" [(ngModel)]="communicationRule.sms.active"></gp-switch>
          </div>
        </gp-form-col>
      </gp-form-row>
      <gp-form-row>
        <gp-form-col cols="12 12 12">
          <gp-simple-input label="Assunto">
            <input type="text" class="form-control" name="smstemplate" [(ngModel)]="communicationRule.sms.template" />
          </gp-simple-input>
        </gp-form-col>
      </gp-form-row>
    </gp-card>

    <gp-card title="WhatsApp" *ngIf="!isTargetAudienceExternalParticipants">
      <gp-form-row>
        <gp-form-col cols="12 6 6">
          <label>Ativo</label>
          <div>
            <gp-switch name="whatsappActive" [(ngModel)]="communicationRule.whatsApp.active"></gp-switch>
          </div>
        </gp-form-col>
        <gp-form-col cols="12 6 6">
          <label>Reutilizar template</label>
          <div>
            <gp-switch name="reusePartnerTemplate" [(ngModel)]="communicationRule.whatsApp.reusePartnerTemplate">
            </gp-switch>
          </div>
        </gp-form-col>
      </gp-form-row>
      <gp-form-row>
        <gp-form-col cols="12 3 3" *ngIf="communicationRule.whatsApp.active && canInformWhasAppSenderNumber">
          <gp-simple-input label="Número do remetente" tooltip="Número que receberá as respostas das mensagens.">
            <gp-input-mask
              name="senderNumber"
              mask="(00) 00000-0000"
              [(ngModel)]="communicationRule.whatsApp.senderNumber"
            ></gp-input-mask>
          </gp-simple-input>
        </gp-form-col>

        <gp-form-col cols="12 6 6" *ngIf="communicationRule.whatsApp.reusePartnerTemplate">
          <gp-simple-input label="Id do Parceiro">
            <input
              type="text"
              class="form-control"
              name="templatePartnerId"
              [(ngModel)]="communicationRule.whatsApp.templatePartnerId"
            />
          </gp-simple-input>
        </gp-form-col>
      </gp-form-row>

      <hr />

      <gp-form-row>
        <gp-form-col cols="12 4 4">
          <gp-simple-input label="E-mail de notificação da aprovação">
            <input
              type="text"
              class="form-control"
              name="notificationTemplateApprovalEmail"
              [(ngModel)]="communicationRule.whatsApp.notificationTemplateApprovalEmail"
            />
          </gp-simple-input>
        </gp-form-col>

        <gp-form-col cols="12 4 4" *ngIf="communicationRule.whatsApp.templateReviewStatus">
          <gp-simple-input label=" Status da análise do template">
            <input
              readonly
              type="text"
              class="form-control"
              name="mappedtemplateReviewStatus"
              [ngModel]="communicationRule.whatsApp.mappedtemplateReviewStatus"
            />
          </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 4 4" *ngIf="!communicationRule.whatsApp.reusePartnerTemplate">
          <gp-spinner-button
            type="button"
            *ngIf="templateNotSendToApproval"
            [inline]="true"
            (click)="sendWhatsAppTemplateToApproval()"
            buttonClass="bg-primary-dark"
            text="Enviar template para Aprovação"
            pull="right"
            icon="send"
            loadingText="Enviando..."
            [loading]="sendingApproval"
          >
          </gp-spinner-button>
        </gp-form-col>
      </gp-form-row>

      <div *ngIf="!communicationRule.whatsApp.reusePartnerTemplate">
        <hr />
        <div>
          <gp-form-row>
            <div class="col-xs-12">
              <div class="top-m1 text-help">
                <span>Textos dinâmicos disponíveis:</span>
                <ul>
                  <li><strong>@NOMEPARTICIPANTE</strong>: nome do participante</li>
                  <li><strong>@DOCUMENTOPARTICIPANTE</strong>: CPF/CNPJ do participante</li>
                </ul>
              </div>
            </div>
          </gp-form-row>

          <gp-form-row>
            <gp-form-col cols="12 12 12">
              <label>Template da mensagem</label>
              <textarea
                id="whatsAppTemplate"
                class="form-control"
                name="whatsAppTemplate"
                rows="5"
                [(ngModel)]="communicationRule.whatsApp.template"
              >
              </textarea>
            </gp-form-col>
          </gp-form-row>

          <gp-form-row>
            <gp-form-col cols="12 12 12">
              <div class="top-m1 text-help">
                <span>
                  A imagem para envio precisa ser do tipo PNG ou JPEG (extensões <strong>.png</strong>,
                  <strong>.jpg</strong> ou <strong>.jpeg</strong>) e precisa ter um tamanho máximo de 5MB.
                </span>
              </div>
            </gp-form-col>
          </gp-form-row>

          <gp-form-row>
            <gp-form-col cols="12 12 12">
              <gp-fileupload-default-folder
                name="whatsAppImageUrl"
                [onlyImages]="true"
                [campaignId]="campaignId"
                [(ngModel)]="communicationRule.whatsApp.imageUrl"
                (error)="onUploadError($event)"
                (uploaded)="onUploadComplete()"
              >
              </gp-fileupload-default-folder>
            </gp-form-col>
          </gp-form-row>
        </div>
      </div>
    </gp-card>
  </div>

  <gp-card *ngIf="communicationRule.id">
    <gp-spinner-button
      type="submit"
      text="Salvar"
      pull="right"
      [pink]="true"
      icon="send"
      loadingText="Processando"
      [loading]="loading"
    >
    </gp-spinner-button>
  </gp-card>
</form>

<gp-modal title="Detalhes do Processamento" #gpModal width="800px" (onClose)="clearDetails()">
  <gp-form-row *ngIf="hasProcessingDetail">
    <gp-form-col cols="12 12 12">
      <gp-spinner-button
        text="Exportar registros de log"
        icon="download"
        pull="right"
        bootstrapClass="success"
        (click)="exportProcessingErrors()"
      >
      </gp-spinner-button>
    </gp-form-col>
  </gp-form-row>

  <gp-form-row>
    <gp-form-col cols="12 12 12">
      <div class="grid-container grid-container--fit">
        <div class="grid-element">
          <gp-card [last]="true" [noPaddingTop]="true" [noPaddingBottom]="true">
            <div class="balance-card">
              <p class="text-center">
                <i class="fa fa-id-card-o fa-2x text-primary" aria-hidden="true"></i>
              </p>
              <p class="text-center text-bold text-primary element-label">Participantes<br />com e-mails</p>
              <p class="text-center text-bold">{{ processingDetailValues.totalFound | number: '1.0' }}</p>
            </div>
          </gp-card>
        </div>

        <div class="grid-element">
          <gp-card [last]="true" [noPaddingTop]="true" [noPaddingBottom]="true">
            <div class="balance-card">
              <p class="text-center">
                <i class="fa fa-send fa-2x text-primary" aria-hidden="true"></i>
              </p>
              <p class="text-center text-bold text-primary element-label">Total enviado</p>
              <p class="text-center text-bold">{{ processingDetailValues.totalSent | number: '1.0' }}</p>
            </div>
          </gp-card>
        </div>

        <div class="grid-element">
          <gp-card [last]="true" [noPaddingTop]="true" [noPaddingBottom]="true">
            <div class="balance-card">
              <p class="text-center">
                <i class="fa fa-exclamation-triangle fa-2x text-primary" aria-hidden="true"></i>
              </p>
              <p class="text-center text-bold text-primary element-label">Total não enviado</p>
              <p class="text-center text-bold">{{ processingDetailValues.totalNotSent | number: '1.0' }}</p>
            </div>
          </gp-card>
        </div>

        <div class="grid-element">
          <gp-card [last]="true" [noPaddingTop]="true" [noPaddingBottom]="true">
            <div class="balance-card">
              <p class="text-center">
                <i class="fa fa-times-rectangle fa-2x text-primary" aria-hidden="true"></i>
              </p>
              <p class="text-center text-bold text-primary element-label">Total de erros</p>
              <p class="text-center text-bold">{{ processingDetailValues.totalErrors | number: '1.0' }}</p>
            </div>
          </gp-card>
        </div>
      </div>
    </gp-form-col>
  </gp-form-row>

  <gp-form-row>
    <gp-form-col cols="12 12 12">
      <div class="grid-container grid-container--fit">
        <div class="grid-element">
          <gp-card [last]="true" [noPaddingTop]="true" [noPaddingBottom]="true">
            <div class="balance-card">
              <p class="text-center">
                <i class="fa fa-envelope fa-2x text-primary element-label" aria-hidden="true"></i>
              </p>
              <p class="text-center text-bold text-primary element-label">Total entregue</p>
              <p class="text-center text-bold">{{ processingDetailValues.totalDelivered | number: '1.0' }}</p>
            </div>
          </gp-card>
        </div>
        <div class="grid-element">
          <gp-card [last]="true" [noPaddingTop]="true" [noPaddingBottom]="true">
            <div class="balance-card">
              <p class="text-center">
                <i class="fa fa-ban fa-2x text-primary" aria-hidden="true"></i>
              </p>
              <p class="text-center text-bold text-primary element-label">Total não entregue</p>
              <p class="text-center text-bold">{{ processingDetailValues.totalNotDelivered | number: '1.0' }}</p>
            </div>
          </gp-card>
        </div>
        <div class="grid-element">
          <gp-card [last]="true" [noPaddingTop]="true" [noPaddingBottom]="true">
            <div class="balance-card">
              <p class="text-center">
                <i class="fa fa-envelope-open fa-2x text-primary" aria-hidden="true"></i>
              </p>
              <p class="text-center text-bold text-primary element-label">Total lido</p>
              <p class="text-center text-bold">{{ processingDetailValues.totalRead | number: '1.0' }}</p>
            </div>
          </gp-card>
        </div>
        <div class="grid-element">
          <gp-card [last]="true" [noPaddingTop]="true" [noPaddingBottom]="true">
            <div class="balance-card">
              <p class="text-center">
                <i class="fa fa-hand-pointer-o fa-2x text-primary" aria-hidden="true"></i>
              </p>
              <p class="text-center text-bold text-primary element-label">Total clicado</p>
              <p class="text-center text-bold">{{ processingDetailValues.totalClicks | number: '1.0' }}</p>
            </div>
          </gp-card>
        </div>
      </div>
    </gp-form-col>
  </gp-form-row>
</gp-modal>
