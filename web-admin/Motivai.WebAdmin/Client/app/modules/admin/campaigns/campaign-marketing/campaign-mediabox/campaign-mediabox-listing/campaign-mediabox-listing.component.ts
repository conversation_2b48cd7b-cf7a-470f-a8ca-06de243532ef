import { ActivatedRoute } from '@angular/router';
import { Component, ViewChild, OnInit } from '@angular/core';

import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignService } from '../../../campaign.service';

@Component({
  selector: 'campaign-mediabox-listing',
  templateUrl: 'campaign-mediabox-listing.component.html'
})
export class CampaignMediaboxListingComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  campaignId: string;
  mediaBoxes: Array<any> = [];
  loading: boolean = false;
  sending: boolean = false;

  constructor(private _campaignService: CampaignService, private route: ActivatedRoute) {
  }

  ngOnInit(): void {
    this.loading = true;
    if (this.route.parent != null && this.route.parent.parent != null) {
      this.route.parent.parent.params.subscribe((params: any) => {
        this.campaignId = params['id'];
        this.loadMediaBoxes();
      });
    }
  }

  private handleError(err) {
    let errorMessage = (err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao efetuar a operação.');
    this.gpAlert.showError(errorMessage);
    this.loading = false;
  }

  private loadMediaBoxes() {
    this._campaignService.getMediaBoxesByCampaign(this.campaignId)
      .subscribe(
        mediaBoxes => {
          if (mediaBoxes)
            this.mediaBoxes = mediaBoxes;
          else
            this.mediaBoxes = [];
          this.loading = false;
        },
        err => this.handleError(err)
      );
  }

  removeMediaBox(mediaBox) {
    if (!confirm(`Deseja remover o media box '${mediaBox.name}'?`)) return;
    if (this.sending) return;
    this.sending = true;
    this._campaignService.removeMediaBox(this.campaignId, mediaBox.id)
      .subscribe(
        mediaBoxes => {
          this.sending = false;
          if (mediaBoxes) {
            this.loadMediaBoxes();
            this.gpAlert.showSuccess('Media box removido com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível remover o media box, por favor, tente novamente.');
          }
        },
        err => this.handleError(err)
      );
  }
}
