import { Component, ViewChild, OnInit } from '@angular/core';
import { DatePipe } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';

import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { SpecialShopService } from '../special-shop.service';

@Component({
  selector: 'campaign-specialshop-listing',
  templateUrl: 'campaign-specialshop-listing.component.html'
})
export class CampaignSpecialShopListingComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  campaignId: string;
  specialShops: Array<any> = [];
  loading: boolean = false;
  sending: boolean = false;

  constructor(private _campaignService: SpecialShopService, private _datePipe: DatePipe,
    private route: ActivatedRoute, private router: Router) {
  }

  ngOnInit(): void {
    this.loading = true;
    if (this.route.parent != null && this.route.parent.parent != null) {
      this.route.parent.parent.params.subscribe((params: any) => {
        this.campaignId = params['id'];
        this.loadSpecialShops();
      });
    }
  }

  private handleError(err) {
    let errorMessage = (err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao efetuar a operação.');
    this.gpAlert.showError(errorMessage);
    this.loading = false;
  }

  private formatDate(date){
    return this._datePipe.transform(date, 'dd/MM/yyyy');
  }

  private loadSpecialShops() {
    this._campaignService.getSpecialShopsByCampaign(this.campaignId)
      .subscribe(
        specialShops => {
          if (specialShops) {
            this.specialShops = specialShops;
            this.specialShops.forEach(s => {
              s.period = `${this.formatDate(s.startDate)} à ${this.formatDate(s.endDate)}`;
            });
          } else {
            this.specialShops = [];
          }
          this.loading = false;
        },
        err => this.handleError(err)
      );
  }

  prepareEdit(specialStore) {
    this.router.navigate(['/campanha', this.campaignId, 'marketing', 'lojas', specialStore.id]);
  }

  removeSpecialShop(specialStore) {
    if (!confirm(`Deseja remover a loja especial '${specialStore.name}'?`)) return;
    if (this.sending) return;
    this.sending = true;
    this._campaignService.removeSpecialShop(this.campaignId, specialStore.id)
      .subscribe(
        result => {
          this.sending = false;
          if (result) {
            this.loadSpecialShops();
            this.gpAlert.showSuccess('Loja especial removida com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível remover a loja especial, por favor, tente novamente.');
          }
        },
        err => this.handleError(err)
      );
  }
}
