import { ActivatedRoute } from '@angular/router';
import { Component, OnInit, ViewChild, EventEmitter, Output } from '@angular/core';
import { CampaignCommunicationRulesService } from '../campaign-communication-rules.service';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignStore } from '../../../campaign.store';
import { Subscription } from 'rxjs';


@Component({
  selector: 'campaign-communication-rules-list',
  template: `
    <gp-card [first]="true">
      <gp-form-row>
        <gp-form-col cols="12">
          <gp-grid
            name="communicationRules"
            [loading]="loading"
            [rows]="communicationRules"
            [columns]="['Nome']"
            [fields]="['name']"
            [showActive]="true"
            [showPagination]="false"
            [showEdit]="true"
            (onEdit)="editCommunicationRule($event)"
            [showDelete]="false"
          >
          </gp-grid>
        </gp-form-col>
      </gp-form-row>
    </gp-card>
    <gp-alert #alert [overlay]="true"></gp-alert>
  `
})
export class CampaignCommunicationRulesListComponent implements OnInit {
  @ViewChild('alert') alert: GpAlertComponent;
  @Output('onEdit') onEdit: EventEmitter<any> = new EventEmitter();

  communicationRules: any = [];
  campaignId: string = '';
  loading: boolean = false;
  private campaignStore: Subscription;

  constructor(
    private route: ActivatedRoute,
    private _campaignStore: CampaignStore,
    private communicationRulesService: CampaignCommunicationRulesService
  ) {}

  ngOnInit() {
    this.campaignStore = this._campaignStore.asObservable.subscribe(_ => {
      this.campaignId = this._campaignStore.id;
    });
    this.findCommunicationRules();
  }

  findCommunicationRules() {
    this.loading = true;
    this.communicationRulesService
      .getByCampaign(this._campaignStore.id)
      .subscribe(
        communicationRules => {
          if (communicationRules != null) {
            this.communicationRules = communicationRules;
          }
          this.loading = false;
        },
        err => {
          this.loading = false;
          this.alert.handleAndShowError(err);
        }
      );
  }

  editCommunicationRule($event: any) {
    if ($event.id) this.onEdit.emit($event.id);
  }
}
