import { CampaignEmailErrorHandlerComponent } from './campaign-emails-errors-handling/campaign-email-error-handler/campaign-email-error-handler.component';
import { CampaignEmailErrorListingComponent } from './campaign-emails-errors-handling/campaign-emails-errors-listing/campaign-email-error-listing.component';
import { Component, OnInit, ViewChild } from '@angular/core';
import { TabsetComponent } from 'ng2-bootstrap';

@Component({
    selector: 'campaign-emails-errors-view',
    templateUrl: './campaign-emails-errors-view.component.html'
})
export class CampaignEmailsErrorsViewComponent implements OnInit {
    @ViewChild('tabs') tabs: TabsetComponent;
    @ViewChild('list') list: CampaignEmailErrorListingComponent; 
    @ViewChild('details') details: CampaignEmailErrorHandlerComponent; 
    constructor() { }

    ngOnInit(): void { }

    onDetails(event) {
        this.details.error = event;
        this.tabs.tabs[1].active = true;
    }


    onSelect() {
        this.list.find();
        this.clear();

    }
    
    clear() {
        this.details.clear();
    }
}
