import { Component, OnInit, ViewChild } from '@angular/core';
import { Subscription } from 'rxjs';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignStore } from '../../../campaign.store';
import { CampaignPagesCustomScriptsService } from '../services/campaign-pages-custom-scripts.service';

@Component({
  selector: 'campaign-script-edit',
  templateUrl: 'campaign-pages-custom-script-edit.component.html',
})
export class CampaignPagesCustomScriptEditComponent implements OnInit { 
  @ViewChild('alert') alert: GpAlertComponent;

  private _campaign$: Subscription; 
  campaignId: string = ''; 

  constructor(private readonly campaignScriptService: CampaignPagesCustomScriptsService,
    private _campaignStore: CampaignStore) {}

  ngOnInit(): void {
    this._campaign$ = this._campaignStore.asObservable
            .subscribe(campaignId => {
                this.campaignId = campaignId;
            });
  }

  campaignScript: any = {};
  loading: boolean = false; 

  clear() { 
    this.campaignScript = {}; 
  }

  showDetails(scriptId: string){ 
    this.loading = true; 
    this.campaignScriptService
      .getCustomScriptById(this.campaignId, scriptId)
      .subscribe(script => { 
        if (script) {
          this.loading = false; 
          this.campaignScript = script; 
        }
      }, error => {
        this.loading = false; 
        this.alert.showError(error); 
      })
  }
  
  createCampaignScript() {
    this.loading = true; 
    this.campaignScriptService
      .createCustomScript(this.campaignId, this.campaignScript)
      .subscribe(_ => {
        this.loading = false; 
        this.alert.showSuccess(`Script salvo com sucesso.`); 
        this.clear(); 
        }, error => { 
          this.loading = false; 
          this.alert.showError(error)
        }
    ); 
  }
}