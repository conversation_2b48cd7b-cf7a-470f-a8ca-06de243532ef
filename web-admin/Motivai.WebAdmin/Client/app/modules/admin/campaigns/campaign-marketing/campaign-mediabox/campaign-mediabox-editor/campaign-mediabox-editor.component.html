<gp-form-validate [formGroup]="formGroup" [validationMessages]="messages" (onSubmit)="saveMediaBox($event)">
  <gp-alert [overlay]="true" #gpAlert></gp-alert>

  <gp-card>
    <div class="row col-xs-12 bottom-m1">
      <h4>Dados do Media Box</h4>
    </div>

    <div *ngIf="!loading">
      <gp-form-row>
        <gp-input-validate cols="12 6" label="Nome" formControlName="name"></gp-input-validate>

        <gp-select-validate cols="12 6" label="Posição" placeholder="Posição" formControlName="position">
          <option value="" disabled>Selecione uma posição</option>
          <option *ngFor="let i of [1, 2, 3, 4, 5]" [value]="i">{{ i }}</option>
        </gp-select-validate>
      </gp-form-row>

      <gp-form-row>
        <gp-select-validate
          cols="12 6"
          label="Tipo Conteúdo"
          placeholder="Tipo conteúdo"
          [(ngModel)]="contentType"
          [ngModelOptions]="{ standalone: true }"
        >
          <option value="BANNER">Banner</option>
          <option value="PRODUCT">Produto</option>
        </gp-select-validate>

        <gp-form-col cols="12 3 3">
          <label>Ativo:</label>
          <div>
            <gp-switch formControlName="active"></gp-switch>
          </div>
        </gp-form-col>

        <gp-form-col cols="12 3 3">
          <label>Abrir em uma nova aba</label>
          <div>
            <gp-switch formControlName="openInNewTab"></gp-switch>
          </div>
        </gp-form-col>
      </gp-form-row>

      <gp-form-row *ngIf="isContentTypeBanner">
        <gp-input-validate cols="12 12" label="Link" formControlName="link" [required]="false"> </gp-input-validate>
      </gp-form-row>
    </div>
    <spinner [show]="loading || uploading" [overlay]="true"></spinner>
  </gp-card>

  <div *ngIf="isContentTypeBanner">
    <gp-card>
      <gp-form-row>
        <gp-form-col cols="12 12 12">
          <label>Selecione a imagem do banner de tamanho grande (Largura ideal: 1680px)</label>
          <gp-fileupload
            name="banner"
            label="Selecionar imagem"
            [images]="true"
            (oncomplete)="onComplete($event)"
            #bannerLarge
          >
          </gp-fileupload>
        </gp-form-col>
        <div class="row bottom-m1" *ngIf="bannerImageUrl">
          <div grid="12 12">
            <img
              class="banner"
              defaultImage="/assets/img/img-loader.gif"
              [lazyLoad]="bannerImageUrl"
              title="Banner"
              style="width:100%;"
            />
          </div>
        </div>
      </gp-form-row>
    </gp-card>

    <gp-form-row>
      <gp-form-col cols="12 6 6" [inputGroup]="false">
        <gp-card>
          <label>Selecione a imagem do banner de tamanho médio (Largura ideal: 1280px)</label>
          <gp-fileupload
            name="bannerTablet"
            label="Selecionar imagem"
            [images]="true"
            (oncomplete)="onCompleteMediumImage($event)"
            #bannerTablet
          >
          </gp-fileupload>
          <div class="row" *ngIf="bannerTabletImageUrl">
            <div grid="12 12">
              <img
                class="banner"
                defaultImage="/assets/img/img-loader.gif"
                [lazyLoad]="bannerTabletImageUrl"
                title="Banner Tablet"
                style="width:100%;"
              />
            </div>
          </div>
        </gp-card>
      </gp-form-col>
      <gp-form-col cols="12 6 6" [inputGroup]="false">
        <gp-card>
          <label
            >Selecione a imagem do banner de tamanho pequeno ({{ smallImageResolutionLabel }} ideal:
            {{ smallImageIdealResolution }}px)</label
          >

          <gp-fileupload name="bannerMobile" label="Selecionar imagem" [images]="true" #bannerMobile> </gp-fileupload>
          <div class="row" *ngIf="bannerMobileImageUrl">
            <div grid="12 12">
              <img
                class="banner"
                defaultImage="/assets/img/img-loader.gif"
                [lazyLoad]="bannerMobileImageUrl"
                title="Banner Mobile"
                style="width:100%;"
              />
            </div>
          </div>
        </gp-card>
      </gp-form-col>
    </gp-form-row>
  </div>

  <div *ngIf="isContentTypeProduct">
    <gp-card title="Produto para Destaque">
      <campaign-partner-sku-selector
        [campaignId]="campaignId"
        [(ngModel)]="selectedProduct"
        [ngModelOptions]="{ standalone: true }"
      >
      </campaign-partner-sku-selector>
    </gp-card>
  </div>

  <gp-card title="Regras de Exibição" *ngIf="!loading">
    <gp-form-row>
      <div class="col-xs-12">
        <h4>Período</h4>
      </div>

      <gp-datepicker cols="12 6 4 4" label="Data de Início" formControlName="startDate"> </gp-datepicker>

      <gp-datepicker cols="12 6 4 4" label="Data de Término" formControlName="endDate"> </gp-datepicker>
    </gp-form-row>

    <div *ngIf="canViewTargetAudiences">
      <gp-form-row>
        <campaign-participants-group-selector
          cols="12 4"
          [(ngModel)]="participantsGroups"
          [multiple]="true"
          [name]="participantsGroups"
          [ngModelOptions]="{ standalone: true }"
        ></campaign-participants-group-selector>
      </gp-form-row>
      <!-- <div class="row">
				<div class="col-xs-12">
					<h4>Públicos Alvos</h4>
				</div>
			</div>
			<div class="row">
				<gp-select-validate cols="12 6 8 8" label="Público Alvo" placeholder="Selecione um público alvo"
					[searchable]="true" [items]="availableTargetsAudiences" formControlName="targetAudience">
				</gp-select-validate>
				<div class="top-p2" grid="12 4">
					<gp-spinner-button text="Adicionar" [pink]="true" icon="plus" (click)="addTargetAudience()">
					</gp-spinner-button>
				</div>
			</div>
			<div class="row">
				<div class="col-xs-12">
					<gp-grid name="targetsGrid" [rows]="targetAudiences" [columns]="['Descrição']"
						[fields]="['text']" [loading]="false" [showActive]="false" [showPagination]="false"
						[showEdit]="false" [showDelete]="true" (onDelete)="removeTargetAudience($event)">
					</gp-grid>
				</div>
			</div> -->
    </div>

    <hr />
    <gp-form-row>
      <div class="col-xs-12">
        <h4>Onde exibir</h4>
      </div>
      <gp-input-checkbox
        grid="12 6 2 2"
        text="Catálogo"
        formControlName="visibleOnCatalog"
        *ngIf="campaignStore.isCatalog"
      ></gp-input-checkbox>

      <gp-input-checkbox
        grid="12 6 2 2"
        text="Site Campanha"
        formControlName="visibleOnSite"
        *ngIf="showCampaignSitePagesSelection"
      ></gp-input-checkbox>

      <!-- App - Módulo Cashback -->
      <gp-input-checkbox
        grid="12 6"
        text="App - Modulo Cashback"
        formControlName="visibleOnAppCashback"
        *ngIf="showCampaignSitePagesSelection"
      ></gp-input-checkbox>
    </gp-form-row>

    <div *ngIf="campaignStore.isCatalog">
      <hr />
      <div class="row">
        <div class="col-xs-12">
          <h4>Departamentos do catálogo</h4>
        </div>
      </div>
      <div class="col-xs-12">
        <gp-input-manycheckbox [items]="departments" formControlName="departments"></gp-input-manycheckbox>
      </div>
    </div>

    <div *ngIf="showCampaignSitePagesSelection">
      <hr />
      <div class="row">
        <div class="col-xs-12">
          <h4>Páginas do site campanha</h4>
        </div>
        <div class="col-xs-12">
          <gp-input-manycheckbox [items]="pages" formControlName="sitePages"></gp-input-manycheckbox>
        </div>
      </div>
    </div>
  </gp-card>

  <gp-card>
    <div class="row">
      <div class="col-xs-12">
        <button
          type="button"
          class="btn btn-default"
          [routerLink]="['/campanha', campaignId, 'marketing', 'mediaboxes']"
        >
          Voltar
        </button>

        <gp-spinner-button
          text="Novo"
          bootstrapClass="default"
          icon="plus"
          (click)="newMediaBox()"
          [disabled]="disableButtons"
        ></gp-spinner-button>

				<gp-spinner-button text="Novo" [actionSecondary]="true" (click)="newMediaBox()"
					[disabled]="disableButtons">
				</gp-spinner-button>

				<gp-spinner-button type="submit" text="Salvar" loadingText="Salvando" [actionPrimary]="true"
					[disabled]="disableButtons" [loading]="sending">
				</gp-spinner-button>

				<gp-spinner-button text="Atualizar Imagem" loadingText="Enviando" bootstrapClass="primary"
					[pink]="true" icon="upload" [disabled]="disableButtons || !isValid()" [loading]="uploading"
					(click)="sendImage()" *ngIf="isEdition">
				</gp-spinner-button>
			</div>
		</div>
	</gp-card>
</gp-form-validate>
