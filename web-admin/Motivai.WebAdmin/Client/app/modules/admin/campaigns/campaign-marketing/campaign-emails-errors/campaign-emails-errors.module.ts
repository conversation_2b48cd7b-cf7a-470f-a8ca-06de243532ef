import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { SelectModule } from 'ng2-select';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../../../../../shared/shared.module';
import { CampaignEmailsErrorsViewComponent } from './campaign-emails-errors-view.component';
import { CampaignEmailErrorListingComponent } from './campaign-emails-errors-handling/campaign-emails-errors-listing/campaign-email-error-listing.component';
import { CampaignEmailErrorHandlerComponent } from './campaign-emails-errors-handling/campaign-email-error-handler/campaign-email-error-handler.component';
import { CampaignEmailErrorTemplateModalComponent } from './campaign-emails-errors-modal/campaign-email-error-template-modal/campaign-email-error-template-modal.component';
import { CampaignEmailsErrorsService } from './campaign-emails-errors.service';
import { CampaignEmailErrorCancelationModalComponent } from './campaign-emails-errors-modal/campaign-email-error-cancelation-modal/campaign-email-error-cancelation-modal.component';
import { CampaignParticipantEmailModalComponent } from './campaign-emails-errors-modal/campaign-participant-email-modal/campaign-participant-email-modal.component';


@NgModule({
    declarations: [
        CampaignEmailsErrorsViewComponent,
        CampaignEmailErrorListingComponent,
        CampaignEmailErrorHandlerComponent,
        CampaignEmailErrorTemplateModalComponent,
        CampaignParticipantEmailModalComponent,
        CampaignEmailErrorCancelationModalComponent
    ],
    imports: [
        CommonModule,
        FormsModule,
        SelectModule,
        SharedModule.forRoot(),
        RouterModule.forChild([
            { path: '', component: CampaignEmailsErrorsViewComponent, pathMatch: 'full' }
        ])
    ],
    exports: [],
    providers: [CampaignEmailsErrorsService],
})
export class CampaignEmailErrorModule { }
