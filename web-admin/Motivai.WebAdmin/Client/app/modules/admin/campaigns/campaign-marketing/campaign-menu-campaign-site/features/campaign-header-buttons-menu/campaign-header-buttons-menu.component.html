<gp-card title="Configurações de menu do site campanha">
    <div *ngFor="let m of menuOptions; let i = index">
        <gp-form-row>
            <gp-form-col cols="12 2 2">
                <gp-simple-input label="Descriçao do botão">
                    <input type="text" class="form-control" name="description" [maxlength]="descriptionMaxLength"
                        placeholder="Descrição do Botão" [(ngModel)]="m.description">
                </gp-simple-input>
                <span>Caracteres Restantes: {{ descriptionMaxLength - (m.description?.length || 0) }}</span>
            </gp-form-col>
            <gp-form-col cols="12 4 4">
                <gp-simple-input label="Url do botão">
                    <input type="text" class="form-control" name="url" placeholder="Url para link do Botão"
                        [(ngModel)]="m.url">
                </gp-simple-input>
            </gp-form-col>
            <gp-form-col cols="12 2 2">
                <label>Posição do botão</label>
                <select class="form-control" [(ngModel)]="m.position">
                    <option value="">Selecione....</option>
                    <option value="1">1</option>
                    <option value="2">2</option>
                    <option value="3">3</option>
                    <option value="4">4</option>
                </select>
            </gp-form-col>
            <gp-form-col cols="12 2 2">
                <label>Estado do usuário</label>
                <select class="form-control" [(ngModel)]="m.userState">
                    <option value="">Selecione....</option>
                    <option value="LOGGED">Logado</option>
                    <option value="NOT_LOGGED">Deslogado</option>
                    <option value="BOTH">Ambos</option>
                </select>
            </gp-form-col>
            <gp-form-col cols="12 2 2">
                <label>Ações</label>
                <br />
                <gp-spinner-button marginRight="5px" [pink]="true" icon="plus" [disabled]="disableButton" (click)="newMenuOption(i)"></gp-spinner-button>
                <gp-spinner-button bootstrapClass="default" icon="times" (click)="deleteMenuOption(i)"></gp-spinner-button>
            </gp-form-col>
        </gp-form-row>
    </div>
    <gp-form-row>
        <gp-form-col cols="12 3 3">
            <gp-spinner-button text="Salvar" pull="ritgh" marginRight="5px" [pink]="true" (click)="save()" *ngIf="canSaveCampaignSiteHeaderLinks"></gp-spinner-button>
        </gp-form-col>
    </gp-form-row>
    <spinner [show]="loading" [overlay]="true"></spinner>
    <gp-alert #alert [overlay]="true"></gp-alert>
</gp-card>
