import { Component, OnInit, ViewChild } from '@angular/core';
import { Subscription } from 'rxjs';

import { AuthStore } from '../../../../../../../../core/auth/auth.store';
import { GpAlertComponent } from '../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { FormatHelper } from '../../../../../../../../shared/formatters/format-helper';
import { RxjsHelpers } from '../../../../../../../../shared/helpers/rxjs-helpers';
import { CampaignStore } from '../../../../../campaign.store';
import { CampaignEmailsService } from '../../../services/campaign-emails.service';
import { getNameDescription } from '../../../models/campaign-emails-notification-type';

@Component({
    selector: 'campaign-emails-notification-logs-edit',
    templateUrl: './campaign-emails-notification-logs-edit.component.html'
})
export class CampaignEmailsNotificationsLogsEditComponent implements OnInit {
    @ViewChild('alert') alert: GpAlertComponent;

    private _campaign$: Subscription;

    emailLog: any = {
        recipientTrackings: []
    };

    campaignId: string = '';

    loading: boolean = false;

    constructor(private _authStore: AuthStore, private _campaignStore: CampaignStore, private _service: CampaignEmailsService) { }

    ngOnInit(): void {
      this._campaign$ = this._campaignStore.asObservable
        .subscribe(id => this.campaignId = id);
    }

    ngOnDestroy(): void {
      RxjsHelpers.unsubscribe(this._campaign$);
    }

    get canViewEmailContent() {
        return this._authStore.role.PERMISSION_CAMPAIGNS_MARKETING_NOTIFICATION_LOGS_CONTENT;
    }

    get canResendNotification() {
        return this._authStore.role.PERMISSION_CAMPAIGNS_MARKETING_NOTIFICATION_LOGS_RESEND;
    }

    get hasEmailContent() {
        return this.emailLog && this.emailLog.content && this.emailLog.content.length > 0;
    }

    get hasRecipientTrackings() {
        return this.emailLog.recipientTrackings && this.emailLog.recipientTrackings.length > 0;
    }

    resendNotification(notification) {
        const notificationBody = {
            logId: this.emailLog.id,
            type: this.emailLog.type,
            campaignId: this.campaignId,
            emailsTo: [notification.email],
            terms: this.emailLog.templateParameters
        };
        this.resendNotificationBody(notificationBody);
    }

    resendNotificationLog() {
        const notificationBody = {
            logId: this.emailLog.id,
            type: this.emailLog.type,
            campaignId: this.campaignId,
            emailsTo: [this.emailLog.emailsTo],
            terms: this.emailLog.templateParameters
        };
        this.resendNotificationBody(notificationBody);
    }

    private resendNotificationBody(notificationBody: any) {
        this.loading = true;
        this._service.resendNotification(this.campaignId, notificationBody)
            .subscribe(
                response => {
                    if (response) {
                        this.alert.showSuccess('Notificação reenviada.');
                    } else {
                        this.alert.showWarning('Não foi possível enviar a notificação, por favor, tente novamente.');
                    }
                    this.loading = false;
                },
                err => {
                    this.loading = false;
                    this.alert.showError(err);
                }
            );
    }

    findCampaignNotificationEmailLogById(logId: string) {
        this.clear();
        this.loading = true;
        this._service.getCampaignNotificationsEmailsLDetailsById(this.campaignId, logId)
            .subscribe(
                response => {
                    if(response)
                        this.emailLog = this.handleEmailLog(response);
                    this.loading = false;
                },
                err => {
                    this.loading = false;
                    this.alert.showError(err);
                }
            );
    }

    handleEmailLog(response) {
        if (response.recipientTrackings) {
            response.recipientTrackings.forEach(rt => {
              rt.formattedSentDate = FormatHelper.formatDateWithTimezone(rt.sentDate);
              if (rt.events) {
                rt.events.forEach(e => {
                  e.formattedName = getNameDescription(e.name);
                  e.formattedReceivedDate = FormatHelper.formatDateWithTimezone(e.receivedDate);
                });
              }
            });
        }
        return response;
    }

    clear() {
        this.emailLog = {
            recipientTrackings: []
        };
    }
}
