<gp-card *ngIf="showMenublock" [noPaddingTop]="true" [noPaddingBottom]="true">
	<div class="row">
		<accordion [closeOthers]="true">
			<accordion-group panelClass="b0 mb-sm panel-default no-shadow" [isOpen]="true">
				<div accordion-heading>
					<label><em class="fa fa-navicon"></em> Features do Marketing</label>
				</div>
				<div class="row menublock text-center">
					<gp-menublock-item size="xs" icon="sitemap" color="text-primary" text="Departamentos do Menu"
						routerLinkActive="active" routerLink="departamentos" *ngIf="canAccessDepartments">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="sitemap" color="text-primary" text="Menu Site Campanha"
						routerLinkActive="active" routerLink="site-campanha" *ngIf="canViewCampaignSiteHeaderLinks">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="google" color="text-primary" text="Google Analytics"
						routerLinkActive="active" routerLink="google-analytics" *ngIf="canAccessGoogleAnalytics">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="level-up" color="text-primary" text="Produtos em Destaque"
						routerLinkActive="active" routerLink="vitrines" *ngIf="canAccessFeaturedProducts">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="photo" color="text-primary" text="Media Box"
						routerLinkActive="active" routerLink="mediaboxes" *ngIf="canAccessMediaBoxes">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="shopping-cart" color="text-primary" text="Lojas Especiais"
						routerLinkActive="active" routerLink="lojas" *ngIf="canAccessSpecialShops">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="envelope-open-o" color="text-primary"
						text="Notificações Transacionais" routerLinkActive="active" routerLink="emails"
						*ngIf="canAccessTransactionalsEmails">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="microphone" color="text-primary" text="Comunicados"
						routerLinkActive="active" routerLink="comunicacao">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="comment" color="text-primary" text="Comunicados Avulsos"
						routerLinkActive="active" routerLink="comunicados-avulsos">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="handshake-o" color="text-primary" text="Régua de Relacionamento"
						routerLinkActive="active" routerLink="regra-comunicacao">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="commenting-o" color="text-primary" text="Mídias Socias"
						routerLinkActive="active" routerLink="midias-sociais" *ngIf="canAccessSocialMedia">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="code" color="text-primary" text="Scripts Customizados"
						routerLinkActive="active" routerLink="scripts">
					</gp-menublock-item>
				</div>
			</accordion-group>
		</accordion>
	</div>
</gp-card>

<router-outlet></router-outlet>
