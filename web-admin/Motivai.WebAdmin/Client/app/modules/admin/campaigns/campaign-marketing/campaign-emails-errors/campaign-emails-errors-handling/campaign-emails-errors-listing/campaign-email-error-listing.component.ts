import { CampaignEmailsErrorsService } from '../../campaign-emails-errors.service';
import { Component, OnInit, ViewChild, Output, EventEmitter } from '@angular/core';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';

@Component({
    selector: 'campaign-email-error-listing',
    templateUrl: './campaign-email-error-listing.component.html'
})
export class CampaignEmailErrorListingComponent implements OnInit {
    @ViewChild('alert') alert: GpAlertComponent;
    @Output('onDetails') onDetails: EventEmitter<any> = new EventEmitter<any>();

    errors: any[] = [];

    params: any = {};

    loading: boolean = false;

    constructor(private _service: CampaignEmailsErrorsService, ) { }

    ngOnInit(): void {
        this.find();
    }

    showDetails(event) {
        this.onDetails.emit(event);
    }

    find() {
        this.loading = true;
        this._service.getEmailsErrors('1').subscribe(
            response => {
                this.errors = response;
                this.loading = false;
            },
            err => {
                this.loading = false;
                this.alert.showError(err);
            }
        );
    }
}
