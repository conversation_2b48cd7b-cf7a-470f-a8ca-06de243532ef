// https://documentation.mailgun.com/en/latest/api-events.html#event-types
export function getNameDescription(eventType: string) {
    switch (eventType) {
        case 'complained':
          return '<PERSON>c<PERSON><PERSON>';
        case 'delivered':
          return 'Entregue';
        case 'accepted':
          return '<PERSON><PERSON>';
        case 'opened':
          return '<PERSON><PERSON>';
        case 'clicked':
          return '<PERSON><PERSON><PERSON>';
        case 'failed':
          return 'Falha na entrega';
        case 'rejected':
          return 'Rejeitado';
        case 'permanent_fail':
          return 'Falha permanente';
        case 'temporary_fail':
          return 'Falha temporária';
        case 'unsubscribed':
          return 'Não inscrito';
        case 'stored':
          return 'Resposta armazenada';
        default:
          return `Não mapeado (${eventType})`;
    }
}
