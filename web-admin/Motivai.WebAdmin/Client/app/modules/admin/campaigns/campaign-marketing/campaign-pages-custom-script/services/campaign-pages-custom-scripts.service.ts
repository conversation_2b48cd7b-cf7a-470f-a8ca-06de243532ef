import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { ApiService } from "../../../../../../core/api/api.service";
import { CampaignScriptsModel } from "../models/campaign-script";

@Injectable()
export class CampaignPagesCustomScriptsService {
    constructor(private _api: ApiService) { }

    getCustomScripts(campaignId: string) : Observable<any[]> {
        return this._api.get(`/api/campaigns/${campaignId}/customscripts`);
    }

    getCustomScriptById(campaignId: string, scriptId: string): Observable<any> {
        return this._api.get(`/api/campaigns/${campaignId}/customscripts/${scriptId}`);
    }

    createCustomScript(campaignId: string, campaignScript: CampaignScriptsModel): Observable<any> {
        if (campaignScript.id) {
            return this._api.put(`/api/campaigns/${campaignId}/customscripts/${campaignScript.id}`, campaignScript);
        }
        return this._api.post(`/api/campaigns/${campaignId}/customscripts`, campaignScript);
    }
}
