<gp-alert #alert [overlay]="true"></gp-alert>

<gp-card [first]="true">
    <gp-form-row>
        <gp-form-col cols="12 6">
            <label>Tipo: </label>
            <span>{{ emailLog.formattedType || '' }}</span>
        </gp-form-col>
        <gp-form-col cols="12 6">
            <label>Destinatário: </label>
            <span>{{ emailLog.emailsTo || '' }}</span>
        </gp-form-col>
    </gp-form-row>
    <gp-form-row>
        <gp-form-col cols="12 6">
            <label>E-mail Ativo: </label>
            <span>{{ emailLog.notificationActive | booleanToYesNo }}</span>
        </gp-form-col>
        <gp-form-col cols="12 6">
            <label>SMS Ativo: </label>
            <span>{{ emailLog.notificationSmsActive | booleanToYesNo }}</span>
        </gp-form-col>
    </gp-form-row>
    <hr>
    <gp-form-row>
        <gp-form-col cols="12 12">
            <label>Ocorreu erro: </label>
            <span>{{ emailLog.errorOccurred | booleanToYesNo }}</span>
        </gp-form-col>
    </gp-form-row>
    <div *ngIf="emailLog.errorOccurred">
        <gp-form-row>
            <gp-form-col cols="12 12">
                <label>Mensagem de erro: </label>
                <span>{{ emailLog.errorMessage || '-' }}</span>
            </gp-form-col>
        </gp-form-row>
        <gp-form-row>
            <gp-form-col cols="12 12">
                <label>Detalhes do erro: </label>
                <span>{{ emailLog.errorDetails || '-' }}</span>
            </gp-form-col>
        </gp-form-row>
    </div>
</gp-card>

<gp-card title="Conteúdo do email" *ngIf="canViewEmailContent && hasEmailContent">
    <gp-form-row>
        <gp-form-col cols="12">
            <gp-editor id="content" [ngModel]="emailLog.content" *ngIf="hasEmailContent">
            </gp-editor>

            <p *ngIf="!hasEmailContent">E-mail não foi construído.</p>
        </gp-form-col>
    </gp-form-row>
</gp-card>

<gp-card title="Destinatário" *ngIf="!hasRecipientTrackings">
    <gp-form-row>
        <gp-form-col cols="12 6">
            <label>E-mail: </label>
            <span>{{ emailLog.emailsTo || '' }}</span>
        </gp-form-col>
    </gp-form-row>

    <gp-form-row *ngIf="canResendNotification">
        <gp-form-col cols="12">
            <gp-spinner-button type="button" [actionSecondary]="true" icon="send" pull="right" text="Reenviar"
                loadingText="Processando" [loading]="loading" (click)="resendNotificationLog()">
            </gp-spinner-button>
        </gp-form-col>
    </gp-form-row>
</gp-card>

<gp-card title="Destinatários" *ngIf="hasRecipientTrackings">
    <div *ngFor="let e of emailLog.recipientTrackings">
        <gp-form-row>
            <gp-form-col cols="12 6">
                <label>Token: </label>
                <span>{{ e.token || '' }}</span>
            </gp-form-col>
            <gp-form-col cols="12 6">
                <label>E-mail: </label>
                <span>{{ e.email || '' }}</span>
            </gp-form-col>
        </gp-form-row>
        <gp-form-row>
            <gp-form-col cols="12 6">
                <label>Enviado: </label>
                <span>{{ e.sent | booleanToYesNo }}</span>
            </gp-form-col>
            <gp-form-col cols="12 6">
                <label>Data do envio: </label>
                <span>{{ e.sentDate | datetimezone }}</span>
            </gp-form-col>
        </gp-form-row>
        <gp-form-row *ngIf="e.errorOccurred">
            <gp-form-col cols="12 6">
                <label>Mensagem do erro: </label>
                <span>{{ e.errorMessage || '-' }}</span>
            </gp-form-col>
            <gp-form-col cols="12 6">
                <label>Detalhes do erro: </label>
                <span>{{ e.errorDetails || '-' }}</span>
            </gp-form-col>
        </gp-form-row>

        <gp-form-row *ngIf="e.events">
            <gp-form-col cols="12">
                <gp-grid name="events" [loading]="loading" [showPagination]="false" [showTotalPages]="false"
                    [rows]="e.events" [columns]="['Nome', 'Data']"
                    [fields]="['formattedName', 'formattedReceivedDate']" [showEdit]="false" [showDelete]="false">
                </gp-grid>
            </gp-form-col>
        </gp-form-row>

        <gp-form-row *ngIf="canResendNotification">
            <gp-form-col cols="12">
                <gp-spinner-button type="button" bootstrapClass="primary" icon="send" text="Reenviar" pull="right"
                    loadingText="Processando" [loading]="loading" (click)="resendNotification(e)">
                </gp-spinner-button>
            </gp-form-col>
        </gp-form-row>
        <hr>
    </div>
</gp-card>
