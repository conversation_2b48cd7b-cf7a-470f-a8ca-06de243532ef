export class CommunicationRuleHelper {
  public static mapStatusDescription(status: string): string {
    if (!status) {
      return 'Status indefinido';
    }

    switch (status) {
      case 'WAITING':
        return 'Aguardando';
      case 'RUNNING':
        return 'Processando';
      case 'COMPLETED':
        return 'Finalizado';

      default:
        return 'Status indefinido';
    }
  }

  public static mapDayDescription(day: string): string {
    if (!day) {
      return 'Dia indefinido';
    }

    switch (day) {
      case 'Sunday':
        return 'Domingo';
      case 'Monday':
        return 'Segunda-feira';
      case 'Tuesday':
        return 'Terça-feira';
      case 'Wednesday':
        return 'Quarta-feira';
      case 'Thursday':
        return 'Quinta-feira';
      case 'Friday':
        return 'Sexta-feira';
      case 'Saturday':
        return 'Sabádo';

      default:
        return 'Dia indefinido';
    }
  }

  public static maptemplateReviewStatus(status: string): string {
    if (!status) {
      return '';
    }

    switch (status) {
      case 'NOT_SENT':
        return 'Não enviado para análise';
      case 'WAITING_REVIEW':
        return 'Aguardando revisão';
      case 'WAITING_APPROVAL':
        return 'Aguardando aprovação';
      case 'APPROVED':
        return 'Aprovado';
      case 'REJECTED':
        return 'Reprovado';
      default:
        return 'Status indefinido';
    }
  }
}
