import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

import { AuthStore } from '../../../../../core/auth/auth.store';
import { CampaignStore } from '../../campaign.store';

@Component({
  selector: 'campaign-marketing-view',
  templateUrl: 'campaign-marketing-view.component.html',
  styles: [`
    .menublock {
      margin-top: -1em;
      /*padding-left: 15%*/s
    }
  `]
})
export class CampaignMarketingViewComponent {
  campaignId: string;

  constructor(private _authStore: AuthStore, private route: ActivatedRoute, private router: Router, private _campaignStore: CampaignStore) { }

  ngOnInit() {
    if (this.route.parent != null) {
      this.route.parent.params.subscribe((params: any) => {
        this.campaignId = params['id'];
        if (!this.campaignId) {
          this.router.navigate(['/campanha']);
        }
      });
    } else {
      this.router.navigate(['/campanha']);
    }
  }

  get showMenublock() {
    return this._campaignStore.isFullCampaignOrUserWithGpBu;
  }

  get canAccessDepartments() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_DEPART_MENU;
  }

  get canAccessFeaturedProducts() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_FEATURED_PRODS;
  }

  get canAccessMediaBoxes() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_MEDIA_BOX;
  }

  get canAccessSpecialShops() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_SPECIAL_SHOPS;
  }

  get canAccessTransactionalsEmails() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_MARKETING_NOTIFICATION_PARAMETRIZATIONS
     || this._authStore.role.PERMISSION_CAMPAIGNS_MARKETING_NOTIFICATION_LOGS;
  }

  get canViewCampaignSiteHeaderLinks() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_SITE_HEADER_LINKS_VIEW;
  }

  get canAccessFeaturedPartners() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_FEATURED_PARTNERS;
  }

  get canAccessGoogleAnalytics() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_GOOGLE_ANALYTICS;
  }

  get canAccessSocialMedia() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_SOCIAL_MEDIA;
  }
}
