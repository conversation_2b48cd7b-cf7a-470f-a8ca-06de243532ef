import { Component, OnInit, ViewChild } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';

import { GpAlertComponent } from "../../../../../shared/components/gp-alert/gp-alert/gp-alert.component";
import { CategoriesService } from "../../../categories/categories.service";
import { CampaignService } from "../../campaign.service";
import { CompanyService } from "../../../companies/company/company.service";

@Component({
  selector: 'campaign-departs-products',
  templateUrl: 'campaign-departs-products.component.html'
})
export class CampaignDepartsProductsComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  campaignId: string;
  homeSkus: Array<any> = [];
  departmentsSkus: Array<any> = [];

  formHome: FormGroup;
  homeMessages = {
    partnerId: {
      required: 'Parceiro é obrigatório'
    },
    sku: {
      required: 'SKU é obrigatório'
    }
  };

  formDep: FormGroup;
  depMessages = {
    departmentId: {
      required: 'Parceiro é obrigatório'
    },
    partnerId: {
      required: 'Parceiro é obrigatório'
    },
    sku: {
      required: 'SKU é obrigatório'
    }
  };

  departments: Array<any> = [];
  campaignPartners: Array<any> = [];

  loadingSkus: boolean = false;
  loadingDeps: boolean = false;
  sending: boolean = false;

  constructor(private _categoriesService: CategoriesService, private _campaignService: CampaignService, private _companyService: CompanyService,
    private route: ActivatedRoute, fb: FormBuilder) {
      this.formHome = fb.group({
        partnerId: ['', Validators.required],
        sku: ['', Validators.required]
      });
      this.formDep = fb.group({
        departmentId: ['', Validators.required],
        partnerId: ['', Validators.required],
        sku: ['', Validators.required]
      });
    }

  ngOnInit(): void {
    this.loadingSkus = true;
    this.loadingDeps = true;

    if (this.route.parent != null && this.route.parent.parent != null) {
      this.route.parent.parent.params.subscribe((params: any) => {
        this.campaignId = params['id'];
        this.loadFeaturedProducts();
        this.loadCombos();
      });
    }
  }

  get disableButton() {
    return this.sending || !this.departments || !this.departments.length;
  }

  get loading() {
    return this.loadingSkus || this.loadingDeps;
  }

  private loadCombos() {
    // Carregamento dos Departmentos
    this._categoriesService.get(undefined, true, 1, undefined, 'name')
      .subscribe(
        departments => {
          if (departments) {
            this.departments = departments.map(dep => {
              return { id: dep.id, text: dep.name };
            });
          }
          this.loadingDeps = false;
        },
        err => this.handleError(err)
      );
    // Carregamento dos Parceiros da Campanha
    this._campaignService.getLinkedPartnersDataByCampaign(this.campaignId)
      .subscribe(
        linkedPartners => {
          if (linkedPartners) {
            this.campaignPartners = linkedPartners.map(part => {
              return { id: part.partnerId, text: part.partnerName };
            });
          }
        },
        err => this.handleError(err)
      );

  }

  private loadFeaturedProducts() {
    this.loadingSkus = true;
    this._campaignService.getFeaturedProductsByCampaign(this.campaignId)
      .subscribe(
        featuredProducts => {
          if (featuredProducts) {
            this.homeSkus = featuredProducts.home;
            this.departmentsSkus = featuredProducts.departments;
          }
          this.loadingSkus = false;
        },
        err => this.handleError(err)
      );
  }

  private handleError(err) {
    let errorMessage = (err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao efetuar a operação.');
    this.gpAlert.showError(errorMessage);
    this.loadingSkus = false;
    this.loadingDeps = false;
    this.sending = false;
  }

  private setSending() {
    this.sending = true;
  }

  addSkuToHome(event) {
    if (!event.valid) {
      this.gpAlert.showWarning('Preencha os campos para continuar.');
      return;
    }
    this.setSending();

    this._companyService.findPartnerProductBySkuCode(event.model.partnerId, event.model.sku)
    .subscribe(
      product => {
        this._campaignService.addSkuToHome(this.campaignId, product).subscribe(
        result => {
          if (result) {
            this.formHome.reset();
            this.loadFeaturedProducts();
            this.gpAlert.showSuccess('SKU adicionado à lista da home page com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível adicionar o SKU, por favor, tente novamente.');
          }
          this.sending = false;
        }, err => this.handleError(err));
      }, err => this.handleError(err));
  }

  removeSkuFromHome(sku) {
    if (!confirm(`Deseja remover o SKU '${sku.name}' da home page?`)) return;
    this.setSending();
    this._campaignService.removeSkuFromHome(this.campaignId, sku.skuId, sku.partnerId)
      .subscribe(
        result => {
          if (result) {
            this.loadFeaturedProducts();
            this.gpAlert.showSuccess('SKU removido com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível remover o SKU, por favor, tente novamente.');
          }
        },
        err => this.handleError(err),
        () => this.sending = false
      );
  }

  addSkuToDepartment(event) {
    if (!event.valid) {
      this.gpAlert.showWarning('Preencha os campos para continuar.');
      return;
    }
    this.setSending();
    this._companyService.findPartnerProductBySkuCode(event.model.partnerId, event.model.sku)
    .subscribe(product => {
      this._campaignService.addSkuToDepartment(this.campaignId, event.model.departmentId, product)
        .subscribe(
          result => {
            if (result) {
              this.formDep.reset();
              this.loadFeaturedProducts();
              this.gpAlert.showSuccess('SKU adicionado à lista do departmento com sucesso.');
            } else {
              this.gpAlert.showWarning('Não foi possível adicionar o SKU, por favor, tente novamente.');
            }
            this.sending = false;
          },
          err => this.handleError(err));
    }, err => this.handleError(err));
  }

  removeSkuFromDepartment(dep, sku) {
    if (!confirm(`Deseja remover o SKU '${sku.name}' do departmento '${dep.departmentName}'?`)) return;
    this.setSending();

    this._campaignService.removeSkuFromDepartment(this.campaignId, dep.departmentId, sku.partnerId, sku.skuId)
      .subscribe(
        result => {
          if (result) {
            this.loadFeaturedProducts();
            this.gpAlert.showSuccess('SKU removido com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível remover o SKU, por favor, tente novamente.');
          }
        },
        err => this.handleError(err),
        () => this.sending = false
      );
  }
}
