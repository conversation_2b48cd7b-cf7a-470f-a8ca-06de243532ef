import { CustomMenuItem } from '../models/custom-menu';
import { AuthenticationStates } from '../models/features-types';

export class CustomMenuBuilder {
  public static buildCustomItemsFromCampaignSitePageSettings(pagesSettings: any): Array<CustomMenuItem> {
    if (!pagesSettings) return [];

    return Object.keys(pagesSettings)
      .map(key => pagesSettings[key])
      .filter(page => page && page.type && page.type.length > 0 && page.enable)
      .map(page => CustomMenuItem.fromCampaignSitePageParam(page));
  }

  public static buildCustomItemsFromCampaignSiteMenuFeature(menuFeatures: Array<any>): Array<CustomMenuItem> {
    if (!menuFeatures) return [];

    return menuFeatures
      .filter(menu => menu && menu.menuItem)
      .map(menu => CustomMenuItem.fromCampaignSiteMenuFeature(menu));
  }
}
