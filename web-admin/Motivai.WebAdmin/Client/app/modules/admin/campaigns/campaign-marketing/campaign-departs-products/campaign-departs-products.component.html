<gp-card [last]="true">
	<div class="row col-xs-12 bottom-m1">
		<h4>Produtos em Destaque por Departamento</h4>
	</div>
	<div class="row col-xs-12">
		<spinner [overlay]="true" [show]="loading"></spinner>
		<gp-alert [overlay]="true" #gpAlert></gp-alert>
		
		<accordion [closeOthers]="true">
			<accordion-group panelClass="b0 mb-sm panel-default">
				<div accordion-heading>
					<label>Vitrine da Home Page</label>
				</div>
				<div class="row">
					<gp-form-validate [formGroup]="formHome" [validationMessages]="homeMessages" (onSubmit)="addSkuToHome($event)" >
						<gp-select-validate cols="12 4 4" label="Parceiro" [searchable]="true" [items]="campaignPartners" placeholder="Parceiro"
							formControlName="partnerId">
						</gp-select-validate>
						<gp-input-validate cols="12 4 4" label="SKU" formControlName="sku"></gp-input-validate>
						<div class="top-p2" grid="12 4 4">
							<gp-spinner-button type="submit" text="Adicionar" bootstrapClass="success" loadingText="Adicionando" [pink]="true" icon="plus"
								pull="right" [disabled]="!formHome.valid || sending" [loading]="sending"></gp-spinner-button>
						</div>
					</gp-form-validate>
				</div>
				<gp-form-row>
					<gp-form-col cols="12 12 12">
						<gp-grid name="homeSkusGrid" [rows]="homeSkus" [columns]="['Parceiro', 'Produto', 'SKU']" [fields]="['partnerName', 'skuName', 'skuCode']"
							[loading]="loading" [showActive]="false" [showPagination]="false" [showEdit]="false" [showDelete]="true" (onDelete)="removeSkuFromHome($event)">
						</gp-grid>
					</gp-form-col>
				</gp-form-row>
			</accordion-group>
			<accordion-group panelClass="b0 mb-sm panel-default">
				<div accordion-heading>
					<label>Vitrine de Departamentos</label>
				</div>
				<div class="row">
					<gp-form-validate [formGroup]="formDep" [validationMessages]="depMessages" (onSubmit)="addSkuToDepartment($event)" >
						<div grid="12 6 3" [group]="true">
							<label for="department">Departamento *</label>
							<gp-categories-select cols="12 6 3 3" [searchable]="true" placeholder="Selecione um departamento"
								formControlName="departmentId">
							</gp-categories-select>
						</div>
						<gp-select-validate cols="12 6 3 3" label="Parceiro" [searchable]="true" [items]="campaignPartners" placeholder="Selecione um parceiro"
							formControlName="partnerId">
						</gp-select-validate>
							<gp-input-validate cols="12 6 3 3" label="SKU" formControlName="sku"></gp-input-validate>
						<div class="top-p2" grid="12 6 3 3">
							<gp-spinner-button type="submit" text="Adicionar" bootstrapClass="success" loadingText="Adicionando" [pink]="true" icon="plus"
							pull="right" [disabled]="!formDep.valid || sending" [loading]="sending"></gp-spinner-button>
						</div>
					</gp-form-validate>
				</div>
				<gp-form-row>
					<gp-form-col cols="12 12 12">
						<gp-datatable [columnsName]="['Departamento', 'Parceiro', 'Produto', 'SKU', '']">
							<tbody>
								<ng-template ngFor let-dep="$implicit" [ngForOf]="departmentsSkus">
									<tr *ngFor="let sku of dep.skus">
										<td>{{dep.departmentName}}</td>
										<td>{{sku.partnerName}}</td>
										<td>{{sku.skuName}}</td>
										<td>{{sku.skuCode}}</td>
										<td width="100px" align="center">
											<button type="button" title="Excluir" class="btn btn-danger btn-sm grid-remove" (click)="removeSkuFromDepartment(dep, sku)">
												<i title="Excluir" class="fa fa-times"></i>
											</button>
										</td>
									</tr>
								</ng-template>
								<tr *ngIf="!departmentsSkus || !departmentsSkus.length">
									<td colspan="5">Nenhum produto destacado.</td>
								</tr>
							</tbody>
						</gp-datatable>
					</gp-form-col>
				</gp-form-row>
			</accordion-group>
		</accordion>
	</div>
</gp-card>
