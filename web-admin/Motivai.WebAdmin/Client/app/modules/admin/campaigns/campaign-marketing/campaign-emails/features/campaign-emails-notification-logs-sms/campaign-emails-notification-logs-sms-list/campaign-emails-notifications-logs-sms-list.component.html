<gp-card title="Filtros" [first]="true">
	<gp-form-row>
		<gp-form-col cols="12 6 6 4">
			<label>Selecione um tipo de sms</label>
			<select name="eventType" class="form-control" [(ngModel)]="params.eventType">
				<option value="" selected>Selecione</option>
				<option value="Participant">Novo Participante</option>
				<option value="Points">Pontos Creditados</option>
				<option value="PointsDistributedNewParticipant">Distribuição de Pontos para Novo Participante</option>
				<option value="PasswordRecovery">Recuperação de Senha</option>
				<option value="Order">Pedido para o Participante</option>
				<option value="PartnerOrder">Pedido para o Parceiro e Fabricante</option>
				<option value="OrderTracking">Situação de Entrega de Pedido</option>
				<option value="OrderDelivery">Pedido Entregue</option>
				<option value="OrderItemEvent">Tracking de Item do Pedido</option>
				<option value="OrderVirtualItemEvent">Link de Vale Virtual do Pedido</option>
				<option value="OrderRefund">Estorno do Pedido</option>
				<option value="OrderCancellation">Cancelamento do Pedido</option>
				<option value="OrderPricing">Precificação de Pedido</option>
				<option value="ProductAvailable">Produto Disponível</option>
				<option value="TokenIssue">Códigos de Segurança</option>
				<option value="PrepaidCardOrderReceived">Cartão Pré-Pago: Pedido Recebido</option>
				<option value="PointsPurchaseReceived">Pedido de Compra de Pontos</option>
				<option value="RechargeOrderReceived">Pedido de Recarga de Celular</option>
				<option value="BillPaymentOrderReceived">Pedido de Pague Contas</option>
				<option value="BankTransferOrderReceived">Cash Back: Pedido Recebido</option>
				<option value="BankTransferOrderTransfered">Cash Back: Transferência Efetuada</option>
				<option value="Login">Login</option>
				<option value="ExpiringPoints">Pontos a Expirar</option>
			</select>
		</gp-form-col>
		<gp-form-col cols="12 6 6 4">
			<gp-simple-input label="Celular">
				<input type="text" class="form-control" name="mobile" [(ngModel)]="params.mobilePhone" />
			</gp-simple-input>
		</gp-form-col>
		<gp-datepicker cols="12 6 6 2" [required]="false" label="De" name="from" [(ngModel)]="params.from">
		</gp-datepicker>
		<gp-datepicker cols="12 6 6 2" [required]="false" label="Até" name="to" [(ngModel)]="params.to">
		</gp-datepicker>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 12">
			<gp-spinner-button type="button" bootstrapClass="primary" icon="search" text="Pesquisar" pull="right"
				loadingText="Processando" [loading]="loading" (click)="applyFilters()" marginTop="24px">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
</gp-card>
<gp-alert [overlay]="true" #alert></gp-alert>
<gp-card>
	<gp-form-row>
		<gp-form-col cols="12">
			<gp-grid name="smsLogs" [loading]="loading" [showPagination]="true" [showTotalPages]="false"
				[rows]="smsLogs" [pageSize]="20"
				[columns]="['Destinatário','Tipo','SMS Ativo','Enviado','Data de Envio']"
				[fields]="['mobilePhone','formattedType','smsActive','sentDescription','formattedSentDate']"
				[showEdit]="true" [showDelete]="false" (onEdit)="showSmsLogDetails($event)" (onPageChanged)="pageChanged($event)"></gp-grid>
		</gp-form-col>
	</gp-form-row>
</gp-card>
