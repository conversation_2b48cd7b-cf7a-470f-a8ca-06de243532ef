import { Component, ViewChild, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';

import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignStore } from '../../../../campaign.store';
import { RxjsHelpers } from '../../../../../../../shared/helpers/rxjs-helpers';
import { getCampaignEventTypes, getCampaignMailVariablesByEventType } from '../../models/campaign-event-types';
import { Item } from '../../../../../../../shared/models/item';
import { CampaignService } from '../../../../campaign.service';

@Component({
  selector: 'campaign-emails',
  templateUrl: 'campaign-emails-notification-parametrization.component.html',
  styles: [`
    .header-image {
      max-height: 250px;
      max-width: 90%;
    }
  `]
})
export class CampaignEmailsParametrizationComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;

  private _campaign$: Subscription;

  headerPath: string;

  campaignId: string = '';
  notification: any = {
    email: {},
    sms: {}
  };
  smsMaxLength: number = 160;
  loading: boolean = false;
  sending: boolean = false;
  uploading: boolean = false;

  campaignEventTypes: Array<Item> = getCampaignEventTypes();
  type: string;

  private availableVars = getCampaignMailVariablesByEventType();

  constructor(private _campaignService: CampaignService, private _campaignStore: CampaignStore) { }

  ngOnInit(): void {
    this.newEmail();
    this._campaign$ = this._campaignStore.asObservable
      .subscribe(id => this.campaignId = id);
  }

  ngOnDestroy(): void {
    RxjsHelpers.unsubscribe(this._campaign$);
  }

  get isFullCampaign() {
    return this._campaignStore.isFullCampaign;
  }

  get isMakeYourCampaign() {
    return this._campaignStore.isMakeYourCampaign;
  }

  get showForm() {
    return !this.loading && this.notification.type;
  }

  get disableButton() {
    return this.loading || this.uploading || !this.notification.type;
  }

  get headerImageUrl() {
    if (this.notification && this.notification.email && this.notification.email.headerImageUrl)
      return this.notification.email.headerImageUrl + '/500';
    return '';
  }

  get availableVarsHelper(): Array<any> {
    if (this.notification && this.notification.type) {
      return this.availableVars[this.notification.type];
    }
    return [];
  }

  private handleError(err) {
    let errorMessage = (err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao efetuar a operação.');
    this.gpAlert.showError(errorMessage);
    this.loading = false;
    this.sending = false;
    this.uploading = false;
  }

  newEmail(type: string = null) {
    this.headerPath = '';
    this.notification = {
      type: type,
      email: {
        active: false
      },
      sms: {
        active: false
      }
    };
  }

  onTypeChange(type) {
    if (!type) {
      return;
    }
    // algumas ações está o Item completo
    if (typeof(type) === 'object') {
      return
    }
    this.loading = true;
    this.gpAlert.clear();
    this._campaignService.getTransactionalNotificationByType(this.campaignId, type)
      .subscribe(
        notification => {
          if (notification) {
            this.notification = notification;
            if (!this.notification.email) this.notification.email = {};
            if (!this.notification.sms) this.notification.sms = {};
          } else {
            this.gpAlert.showWarning('E-mail transacional não encontrado pelo tipo informado, preencha o formulário e salve para criar.');
            this.newEmail(type);
          }
          this.loading = false;
        },
        err => this.handleError(err)
      );
  }

  saveEmail() {
    this.sending = true;
    this.gpAlert.clear();
    this.headerPath = `api/campaigns/${this.campaignId}/marketing/transactionalnotifications/${this.notification.type}/headerimage`;
    this._campaignService.saveTransactionalNotification(this.campaignId, this.notification)
      .subscribe(
        result => {
          if (result) {
            this.gpAlert.showSuccess('E-mail transacional salvo com sucesso.');
            this.sending = false;
          } else {
            this.gpAlert.showWarning('Não foi possível salvar o e-mail transacional, por favor, tente novamente.');
          }
        },
        err => this.handleError(err)
      );
  }

  onComplete(event) {
    this.gpAlert.clear();
    if (event.success) {
      this.gpAlert.showSuccess('Arquivo enviado com sucesso.');
    } else {
      this.gpAlert.showError(event.errorMessage);
    }
    this.sending = false;
    this.uploading = false;
  }
}
