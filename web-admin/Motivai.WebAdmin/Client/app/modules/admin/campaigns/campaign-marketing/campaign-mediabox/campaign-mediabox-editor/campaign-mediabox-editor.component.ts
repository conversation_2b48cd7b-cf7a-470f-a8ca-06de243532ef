import { <PERSON>mponent, ViewChild, <PERSON><PERSON>nit, OnD<PERSON>roy } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { CustomValidators } from 'ng2-validation';
import { Subscription } from 'rxjs';

import { CampaignService } from '../../../campaign.service';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CategoriesService } from '../../../../categories/categories.service';
import { AuthStore } from '../../../../../../core/auth/auth.store';
import { GpFileUploadComponent } from '../../../../../../shared/components/gp-fileupload/gp-fileupload.component';
import { Item } from '../../../../../../shared/models/item';
import { CampaignStore } from '../../../campaign.store';
import { PERMISSION_CAMPAIGNS_CRM_TARGET_AUDIENCE_VIEW } from '../../../../../../core/auth/access-points';
import { EMPTY } from '../../../../../../core/constants/constants-value';
import { TargetAudienceService } from '../../../campaign-crm/campaign-crm-target-audience/targetAudience.service';
import { RxjsHelpers } from '../../../../../../shared/helpers/rxjs-helpers';

@Component({
  selector: 'campaign-mediabox-editor',
  templateUrl: 'campaign-mediabox-editor.component.html'
})
export class CampaignMediaboxEditorComponent implements OnInit, OnDestroy {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @ViewChild('bannerLarge') bannerLarge: GpFileUploadComponent;
  @ViewChild('bannerTablet') bannerTablet: GpFileUploadComponent;
  @ViewChild('bannerMobile') bannerMobile: GpFileUploadComponent;

  uploadPath: string = '';

  campaignId: string;

  mediaBoxId?: string = undefined;
  contentType: string = 'BANNER';
  selectedProduct: any = null;

  bannerImageUrl: string | null;
  bannerImageUrlToSave: string;
  bannerTabletImageUrl: string | null;
  bannerTabletImageUrlToSave: string;
  bannerMobileImageUrl: string | null;
  bannerMobileImageUrlToSave: string;

  formGroup: FormGroup;
  messages = {
    name: { required: 'Nome é obrigatório' },
    position: { required: 'Posição é obrigatória' },
    startDate: {
      required: 'Data de início é obrigatória',
      date: 'Data de início inválida'
    },
    endDate: {
      required: 'Data de término é obrigatória',
      date: 'Data de término inválida'
    },
    departments: {
      required: 'Deve ser selecionado pelo menos um departamento'
    },
    openInNewTab: {}
  };

  availableTargetsAudiences: Array<Item> = [];
  dbTargetAudiences: Array<string> = [];
  participantsGroups: Array<Item> = [];

  departments: Array<Item> = [];
  pages: Item[] = [
    Item.of('Login', 'Login'),
    Item.of('Home', 'Home Page'),
    Item.of('BenefitsClub', 'Clube de Benefícios')
  ];

  loading: boolean = false;
  sending: boolean = false;
  uploadingLargeImage: boolean = false;
  uploadingTabletImage: boolean = false;
  uploadingMobileImage: boolean = false;

  private _subscriptions: Array<Subscription> = [];

  constructor(
    private _campaignService: CampaignService,
    private _categoryService: CategoriesService,
    private route: ActivatedRoute,
    fb: FormBuilder,
    public campaignStore: CampaignStore,
    private _auth: AuthStore,
    private targetAudienceService: TargetAudienceService
  ) {
    this.formGroup = fb.group({
      name: ['', Validators.required],
      active: [false],
      position: ['', Validators.required],
      link: [''],
      openInNewTab: [null],
      startDate: [null, Validators.compose([Validators.required, CustomValidators.date('dd/mm/yyyy')])],
      endDate: [null, Validators.compose([Validators.required, CustomValidators.date('dd/mm/yyyy')])],
      departments: [null],
      visibleOnCatalog: [false],
      visibleOnSite: [false],
      visibleOnAppCashback: [false],
      sitePages: [null]
    });
  }

  get isContentTypeBanner() {
    return this.contentType == 'BANNER';
  }

  get isContentTypeProduct() {
    return this.contentType == 'PRODUCT';
  }

  get isEdition() {
    return this.campaignId && this.mediaBoxId;
  }

  get hasSelectedFile() {
    if (this.isContentTypeProduct) return false;
    return (
      (this.bannerLarge && this.bannerLarge.hasSelectedFile()) ||
      this.bannerTablet.hasSelectedFile() ||
      this.bannerMobile.hasSelectedFile()
    );
  }

  get smallImageIdealResolution(): string {
    if (this.formGroup.get("visibleOnAppCashback").value) {
      return "1226x525";
    }
    return "600";
  }

  get smallImageResolutionLabel() {
    if (this.formGroup.get("visibleOnAppCashback").value) {
      return "Largura x Altura";
    }
    return "Largura"
  }

  get disableButtons() {
    return this.sending || this.uploadingLargeImage || this.uploadingTabletImage || this.uploadingMobileImage;
  }

  get canViewTargetAudiences() {
    return (
      this._auth.hasPermissionTo(PERMISSION_CAMPAIGNS_CRM_TARGET_AUDIENCE_VIEW) && this.campaignStore.isFullCampaign
    );
  }

  get showCampaignSitePagesSelection() {
    return this.campaignStore.isSiteCampaign && this.isContentTypeBanner;
  }

  get uploading() {
    return this.uploadingLargeImage || this.uploadingTabletImage || this.uploadingMobileImage;
  }

  ngOnInit(): void {
    this.loading = true;
    // Carregamento dos Departamentos
    this._categoryService.getCombo(1, undefined, true).subscribe(
      departments => {
        this.loading = false;
        if (departments) {
          const departmentsItems = departments.map(dep => Item.of(dep.id, dep.name));
          // Guid.Empty
          departmentsItems.push(Item.of(EMPTY, 'Home Page'));
          this.departments = departmentsItems;
        }
      },
      err => this.handleError(err)
    );

    const campaignId$ = this.campaignStore.asObservable.subscribe(id => {
      this.campaignId = id;
      this.loadData();
    });
    this._subscriptions.push(campaignId$);
  }

  ngOnDestroy() {
    RxjsHelpers.unsubscribeMany(this._subscriptions);
  }

  private handleError(err) {
    let errorMessage = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao efetuar a operação.';
    this.gpAlert.showError(errorMessage);
    this.loading = false;
    this.sending = false;
  }

  private loadData() {
    const routeParams$ = this.route.params.subscribe((params: any) => {
      this.mediaBoxId = params['mediaboxId'];
      if (this.mediaBoxId) {
        this.loadMediaBox();
      }
    });
    this._subscriptions.push(routeParams$);
  }

  private setLargeImageUrl(imageUrl: string) {
    this.bannerImageUrl = null; // force update
    if (imageUrl) {
      this.bannerImageUrl = `${imageUrl}/1680`;
    }
  }

  private setMediumImageUrl(imageMediumUrl: string) {
    this.bannerTabletImageUrl = null; // force update
    if (imageMediumUrl) {
      this.bannerTabletImageUrl = `${imageMediumUrl}/1280`;
    }
  }

  private setSmallImageUrl(imageSmallUrl: string) {
    this.bannerMobileImageUrl = null; // force update

    if (imageSmallUrl && !this.formGroup.get("visibleOnAppCashback").value) {
      this.bannerMobileImageUrl = `${imageSmallUrl}/599`;
    }

    if (imageSmallUrl && this.formGroup.get("visibleOnAppCashback").value) {
      this.bannerMobileImageUrl = `${imageSmallUrl}/525`;
    }
  }

  newMediaBox() {
    this.mediaBoxId = undefined;
    this.contentType = null;
    this.selectedProduct = null;
    this.formGroup.reset();
    this.clearImages();
    this.bannerLarge.removeAll();
    this.bannerLarge.createUploader();
  }

  private clearImages() {
    this.bannerImageUrl = null;
    this.bannerImageUrlToSave = null;
    this.bannerMobileImageUrl = null;
    this.bannerMobileImageUrlToSave = null;
    this.bannerTabletImageUrl = null;
    this.bannerTabletImageUrlToSave = null;
  }

  private loadMediaBox() {
    if (!this.campaignId || !this.mediaBoxId) return;
    this.loading = true;
    this._campaignService.getMediaBoxById(this.campaignId, this.mediaBoxId).subscribe(
      mediaBox => {
        if (mediaBox) {
          this.contentType = mediaBox.contentType || 'BANNER';
          this.selectedProduct = mediaBox.product;
          this.formGroup.patchValue(mediaBox);
          if (mediaBox.participantsGroups) {
            this.participantsGroups = mediaBox.participantsGroups;
          }
          this.setLargeImageUrl(mediaBox.imageUrl);
          this.setMediumImageUrl(mediaBox.mediumImageUrl);
          this.setSmallImageUrl(mediaBox.smallImageUrl);
        } else {
          this.mediaBoxId = undefined;
          this.clearImages();
          this.gpAlert.showWarning('Media box não encontrado.');
        }
        this.loading = false;
      },
      err => this.handleError(err)
    );
  }

  private onchangeComunication() {
    const control = this.formGroup.get('comunication');
    const value = control.value;

    if (value) {
      this.formGroup.get('link').disable();
      return;
    }

    this.formGroup.get('link').enable();
  }

  isValid() {
    return this.formGroup.valid && this.hasSelectedFile;
  }

  saveMediaBox(event) {
    if (!event.valid) {
      this.gpAlert.showWarning('Preencha os campos corretamente para continuar.');
      return;
    }
    let mediaBox = event.model;

    if (mediaBox.visibleOnAppCashback && !this.bannerMobile.hasSelectedFile && !this.bannerMobileImageUrl) {
      this.gpAlert.showWarning("Selecione uma imagem do banner pequeno conforme especificado.");
      return;
    }

    this.sending = true;

    if (this.mediaBoxId) {
      mediaBox.id = this.mediaBoxId;
    }
    mediaBox.contentType = this.contentType;

    if (this.participantsGroups != null && this.participantsGroups.length > 0) {
      mediaBox.participantsGroups = this.participantsGroups;
    } else {
      mediaBox.participantsGroups = null;
    }
    if (this.isContentTypeProduct) {
      mediaBox.product = this.selectedProduct;
    }
    mediaBox.imageUrl = this.bannerImageUrlToSave;
    mediaBox.mediumImageUrl = this.bannerTabletImageUrlToSave;
    mediaBox.smallImageUrl = this.bannerMobileImageUrlToSave;

    this._campaignService.saveMediaBox(this.campaignId, mediaBox).subscribe(
      result => {
        if (result) {
          if (!this.mediaBoxId) {
            this.mediaBoxId = result;
          }
          if (this.hasSelectedFile) {
            this.gpAlert.showSuccess('Media box salvo com sucesso. Aguarde o upload da imagem.');
            this.sendImage();
            this.sending = false;
          } else {
            this.gpAlert.showSuccess('Media box salvo com sucesso.');
            this.sending = false;
          }
        } else {
          this.sending = false;
          this.gpAlert.showWarning('Não foi possível salvar o media box, por favor, tente novamente.');
        }
      },
      err => this.handleError(err)
    );
  }

  sendImage() {
    if (!this.hasSelectedFile) {
      this.gpAlert.showWarning('Selecione uma imagem para upload.');
      return;
    }

    this.uploadSmallImage();
    this.uploadMediumImage();
    this.uploadLargeImage();
  }

  private uploadLargeImage() {
    if (!this.bannerLarge || !this.bannerLarge.hasSelectedFile()) return;

    this.uploadingLargeImage = true;
    this.bannerLarge.path = `api/campaigns/${this.campaignId}/marketing/mediaboxes/${this.mediaBoxId}/images/large`;
    this.bannerLarge.onComplete = uploadEvent => {
      this.uploadingLargeImage = false;
      if (uploadEvent.success) {
        this.showImageSuccessMessage();
        const imageUrl = this.bannerLarge.extractResultFromResponse(uploadEvent);
        if (imageUrl) {
          this.setBannerLargeImageUrl(imageUrl);
        }
      }
      this.bannerLarge.createUploader();
    };
    this.bannerLarge.uploadFile();
  }

  private uploadMediumImage() {
    if (!this.bannerTablet || !this.bannerTablet.hasSelectedFile()) return;

    this.uploadingTabletImage = true;
    this.bannerTablet.path = `api/campaigns/${this.campaignId}/marketing/mediaboxes/${this.mediaBoxId}/images/medium`;
    this.bannerTablet.onComplete = uploadEvent => {
      this.uploadingTabletImage = false;
      if (uploadEvent.success) {
        this.showImageSuccessMessage();
        const imageUrl = this.bannerTablet.extractResultFromResponse(uploadEvent);
        if (imageUrl) {
          this.setBannerMediumImageUrl(imageUrl);
        }
      }
      this.bannerTablet.createUploader();
    };
    this.bannerTablet.uploadFile();
  }

  private uploadSmallImage() {
    if (!this.bannerMobile || !this.bannerMobile.hasSelectedFile()) return;

    this.uploadingMobileImage = true;

    this.bannerMobile.path = `api/campaigns/${this.campaignId}/marketing/mediaboxes/${this.mediaBoxId}/images/small`;
    this.bannerMobile.onComplete = uploadEvent => {
      this.uploadingMobileImage = false;
      if (uploadEvent.success) {
        this.showImageSuccessMessage();
        const imageUrl = this.bannerMobile.extractResultFromResponse(uploadEvent);
        if (imageUrl) {
          this.setBannerSmallImageUrl(imageUrl);
        }
      }
      this.bannerMobile.createUploader();
    };
    this.bannerMobile.uploadFile();
  }

  showImageSuccessMessage() {
    if (!this.uploadingLargeImage && !this.uploadingTabletImage && !this.uploadingMobileImage) {
      this.gpAlert.showSuccess('Imagens atualizadas com sucesso!');
    }
  }

  private setBannerLargeImageUrl(imageUrl: string) {
    this.bannerImageUrl = null;
    if (imageUrl) {
      this.bannerImageUrlToSave = imageUrl;
      this.bannerImageUrl = `${imageUrl}/1680`;
    }
  }

  private setBannerMediumImageUrl(mediumImageUrl: string) {
    this.bannerTabletImageUrl = null;
    if (mediumImageUrl) {
      this.bannerTabletImageUrlToSave = mediumImageUrl;
      this.bannerTabletImageUrl = `${mediumImageUrl}/1280`;
    }
  }

  private setBannerSmallImageUrl(imageSmallUrl: string) {
    this.bannerMobileImageUrl = null;
    this.bannerMobileImageUrlToSave = imageSmallUrl;
    if (imageSmallUrl && !this.formGroup.get("visibleOnAppCashback").value) {
      this.bannerMobileImageUrl = `${imageSmallUrl}/599`;
    }

    if (imageSmallUrl && this.formGroup.get("visibleOnAppCashback").value) {
      this.bannerMobileImageUrl = `${imageSmallUrl}/525`;
    }
  }
}
