<gp-card [first]="true" title="Filtros">
    <gp-form-row>
        <gp-form-col cols="12 4">
            <gp-simple-input label="Email">
                <input type="text" class="form-control" [(ngModel)]="params.email">
            </gp-simple-input>
        </gp-form-col>
        <gp-datepicker cols="12 4" [required]="false" label="Data da Execução" name="date"
            [(ngModel)]="params.exectutionDate">
        </gp-datepicker>
        <gp-form-col cols="12 4">
            <gp-spinner-button type="button" text="Pesquisar" [search]="true" (click)="find()" marginTop="28px">
            </gp-spinner-button>
        </gp-form-col>
    </gp-form-row>
</gp-card>
<gp-card>
    <gp-form-row>
        <gp-form-col cols="12">
            <gp-grid name="error" [rows]="errors" [columns]="['tipo do evento', 'destinatario', 'documento', 'assunto', 'mensagem de erro', 'tentativas']"
                [fields]="['type', 'to', 'document', 'subject', 'error', 'attempts']" [loading]="loading" [showActive]="false"
                [showPagination]="false" [showEdit]="true" [showDelete]="false" (onEdit)="showDetails($event)" emptyMessage="Nenhuma erro encontrado.">
            </gp-grid>
        </gp-form-col>
    </gp-form-row>
</gp-card> 