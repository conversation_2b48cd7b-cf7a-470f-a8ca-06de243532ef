<gp-alert [overlay]="true" #gpAlert></gp-alert>

<gp-card [first]="true">
	<gp-form-row>
		<gp-form-col cols="12 6">
			<gp-simple-input label="Descrição" [required]="true">
				<input type="text" name="description" class="form-control" [(ngModel)]="menu.description" required />
			</gp-simple-input>
		</gp-form-col>

		<gp-form-col cols="12 6">
			<gp-simple-input label="Ativo">
				<div>
					<gp-switch name="active" [(ngModel)]="menu.active"></gp-switch>
				</div>
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<segmentation-parametrization title="Segmentação do Menu" [campaignId]="campaignId"
	[segmentations]="segmentationsConfiguration">
</segmentation-parametrization>

<gp-card title="Menu">
	<spinner [overlay]="true" [show]="loading"></spinner>

	<div>
		<ng-template tabHeading>Árvore de Menus</ng-template>

		<div *ngIf="!hasItems">
			<div>
				<p>Para iniciar a criação do menu, você pode:</p>
				<ul>
					<li>Criar um menu utilizando as configurações atuais da campanha, clicando no botão <strong>Construir Menu da Campanha</strong>.</li>
					<li>Criar os itens manualmente, clicando no botão <strong>Adicionar Item</strong></li>
				</ul>
			</div>

			<gp-spinner-button bootstrapClass="primary" icon="gear"
				text="Construir Menu da Campanha" (click)="createMenuFromCampaignSitePageConfigurations()">
			</gp-spinner-button>

			<gp-spinner-button bootstrapClass="primary" icon="plus" marginLeft="1em"
				text="Adicionar Item" (click)="createNewMenuItemInRoot()">
			</gp-spinner-button>
		</div>

		<div *ngIf="hasItems">
			<div>
				<h4>Instruções:</h4>
				<div>
					<ul>
						<li>Menu de ações: pode ser acessado ao clicar com o botão direito do mouse em algum item do menu;</li>
						<li>O menu da campanha pode ter apenas dois nívels: menu principal e o submenu de um item que está no menu principal;</li>
						<li>Para renomear o texto que é exibido no menu, click com o botão direito do mouse no item e então click em <strong>Alterar texto do item</strong> no menu;</li>
						<li>Para adicionar um item no menu principal, click na opção <strong>Adicionar item no menu principal</strong> no menu de ações;</li>
						<li>Para adicionar um item no submenu, click na opção <strong>Adicionar item no submenu</strong> no menu de ações;</li>
						<li>Para remover um item do menu principal ou do submenu, click na opção <strong>Remove item</strong> no menu de ações;</li>
						<li>As regiões em verde são os lugares que são possíveis arrastar os itens de menu.</li>
					</ul>
				</div>
			</div>

			<hr />

			<gp-form-row>
				<gp-form-col cols="12 6">
					<Tree #tree [nodes]="nodes" [options]="options">
					</Tree>
				</gp-form-col>
			</gp-form-row>
		</div>
	</div>
</gp-card>

<gp-card [last]="true">
	<gp-form-row>
		<gp-form-col cols="12" [inputGroup]="false">
			<gp-spinner-button type="submit" [pink]="true" text="Salvar" pull="right" icon="send"
				[loading]="sending" loadingText="Aguarde" (click)="saveMenu()">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<context-menu #contextMenuItem>
	<ng-template contextMenuItem (execute)="createNewMenuItemInRoot()">
		<div style="height: 30px; line-height: 25px">
			<i class="fa fa-plus"></i> Adicionar item no menu principal
		</div>
	</ng-template>
	<ng-template contextMenuItem (execute)="renameItem($event)">
		<div style="height: 30px; line-height: 25px">
			<i class="fa fa-pencil"></i> Alterar texto do item
		</div>
	</ng-template>
	<ng-template contextMenuItem (execute)="createNewMenuSubitem($event)">
		<div style="height: 30px; line-height: 25px">
			<i class="fa fa-indent"></i> Adicionar item no submenu
		</div>
	</ng-template>
	<ng-template contextMenuItem (execute)="removeItem($event)">
		<div style="height: 30px; line-height: 25px">
			<i class="fa fa-trash"></i> Remove item do menu principal
		</div>
	</ng-template>
</context-menu>

<context-menu #contextMenuSubitem>
	<ng-template contextMenuItem (execute)="renameItem($event)">
		<div style="height: 30px; line-height: 25px">
			<i class="fa fa-pencil"></i> Alterar texto do item
		</div>
	</ng-template>
	<ng-template contextMenuItem (execute)="removeItem($event)">
		<div style="height: 30px; line-height: 25px">
			<i class="fa fa-trash"></i> Remove item do submenu
		</div>
	</ng-template>
</context-menu>

<campaign-custom-menu-item-editor (save)="onCustomItemSave($event)" (error)="onError($event)" #customMenuItemEditor>
</campaign-custom-menu-item-editor>
