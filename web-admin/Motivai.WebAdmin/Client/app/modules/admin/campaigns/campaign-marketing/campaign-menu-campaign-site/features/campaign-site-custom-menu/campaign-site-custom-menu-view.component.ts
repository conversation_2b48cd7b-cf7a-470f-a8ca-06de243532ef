import { Component, ViewChild } from '@angular/core';
import { TabsetComponent } from 'ng2-bootstrap';

import { CampaignMenusSearchEditComponent } from './campaign-site-custom-menu-editor/campaign-site-custom-menu-editor.component';
import { CampaignSiteCustomMenuListComponent } from './campaign-site-custom-menu-list/campaign-site-custom-menu-list.component';

@Component({
  selector: 'campaign-site-custom-menu-view',
  templateUrl: './campaign-site-custom-menu-view.component.html'
})
export class CampaignMainMenuComponent {
  @ViewChild('tabs') tabs: TabsetComponent;
  @ViewChild('list') list: CampaignSiteCustomMenuListComponent;
  @ViewChild('edit') editor: CampaignMenusSearchEditComponent;

  onSelectMenu(event: any) {
    this.tabs.tabs[1].active = true;
    this.editor.findCustomMenuById(event.id);
  }

  clear() {
    this.editor.clear();
  }

  back() {
    this.editor.clear();
    this.tabs.tabs[1].active = false;
    this.tabs.tabs[0].active = true;
  }

  searchMenus() {
    if (this.editor)
      this.editor.clear();
    this.list.getCampaignSiteCustomMenus();
  }
}
