import { CampaignEmailsService } from '../../../services/campaign-emails.service';
import { RxjsHelpers } from '../../../../../../../../shared/helpers/rxjs-helpers';
import { Component, EventEmitter, OnDestroy, OnInit, Output, ViewChild } from "@angular/core";
import { Subscription } from "rxjs";
import { CampaignStore } from "../../../../../campaign.store";
import { BooleanToYesNoPipe } from "../../../../../../../../shared/pipes/boolean-to-yes-no.pipe";
import { FormatHelper } from "../../../../../../../../shared/formatters/format-helper";
import { GpAlertComponent } from "../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component";
import { GpGridComponent } from "../../../../../../../../shared/components/gp-grid/gp-grid.component";
import { getCampaignEventTypeDescription } from '../../../models/campaign-event-types';


@Component({
  selector: 'campaign-emails-notifications-logs-sms-list',
  templateUrl: './campaign-emails-notifications-logs-sms-list.component.html'
})
export class CampaignEmailsNotificationsLogsSmsListComponent implements OnInit, OnDestroy {

  @ViewChild('alert') alert: GpAlertComponent;
  @Output() onEdit: EventEmitter<any> = new EventEmitter<any>();

  @ViewChild('gpGrid') gpGrid: GpGridComponent;


  private _campaign$: Subscription;

  smsLogs: any[] = [];
  campaignId: string = '';
  loading: boolean = false;
  params: any = {
    skip: 0,
    limit: 20
  };

  constructor(private _campaignStore: CampaignStore, private _service: CampaignEmailsService) { }

  ngOnInit(): void {
    this._campaign$ = this._campaignStore.asObservable.subscribe(id => {
      this.campaignId = id;
      this.findSmsNotificationsLogs();
    });
  }

  ngOnDestroy(): void {
    RxjsHelpers.unsubscribe(this._campaign$);
  }

  showSmsLogDetails(event) {
    this.onEdit.emit(event);
  }

  applyFilters() {
    if (this.gpGrid) {
      this.gpGrid.resetPagination();
    }
    this.params.skip = 0;
    this.findSmsNotificationsLogs();
  }

  pageChanged(event) {
    this.params.skip = event.skip;
    this.params.limit = event.limit;
    this.findSmsNotificationsLogs();
  }

  findSmsNotificationsLogs() {
    this.loading = true;
    this._service
      .getCampaignNotificationsSmsLogs(
        this.campaignId,
        this.params.eventType,
        this.params.mobilePhone,
        this.params.from,
        this.params.to,
        this.params.skip,
        this.params.limit
      )
      .subscribe(
        response => {
          this.smsLogs = response;
          if (response) {
            this.smsLogs.forEach(r => {
              r.sentDescription = BooleanToYesNoPipe.toYesNo(r.smsSent);
              r.smsActive = BooleanToYesNoPipe.toYesNo(r.notificationSmsActive);
              r.formattedType = getCampaignEventTypeDescription(r.type);
              r.formattedSentDate = FormatHelper.formatDateWithTimezone(r.smsSentDate);
            });
          }

          this.loading = false;
        },
        err => {
          this.loading = false;
          this.alert.showError(err);
        }
      );
  }


}
