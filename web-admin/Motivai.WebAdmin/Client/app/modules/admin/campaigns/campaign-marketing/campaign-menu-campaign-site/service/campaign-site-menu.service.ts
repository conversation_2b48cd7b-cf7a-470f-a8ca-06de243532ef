import { Injectable } from '@angular/core';

import { ApiService } from '../../../../../../core/api/api.service';

import { Observable } from 'rxjs/Observable';
import { CustomMenuBuilder } from './custom-menu-builder';
import { CustomMenuItem } from '../models/custom-menu';

@Injectable()
export class CampaignSiteMenuService {
  constructor(private _api: ApiService) { }

  getCampaignSiteHeaderLink(campaignId: string): Observable<any> {
    return this._api.get(`/api/campaigns/${campaignId}/site/menu/buttons`);
  }

  saveCampaignSiteHeaderLinks(campaignId: string, menuOptions: any[]): Observable<any[]> {
    return this._api.put(`/api/campaigns/${campaignId}/site/menu/buttons`, menuOptions);
  }

  saveCustomCampaignSiteMenu(campaignId: string, campaignSiteCustomMenu: any): Observable<string> {
    if (campaignSiteCustomMenu.id) {
      return this._api.put(`/api/campaigns/${campaignId}/site/menu/custom/${campaignSiteCustomMenu.id}`, campaignSiteCustomMenu);
    }
    return this._api.post(`/api/campaigns/${campaignId}/site/menu/custom`, campaignSiteCustomMenu);
  }

  getCampaignSiteCustomMenus(campaignId: string): Observable<any[]> {
    return this._api.get(`/api/campaigns/${campaignId}/site/menu/custom`);
  }

  getCampaignSiteCustomMenuById(campaignId: string, id: string): Observable<any> {
    return this._api.get(`/api/campaigns/${campaignId}/site/menu/custom/${id}`);
  }

  getCustomMenuItems(campaignId: string): Observable<Array<CustomMenuItem>> {
    return Observable.zip(
        this.getCustomItemsFromCampaignSitePageSettings(campaignId),
        this.getCustomItemsFromCampaignSiteMenuFeature(campaignId)
      )
      .map(response => {
        if (!response || response.length < 2) {
          return [];
        }
        return response[0].concat(response[1]);
      });
  }

  getCustomItemsFromCampaignSitePageSettings(campaignId: string): Observable<Array<CustomMenuItem>> {
    return this.getSiteSettings(campaignId)
      .map(settings => {
        if (!settings || !settings.pageSettings) {
          return [];
        }
        return CustomMenuBuilder.buildCustomItemsFromCampaignSitePageSettings(settings.pageSettings);
      });
  }

  private getSiteSettings(campaignId: string): Observable<any> {
    return this._api.get(`/api/campaigns/${campaignId}/site/menu/settings`);
  }

  getCustomItemsFromCampaignSiteMenuFeature(campaignId: string): Observable<Array<CustomMenuItem>> {
    return this.getMenuItems(campaignId)
      .map(menuFeatures => {
        if (!menuFeatures || !menuFeatures.length) {
          return [];
        }
        return CustomMenuBuilder.buildCustomItemsFromCampaignSiteMenuFeature(menuFeatures);
      });
  }

  private getMenuItems(campaignId: string): Observable<any[]> {
    return this._api.get(`/api/campaigns/${campaignId}/site/menu/menuitems`);
  }
}
