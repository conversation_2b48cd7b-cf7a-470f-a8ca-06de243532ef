import { Component } from '@angular/core';
import { AuthStore } from '../../../../../core/auth/auth.store';

@Component({
  selector: 'campaign-menu-campaign-site',
  templateUrl: './campaign-menu-campaign-site.component.html'
})
export class CampaignMenuCampaignSiteComponent {
  constructor(private authStore: AuthStore) {}

  get canViewMainMenu() {
    // return this.authStore.role.CAMPAIGNS_MARKETING_SITE_CUSTOM_MENU_VIEW;
    return true;
  }

  get canCreateCampaignSiteHeaderLinks() {
    return this.authStore.role.PERMISSION_CAMPAIGNS_SITE_HEADER_LINKS_CREATE;
  }
}
