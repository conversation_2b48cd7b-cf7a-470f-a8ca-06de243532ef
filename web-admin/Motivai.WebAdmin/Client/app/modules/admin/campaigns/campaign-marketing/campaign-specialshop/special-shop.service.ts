import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { ApiService } from '../../../../../core/api/api.service';

@Injectable()
export class SpecialShopService {
	constructor(private _api: ApiService) { }

	getSpecialShopsByCampaign(campaignId: string, onlyForCatalogMenu: boolean = false): Observable<Array<any>> {
		const params = { onlyForCatalogMenu };
		return this._api.get(`/api/campaigns/${campaignId}/marketing/specialshops`, params);
	}

	getSpecialShopById(campaignId: string, specialShopId: string): Observable<any> {
		return this._api.get(`/api/campaigns/${campaignId}/marketing/specialshops/${specialShopId}`);
	}

	saveSpecialShop(campaignId: string, specialShop: any): Observable<string> {
		if (specialShop.id) {
			return this._api.putWithStaticContent(`/api/campaigns/${campaignId}/marketing/specialshops/${specialShop.id}`, specialShop);
		}
		return this._api.post(`/api/campaigns/${campaignId}/marketing/specialshops`, specialShop);
	}

	removeSpecialShop(campaignId: string, specialShopId: string): Observable<boolean> {
		return this._api.delete(`/api/campaigns/${campaignId}/marketing/specialshops/${specialShopId}`);
	}
}
