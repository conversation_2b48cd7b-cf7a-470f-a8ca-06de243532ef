import { Component, OnInit, ViewChild } from '@angular/core';

import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignGoogleAnalyticsService } from '../../campaign-google-analytics.service';
import { CampaignStore } from '../../campaign.store';

@Component({
  selector: 'campaign-google-analytics',
  templateUrl: 'campaign-google-analytics.component.html'
})
export class CampaignGoogleAnalyticsComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;

  campaignGaUtm: any = {};
  loading: boolean = false;
  sending: boolean = false;

  urlGenerator = {
    originalUrl: '',
    generatedUrl: ''
  };

  constructor(private _campaignService: CampaignGoogleAnalyticsService,
    private _campaignStore: CampaignStore) {}

  get disableButton() {
    return this.loading || this.sending;
  }

  ngOnInit() {
    this.loading = true;
    this._campaignService.loadCampaignGaUtm(this._campaignStore.id)
      .subscribe(
        utm => {
          this.loading = false;
          this.campaignGaUtm = utm || {};
        },
        err => {
          this.loading = false;
          this.gpAlert.handleAndShowError(err);
        }
      );
  }

  generateLink() {
    if (!this.urlGenerator.originalUrl) {
      this.urlGenerator.generatedUrl = '';
      return;
    }
    let url = this.urlGenerator.originalUrl;
    if (this.campaignGaUtm.campaign) {
      url = this.addQueryParam(url, 'utm_campaign', this.campaignGaUtm.campaign);
    }
    if (this.campaignGaUtm.source) {
      url = this.addQueryParam(url, 'utm_source', this.campaignGaUtm.source);
    }
    if (this.campaignGaUtm.medium) {
      url = this.addQueryParam(url, 'utm_medium', this.campaignGaUtm.medium);
    }
    if (this.campaignGaUtm.content) {
      url = this.addQueryParam(url, 'utm_content', this.campaignGaUtm.content);
    }
    if (this.campaignGaUtm.term) {
      url = this.addQueryParam(url, 'utm_term', this.campaignGaUtm.term);
    }
    this.urlGenerator.generatedUrl = url;
  }

  private addQueryParam(url: string, paramName: string, paramValue: string) {
    if (url.indexOf('?') < 0) {
      return `${url}?${paramName}=${paramValue}`;
    } else {
      return `${url}&${paramName}=${paramValue}`;
    }
  }

  saveUtm() {
    if (!this.campaignGaUtm.campaign || !this.campaignGaUtm.source || !this.campaignGaUtm.medium) {
      this.gpAlert.showWarning('Preencha os campos corretamente para prosseguir.');
      return;
    }
    this.sending = true;
    this._campaignService.saveCampaignGaUtm(this._campaignStore.id, this.campaignGaUtm)
      .subscribe(
        result => {
          this.sending = false;
          if (result) {
            this.gpAlert.showSuccess('UTM salvo com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível salvar o UTM, por favor, tente novamente.');
          }
        },
        err => {
          this.sending = false;
          this.gpAlert.handleAndShowError(err);
        }
      );
  }
}
