<gp-alert [overlay]="true" #gpAlert></gp-alert>

<gp-form-validate [formGroup]="formGroup" [validationMessages]="messages" (onSubmit)="saveSpecialShop($event)">
	<gp-card>
		<spinner [overlay]="true" [show]="loading"></spinner>
		<div class="row col-xs-12 bottom-m1">
			<h4>
				<span *ngIf="!isEdition">Cadastrado</span>
				<span *ngIf="isEdition">Alteração</span>
				de Loja Especial
			</h4>
		</div>

		<gp-form-row>
			<gp-input-validate cols="12 6 6" label="Nome" formControlName="name"></gp-input-validate>
			<div grid="12 2 2 2" [group]="true">
				<label>Ativo:</label>
				<div>
					<gp-switch formControlName="active"></gp-switch>
				</div>
			</div>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 6">
				<label>Destacar loja</label>
				<div>
					<gp-switch [ngModelOptions]="{standalone: true}" [(ngModel)]="viewParametrization.showAsFeatured">
					</gp-switch>
				</div>
			</gp-form-col>

			<gp-form-col cols="12 6">
				<gp-simple-input label="Local de apresentação da loja"
					tooltip="Grid: posição customizado no topo da página principal. Card: posição após o grid das lojas especiais, tamanho fixo.">
					<gp-select name="presentationType" [disabled]="!viewParametrization.showAsFeatured"
						[required]="viewParametrization.showAsFeatured" [items]="presentationOptions"
						[(ngModel)]="viewParametrization.presentationType" [ngModelOptions]="{standalone: true}">
					</gp-select>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 6">
				<gp-simple-input label="Posição" tooltip="Ordem para ser exibido na listagem das lojas especiais.">
					<gp-input-mask name="position" [onlyInteger]="true" [disabled]="!viewParametrization.showAsFeatured"
						[(ngModel)]="viewParametrization.gridItemPosition" [ngModelOptions]="{standalone: true}">
					</gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6">
				<gp-simple-input label="Tamanho">
					<gp-select name="gridItemSize" [disabled]="!viewParametrization.showAsFeatured"
						[required]="viewParametrization.showAsFeatured" [items]="gridSizeItems"
						[(ngModel)]="viewParametrization.gridItemSize" [ngModelOptions]="{standalone: true}">
					</gp-select>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 3">
				<label>Exibir no menu do Catálago</label>
				<div>
					<gp-switch [ngModelOptions]="{standalone: true}" [(ngModel)]="viewParametrization.showAtCatalogMenu">
					</gp-switch>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 3">
				<label>Abrir em uma nova aba</label>
				<div>
					<gp-switch [ngModelOptions]="{standalone: true}" [(ngModel)]="viewParametrization.openInNewTab">
					</gp-switch>
				</div>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card title="Conteúdo da Loja">
		<gp-form-row>
			<gp-form-col cols="12 6">
				<p>Formatos de conteúdos permitidos na loja especial, em ordem de prioridade:</p>
				<ul>
					<li>Link de site</li>
					<li>Conteúdo HTML</li>
					<li>Listagem de produtos do catálogo</li>
				</ul>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-input-validate cols="12 6 6" label="URL da página" formControlName="pageLinkUrl">
			</gp-input-validate>
		</gp-form-row>

		<accordion>
			<accordion-group panelClass="b0 mb-sm panel-default">
				<div accordion-heading>
					<em class="fa fa-navicon"></em>
					<label>Página Dinâmica</label>
				</div>
				<gp-form-row>
					<gp-form-col cols="12">
						<gp-editor id="pageContent" name="pageContent" formControlName="pageContent"></gp-editor>
					</gp-form-col>
				</gp-form-row>
			</accordion-group>
		</accordion>
	</gp-card>

	<gp-form-row>
		<gp-form-col cols="12 12">
			<gp-card>
				<div class="row col-xs-12">
					<label>Selecione a imagem do link (Resolução ideal: {{ bannerLinkImageIdealResolution }})*</label>
				</div>
				<div class="row" grid="12 12" [group]="true">
					<gp-fileupload name="banner" label="Selecione a imagem de link" [path]="linkBannerPath" [images]="true"
						(oncomplete)="onComplete($event)" #gpFileLink></gp-fileupload>
				</div>
				<div grid="12 12 6">
					<img class="banner" defaultImage="/assets/img/img-loader.gif" [lazyLoad]="linkBannerUrl"
						title="Banner do link" *ngIf="linkBannerUrl" />
				</div>
			</gp-card>
		</gp-form-col>
	</gp-form-row>

	<gp-form-row>
		<gp-form-col>
			<gp-card>
				<div class="row col-xs-12">
					<label>Selecione a imagem do banner de tamanho grande (Largura ideal: 1680px)</label>
				</div>
				<div class="row" grid="12 12" [group]="true">
					<gp-fileupload name="banner" label="Selecione o banner principal" [path]="principalBannerPath" [images]="true"
						(oncomplete)="onComplete($event)" #gpFilePrincipal></gp-fileupload>
				</div>
				<div grid="12 12 6">
					<img class="banner" defaultImage="/assets/img/img-loader.gif" [lazyLoad]="principalBannerUrl"
						title="Banner principal" *ngIf="principalBannerUrl" />
				</div>
			</gp-card>
		</gp-form-col>

		<gp-form-col>
			<gp-card>
				<div class="row col-xs-12">
					<label>Selecione a imagem do banner de tamanho pequeno (Largura ideal: 600px)</label>
				</div>
				<div class="row" grid="12 12" [group]="true">
					<gp-fileupload name="banner" label="Selecione o banner de tamanho pequeno" [path]="smallBannerPath"
						[images]="true" (oncomplete)="onComplete($event)" #gpFileSmall></gp-fileupload>
				</div>
				<div grid="12 12 6">
					<img class="banner" defaultImage="/assets/img/img-loader.gif" [lazyLoad]="smallBannerUrl"
						title="Banner do link" *ngIf="smallBannerUrl" />
				</div>
			</gp-card>
		</gp-form-col>
	</gp-form-row>

	<gp-card title="Regras para Exibição" *ngIf="!loading">
		<div class="row">
			<div class="col-md-12">
				<h4>Período</h4>
			</div>
			<gp-datepicker cols="12 4" label="Data de Início" formControlName="startDate"></gp-datepicker>
			<gp-timepicker cols="12 2" label="Hora (de)" name="startTime" formControlName="startTime"></gp-timepicker>

			<gp-datepicker cols="12 4" label="Data de Término" formControlName="endDate"></gp-datepicker>
			<gp-timepicker cols="12 2" label="Hora (até)" name="endTime" formControlName="endTime"></gp-timepicker>
		</div>
		<br>
		<gp-form-row>
			<campaign-participants-group-selector cols="12 4" [(ngModel)]="participantsGroups" [multiple]="true"
				[name]="participantsGroups" [ngModelOptions]="{standalone: true}">
			</campaign-participants-group-selector>
		</gp-form-row>
	</gp-card>

	<gp-card title="Produtos Vinculados" *ngIf="!loading">
		<div class="row">
			<gp-form-validate [formGroup]="formSku" [validationMessages]="skuMessages" (onSubmit)="addSku($event)">
				<gp-select-validate cols="12 12 4 3" label="Parceiro" [searchable]="true" [items]="campaignPartners"
					placeholder="Parceiro" formControlName="partnerId">
				</gp-select-validate>
				<gp-input-validate cols="12 4 3 2" label="SKU" formControlName="sku" (blur)="searchSku()">
				</gp-input-validate>
				<div [group]="true" grid="12 8 6 5" class="top-p2">
					<span class="product-name">{{product ? product.name : 'Selecione o parceiro e digite o SKU'}}</span>
				</div>
				<div class="pull-right top-p2" grid="12 4 4 2">
					<gp-spinner-button type="submit" text="Adicionar filtro" bootstrapClass="success"
						[disabled]="!isSkuValid() || disableButtons" [pink]="true" icon="plus" [loading]="sending"
						loadingText="Adicionando"></gp-spinner-button>
				</div>
			</gp-form-validate>
		</div>
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-grid name="skusGrid" [rows]="skus" [columns]="['Parceiro', 'Produto', 'SKU']"
					[fields]="['partnerName', 'name', 'skuCode']" [showActive]="false" [showPagination]="false" [showEdit]="false"
					[showDelete]="true" [loading]="false" (onDelete)="removeSku($event)">
				</gp-grid>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card [last]="true">
		<gp-form-row>
			<gp-form-col cols="12" [inputGroup]="false">
				<button type="button" class="btn btn-default" [routerLink]="['/campanha', campaignId, 'marketing', 'lojas']">
					<i class="fa fa-arrow-left"></i>
					Voltar
				</button>

				<gp-spinner-button type="submit" text="Salvar" bootstrapClass="success" loadingText="Salvando" pull="right"
					[pink]="true" icon="send" [disabled]="disableButtons || !isValid()" [loading]="sending">
				</gp-spinner-button>

				<gp-spinner-button text="Nova" bootstrapClass="primary" icon="plus" pull="right" marginRight="1em"
					(click)="newSpecialShop()" [disabled]="disableButtons">
				</gp-spinner-button>

			</gp-form-col>
		</gp-form-row>
	</gp-card>
</gp-form-validate>