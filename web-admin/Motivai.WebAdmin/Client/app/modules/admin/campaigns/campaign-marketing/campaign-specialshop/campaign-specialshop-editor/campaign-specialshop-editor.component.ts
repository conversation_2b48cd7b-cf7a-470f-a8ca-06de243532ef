import { Component, ViewChild, OnInit } from '@angular/core';
import { FormGroup, Validators, FormBuilder } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { CustomValidators } from 'ng2-validation';

import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpFileUploadComponent } from '../../../../../../shared/components/gp-fileupload/gp-fileupload.component';
import { CampaignService } from '../../../campaign.service';
import { CompanyService } from '../../../../companies/company/company.service';
import { Item } from '../../../../../../shared/models/item';
import { SpecialShopService } from '../special-shop.service';
import { TargetAudienceService } from '../../../campaign-crm/campaign-crm-target-audience/targetAudience.service';
import { DateHelper } from '../../../../../../shared/helpers/date-helper';

@Component({
  selector: 'campaign-specialshop-editor',
  templateUrl: 'campaign-specialshop-editor.component.html',
  styles: [`
    .product-name {
      font-size: 1.3em;
    }
    .banner {
      max-height: 250px;
      max-width: 400px;
    }
  `]
})
export class CampaignSpecialShopEditorComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;

  @ViewChild('gpFilePrincipal') gpFilePrincipal: GpFileUploadComponent;
  @ViewChild('gpFileSmall') gpFileSmall: GpFileUploadComponent;

  @ViewChild('gpFileLink') gpFileLink: GpFileUploadComponent;

  principalBannerPath: string = '';
  linkBannerPath: string = '';
  smallBannerPath: string = '';
  campaignPartners: Array<any> = [];
  presentationOptions: Array<Item> = [Item.of('Grid', 'Grid'),Item.of('Card', 'Card')];
  gridSizeItems: Array<Item> = [
    Item.of('25', '25% (260x250)'),
    Item.of('33', '33% (356x250)'),
    Item.of('50', '50% (552x250)'),
    Item.of('66', '66% (745x250)'),
    Item.of('75', '75% (842x250)'),
    Item.of('100', '100% (1135x250)'),
  ];

  campaignId: string;
  specialShopId?: string = undefined;
  principalBannerUrl?: string = undefined;
  linkBannerUrl?: string = undefined;
  smallBannerUrl?: string = undefined;
  skus: Array<any> = [];
  product?: any = undefined;

  availableTargetsAudiences: Array<Item> = [];
  dbTargetAudiences: Array<string> = [];
  targetAudiences: Array<any> = [];

  participantsGroups: Array<Item> = [];
  viewParametrization: any = {};

  formGroup: FormGroup;
  messages = {
    name: { required: 'Nome é obrigatório' },
    active: { required: 'Situação é obrigatória' },
    startDate: {
      required: 'Data de início é obrigatória',
      date: 'Data de início inválida'
    },
    endDate: {
      required: 'Data de término é obrigatória',
      date: 'Data de término inválida'
    },
  };

  formSku: FormGroup;
  skuMessages = { partnerId: { required: 'Parceiro é obrigatório' }, sku: { required: 'Código do SKU é obrigatório' } };

  loading: boolean = false;
  sending: boolean = false;
  sendingSku: boolean = false;
  uploading: boolean = false;

  constructor(private _campaignService: CampaignService, private _companyService: CompanyService,
      private _specialShopServiec: SpecialShopService, private route: ActivatedRoute
      , fb: FormBuilder, private targetAudienceService: TargetAudienceService) {
    this.formGroup = fb.group({
      name: ['', Validators.required],
      active: [false, Validators.required],
      startDate: [null, Validators.compose([Validators.required, CustomValidators.date('dd/mm/yyyy')])],
      startTime: [false, ''],
      endDate: [null, Validators.compose([Validators.required, CustomValidators.date('dd/mm/yyyy')])],
      endTime: [false, ''],
      targetAudience: [null],
      pageLinkUrl: [''],
      pageContent: [''],
      viewParametrization: {}
    });
    this.formSku = fb.group({
      partnerId: ['', Validators.required],
      sku: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    this.loading = true;
    if (this.route.parent != null && this.route.parent.parent != null) {
      this.route.parent.parent.params.subscribe((params: any) => {
        this.campaignId = params['id'];
        this.route.params.subscribe((params: any) => {
          this.specialShopId = params['specialShopId'];
          if (this.specialShopId) {
            this.initGpFiles();
            this.loadSpecialShop();
          } else {
            this.loading = false;
          }
        });
        this.loadPartners();
      });
    }
  }

  get bannerLinkImageIdealResolution() {
    if (this.viewParametrization.showAsFeatured && this.viewParametrization.presentationType == 'Grid') {
      if (this.viewParametrization.gridItemSize) {
        switch (this.viewParametrization.gridItemSize) {
          case '3': return '1200x350';
          case '2': return '900x350';
          case '1': return '450x450';
        }
      }
    }
    return '400x400';
  }

  get isEdition() {
    return this.campaignId && this.specialShopId;
  }

  get hasAtLeastSelectedOneBanner() {
    return this.hasSelectedPrincipalBanner
      || this.hasSelectedSmallBanner;
  }

  get hasSelectedPrincipalBanner() {
    return this.gpFilePrincipal && this.gpFilePrincipal.hasSelectedFile();
  }

  get hasPrincipalBanner() {
    return this.hasSelectedPrincipalBanner || this.principalBannerUrl;
  }

  get hasSelectedSmallBanner() {
    return this.gpFileSmall && this.gpFileSmall.hasSelectedFile();
  }

  get hasSmallBanner() {
    return this.hasSelectedSmallBanner || this.smallBannerUrl;
  }

  get hasSelectedLinkBanner() {
    return this.gpFileLink && this.gpFileLink.hasSelectedFile();
  }

  get hasLinkBanner() {
    return this.hasSelectedLinkBanner || this.linkBannerUrl;
  }

  get disableButtons() {
    return this.sending || this.uploading || this.sendingSku;
  }

  private loadPartners() {
    this._campaignService.getLinkedPartnersDataByCampaign(this.campaignId)
      .subscribe(
        linkedPartners => {
          this.campaignPartners = linkedPartners.map(part => {
            return {id: part.partnerId, text: part.partnerName};
          });
        },
        err => this.handleError(err)
      );
  }

  private handleError(err) {
    let errorMessage = (err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao efetuar a operação.');
    this.gpAlert.showError(errorMessage);
    this.loading = false;
    this.sending = false;
  }

  private initGpFiles() {
    this.principalBannerPath = `api/campaigns/${this.campaignId}/marketing/specialshops/${this.specialShopId}/principalbannerimage`;
    if (this.gpFilePrincipal)
      this.gpFilePrincipal.path = this.principalBannerPath;
    this.linkBannerPath = `api/campaigns/${this.campaignId}/marketing/specialshops/${this.specialShopId}/linkbannerimage`;
    if (this.gpFileLink)
      this.gpFileLink.path = this.linkBannerPath;
    this.smallBannerPath = `api/campaigns/${this.campaignId}/marketing/specialshops/${this.specialShopId}/bannersmallimage`;
    if (this.gpFileSmall)
      this.gpFileSmall.path = this.smallBannerPath;
  }

  newSpecialShop() {
    this.gpAlert.clear();
    this.specialShopId = undefined;
    this.principalBannerUrl = undefined;
    this.linkBannerUrl = undefined;
    this.smallBannerUrl = undefined;
    this.skus = [];
    this.product = undefined;
    this.formGroup.patchValue({
      startDate: null,
      endDate: null
    });
    this.formGroup.reset();
    this.gpFilePrincipal.removeAll();
    this.gpFileLink.removeAll();
    this.gpFileSmall.removeAll();
    this.viewParametrization = {};
  }

  private loadSpecialShop() {
    if (!this.specialShopId) return;
    this.loading = true;
    this._specialShopServiec.getSpecialShopById(this.campaignId, this.specialShopId)
      .subscribe(
        specialShop => {
          if (specialShop) {
            if (specialShop.startDate != null) {
              specialShop.startTime = new Date(specialShop.startDate);
            }
            if (specialShop.endDate != null) {
              specialShop.endTime = new Date(specialShop.endDate);
            }

            this.formGroup.patchValue(specialShop);

            if (specialShop.principalBannerImageUrl)
              this.principalBannerUrl = specialShop.principalBannerImageUrl + '/250';
            else
              this.principalBannerUrl = undefined;

            if (specialShop.linkBannerImageUrl)
              this.linkBannerUrl = specialShop.linkBannerImageUrl + '/250';
            else
              this.linkBannerUrl = undefined;

            if (specialShop.bannerSmallImageUrl)
              this.smallBannerUrl = specialShop.bannerSmallImageUrl + '/250';
            else
              this.smallBannerUrl = undefined;

            if (specialShop.skus && specialShop.skus.length) {
              this.skus = specialShop.skus;
            } else {
              this.skus = [];
            }

            if (specialShop.participantsGroups) {
              this.participantsGroups = specialShop.participantsGroups;
            }
            if (specialShop.viewParametrization != null) {
              this.viewParametrization = specialShop.viewParametrization;
            }
          } else {
            this.specialShopId = undefined;
            this.gpAlert.showWarning('Loja especial não encontrada.');
          }
          this.loading = false;
        },
        err => this.handleError(err)
      );
  }

  removeTargetAudience(targetAudience) {
    if (!targetAudience || !targetAudience.id) return;
    this.targetAudiences = this.targetAudiences.filter(t => t.id != targetAudience.id);
  }

  addTargetAudience() {
    let ctrl = this.formGroup.get('targetAudience');
    if (ctrl) {
      let id = ctrl.value;
      if (id) {
        let selectedTarget = this.availableTargetsAudiences.find(t => t.id == id);
        if (selectedTarget && this.targetAudiences.findIndex(t => t.id == id) < 0) {
          this.targetAudiences.push(selectedTarget);
        }
      }
      ctrl.setValue(null);
    }
  }

  isValid() {
    return this.formGroup.valid && (this.hasSelectedLinkBanner || this.hasLinkBanner);
  }

  saveSpecialShop(event) {
    if (!event.valid) {
      this.gpAlert.showWarning('Preencha os campos corretamente para continuar.');
      return;
    }
    this.sending = true;
    this.gpAlert.clear();
    let specialShop = event.model;
    if (this.specialShopId) {
      specialShop.id = this.specialShopId;
    }
    if (this.skus)
      specialShop.skus = this.skus.map(s => s.skuId);
    if (this.participantsGroups != null) {
      specialShop.participantsGroups = this.participantsGroups;
    } else {
      specialShop.participantsGroups = null;
    }

    if (this.viewParametrization != null) {
       specialShop.viewParametrization = this.viewParametrization;
    }

    if (specialShop.startTime) {
      specialShop.startDate = DateHelper.setTimeAndConvertToUTC(specialShop.startDate, specialShop.startTime);
    }

    if (specialShop.endTime) {
      specialShop.endDate = DateHelper.setTimeAndConvertToUTC(specialShop.endDate, specialShop.endTime);
    }

    this._specialShopServiec.saveSpecialShop(this.campaignId, specialShop)
      .subscribe(
        result => {
          if (result) {
            if (!this.specialShopId) {
              this.specialShopId = result;
            }
            this.initGpFiles();
            if (this.hasSelectedPrincipalBanner || this.hasSelectedLinkBanner || this.hasSelectedSmallBanner) {
              this.gpAlert.showSuccess('Loja especial salva com sucesso. Aguarde o upload das imagens.');
              this.uploadFiles();
            } else {
              this.gpAlert.showSuccess('Loja especial salva com sucesso.');
              this.sending = false;
            }
          } else {
            this.sending = false;
            this.gpAlert.showWarning('Não foi possível salvar a loja especial, por favor, tente novamente.');
          }
        },
        err => this.handleError(err)
      );
  }

  uploadFiles() {
    if (!this.hasSelectedPrincipalBanner && !this.hasSelectedLinkBanner && !this.hasSelectedSmallBanner) {
      this.gpAlert.showWarning('Selecione um arquivo para upload.');
      this.sending = false;
      return;
    }
    if (this.hasSelectedPrincipalBanner) {
      this.uploading = true;
      this.gpFilePrincipal.uploadFile();
    }
    if (this.hasSelectedLinkBanner) {
      this.uploading = true;
      this.gpFileLink.uploadFile();
    }
    if (this.hasSelectedSmallBanner) {
      this.uploading = true;
      this.gpFileSmall.uploadFile();
    }
  }

  onComplete(event) {
    this.gpAlert.clear();
    if (event.success) {
      this.gpAlert.showSuccess('Arquivo enviado com sucesso.');
    } else {
      this.gpAlert.showError(event.errorMessage);
    }
    this.sending = false;
    this.uploading = false;
  }

  isSkuValid() {
    return this.product && this.product.skuId;
  }

  searchSku() {
    this.gpAlert.clear();
    this.product = null;
    let partnerId = '', skuCode = '';
    let partnerControl = this.formSku.get('partnerId');

    if (partnerControl)
      partnerId = partnerControl.value;
    let skuControl = this.formSku.get('sku');
    if (skuControl)
      skuCode = skuControl.value;
    if (!partnerId || !skuCode) {
      return;
    }
    this.product = { name: 'Pesquisando...' };
    this._companyService.findPartnerProductBySkuCode(partnerId, skuCode)
      .subscribe(
        product => {
          if (product) {
            this.product = product;
          } else {
            this.gpAlert.showWarning('Produto não encontrado pelo SKU informado.');
            this.product = null;
          }
        },
        err => this.handleError(err)
      );
  }

  addSku(event) {
    if (!this.isSkuValid()) {
      this.gpAlert.showWarning('Preencha os campos para continuar.');
      return;
    }
    if (this.skus.findIndex(s => s.partnerId == this.product.partnerId && s.skuId == this.product.skuId) >= 0) {
      this.gpAlert.showWarning('SKU deste parceiro já está na lista.');
      this.product = undefined;
      this.formSku.reset();
      return;
    }
    this.skus.push(this.product);
    this.product = undefined;
    this.formSku.reset();
  }

  removeSku(sku) {
    if (!confirm(`Deseja excluir o SKU do produto '${sku.name}' do parceiro '${sku.partnerName}'?`)) return;
    this.skus = this.skus.filter(s => s.partnerId == sku.partnerId && s.skuId != sku.skuId);
  }
}
