import { CampaignFeatureTypes } from '../../../../../../shared/components/business/campaigns/campaign-feature-selector/models/campaign-feature-types.enum';
import { AuthenticationStates, FeatureTypes } from './features-types';
import { CustomMenuItem } from './custom-menu';
import { SegmentationsConfiguration } from '../../../../../../shared/components/business/campaigns/segmentation-parametrization/segmentations-configuration';

export class CustomItem {
  featureType: FeatureTypes | CampaignFeatureTypes;
  featureId: string;
  itemText: string;
  authenticationState: AuthenticationStates;
  segmentations: SegmentationsConfiguration;

  static empty(): CustomItem {
    return {
      authenticationState: AuthenticationStates.BOTH,
    } as CustomItem;
  }
}

export function buildCustomMenuItem(customItem: CustomItem): CustomMenuItem {
  return {
    featureType: customItem.featureType,
    featureId: customItem.featureId,
    itemText: customItem.itemText,
    authenticationState: customItem.authenticationState,
    segmentations: customItem.segmentations,
  } as CustomMenuItem;
}
