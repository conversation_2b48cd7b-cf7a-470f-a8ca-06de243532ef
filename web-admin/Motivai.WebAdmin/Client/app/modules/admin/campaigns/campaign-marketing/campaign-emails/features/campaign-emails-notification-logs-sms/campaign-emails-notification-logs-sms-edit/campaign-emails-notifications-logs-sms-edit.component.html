<gp-alert #alert [overlay]="true"></gp-alert>

<gp-card [first]="true">
    <gp-form-row>
        <gp-form-col cols="12 6">
            <label>Tipo: </label>
            <span>{{ smsLog.formattedType || '' }}</span>
        </gp-form-col>
        <gp-form-col cols="12 6">
            <label>Celular: </label>
            <span>{{ smsLog.mobilePhone || '' }}</span>
        </gp-form-col>
    </gp-form-row>
    <gp-form-row *ngIf="canShowSmsContent">
        <gp-form-col cols="12">
            <label>Conteudo SMS: </label>
            <span>{{ smsLog.smsContent || '' }}</span>
        </gp-form-col>
    </gp-form-row>
    <gp-form-row *ngIf="canResendNotification">
        <gp-form-col cols="12">
            <gp-spinner-button type="button" bootstrapClass="primary" icon="send" text="Reenviar" pull="right"
                loadingText="Processando" [loading]="loading" (click)="resendSmsNotification()">
            </gp-spinner-button>
        </gp-form-col>
    </gp-form-row>
</gp-card>
