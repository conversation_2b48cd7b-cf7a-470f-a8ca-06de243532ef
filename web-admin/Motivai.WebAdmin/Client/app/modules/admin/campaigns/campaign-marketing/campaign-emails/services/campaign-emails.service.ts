import { Injectable } from '@angular/core';
import moment = require('moment');

import { ApiService } from '../../../../../../core/api/api.service';

@Injectable()
export class CampaignEmailsService {

    constructor(private _api: ApiService) { }

    getCampaignNotificationsEmailsLogs(campaignId: string, email: string, emailType: string, from: string, to: string, skip: number, limit: number) {
        const params: any = {};

        if (email) {
            params.email = email;
        }

        if(emailType) {
            params.eventType = emailType;
        }

        if (from) {
            params.from = moment(from).format('YYYY-MM-DD');
        }

        if (to) {
            params.to = moment(to).format('YYYY-MM-DD');
        }

        params.skip = skip || 0;
        params.limit = limit || 10;

        return this._api.get(`/api/campaigns/${campaignId}/notifications/emails`, params)
    }

    getCampaignNotificationsSmsLogs(campaignId: string, eventType: string, mobilePhone: string,  from: string, to: string, skip: number, limit: number){
        let params: any = {}

        params.smsOnly = true;

        if(mobilePhone) {
            params.mobilePhone = mobilePhone;
        }

        if(eventType) {
            params.eventType = eventType;
        }

        if(from) {
            params.from = moment(from).format('YYYY-MM-DD');
        }

        if(to) {
            params.to = moment(to).format('YYYY-MM-DD');
        }

        params.skip = skip || 0;
        params.limit = limit || 10;

        return this._api.get(`/api/campaigns/${campaignId}/notifications/emails`, params)
    }


    resendNotification(campaignId: string, notification: any) {
        return this._api.post(`/api/campaigns/${campaignId}/notifications/emails/resend`, notification);
    }

    getCampaignNotificationsEmailsLDetailsById(campaignId: string, logId: string) {
        return this._api.get(`/api/campaigns/${campaignId}/notifications/emails/${logId}`);
    }

    resendSmsNotification(campaignId: string, logId: string) {
        return this._api.post(`/api/campaigns/${campaignId}/notifications/emails/${logId}/sms/resend`, null);
    }
}
