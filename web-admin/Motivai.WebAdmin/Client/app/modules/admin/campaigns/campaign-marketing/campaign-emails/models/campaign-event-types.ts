import { compareStrings } from '../../../../../../shared/helpers/comparators';
import { Item } from '../../../../../../shared/models/item';

const CAMPAIGN_EVENT_TYPES = [
	Item.of('Participant', 'Novo Participante'),
	Item.of('Login', 'Login'),
	Item.of('FirstAccess', 'Primeiro Acesso'),
	Item.of('Points', 'Pontos Creditados'),
	Item.of('PointsDistributedNewParticipant', 'Distribuição de Pontos para Novo Participante'),
	Item.of('TokenIssue', 'Códigos de Segurança'),
	Item.of('PasswordRecovery', 'Recuperação de Senha'),
	Item.of('PointsPurchaseReceived', 'Pedido de Compra de Pontos'),
	Item.of('Order', 'Pedido para o Participante'),
	Item.of('MarketplaceOrderWithService', 'Pedido de Serviço para o Participante'),
	Item.of('RechargeOrderReceived', 'Pedido de Recarga de Celular'),
	Item.of('BillPaymentOrderReceived', 'Pedido de Pague Contas'),
	Item.of('PrepaidCardOrderReceived', 'Cartão Pré-Pago: Pedido Recebido'),
	Item.of('BankTransferOrderReceived', 'Pedido de Transferência Bancária Recebido'),
	Item.of('BankTransferOrderTransfered', 'Pedido de Transferência Bancária - Transferência Efetuada'),
	Item.of('OrderRefund', 'Estorno do Pedido'),
	Item.of('OrderCancellation', 'Cancelamento do Pedido'),
	Item.of('OrderPricing', 'Precificação de Pedido'),
	Item.of('PartnerOrder', 'Pedido para o Parceiro'),
	Item.of('OrderItemEvent', 'Tracking de Item do Pedido'),
	Item.of('OrderVirtualItemEvent', 'Link de Vale Virtual do Pedido'),
	// Item.of('OrderTracking', 'Situação de Entrega de Pedido'),
	// Item.of('OrderDelivery', 'Pedido Entregue'),
	Item.of('ProductAvailable', 'Produto Disponível'),
	Item.of('ExpiringPoints', 'Pontos a Expirar'),
	Item.of('FeatureApproval', 'Aprovação de Feature'),
	Item.of('PaymentMethodConfirmation', 'Notificação Método de Pagamento')
];

export function getCampaignEventTypes() {
	return CAMPAIGN_EVENT_TYPES.sort((a, b) => compareStrings(a.text, b.text));
}

export function getCampaignMailVariablesByEventType() {
	return {
		Participant: [
			{ var: 'NOMECAMPANHA', description: 'Nome da campanha' },
			{ var: 'URLCAMPANHA', description: 'URL do catálogo' },
			{ var: 'URLSITECAMPANHA', description: 'URL do site campanha' },
			{ var: 'URLACESSOCAMPANHA', description: 'URL principal de acesso à campanha' },
			{ var: 'NOMEPARTICIPANTE', description: 'Nome do novo participante' },
			{ var: 'USUARIO', description: 'Login do novo participante' },
			{ var: 'SENHA', description: 'Senha temporária do novo participante' }
		],
		Points: [
			{ var: 'NOMECAMPANHA', description: 'Nome da campanha' },
			{ var: 'URLCAMPANHA', description: 'URL do catálogo' },
			{ var: 'URLSITECAMPANHA', description: 'URL do site campanha' },
			{ var: 'URLACESSOCAMPANHA', description: 'URL principal de acesso à campanha' },
			{ var: 'NOMEPARTICIPANTE', description: 'Nome do participante' },
			{ var: 'DESCRICAOLOTE', description: 'Descrição do lote' },
			{ var: 'TOTALPONTOS', description: 'Total de pontos que o participante recebeu' },
			{ var: 'TOTALPARADISTRIBUIR', description: 'Total de pontos que o participante deve distribuir' },
			{ var: 'TOTALPARAUSAR', description: 'Total de pontos que o participante pode user' },
			{ var: 'EXTRATO', description: 'Um resumo do extrato do participante' },
			{ var: 'TABELADEOFERTAS', description: 'Até 3 promoções do clube de benefícios' },
		],
		PasswordRecovery: [
			{ var: 'NOMECAMPANHA', description: 'Nome da campanha' },
			{ var: 'NOMEPARTICIPANTE', description: 'Nome do participante' },
			{ var: 'USUARIO', description: 'Login do novo participante' },
			{ var: 'NOVASENHA', description: 'Senha gerada' },
			{ var: 'URLACESSOCAMPANHA', description: 'URL principal de acesso à campanha' },
			{ var: 'URLCAMPANHA', description: 'URL do catalogo da campanha' },
			{ var: 'URLSITECAMPANHA', description: 'URL do site campanha' }
		],
		Order: [
			{ var: 'NUMEROPEDIDO', description: 'Número do pedido gerado' },
			{ var: 'NOMEPARTICIPANTE', description: 'Nome do participante' },
			{ var: 'ENDERECOENTREGA', description: 'Endereço de entrega' },
			{ var: 'TABELAITENS', description: 'Tabela com os itens do pedido' },
			{ var: 'VALORFRETE', description: 'Valor do frete' },
			{ var: 'VALORTOTAL', description: 'Valor total do pedido (frete + produtos)' },
			{ var: 'URLCAMPANHA', description: 'URL do catalogo campanha' },
			{ var: 'URLSITECAMPANHA', description: 'URL do site campanha' }
		],
		PartnerOrder: [
			{ var: 'NUMEROPEDIDO', description: 'Número do pedido gerado' },
			{ var: 'NOMEDESTINATARIO', description: 'Nome do destinatário do e-mail' },
			{ var: 'NOMERECEBEDOR', description: 'Nome do recebedor do(s) produto(s)' },
			{ var: 'ENDERECOENTREGA', description: 'Endereço de entrega' },
			{ var: 'TABELAITENS', description: 'Tabela com os itens do pedido' },
		],
		OrderTracking: [],
		OrderDelivery: [],
		OrderItemEvent: [
			{ var: 'NOMEPARTICIPANTE', description: 'Nome do participante' },
			{ var: 'NUMEROPEDIDO', description: 'Número do pedido' },
			{ var: 'URLCAMPANHA', description: 'URL do catálogo da campanha' },
			{ var: 'NOMECAMPANHA', description: 'Nome da campanha' },
			{ var: 'CODIGOSKU', description: 'Código SKU do item do pedido' },
			{ var: 'NOMEPRODUTO', description: 'Nome do produto' },
			{ var: 'STATUSITEM', description: 'Status do item do pedido' },
			{ var: 'COMUNICACAO', description: 'Mensagem de comunicação' },
			{ var: 'TIPOCOMUNICACAO', description: 'Tipo da comunicação (Informação, Entrega, etc)' },
			{ var: 'DATA', description: 'Data da comunicação' }
		],
		OrderVirtualItemEvent: [
			{ var: 'NOMEPARTICIPANTE', description: 'Nome do participante' },
			{ var: 'NUMEROPEDIDO', description: 'Número do pedido' },
			{ var: 'URLCAMPANHA', description: 'URL do catálogo da campanha' },
			{ var: 'NOMECAMPANHA', description: 'Nome da campanha' },
			{ var: 'TABELALINKS', description: 'Tabela com os links dos vales' },
			{ var: 'LINKVALE', description: 'Link do vale virtual (somente SMS)' },
		],
		OrderRefund: [
			{ var: 'NOMEPARTICIPANTE', description: 'Nome do participante' },
			{ var: 'NUMEROPEDIDO', description: 'Número do pedido' },
			{ var: 'URLCAMPANHA', description: 'URL do catálogo' },
			{ var: 'URLSITECAMPANHA', description: 'URL do site campanha' },
			{ var: 'URLACESSOCAMPANHA', description: 'URL principal de acesso à campanha' },
			{ var: 'NOMECAMPANHA', description: 'Nome da campanha' },
			{ var: 'DATA', description: 'Data do estorno' }
		],
		OrderCancellation: [
			{ var: 'NOMEPARTICIPANTE', description: 'Nome do participante' },
			{ var: 'NUMEROPEDIDO', description: 'Número do pedido' },
			{ var: 'URLCAMPANHA', description: 'URL do catálogo' },
			{ var: 'URLSITECAMPANHA', description: 'URL do site campanha' },
			{ var: 'URLACESSOCAMPANHA', description: 'URL principal de acesso à campanha' },
			{ var: 'NOMECAMPANHA', description: 'Nome da campanha' },
			{ var: 'DATA', description: 'Data do cancelamento' },
			{ var: 'MOTIVO', description: 'Motivo do cancelamento' }
		],
		OrderPricing: [
			{ var: 'NOMEPARTICIPANTE', description: 'Nome do participante' },
			{ var: 'NUMEROPEDIDO', description: 'Número do pedido' },
			{ var: 'URLCAMPANHA', description: 'URL do catálogo da campanha' },
			{ var: 'NOMECAMPANHA', description: 'Nome da campanha' },
			{ var: 'DATA', description: 'Data da precificação' }
		],
		ProductAvailable: [
			{ var: 'NOMEPRODUTO', description: 'Nome do produto' },
			{ var: 'LINKPRODUTO', description: 'URL para acessar o produto no catálogo' },
			{ var: 'NOMEPARTICIPANTE', description: 'Nome do novo participante' }
		],
		TokenIssue: [
			{ var: 'NOMECAMPANHA', description: 'Nome da campanha' },
			{ var: 'URLSITECAMPANHA', description: 'URL do site campanha' },
			{ var: 'URLCAMPANHA', description: 'URL do catálogo da campanha' },
			{ var: 'NOMEPARTICIPANTE', description: 'Nome do participante' },
			{ var: 'TOKEN', description: 'Código de segurança' }
		],
		PrepaidCardOrderReceived: [
			{ var: 'NOMECAMPANHA', description: 'Nome da campanha' },
			{ var: 'URLCAMPANHA', description: 'URL do catálogo da campanha' },
			{ var: 'NOMEPARTICIPANTE', description: 'Nome do participante' },
			{ var: 'NUMEROPEDIDO', description: 'Número do pedido' },
			{ var: 'QTDEPONTOS', description: 'Quantidade de pontos preenchido para transferência' },
			{ var: 'VALORCREDITO', description: 'Valor em reais que será creditado no cartão' },
			{ var: 'NUMEROCARTAO', description: 'Número do cartão mascarado em que será efetuado o crédito' }
		],
		PointsPurchaseReceived: [
			{ var: 'NOMECAMPANHA', description: 'Nome da campanha' },
			{ var: 'URLCAMPANHA', description: 'URL do catálogo' },
			{ var: 'URLSITECAMPANHA', description: 'URL do site campanha' },
			{ var: 'URLACESSOCAMPANHA', description: 'URL principal de acesso à campanha' },
			// { var: 'NUMEROPEDIDO', description: 'Número do pedido' },
			{ var: 'NOMEPARTICIPANTE', description: 'Nome do participante' },
			{ var: 'TELEFONE', description: 'Telefone preenchido na compra' },
			{ var: 'QTDEPONTOS', description: 'Quantidade de pontos comprados' },
			{ var: 'VALORTOTAL', description: 'Valor total do pedido em pontos' },
			{ var: 'QTDEPARCELAS', description: 'Quantidade de parcelas selecionada' },
			{ var: 'VALORPARCELAS', description: 'Valor das parcelas' },
		],
		RechargeOrderReceived: [
			{ var: 'NOMECAMPANHA', description: 'Nome da campanha' },
			{ var: 'URLCAMPANHA', description: 'URL do catálogo' },
			{ var: 'URLSITECAMPANHA', description: 'URL do site campanha' },
			{ var: 'URLACESSOCAMPANHA', description: 'URL principal de acesso à campanha' },
			{ var: 'NOMEPARTICIPANTE', description: 'Nome do participante' },
			{ var: 'NUMEROPEDIDO', description: 'Número do pedido' },
			{ var: 'CELULAR', description: 'Número do celular' },
			{ var: 'OPERADORA', description: 'Operadora selecionada' },
			{ var: 'VALORRECARGA', description: 'Valor a ser creditado' },
			{ var: 'CUSTOTOTAL', description: 'Custo total da recarga em pontos' },
			{ var: 'COMPROVANTE', description: 'Comprovante da operação' }
		],
		BillPaymentOrderReceived: [
			{ var: 'NOMECAMPANHA', description: 'Nome da campanha' },
			{ var: 'URLCAMPANHA', description: 'URL do catálogo' },
			{ var: 'URLSITECAMPANHA', description: 'URL do site campanha' },
			{ var: 'URLACESSOCAMPANHA', description: 'URL principal de acesso à campanha' },
			{ var: 'NOMEPARTICIPANTE', description: 'Nome do participante' },
			{ var: 'NUMEROPEDIDO', description: 'Número do pedido' },
			{ var: 'EMISSOR', description: 'Emissor' },
			{ var: 'CODIGOBARRAS', description: 'Código de barras' },
			{ var: 'VALORCONTA', description: 'Valor da conta a ser paga' },
			{ var: 'CUSTOTOTAL', description: 'Custo total do pagamento em pontos' },
			{ var: 'COMPROVANTE', description: 'Comprovante da operação' }
		],
		BankTransferOrderReceived: [
			{ var: 'NOMECAMPANHA', description: 'Nome da campanha' },
			{ var: 'URLCAMPANHA', description: 'URL do catálogo' },
			{ var: 'URLSITECAMPANHA', description: 'URL do site campanha' },
			{ var: 'URLACESSOCAMPANHA', description: 'URL principal de acesso à campanha' },
			{ var: 'NOMEPARTICIPANTE', description: 'Nome do participante' },
			{ var: 'NUMEROPEDIDO', description: 'Número do pedido' },
			{ var: 'QTDEPONTOS', description: 'Quantidade de pontos preenchido para transferência' },
			{ var: 'VALORCREDITO', description: 'Valor em reais que será creditado na conta' },
			{ var: 'FORMATRANSFERENCIA', description: 'Forma de transferência' },
			{ var: 'BANCO', description: 'Banco da conta' },
			{ var: 'NUMEROAGENCIA', description: 'Agência da conta' },
			{ var: 'NUMEROCONTA', description: 'Número da conta' },
			// { var: 'CHAVEPIX', description: 'Chave PIX' },
		],
		BankTransferOrderTransfered: [
			{ var: 'NOMECAMPANHA', description: 'Nome da campanha' },
			{ var: 'URLCAMPANHA', description: 'URL do catálogo' },
			{ var: 'URLSITECAMPANHA', description: 'URL do site campanha' },
			{ var: 'URLACESSOCAMPANHA', description: 'URL principal de acesso à campanha' },
			{ var: 'NOMEPARTICIPANTE', description: 'Nome do participante' },
			{ var: 'NUMEROPEDIDO', description: 'Número do pedido' },
			{ var: 'QTDEPONTOS', description: 'Quantidade de pontos preenchido para transferência' },
			{ var: 'VALORCREDITO', description: 'Valor em reais que será creditado na conta' },
			{ var: 'FORMATRANSFERENCIA', description: 'Forma de transferência' },
			{ var: 'BANCO', description: 'Banco da conta' },
			{ var: 'NUMEROAGENCIA', description: 'Agência da conta' },
			{ var: 'NUMEROCONTA', description: 'Número da conta' },
			// { var: 'CHAVEPIX', description: 'Chave PIX' },
			{ var: 'URLCOMPROVANTE', description: 'URL do comprovante de transferência' }
		],
		PointsDistributedNewParticipant: [
			{ var: 'NOMECAMPANHA', description: 'Nome da campanha' },
			{ var: 'NOMEPARTICIPANTE', description: 'Nome do novo participante' },
			{ var: 'USUARIO', description: 'Login do novo participante' },
			{ var: 'SENHA', description: 'Senha temporária do novo participante' },
			{ var: 'URLCAMPANHA', description: 'URL do catalogo da campanha' },
			{ var: 'TOTALPONTOS', description: 'Total de pontos que o participante recebeu na distribuicao' }
		],
		Login: [
			{ var: 'NOMECAMPANHA', description: 'Nome da campanha' },
			{ var: 'URLCAMPANHA', description: 'URL do catálogo' },
			{ var: 'URLSITECAMPANHA', description: 'URL do site campanha' },
			{ var: 'URLACESSOCAMPANHA', description: 'URL principal de acesso à campanha' },
			{ var: 'NOMEPARTICIPANTE', description: 'Nome do participante' },
			{ var: 'INFORMACOESDESEGURANCA', description: 'Informações do dispositivo e IP de acesso' }
		],
		ExpiringPoints: [
			{ var: 'NOMECAMPANHA', description: 'Nome da campanha' },
			{ var: 'NOMEPARTICIPANTE', description: 'Nome do participante' },
			{ var: 'URLCAMPANHA', description: 'URL do catálogo' },
			{ var: 'URLSITECAMPANHA', description: 'URL do site campanha' },
			{ var: 'URLACESSOCAMPANHA', description: 'URL principal de acesso à campanha' },
			// { var: 'DATAEXPIRACAO', description: 'Data da expiração' },
			{ var: 'TOTALPONTOSEXPIRAR', description: 'Valor total de pontos que está expirando' },
		],
		FirstAccess: [
			{ var: 'NOMECAMPANHA', description: 'Nome da campanha' },
			{ var: 'URLCAMPANHA', description: 'URL do catálogo' },
			{ var: 'URLSITECAMPANHA', description: 'URL do site campanha' },
			{ var: 'URLACESSOCAMPANHA', description: 'URL principal de acesso à campanha' },
			{ var: 'NOMEPARTICIPANTE', description: 'Nome do participante' }
		],
		FeatureApproval: [
			{ var: 'NOMECAMPANHA', description: 'Nome da campanha' },
			{ var: 'NOMEPARTICIPANTE', description: 'Nome do participante' },
			{ var: 'URLAPROVACAO', description: 'URL para a aprovação' },
			{ var: 'QRCODE', description: 'QR CODE com da url de aprovação' },
			{ var: 'URLACESSOCAMPANHA', description: 'URL principal de acesso à campanha' },
			{ var: 'CLIENTE', description: 'Nome empresa' }
		],
		MarketplaceOrderWithService: [
			{ var: 'URLSITECAMPANHA', description: 'URL do site campanha' },
			{ var: 'URLCAMPANHA', description: 'URL do catalogo campanha' },
			{ var: 'URLACESSOCAMPANHA', description: 'URL principal de acesso à campanha' },
			{ var: 'NOMEPRODUTO', description: 'Nome do produto' },
			{ var: 'NUMEROPEDIDO', description: 'Número do pedido gerado' },
			{ var: 'NOMEPARTICIPANTE', description: 'Nome do participante' },
			{ var: 'ENDERECOENTREGA', description: 'Endereço de entrega' },
			{ var: 'TABELAITENS', description: 'Tabela com os itens do pedido' },
			{ var: 'VALORFRETE', description: 'Valor do frete' },
			{ var: 'VALORTOTAL', description: 'Valor total do pedido (frete + produtos)' },
		],
		PaymentMethodConfirmation: [
			{ var: 'NOMEPARTICIPANTE', description: 'Nome do participante' },
			{ var: 'NUMEROPEDIDO', description: 'Número do pedido' },
			{ var: 'TIPOPAGAMENTO', description: 'Método de pagamento' },
			{ var: 'TOTAL', description: 'Valor total pago' },
			{ var: 'STATUS', description: 'Status do pagamento'},
			{ var: 'DIATRANSACAO', description: 'Dia da transação'},
			{ var: 'HORATRANSACAO' , description: 'Hora da transação'},
		]
	};
}

export function getCampaignEventTypeDescription(type: string) {
	const item = CAMPAIGN_EVENT_TYPES.find(t => t.id == type);
	if (!item) {
		return 'Não Mapeado';
	}
	return item.text;
}
