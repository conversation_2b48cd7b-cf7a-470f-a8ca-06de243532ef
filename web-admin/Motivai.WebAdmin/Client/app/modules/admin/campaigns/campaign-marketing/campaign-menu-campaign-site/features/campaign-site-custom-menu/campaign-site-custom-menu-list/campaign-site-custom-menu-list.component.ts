import { Component, EventEmitter, OnInit, Output, ViewChild } from '@angular/core';
import { Subscription } from 'rxjs';

import { GpAlertComponent } from '../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { FormatHelper } from '../../../../../../../../shared/formatters/format-helper';

import { ParticipantsGroupService } from '../../../../../campaign-participants/campaign-participants-groups/participants-groups.service';
import { CampaignStore } from '../../../../../campaign.store';
import { CampaignSiteMenuService } from '../../../service/campaign-site-menu.service';

@Component({
  selector: 'campaign-site-custom-menu-list',
  templateUrl: './campaign-site-custom-menu-list.component.html'
})
export class CampaignSiteCustomMenuListComponent implements OnInit {
  @Output('select') selectEmitter: EventEmitter<any> = new EventEmitter();

  @ViewChild('alert') alert: GpAlertComponent;

  private _campaign$: Subscription;

  menus: any[] = [];
  groups: any[] = [];

  loading: boolean = false;

  constructor(
    private campaignStore: CampaignStore,
    private campaignSiteMenuService: CampaignSiteMenuService,
    private groupsService: ParticipantsGroupService
  ) {}

  ngOnInit() {
    this._campaign$ = this.campaignStore.asObservable.subscribe(campaignId => {
      if (campaignId) {
        this.getCampaignSiteCustomMenus();
      }
    });
  }

  getCampaignSiteCustomMenus() {
    this.loading = true;
    this.campaignSiteMenuService.getCampaignSiteCustomMenus(this.campaignStore.id).subscribe(
      menus => {
        if (menus && menus.length > 0) {
          menus.forEach(m => {
            m.formattedCreateDate = FormatHelper.formatDateWithTimezone(m.createDate);
          });
          this.menus = menus;
        } else {
          this.menus = [];
        }
        this.loading = false;
      },
      err => {
        this.loading = false;
        this.alert.handleAndShowError(err);
      }
    );
  }

  getGroups() {
    this.loading = true;
    this.groupsService.search(this.campaignStore.id).subscribe(
      groups => {
        if (groups && groups.length > 0) {
          this.groups = groups;
          this.handleGroups();
        } else {
          this.groups = [];
        }
        this.loading = false;
      },
      err => {
        this.loading = false;
        this.alert.showError(err, true);
      }
    );
  }

  private handleGroups() {
    if (this.hasGroups()) {
      this.menus.forEach(menu => {
        let groupNames = '';
        menu.participantsGroupsIds.forEach((id, index, array) => {
          let groupName = this.findGroupNameById(id);
          if (index !== array.length - 1) {
            groupNames += groupName.concat('; ');
          } else {
            groupNames += groupName;
          }
        });
        menu['groupNames'] = groupNames;
      });
    }
  }

  private hasGroups() {
    return this.groups && this.groups.length > 0;
  }

  private findGroupNameById(id: string) {
    return this.groups.find(g => g.id === id).name;
  }

  onClickEditMenu(event: any) {
    this.selectEmitter.emit(event);
  }
}
