import { Component, OnInit, ViewChild } from '@angular/core';

import { AuthStore } from '../../../../../../../core/auth/auth.store';

import { CampaignStore } from '../../../../campaign.store';

import { CampaignSiteMenuService } from '../../service/campaign-site-menu.service';

import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';

@Component({
    selector: 'campaign-header-buttons-menu',
    templateUrl: './campaign-header-buttons-menu.component.html'
})
export class CampaignHeaderButtonsMenuComponent implements OnInit {
    @ViewChild('alert') alert: GpAlertComponent;
    menuOptions: any[] = [{

    }];
    campaignId: string;
    loading: boolean = false;
    disableButton: boolean = false;
    descriptionMaxLength: number = 32;

    constructor(
        private campaignSiteMenuService: CampaignSiteMenuService, 
        private _store: CampaignStore, private _auth: AuthStore
    ) { }

    ngOnInit(): void {
        this.campaignId = this._store.id;

        if (this.campaignId) {
            this.getMenuOptions();
        }
    }

    newMenuOption() {
        if (this.menuOptions.length <= 3) {
            this.disableButton = false;
            this.menuOptions.push({});
            if (this.menuOptions.length > 3) {
                this.disableButton = true;
            }
        }
    }

    deleteMenuOption(i) {
        if (this.menuOptions.length > 1) {
            this.menuOptions.splice(i, 1);
            if (this.menuOptions.length <= 3) {
                this.disableButton = false;
            }
        } else {
            this.menuOptions = [{}];
            this.disableButton = false;
        }

    }
    
    save() {
        this.loading = true;
        this.campaignSiteMenuService.saveCampaignSiteHeaderLinks(this.campaignId, this.menuOptions).subscribe(
            options => {
                this.getMenuOptions();
                this.loading = false;
                this.alert.showSuccess('Sucesso!');
            },
            err => {
                this.loading = false;
                this.alert.showError(err);
            }
        );
    }

    getMenuOptions() {
        this.loading = true;
        this.campaignSiteMenuService.getCampaignSiteHeaderLink(this.campaignId)
        .subscribe(
            options => {
                this.menuOptions = options || [{}];
                this.loading = false;
            },
            err => {
                this.loading = false;
                this.alert.showError(err);
            }
        );
    }

    get canSaveCampaignSiteHeaderLinks() {
        return this._auth.role.PERMISSION_CAMPAIGNS_SITE_HEADER_LINKS_CREATE;
    }
}
