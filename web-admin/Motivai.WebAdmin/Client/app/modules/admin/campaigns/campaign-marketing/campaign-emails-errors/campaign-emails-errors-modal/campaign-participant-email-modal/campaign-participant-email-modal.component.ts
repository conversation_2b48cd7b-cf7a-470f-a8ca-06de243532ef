import { Component, OnInit, ViewChild, EventEmitter, Output } from '@angular/core';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpModalComponent } from '../../../../../../../shared/components/gp-modal/gp-modal.component';

@Component({
    selector: 'campaign-participant-email-modal',
    templateUrl: './campaign-participant-email-modal.component.html'
})
export class CampaignParticipantEmailModalComponent implements OnInit {
    @ViewChild('alert') alert: GpAlertComponent;
    @ViewChild('modal') modal: GpModalComponent;
    @Output('email') email: EventEmitter<any> = new EventEmitter();

    participant: any = {};

    loading: boolean = false;

    constructor() { }

    ngOnInit(): void { }

    openModal(email: string) {
        if (email != null)
            this.participant.email = email;
        this.modal.show();
    }

    openModalInsertByTracingId(email: string) {
        if (email != null)
            this.participant.email = email;
        this.modal.show();
    }

    confirmEmail() {
        if (!this.participant.email)
            this.alert.showWarning('Preencha o email para prosseguir.');

        this.email.emit(this.participant.email);
        this.modal.hide();
    }
}
