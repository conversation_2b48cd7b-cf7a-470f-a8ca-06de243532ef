import { Component, OnInit, ViewChild } from '@angular/core';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpModalComponent } from '../../../../../../../shared/components/gp-modal/gp-modal.component';

@Component({
    selector: 'campaign-email-error-template-modal',
    templateUrl: './campaign-email-error-template-modal.component.html'
})
export class CampaignEmailErrorTemplateModalComponent implements OnInit {
    @ViewChild('alert') alert: GpAlertComponent;
    @ViewChild('modal') modal: GpModalComponent;

    template: string = '';

    constructor() { }

    ngOnInit(): void { }

    openModal() {
        this.modal.show();
    }
}
