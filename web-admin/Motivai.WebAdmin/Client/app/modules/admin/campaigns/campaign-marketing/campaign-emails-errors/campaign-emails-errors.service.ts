import { Observable } from 'rxjs';
import { Injectable } from '@angular/core';
import { ApiService } from '../../../../../core/api/api.service';

@Injectable()
export class CampaignEmailsErrorsService {

    constructor(private _api: ApiService) {}

    getEmailsErrors(campaignId: string): Observable<any[]> {
        return this._api.get(`/api/campaigns/${campaignId}/notification/errors`);
    }

    sendEmailErrorsByTracingId(tracingId: string, emailTo: string): Observable<any> {
        return this._api.post(`/api/${tracingId}`, {emailTo});
    }
}
