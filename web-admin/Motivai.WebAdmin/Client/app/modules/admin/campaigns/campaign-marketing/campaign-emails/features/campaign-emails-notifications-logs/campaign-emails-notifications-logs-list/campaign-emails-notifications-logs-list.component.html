<gp-alert [overlay]="true" #alert></gp-alert>

<gp-card title="Filtros" [first]="true">
	<gp-form-row>
		<gp-form-col cols="12 6 6 4">
			<label>Selecione um tipo de e-mail</label>
			<gp-select name="emailType" [multiple]="false" placeholder="Selecione"
				[items]="campaignEventTypes" [(ngModel)]="params.emailType">
			</gp-select>
		</gp-form-col>
		<gp-form-col cols="12 6 6 4">
			<gp-simple-input label="E-mail">
				<input type="text" class="form-control" name="email" [(ngModel)]="params.email" />
			</gp-simple-input>
		</gp-form-col>
		<gp-datepicker cols="12 6 6 2" [required]="false" label="De" name="from" [(ngModel)]="params.from">
		</gp-datepicker>
		<gp-datepicker cols="12 6 6 2" [required]="false" label="Até" name="to" [(ngModel)]="params.to">
		</gp-datepicker>
	</gp-form-row>
	<gp-form-row>

		<gp-form-col cols="12 12">
			<gp-spinner-button type="button" bootstrapClass="primary" icon="search" text="Pesquisar" pull="right"
				loadingText="Aguarde" [loading]="loading" (click)="applyFilters()" marginTop="24px">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card>
	<gp-form-row>
		<gp-form-col cols="12">
			<gp-grid name="emailsLogs" [loading]="loading" [showPagination]="true" [showTotalPages]="false"
				[rows]="emailsLogs" [pageSize]="20"
				[columns]="['Destinatário','Tipo','E-mail Ativo','SMS Ativo','Enviado','Data de Envio','Ocorreu Erro','Mensagem do Erro']"
				[fields]="['emailsTo','formattedType','emailActive','smsActive','sentDescription','formattedCreateDate','errorOccurredDescription','errorMessage']"
				[showEdit]="true" [showDelete]="false" (onEdit)="showEmailsLogDetails($event)" (onPageChanged)="pageChanged($event)">
			</gp-grid>
		</gp-form-col>
	</gp-form-row>
</gp-card>