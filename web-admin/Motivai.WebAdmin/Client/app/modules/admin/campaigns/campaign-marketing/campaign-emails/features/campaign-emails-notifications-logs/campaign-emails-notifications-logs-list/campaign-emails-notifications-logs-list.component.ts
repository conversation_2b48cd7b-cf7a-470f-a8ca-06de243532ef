import {
  Component,
  EventEmitter,
  OnInit,
  Output,
  ViewChild,
  OnDestroy
} from '@angular/core';
import { Subscription } from 'rxjs';

import { GpAlertComponent } from '../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { RxjsHelpers } from '../../../../../../../../shared/helpers/rxjs-helpers';
import { CampaignStore } from '../../../../../campaign.store';
import { CampaignEmailsService } from '../../../services/campaign-emails.service';
import { FormatHelper } from '../../../../../../../../shared/formatters/format-helper';
import { GpGridComponent } from '../../../../../../../../shared/components/gp-grid/gp-grid.component';
import { BooleanToYesNoPipe } from '../../../../../../../../shared/pipes/boolean-to-yes-no.pipe';
import { getCampaignEventTypeDescription, getCampaignEventTypes } from '../../../models/campaign-event-types';
import { Item } from '../../../../../../../../shared/models/item';

@Component({
  selector: 'campaign-emails-notifications-logs-list',
  templateUrl: './campaign-emails-notifications-logs-list.component.html'
})
export class CampaignEmailsNotificationsLogsListComponent
  implements OnInit, OnDestroy {
  @ViewChild('alert') alert: GpAlertComponent;
  @Output() onEdit: EventEmitter<any> = new EventEmitter<any>();
  @ViewChild('gpGrid') gpGrid: GpGridComponent;

  campaignEventTypes: Array<Item> = getCampaignEventTypes();

  private _campaign$: Subscription;
  emailsLogs: any[] = [];
  campaignId: string = '';
  loading: boolean = false;

  params: any = {
    skip: 0,
    limit: 20
  };

  constructor(
    private _campaignStore: CampaignStore,
    private _service: CampaignEmailsService
  ) {}

  ngOnInit(): void {
    this._campaign$ = this._campaignStore.asObservable.subscribe(id => {
      this.campaignId = id;
      this.findEmailsNotificationsLogs();
    });
  }

  ngOnDestroy(): void {
    RxjsHelpers.unsubscribe(this._campaign$);
  }

  applyFilters() {
    if (this.gpGrid) {
      this.gpGrid.resetPagination();
    }
    this.params.skip = 0;
    this.findEmailsNotificationsLogs();
  }

  findEmailsNotificationsLogs() {
    this.loading = true;
    this._service
      .getCampaignNotificationsEmailsLogs(
        this.campaignId,
        this.params.email,
        this.params.emailType,
        this.params.from,
        this.params.to,
        this.params.skip,
        this.params.limit
      )
      .subscribe(
        response => {
          this.emailsLogs = response;
          if (response) {
            this.emailsLogs.forEach(r => {
              // TODO: Apenas alterar (não remover)
              r.sentDescription = BooleanToYesNoPipe.toYesNo(r.sent);
              r.errorOccurredDescription = BooleanToYesNoPipe.toYesNo(r.errorOccurred);
              r.emailActive = BooleanToYesNoPipe.toYesNo(r.notificationActive);
              r.smsActive = BooleanToYesNoPipe.toYesNo(r.notificationSmsActive);
              r.formattedCreateDate = FormatHelper.formatDateWithTimezone(r.createDate);
              r.formattedType = getCampaignEventTypeDescription(r.type);
            });
          }
          this.loading = false;
        },
        err => {
          this.loading = false;
          this.alert.showError(err);
        }
      );
  }

  showEmailsLogDetails(event) {
    this.onEdit.emit(event);
  }

  pageChanged(event) {
    this.params.skip = event.skip;
    this.params.limit = event.limit;
    this.findEmailsNotificationsLogs();
  }
}
