<gp-alert [overlay]="true" #gpAlert></gp-alert>

<tabset #tabs class="bg-white p0 tab-no-border" [justified]="true">
	<tab>
	  <ng-template tabHeading>Ordenação</ng-template>
		<gp-card [first]="true">
			<div class="row col-xs-12 bottom-m1">
				<h4>Ordem dos Departamentos</h4>
			</div>
			<div class="row">
				<div grid="12 6 3" [group]="true">
					<label for="department">Departamento</label>
					<ng-select [items]="allDepartments" placeholder="Selecione um departamento" (data)="selectDepartment($event)">
					</ng-select>
				</div>
				<!-- div grid="12 6 3" [group]="true">
					<label for="category">Categoria</label>
					<ng-select [items]="categories" placeholder="Selecione uma categoria" (data)="selectCategory($event)"
						[active]="menuFilter.selectedCategory" [disabled]="disableCategoriesSelect">
					</ng-select>
				</div -->
			</div>
			<div class="row">
				<div grid="12 12" [group]="true">
					<gp-input-checkbox text="Mostrar somente departamentos, categorias e subcategorias com produtos" [(ngModel)]="useOnlyWithProducts"></gp-input-checkbox>
				</div>
			</div>
			<div class="row">	
				<div class="col-xs-12" *ngIf="!loading">
					<div>
						<p>Os itens em verde serão destacados no menu da campanha.</p>
					</div>
					<ul class="list-group" dnd-sortable-container [sortableData]="departments">
						<li *ngFor="let item of departments; let i = index" class="list-group-item" [ngClass]="{'green-text': i < 6}"
							dnd-sortable [sortableIndex]="i">{{item.text}}</li>
					</ul>
					<div *ngIf="!departments || !departments.length">
						<p>Nenhum departamento ou categoria encontrada.</p>
					</div>
				</div>
				<spinner [show]="loading"></spinner>
			</div>
		</gp-card>
	</tab>
	<tab>
		<ng-template tabHeading>Segmentação</ng-template>
			<segmentation-parametrization [first]="true" title="Segmentações dos Serviços" [campaignId]="campaignId" [segmentations]="segmentationsConfiguration">
			</segmentation-parametrization>
	</tab>
</tabset>

<gp-card [last]="true">
	<div class="row">
		<div class="col-xs-12">
			<gp-spinner-button type="button" text="Salvar" bootstrapClass="success" loadingText="Salvando" [pink]="true" icon="send"
				[disabled]="disableButton" [loading]="sending" (click)="saveDepartmentsOrder()"></gp-spinner-button>
			<!-- <gp-spinner-button type="button" text="Atualizar menu" bootstrapClass="success" loadingText="Atualizando" [pink]="true" icon="send"
				[disabled]="disableButton" [loading]="updating" (click)="updateMenu()"></gp-spinner-button> -->
		</div>
	</div>
</gp-card>