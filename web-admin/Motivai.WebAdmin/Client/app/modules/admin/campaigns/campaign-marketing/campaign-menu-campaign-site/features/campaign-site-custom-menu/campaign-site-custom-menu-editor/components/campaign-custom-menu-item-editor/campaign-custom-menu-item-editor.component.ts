import { Component, EventEmitter, OnInit, Output, ViewChild } from '@angular/core';
import { Subscription } from 'rxjs/Subscription';

import { GpModalComponent } from '../../../../../../../../../../shared/components/gp-modal/gp-modal.component';
import { compareItems } from '../../../../../../../../../../shared/helpers/comparators';
import { RxjsHelpers } from '../../../../../../../../../../shared/helpers/rxjs-helpers';
import { Item } from '../../../../../../../../../../shared/models/item';
import { CampaignStore } from '../../../../../../../campaign.store';
import {
  CampaignFeatureTypes,
  createCampaignFeaturesTypesItems
} from '../../../../../../../../../../shared/components/business/campaigns/campaign-feature-selector/models/campaign-feature-types.enum';
import { buildCustomMenuItem, CustomItem } from '../../../../../models/custom-item';
import { CustomMenuItem } from '../../../../../models/custom-menu';
import { canBePublic, createAuthenticationStatesItems, FeatureTypes } from '../../../../../models/features-types';
import { CampaignSiteMenuService } from '../../../../../service/campaign-site-menu.service';
import { CampaignFeaturesItemsService } from '../../../../../../../../../../shared/components/business/campaigns/campaign-feature-selector/services/campaign-features-items.service';

@Component({
  selector: 'campaign-custom-menu-item-editor',
  templateUrl: './campaign-custom-menu-item-editor.component.html'
})
export class CampaignCustomMenuItemEditorComponent implements OnInit {
  @Output('save') saveEmitter: EventEmitter<CustomMenuItem> = new EventEmitter<CustomMenuItem>();
  @Output('error') errorEmitter: EventEmitter<any> = new EventEmitter<any>();

  @ViewChild('modal') modal: GpModalComponent;

  featureGroupType: string = null;
  customItem: CustomItem = CustomItem.empty();

  participantAuthenticationStates: Array<Item> = createAuthenticationStatesItems();

  loading: boolean = false;

  campaignFeatureTypes: Array<Item> = createCampaignFeaturesTypesItems();

  campaignSitePages: Array<CustomMenuItem> = [];
  campaignSitePagesItems: Array<Item> = [];

  campaignFeatureItems: Array<Item> = [];

  private _campaign$: Subscription;

  constructor(
    private _campaignStore: CampaignStore,
    private _campaignFeaturesService: CampaignFeaturesItemsService,
    private _campaignSiteMenuService: CampaignSiteMenuService
  ) {}

  get isFeatureTypeCustomUrl(): boolean {
    return this.customItem.featureType === FeatureTypes.LINK;
  }

  get canSelectAuthenticationState(): boolean {
    return canBePublic(this.customItem.featureType as FeatureTypes);
  }

  get isCampaignPageSelected(): boolean {
    return this.featureGroupType == 'CampaignSitePage';
  }

  get isCampaignFeatureSelected(): boolean {
    return this.featureGroupType == 'CampaignSiteFeature';
  }

  get isCampaignFeatureLinkSelected(): boolean {
    return this.featureGroupType == 'CustomMenuItemLink';
  }

  get isCustomMenuTextSelected(): boolean {
    return this.featureGroupType == 'CustomMenuItem';
  }

  ngOnInit() {
    this._campaign$ = this._campaignStore.asObservable.subscribe(id => this.loadSitePages());
  }

  ngOnDestroy(): void {
    RxjsHelpers.unsubscribe(this._campaign$);
  }

  private loadSitePages() {
    this.loading = true;
    this._campaignSiteMenuService.getCustomItemsFromCampaignSitePageSettings(this._campaignStore.id)
      .subscribe(customMenuItems => {
        if (customMenuItems && customMenuItems.length) {
          this.campaignSitePages = customMenuItems;
          this.campaignSitePagesItems = this.campaignSitePages
            .map(page => Item.of(page.featureType, page.itemText))
            .sort(compareItems);
        }
        this.loading = false;
      },
      err => {
        this.loading = false;
        this.errorEmitter.emit(err);
      }
    );
  }

  public showEditor() {
    this.clear();
    this.modal.show();
  }

  private closeModal() {
    this.modal.hide();
  }

  clear() {
    this.featureGroupType = null;
    this.customItem = CustomItem.empty();
  }

  onFeatureTypeChange() {
    if (!this.customItem.featureType) {
      return;
    }

    this.customItem.featureId = null;
    this.campaignFeatureItems = [];
    this.loading = true;
    this._campaignFeaturesService
      .findCampaignFeatureItemsByType(this._campaignStore.id, this.customItem.featureType as CampaignFeatureTypes)
      .subscribe(
        items => {
          this.campaignFeatureItems = items || [];
          this.loading = false;
        },
        err => {
          this.loading = false;
          this.errorEmitter.emit(err);
        }
      );
  }

  onCampaignSitePageItemChange() {
    if (!this.customItem.featureType) {
      return;
    }

    const item = this.campaignSitePagesItems.find(item => item.id == this.customItem.featureType);
    this.setCustomItemMenuText(item);
  }

  onCampaignFeatureItemChange() {
    if (!this.customItem.featureId) {
      return;
    }

    const item = this.findCampaignFeatureSelectedItem();
    this.setCustomItemMenuText(item);
  }

  private findCampaignFeatureSelectedItem(): Item {
    return this.campaignFeatureItems.find(item => item.id == this.customItem.featureId);
  }

  private setCustomItemMenuText(item: Item) {
    if (item) {
      this.customItem.itemText = item.text;
    } else {
      this.customItem.itemText = '';
    }
  }

  addCustomItem() {
    if (this.isCampaignFeatureSelected) {
      const selectedItem = this.findCampaignFeatureSelectedItem();
      if (selectedItem.data && selectedItem.data.segmentations) {
        this.customItem.segmentations = selectedItem.data.segmentations;
      }
    } else if (this.isCampaignFeatureLinkSelected) {
      this.customItem.featureType = FeatureTypes.LINK;
    } else if (this.isCustomMenuTextSelected) {
      this.customItem.featureType = FeatureTypes.NONE;
    }

    const menuItem = buildCustomMenuItem(this.customItem);
    this.saveEmitter.emit(menuItem);
    this.closeModal();
  }
}

