import { CampaignEmailErrorCancelationModalComponent } from '../../campaign-emails-errors-modal/campaign-email-error-cancelation-modal/campaign-email-error-cancelation-modal.component';
import { CampaignEmailErrorTemplateModalComponent } from '../../campaign-emails-errors-modal/campaign-email-error-template-modal/campaign-email-error-template-modal.component';
import { Component, OnInit, ViewChild } from '@angular/core';
import { CampaignParticipantEmailModalComponent } from '../../campaign-emails-errors-modal/campaign-participant-email-modal/campaign-participant-email-modal.component';

@Component({
    selector: 'campaign-email-error-handler',
    templateUrl: './campaign-email-error-handler.component.html'
})
export class CampaignEmailErrorHandlerComponent implements OnInit {
    @ViewChild('emailTemplateModal') emailTemplateModal: CampaignEmailErrorTemplateModalComponent;
    @ViewChild('newEmailModal') newEmailModal: CampaignParticipantEmailModalComponent;
    @ViewChild('cancelationModal') cancelationModal: CampaignEmailErrorCancelationModalComponent;

    error: any = {
        from: {}
    };

    loading: boolean = false;

    constructor() { }


    ngOnInit(): void { }

    get disableButtons() {
        return this.loading || this.error;
    }

    openEmailTemplateModal() {
        this.emailTemplateModal.openModal();
    }

    confirmEmail() {
        this.newEmailModal.openModal(this.error.to);
    }

    cancelEmail() {
        this.cancelationModal.openModal(this.error.id);
    }

    retryEmail(email) {

    }

    clear() {
        this.error = {from: {}};
    }
}
