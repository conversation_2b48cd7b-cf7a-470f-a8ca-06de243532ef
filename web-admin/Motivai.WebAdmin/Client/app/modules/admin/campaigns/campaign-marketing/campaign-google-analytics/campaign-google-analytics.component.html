<form>
	<gp-card title="Parâmetros de UTM da Campanha">
		<gp-form-row>
			<gp-form-col cols="12 6 3">
				<gp-simple-input label="Nome (Name)" [required]="true">
					<input type="text" class="form-control" name="campaign" [(ngModel)]="campaignGaUtm.campaign" required />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 9" additionalClasses="top-p27">
				<span>Nome para identificar a campanha de marketing ou produto: usado na análise de keywords.</span>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6 3">
				<gp-simple-input label="Origem (Source)" [required]="true">
					<input type="text" class="form-control" name="source" [(ngModel)]="campaignGaUtm.source" required />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 9" additionalClasses="top-p27">
				<span>Plataforma pela qual o tráfego é originado (recomenda-se usar o nome do canal: facebook, twitter, e-mail, etc).</span>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6 3">
				<gp-simple-input label="Meio (Medium)" [required]="true">
					<input type="text" class="form-control" name="medium" [(ngModel)]="campaignGaUtm.medium" required />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 9" additionalClasses="top-p27">
				<span>Identifica como o tráfego está chegando (cpc, posts em midias sociais, newsletter, link de filiado, etc.</span>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6 3">
				<gp-simple-input label="Conteúdo (Content)">
					<input type="text" class="form-control" name="content" [(ngModel)]="campaignGaUtm.content" />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 9" additionalClasses="top-p27">
				<span>Usado para teste A/B ou diferenciar links que apontam para uma mesma URL (em um mesmo e-mail poderia ter um botão ou o header que acessa a mesma página, assim conseguiria diferenciar qual deles foi clicado).</span>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6 3">
				<gp-simple-input label="Termo (Term)">
					<input type="text" class="form-control" name="term" [(ngModel)]="campaignGaUtm.term" />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 9" additionalClasses="top-p27">
				<span>Usado para rastrear as palavras-chave que são usadas nas campanhas do Google Adwords.</span>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card title="Gerador de URL">
		<gp-form-row>
			<gp-form-col cols="12 6 9">
				<gp-simple-input label="URL" [required]="true">
					<input type="text" class="form-control" name="originalurl" [(ngModel)]="urlGenerator.originalUrl" />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 3" additionalClasses="top-p27">
				<gp-spinner-button type="button" text="Gerar" loadingText="Gerando" [pink]="true" icon="link"
					[disabled]="disableButton" [loading]="sending" (click)="generateLink()"></gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6 9">
				<input type="text" class="form-control" name="generatedUrl" [value]="urlGenerator.generatedUrl" readonly="true" />
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card [last]="true">
		<gp-form-row>
			<gp-form-col [inputGroup]="false">
				<gp-spinner-button type="button" text="Salvar" loadingText="Salvando" [pink]="true" icon="send"
					[disabled]="disableButton" [loading]="sending" (click)="saveUtm()"></gp-spinner-button>
				<gp-alert [overlay]="true" #gpAlert></gp-alert>
			</gp-form-col>
		</gp-form-row>
	</gp-card>
</form>
