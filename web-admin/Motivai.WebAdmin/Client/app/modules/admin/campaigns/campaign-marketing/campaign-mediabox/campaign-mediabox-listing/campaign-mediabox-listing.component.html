<gp-card [last]="true">
<div class="row col-xs-12 bottom-m1">
	<h4>Media Boxes Cadastrado</h4>
</div>
<div class="row">
	<div class="col-xs-12">
		<gp-datatable [columnsName]="['Nome', 'Posi<PERSON>', 'Período', 'Nova aba', 'Link', 'Imagem', '']">
			<tbody>
				<tr *ngFor="let media of mediaBoxes">
					<td>{{media.name}}</td>
					<td>{{media.position}}</td>
					<td>{{media.startDate | date:'dd/MM/yyyy'}} à {{media.endDate | date:'dd/MM/yyyy'}}</td>
					<td>{{media.openInNewTab == false ? 'Não': 'Sim' }}</td>
					<td><a [href]="media.link" target="_blank">Link</a></td>
					<td><a [href]="media.imageUrl + '/800/400'" target="_blank">Imagem</a></td>
					<td width="100px" align="center">
						<button class="btn btn-default btn-sm" [routerLink]="['/campanha', campaignId, 'marketing', 'mediaboxes', media.id]">
							<i title="Editar" class="fa fa-pencil-square-o grid-edit"></i>
						</button>
						<button class="btn btn-danger btn-sm grid-remove" (click)="removeMediaBox(media)">
							<i title="Excluir" class="fa fa-times"></i>
						</button>
					</td>
				</tr>
				<tr *ngIf="(!mediaBoxes || !mediaBoxes.length) && !loading">
					<td colspan="6">Nenhum media box encontrado.</td>
				</tr>
			</tbody>
		</gp-datatable>
		<spinner [overlay]="true" [show]="loading"></spinner>
	</div>
</div>
<div class="row">
	<div class="col-xs-12 top-m1">
		<button type="button" class="btn btn-default" [routerLink]="['/campanha', campaignId, 'edicao']">Voltar</button>
		<gp-spinner-button type="button" text="Novo" [pink]="true" icon="plus"
			[routerLink]="['/campanha', campaignId, 'marketing', 'mediaboxes', 'novo']"></gp-spinner-button>
		<div class="div-alert">
			<gp-alert #gpAlert></gp-alert>
		</div>
	</div>
</div>
</gp-card>
