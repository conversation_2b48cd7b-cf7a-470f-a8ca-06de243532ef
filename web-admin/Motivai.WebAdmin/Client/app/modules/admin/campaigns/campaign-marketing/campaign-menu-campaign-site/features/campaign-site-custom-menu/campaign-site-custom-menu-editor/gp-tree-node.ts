import { CustomMenuItem } from '../../../models/custom-menu';

export interface GpTreeNode {
  id: number;
  name: string;
  value: CustomMenuItem;
  children?: GpTreeNode[];
}

export function createNodeWithChildren(id: number, name: string, children: GpTreeNode[] = null, value: any = null): GpTreeNode {
  return {
    id,
    name,
    value,
    children
  } as GpTreeNode;
}

export function createNodeFromItem(id: number, item: CustomMenuItem): GpTreeNode {
  return {
    id,
    name: item.itemText,
    value: item,
  } as GpTreeNode;
}
