import { Component, ViewChild } from '@angular/core';
import { TreeComponent, TreeModel, TreeNode } from 'angular2-tree-component';
import { ContextMenuComponent, ContextMenuService } from 'ngx-contextmenu';
import { SegmentationsConfiguration } from '../../../../../../../../shared/components/business/campaigns/segmentation-parametrization/segmentations-configuration';

import { GpAlertComponent } from '../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignStore } from '../../../../../campaign.store';
import { CustomMenu, CustomMenuItem } from '../../../models/custom-menu';
import { CampaignSiteMenuService } from '../../../service/campaign-site-menu.service';
import { CampaignCustomMenuItemEditorComponent } from './components/campaign-custom-menu-item-editor/campaign-custom-menu-item-editor.component';
import { createNodeFromItem, GpTreeNode } from './gp-tree-node';


const MAXIMUM_TREE_DEPTH: number = 2;

// https://angular2-tree.readme.io/v2.8.0/docs/changing-the-tree
@Component({
  selector: 'campaign-site-custom-menu-editor',
  templateUrl: './campaign-site-custom-menu-editor.component.html'
})
export class CampaignMenusSearchEditComponent {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @ViewChild('tree') tree: TreeComponent;
  @ViewChild('contextMenuItem') contextMenuItem: ContextMenuComponent;
  @ViewChild('contextMenuSubitem') contextMenuSubitem: ContextMenuComponent;
  @ViewChild('customMenuItemEditor') customMenuItemEditor: CampaignCustomMenuItemEditorComponent;

  menu: CustomMenu;
  segmentationsConfiguration: SegmentationsConfiguration = {} as SegmentationsConfiguration;

  loading: boolean = false;
  sending: boolean = false;

  nodeCounter: number = 1;
  nodes: GpTreeNode[] = [];
  parentTreeNodeAction: TreeNode;

  options: any = {
    allowDrag: () => true,
    allowDrop: (elem: any, { parent }: any) => {
      return parent.level < MAXIMUM_TREE_DEPTH && elem.data.id != parent.data.id;
    },
    actionMapping: {
      mouse: {
        contextMenu: (tree, node, event) => this.onTreeContextMenu(event, node),
        drop: (tree, node, event, { from, to }) => this.onMoveNode(tree, node, event, from, to)
      }
    }
  };

  constructor(
    private _campaignStore: CampaignStore,
    private _campaignSiteMenuService: CampaignSiteMenuService,
    private _contextMenuService: ContextMenuService,
  ) {}

  get hasItems(): boolean {
    return this.nodes && this.nodes.length > 0;
  }

  ngOnInit() {
    this.clear();
  }

  clear() {
    this.menu = CustomMenu.empty(this._campaignStore.id);
    this.segmentationsConfiguration = {} as SegmentationsConfiguration;
    this.nodes = [];
    this.updateMenu();
    this.nodeCounter = 1;
  }

  onError(err: any) {
    this.gpAlert.handleAndShowError(err);
  }

  findCustomMenuById(menuId: string) {
    this.loading = true;
    this.clear();
    this._campaignSiteMenuService.getCampaignSiteCustomMenuById(this._campaignStore.id, menuId)
      .subscribe(
        menu => {
          console.log(menu);
          this.menu = menu || {};
          this.segmentationsConfiguration = this.menu.segmentations || {} as SegmentationsConfiguration;
          this.createMenuNodesFromMenuItems();
          this.loading = false;
        },
        err => {
          this.loading = false;
          this.onError(err);
        }
      );
  }

  private createMenuNodesFromMenuItems() {
    if (!this.menu)
      return;

    if (this.menu.menuItems && this.menu.menuItems.length > 0) {
      this.nodes = this.menu.menuItems.map(item => {
        const nodeItem = this.createNodeForItem(item);
        if (item.subItems && item.subItems.length > 0) {
          nodeItem.children = item.subItems.map(subitem => this.createNodeForItem(subitem));
        }
        return nodeItem;
      });
    }
    this.updateMenu();
  }

  private createNodeForItem(menuItem: CustomMenuItem): GpTreeNode {
    return createNodeFromItem(this.nodeCounter++, menuItem);
  }

  onCustomItemSave(menuItem: CustomMenuItem) {
    const newNode = this.createNodeForItem(menuItem);
    if (this.parentTreeNodeAction) {
      const index = this.parentTreeNodeAction.children ? this.parentTreeNodeAction.children.length : 0;
      this.addNodeToParent(this.parentTreeNodeAction, newNode, index);
    } else {
      this.nodes.push(newNode);
    }
    this.updateMenu();
  }

  createMenuFromCampaignSitePageConfigurations() {
    this.loading = true;
    this._campaignSiteMenuService.getCustomMenuItems(this._campaignStore.id)
      .subscribe(
        menuItems => {
          if (!this.menu) {
            this.menu = CustomMenu.empty(this._campaignStore.id);
          }
          this.menu.menuItems = menuItems;
          this.createMenuNodesFromMenuItems();
          this.loading = false;
        },
        err => {
          this.loading = false;
          this.onError(err);
        }
      );
  }

  saveMenu() {
    if (this.menu.active && this.nodes.length == 0) {
      this.gpAlert.showWarning('Os itens do menu são obrigatórios quando o menu estiver ativo.');
      return;
    }

    this.menu.menuItems = this.nodes.map(node => {
        const item = node.value;
        if (node.children && node.children.length > 0) {
          item.subItems = node.children.map(c => c.value);
        }
        return item;
      });

    let lastMenuIndex = 0;
    for (const item of this.menu.menuItems) {
      item.menuItemIndex = ++lastMenuIndex;
      if (item.subItems && item.subItems.length > 0) {
        let lastSubItemIndex = 0;
        for (const subitem of item.subItems) {
          subitem.menuItemIndex = ++lastSubItemIndex;
        }
      }
    }

    this.menu.segmentations = this.segmentationsConfiguration;

    this.loading = true;
    this._campaignSiteMenuService.saveCustomCampaignSiteMenu(this._campaignStore.id, this.menu)
      .subscribe(id => {
        this.loading = false;
        if (id) {
          this.gpAlert.showSuccess('Menu salvo com sucesso.');
          if (!this.menu.id) {
            this.menu.id = id;
          }
        } else {
          this.gpAlert.showWarning('Não foi possível salvar o menu, por favor, tente novamente.');
        }
      },
      error => {
        this.loading = false;
        this.gpAlert.handleAndShowError(error);
      });
  }

  // ========== Inicio Eventos da ContextMenu ==========

  showMenuItemEditor() {
    this.customMenuItemEditor.showEditor();
  }

  createNewMenuItemInRoot() {
    this.parentTreeNodeAction = null;
    this.showMenuItemEditor();
  }

  createNewMenuSubitem(event): void {
    if (event.item) {
      this.showMenuItemEditor();
    }
  }

  renameItem(event): void {
    if (!event.item) {
      return;
    }
    this.gpAlert.showInput('Informe o texto do item do menu:', event.item.data.name)
      .then(result => {
        if (!result.value || !result.value.length) {
          return;
        }

        const node = this.findNodeById(this.nodes, event.item.data.id);
        if (!node)
          return;
        node.name = result.value;
        this.updateMenu();
      });
  }

  removeItem(event): void {
    if (!event.item) {
      return;
    }
    this.gpAlert.confirm(`Confirma a remoção do item '${event.item.data.name}' do menu?`)
      .then(result => {
        if (result.value) {
          this.removeNodeById(event.item.data.id);
          this.updateMenu();
        }
      });
  }

  // ========== Fim dos Eventos da ContextMenu ==========

  // ========== Inicio Eventos da Árvore de Menu ==========

  private updateMenu() {
    if (this.tree && this.tree.treeModel) {
      this.tree.treeModel.update();
    }
  }

  private onTreeContextMenu(event, node: TreeNode): void {
    event.preventDefault();

    // console.log({ contextMenu: this.contextMenuItem, event: event, item: node });

    this.parentTreeNodeAction = node;

    if (node.level < MAXIMUM_TREE_DEPTH) {
      this._contextMenuService.show.next({
        contextMenu: this.contextMenuItem,
        event: event,
        item: node,
      });
    } else {
      this._contextMenuService.show.next({
        contextMenu: this.contextMenuSubitem,
        event: event,
        item: node,
      });
    }
  }

  private onMoveNode(tree: TreeModel, treeNode: TreeNode, event, draggedNode: TreeNode, targetLocation: { index: number, parent: TreeNode }): void {
    if (targetLocation.parent.level >= MAXIMUM_TREE_DEPTH) {
      this.gpAlert.showWarning('Não é permitido mover um subitem de menu para dentro de outro subitem.');
      return;
    }

    // console.log({
    //   treeNode,
    //   event,
    //   draggedNode,
    //   targetLocation
    // });

    const nodeToMove = this.findNodeById(this.nodes, draggedNode.data.id);
    const previousParent = this.findNodeById(this.nodes, draggedNode.parent.data.id);
    console.log('previousParent', previousParent);

    if (previousParent) {
      const index = previousParent.children.indexOf(nodeToMove);
      previousParent.children.splice(index, 1);
    } else {
      this.removeNodeFrom(nodeToMove, this.nodes);
    }

    this.addNodeToParent(targetLocation.parent, nodeToMove, targetLocation.index);

    this.updateMenu();
  }

  private addNodeToParent(parent: TreeNode, nodeToMove: GpTreeNode, index: number) {
    const targetParent = this.findNodeById(this.nodes, parent.data.id);
    if (targetParent) {
      if (targetParent.children) {
        targetParent.children.splice(index, 0, nodeToMove);
      } else {
        targetParent.children = [nodeToMove];
      }
    } else if (this.isTreeRoot(parent)) {
      this.nodes.splice(index, 0, nodeToMove);
    }
  }

  private isTreeRoot(node: TreeNode) {
    return node.level === 0 && node.parent == null;
  }

  private findNodeById(nodes: GpTreeNode[], nodeId: number): GpTreeNode {
    if (!nodes || !nodes.length)
      return null;

    for (const node of nodes) {
      if (node.id === nodeId) {
        return node;
      }
      if (node.children && node.children.length > 0) {
        const found = this.findNodeById(node.children, nodeId);
        if (found) {
          return found;
        }
      }
    }
    return null;
  }

  private removeNodeById(nodeId: number): void {
    const nodeToMove = this.findNodeById(this.nodes, nodeId);
    this.removeNode(nodeToMove);
  }

  private removeNode(node: GpTreeNode): void {
    let index = this.nodes.indexOf(node);
    if (index >= 0) {
      this.nodes.splice(index, 1);
      return;
    }

    for (const node of this.nodes) {
      if (node.children && node.children.length > 0) {
        let index = node.children.indexOf(node);
        if (index >= 0) {
          node.children.splice(index, 1);
          return;
        }
      }
    }
  }

  private removeNodeFrom(nodeToMove: GpTreeNode, nodes: GpTreeNode[]): boolean {
    const index = nodes.indexOf(nodeToMove);
    if (index < 0)
      return false;
    return nodes.splice(index, 1).length > 0;
  }

  // ========== Fim Eventos da Árvore de Menu ==========
}
