import { SegmentationsConfiguration } from '../../../../../../shared/components/business/campaigns/segmentation-parametrization/segmentations-configuration';
import { AuthenticationStates, FeatureTypes } from './features-types';

export class CustomMenu {
  id: string;
  campaignId: string;
  createDate: Date;
  active: boolean;
  segmentations: SegmentationsConfiguration;
  menuItems: Array<CustomMenuItem>;

  static empty(campaignId: string): CustomMenu {
    return {
      campaignId: campaignId,
      createDate: new Date(),
      active: true,
      menuItems: []
    } as CustomMenu;
  }
}

export class CustomMenuItem {
  featureType: FeatureTypes;
  featureId: string;
  itemText: string;
  authenticationState: AuthenticationStates;
  menuItemIndex: number;
  segmentations?: SegmentationsConfiguration;
  subItems?: Array<CustomMenuItem>;

  public static fromCampaignSitePageParam(pageParam: any) {
    return {
      featureType: pageParam.type,
      featureId: null,
      itemText: pageParam.text,
      authenticationState: pageParam.requireAuth === true ? AuthenticationStates.AUTHENTICATED : AuthenticationStates.BOTH,
      menuItemIndex: pageParam.menuItemIndex
    } as CustomMenuItem;
  }

  public static fromCampaignSiteMenuFeature(menuFeature: any) {
    return {
      featureType: menuFeature.featureType,
      featureId: menuFeature.featureId,
      itemText: menuFeature.menuItem.text,
      authenticationState: menuFeature.menuItem.requireAuth === true ? AuthenticationStates.AUTHENTICATED : AuthenticationStates.BOTH,
      menuItemIndex: menuFeature.menuItem.menuItemIndex,
      segmentations: menuFeature.segmentation
    } as CustomMenuItem;
  }
}
