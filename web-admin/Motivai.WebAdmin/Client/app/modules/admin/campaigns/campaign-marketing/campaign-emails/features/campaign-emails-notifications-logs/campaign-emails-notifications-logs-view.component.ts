import { Component, OnInit, ViewChild } from '@angular/core';
import { TabsetComponent } from 'ng2-bootstrap';
import { CampaignEmailsNotificationsLogsEditComponent } from './campaign-emails-notification-logs-edit/campaign-emails-notification-logs-edit.component';
import { CampaignEmailsNotificationsLogsListComponent } from './campaign-emails-notifications-logs-list/campaign-emails-notifications-logs-list.component';

@Component({
  selector: 'campaign-emails-notifications-logs-view',
  templateUrl: './campaign-emails-notifications-logs-view.component.html'
})
export class CampaignEmailsNotificationsLogsViewComponent implements OnInit {
  @ViewChild('tabs') tabs: TabsetComponent;
  @ViewChild('list') list: CampaignEmailsNotificationsLogsListComponent;
  @ViewChild('edit') edit: CampaignEmailsNotificationsLogsEditComponent;

  constructor() {}

  ngOnInit(): void {
    this.tabs.tabs[1].disabled = true;
  }

  refreshGrid() {
    this.list.findEmailsNotificationsLogs();
    this.edit.clear();
    this.tabs.tabs[1].disabled = true;
  }

  showEmailsLogDetails(event) {
    this.edit.findCampaignNotificationEmailLogById(event.id);
    this.tabs.tabs[1].disabled = false;
    this.tabs.tabs[1].active = true;
  }
}
