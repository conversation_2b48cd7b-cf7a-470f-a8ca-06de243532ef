import { Item } from '../../../../../../shared/models/item';

export enum FeatureTypes {
  LOGIN = 'LOGIN',
  HOME = 'HOME',
  CAMPAIGNS_GROUP_ACCESS = 'CAMPAIGNS_GROUP_ACCESS',
  HOW_TO_PARTICIPATE = 'HOW_TO_PARTICIPATE',
  REGULAMENT = 'REGULAMENT',
  MECHANIC = 'MECHANIC',
  RANKING = 'RANKING',
  ACCOUNT_OPERATORS = 'ACCOUNT_OPERATORS',
  PARTICIPANTS_INVITATION = 'PARTICIPANTS_INVITATION',
  PARTICIPANTS_MANAGEMENT_VIEW = 'PARTICIPANTS_MANAGEMENT_VIEW',
  POINTS_EXTRACT = 'POINTS_EXTRACT',
  CATALOG = 'CATALOG',
  BILLS_PAYMENT = 'BILLS_PAYMENT',
  POINTS_DISTRIBUTION = 'POINTS_DISTRIBUTION',
  P2P_TRANSFER = 'P2P_TRANSFER',
  IMAGES_GALLERY = 'IMAGES_GALLERY',
  VIDEOS_GALLERY = 'VIDEOS_GALLERY',
  POSTS = 'POSTS',
  PARTICIPANTS_POSTS = 'PARTICIPANTS_POSTS',
  DYNAMIC_PAGES = 'DYNAMIC_PAGES',
  DYNAMIC_SECTIONS = 'DYNAMIC_SECTIONS',
  SURVEYS = 'SURVEYS',
  DYNAMIC_FORMS = 'DYNAMIC_FORMS',
  CAMPAIGN_PARTICIPANTS_GOALS = 'CAMPAIGN_PARTICIPANTS_GOALS',
  PARTICIPANT_PERFORMANCE_EVALUATIONS = 'PARTICIPANT_PERFORMANCE_EVALUATIONS',
  ELEARNINGS = 'ELEARNINGS',
  RAFFLES = 'RAFFLES',
  POINTS_ENGINES_ACCUMULATION = 'POINTS_ENGINES_ACCUMULATION',
  PREPAID_CARDS = 'PREPAID_CARDS',
  PROMOTIONS_SEARCH = 'PROMOTIONS_SEARCH',
  FAQ = 'FAQ',
  CONTACT = 'CONTACT',
  LINK = 'LINK',
  NONE = 'NONE',
}

export function canBePublic(featureType: FeatureTypes): boolean {
  return (
    featureType !== FeatureTypes.LINK &&
    featureType !== FeatureTypes.POINTS_EXTRACT &&
    featureType !== FeatureTypes.BILLS_PAYMENT &&
    featureType !== FeatureTypes.PARTICIPANTS_INVITATION &&
    featureType !== FeatureTypes.DYNAMIC_FORMS &&
    featureType !== FeatureTypes.CAMPAIGN_PARTICIPANTS_GOALS &&
    featureType !== FeatureTypes.POINTS_ENGINES_ACCUMULATION &&
    featureType !== FeatureTypes.POINTS_DISTRIBUTION &&
    featureType !== FeatureTypes.P2P_TRANSFER &&
    featureType !== FeatureTypes.PARTICIPANT_PERFORMANCE_EVALUATIONS &&
    featureType !== FeatureTypes.PROMOTIONS_SEARCH &&
    featureType !== FeatureTypes.ELEARNINGS
  );
}

export enum AuthenticationStates {
  BOTH = 'BOTH',
  AUTHENTICATED = 'AUTHENTICATED',
  UNAUTHENTICATED = 'UNAUTHENTICATED'
}

export function createAuthenticationStatesItems(): Array<Item> {
  return [
    Item.of(AuthenticationStates.BOTH, 'Logado ou não logado'),
    Item.of(AuthenticationStates.AUTHENTICATED, 'Logado'),
    Item.of(AuthenticationStates.UNAUTHENTICATED, 'Não logado')
  ];
}
