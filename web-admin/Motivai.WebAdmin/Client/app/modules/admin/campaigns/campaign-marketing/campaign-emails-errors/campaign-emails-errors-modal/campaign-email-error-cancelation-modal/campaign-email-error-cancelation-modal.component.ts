import { GpModalComponent } from '../../../../../../../shared/components/gp-modal/gp-modal.component';
import { Component, OnInit, ViewChild } from '@angular/core';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';

@Component({
    selector: 'campaign-email-error-cancelation-modal',
    templateUrl: './campaign-email-error-cancelation-modal.component.html'
})
export class CampaignEmailErrorCancelationModalComponent implements OnInit {
    @ViewChild('modal') modal: GpModalComponent;
    @ViewChild('alert') alert: GpAlertComponent;

    reason: string = '';

    loading: boolean = false;

    constructor() { }

    ngOnInit(): void { }

    openModal(emailId) {
        if (!emailId)
            return this.alert.showWarning('Email inválido.');
        this.modal.show();
    }

    cancelEmail() {

    }
}
