import { Component, OnInit, ViewChild } from '@angular/core';
import { TabsetComponent, TabDirective } from 'ng2-bootstrap';
import { CampaignCommunicationListComponent } from '../../campaign-relationship/campaign-communication/campaign-communication-list/campaign-communication-list.component';
import { CampaignCommunicationEditComponent } from '../../campaign-relationship/campaign-communication/campaign-communication-edit/campaign-communication-edit.component';
import { CampaignCommunicationRulesEditComponent } from './campaign-communication-rules-edit/campaign-communication-rules-edit.component';
import { CampaignCommunicationRulesListComponent } from './campaign-communication-rules-list/campaign-communication-rules-list.component';

@Component({
  selector: 'app-campaign-communication-rules',
  templateUrl: './campaign-communication-rules.component.html'
})
export class CampaignCommunicationRulesComponent implements OnInit {
  @ViewChild('tabs') tabs: TabsetComponent;
  @ViewChild('listComponent') listComponent: CampaignCommunicationRulesListComponent;
  @ViewChild('editComponent') editComponent: CampaignCommunicationRulesEditComponent;

  constructor() { }

  ngOnInit() {
  }


  editCommunicationRule($event: string) {
    this.editComponent.clear();
    this.tabs.tabs[1].active = true;
    this.editComponent.communicationRulesId = $event;
  }


  checkSelectEvent(ev?: any) {
    if (!(ev instanceof TabDirective)) return false;
    return true;
  }

  refreshGrid(ev?: any) {
    if (this.checkSelectEvent(ev)) {
      this.editComponent.clear();
      this.listComponent.findCommunicationRules();
    }
  }


}
