import { Component, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { PERMISSION_CAMPAIGNS_MARKETING_NOTIFICATION_LOGS, PERMISSION_CAMPAIGNS_MARKETING_NOTIFICATION_PARAMETRIZATIONS } from '../../../../../../core/auth/access-points';
import { AuthStore } from '../../../../../../core/auth/auth.store';
import { RxjsHelpers } from '../../../../../../shared/helpers/rxjs-helpers';
import { CampaignStore } from '../../../campaign.store';

@Component({
    selector: 'campaign-emails-view',
    templateUrl: './campaign-emails-view.component.html'
})
export class CampaignEmailsViewComponent implements OnInit {
    private _campaign$: Subscription;


    campaignId: string = '';

    constructor(private _authStore: AuthStore, private _campaignStore: CampaignStore) { }

    ngOnInit(): void {
      this._campaign$ = this._campaignStore.asObservable
        .subscribe(id => this.campaignId = id);
    }

    ngOnDestroy(): void {
      RxjsHelpers.unsubscribe(this._campaign$);
    }

    get canViewEmailsParametrizations() {
      return this._authStore.role.PERMISSION_CAMPAIGNS_MARKETING_NOTIFICATION_PARAMETRIZATIONS;
    }

    get canViewEmailsLogs() {
      return this._authStore.role.PERMISSION_CAMPAIGNS_MARKETING_NOTIFICATION_LOGS;
    }

    get canViewSmsLogs() {
      return this._authStore.role.PERMISSION_CAMPAIGNS_MARKETING_NOTIFICATION_SMS_LOGS;
    }
}
