<gp-card [first]="true" title="Informações de E-mail">
    <gp-form-row>
        <gp-form-col cols="12 4">
            <gp-simple-input label="Destinatário">
                <input type="text" name="nameFrom" disabled="true" class="form-control" [(ngModel)]="error.from.nameFrom" />
            </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 4">
            <gp-simple-input label="E-mail do Destinatário">
                <input type="text" name="to" disabled="true" class="form-control" [(ngModel)]="error.to" />
            </gp-simple-input>
        </gp-form-col>
    </gp-form-row>
    <gp-form-row>
        <gp-form-col cols="12">
            <gp-simple-input label="Descrição do Erro">
                <input type="text" name="description" disabled="true" class="form-control" [(ngModel)]="error.error" />
            </gp-simple-input>
        </gp-form-col>
    </gp-form-row>
</gp-card>
<gp-card title="Ultima tentativa">
    <gp-form-row>
        <gp-form-col cols="12 4">
            <gp-simple-input label="Assunto">
                <input type="text" name="subject" disabled="true" class="form-control" [(ngModel)]="error.subject" />
            </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 4">
            <gp-simple-input label="Tipo">
                <input type="text" name="type" disabled="true" class="form-control" [(ngModel)]="error.type" />
            </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 4">
            <gp-spinner-button text="Visualzar E-mail" loadingText="processando..." bootstrapClass="primary"
                icon="envelope" [loading]="loading" (click)="openEmailTemplateModal()" marginTop="26px"></gp-spinner-button>
        </gp-form-col>
    </gp-form-row>
</gp-card>
<gp-card title="Tentativas">
    <gp-form-row>
        <gp-form-col cols="12">
            <gp-grid name="error" [rows]="error.attemptsHistory" [columns]="['Nº Tentativa', 'Email', 'Motivo']"
                [fields]="['attempt', 'email', 'reason']" [loading]="loading" [showActive]="false"
                [showPagination]="false" [showEdit]="false" [showDelete]="false"
                emptyMessage="Nenhuma tentativa encontrada.">
            </gp-grid>
        </gp-form-col>
    </gp-form-row>
</gp-card>
<gp-card>
    <gp-form-row>
        <gp-form-col cols="12" [inputGroup]="false">
            <gp-spinner-button text="Cancelar Email" loadingText="processando..." [disabled]="loading || !error.length" pull="right" bootstrapClass="default"
                icon="envelope" [loading]="loading" (click)="cancelEmail()" marginLeft="5px"></gp-spinner-button>
            <gp-spinner-button text="Reenviar Email" loadingText="processando..." [disabled]="disableButtons" pull="right" bootstrapClass="success"
                icon="envelope" [loading]="loading" (click)="confirmEmail()"></gp-spinner-button>
        </gp-form-col>
    </gp-form-row>
</gp-card>
<campaign-email-error-template-modal #emailTemplateModal></campaign-email-error-template-modal>
<campaign-participant-email-modal #newEmailModal (email)="retryEmail($event)"></campaign-participant-email-modal>
<campaign-email-error-cancelation-modal #cancelationModal></campaign-email-error-cancelation-modal>
