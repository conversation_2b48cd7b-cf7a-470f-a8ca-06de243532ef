import { <PERSON>mponent, OnInit, ViewChild, Input, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { NgForm } from '@angular/forms';
import { Subscription } from 'rxjs/Subscription';

import { AuthStore } from '../../../../../../core/auth/auth.store';
import { PERMISSION_CAMPAIGNS_MARKETING_RELATIONSHIP_RULE_WHATSAPP_FROM } from '../../../../../../core/auth/access-points';

import { Item } from '../../../../../../shared/models/item';
import { RxjsHelpers } from '../../../../../../shared/helpers/rxjs-helpers';

import { GpModalComponent } from '../../../../../../shared/components/gp-modal/gp-modal.component';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';

import { CampaignStore } from '../../../campaign.store';
import { CampaignCommunicationRulesService } from '../campaign-communication-rules.service';
import { CommunicationRuleHelper } from './communication-rule-helper';
import { CommunicationRulesTargetAudience } from '../models/communication-rules-target-audience';

@Component({
  selector: 'campaign-communication-rules-edit',
  templateUrl: './campaign-communication-rules-edit.component.html',
  styles: [
    `
      .grid-element .balance-card {
        padding-top: 5px;
      }

      .grid-element p.element-label {
        min-height: 42px;
      }
    `
  ]
})
export class CampaignCommunicationRulesEditComponent implements OnInit, OnDestroy {
  @ViewChild('alert') alert: GpAlertComponent;
  @ViewChild('communicationRulesForm') surveyForm: NgForm;
  @ViewChild('gpModal') gpModal: GpModalComponent;

  campaignId: string = '';
  loading: boolean = false;
  uploading: boolean = false;
  participantsGroups: Array<Item> = [];

  scheduleId: string;
  processingDetailId: string;

  processingDetailValues: any = {};

  communicationRuleId: string;
  communicationRule: any = {
    id: null,
    email: {},
    sms: {},
    whatsApp: {},
    targetAudience: {},
    processingDates: []
  };

  targetAudience: string;
  targerAudiences: Item[] = [
		Item.of(CommunicationRulesTargetAudience.INTERNALPARTICIPANTS, "Interno Campanha"),
		Item.of(CommunicationRulesTargetAudience.EXTERNALPARTICIPANTS, 'Externo Campanha')
	];

  gridProcessingDates: any = [];
  processingDates: any = [];
  days: number[] = [];

  day: string = '';
  hour: string = '';

  private _campaign$: Subscription;
  sendingApproval: boolean;

  constructor(
    private _campaignStore: CampaignStore,
    private communicationRulesService: CampaignCommunicationRulesService,
    private _authStore: AuthStore
  ) {}

  ngOnInit() {
    this.campaignId = this._campaignStore.id;
    for (let i = 1; i <= 31; i++) {
      this.days.push(i);
    }

    this._campaign$ = this._campaignStore.asObservable.subscribe(id => (this.campaignId = id));
  }

  ngOnDestroy(): void {
    RxjsHelpers.unsubscribe(this._campaign$);
  }

  @Input()
  set communicationRulesId(v: string) {
    if (v) {
      this.communicationRuleId = v;
      this.findCommunicationRule();
    }
  }

  get canInformWhasAppSenderNumber() {
    return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_MARKETING_RELATIONSHIP_RULE_WHATSAPP_FROM);
  }

  get isTargetAudienceExternalParticipants(): boolean {
    return this.communicationRule && (this.communicationRule.targetAudience && this.communicationRule.targetAudience.externalParticipants);
  }

  handleTargetAudience(targetAudience: CommunicationRulesTargetAudience) {
    switch (targetAudience) {
      case CommunicationRulesTargetAudience.INTERNALPARTICIPANTS:
        this.markTargetAudienceAsInternal();
        break;
      case CommunicationRulesTargetAudience.EXTERNALPARTICIPANTS:
        this.markTargetAudienceAsExternal();
        break;
      default:
        this.markTargetAudienceAsInternal();
    }
  }

  markTargetAudienceAsExternal(): void {
    if (!this.communicationRule.targetAudience)
      this.communicationRule.targetAudience = {};

    this.communicationRule.targetAudience.internalParticipants = false;
    this.communicationRule.targetAudience.externalParticipants = true;
    this.targetAudience = CommunicationRulesTargetAudience.EXTERNALPARTICIPANTS;
  }

  markTargetAudienceAsInternal(): void {
    if (!this.communicationRule.targetAudience)
      this.communicationRule.targetAudience = {};

    this.communicationRule.targetAudience.externalParticipants = false;
    this.communicationRule.targetAudience.internalParticipants = true;
    this.targetAudience = CommunicationRulesTargetAudience.INTERNALPARTICIPANTS;
  }

  setEmailFileUrl(fileUrl: string): void {
    if (!this.communicationRule.targetAudience)
      this.communicationRule.targetAudience = {};

    this.communicationRule.targetAudience.fileUrl = fileUrl;
  }

  findCommunicationRule() {
    this.loading = true;
    this.communicationRulesService.getById(this.campaignId, this.communicationRuleId).subscribe(
      (communicationRules: any) => {
        if (!communicationRules) this.alert.handleAndShowError('Ocorreu um erro ao encontrar a regra, tente novamente');

        this.communicationRule = communicationRules;
        this.handleCommnicationRule();
        this.processingDates = communicationRules.processingDates;
        if (!this.processingDates) {
          this.processingDates = [];
        }
        this.communicationRule.periodity = 'DAY_OF_THE_WEEK';
        this.prepareGrid();
        this.loading = false;
      },
      (err: any) => {
        this.alert.handleAndShowError(err);
        this.loading = false;
      }
    );
  }

  get hasProcessingDetail(): boolean {
    return !!this.processingDetailValues
  }

  private handleCommnicationRule() {
    if (this.communicationRule.whatsApp) {
      this.communicationRule.whatsApp.mappedtemplateReviewStatus = CommunicationRuleHelper.maptemplateReviewStatus(
        this.communicationRule.whatsApp.templateReviewStatus
      );
    } else {
      this.communicationRule.whatsApp = {};
    }

    if (!this.communicationRule.targetAudience) {
      this.handleTargetAudience(null);
    } else {
      this.handleTargetAudience(
        this.communicationRule.targetAudience.internalParticipants 
        ? CommunicationRulesTargetAudience.INTERNALPARTICIPANTS
        : CommunicationRulesTargetAudience.EXTERNALPARTICIPANTS
      );
    }

    this.communicationRule.whatsApp.mappedtemplateReviewStatus = CommunicationRuleHelper.maptemplateReviewStatus(
      this.communicationRule.whatsApp.templateReviewStatus
    );
  }

  prepareGrid() {
    if (this.communicationRule.processingDates != null) {
      this.communicationRule.processingDates.forEach((processingDate: any) => {
        let when = processingDate.when == null ? null : processingDate.when.split('-');

        if (when != null)
          this.gridProcessingDates.push({
            id: processingDate.id,
            active: processingDate.active,
            day: CommunicationRuleHelper.mapDayDescription(when[0]),
            hour: when[1],
            status: CommunicationRuleHelper.mapStatusDescription(processingDate.status)
          });
      });
    }
  }

  showDetails(processId: string) {
    this.loading = true;
    this.communicationRulesService.getProcessingDetails(this.campaignId, this.communicationRuleId, processId).subscribe(
      details => {
        if (details) {
          this.scheduleId = processId;
          this.processingDetailId = details.id;
          this.processingDetailValues = details.counters;
        } else {
          this.processingDetailValues = {};
        }
        this.gpModal.show();
        this.loading = false;
      },
      err => {
        this.alert.handleAndShowError(err);
        this.loading = false;
      }
    );
  }

  saveDataCommunicationRules() {
    if (this.campaignId) {
      this.loading = true;

      this.communicationRulesService.save(this.campaignId, this.communicationRule).subscribe(
        (communicationRuleId: any) => {
          if (!communicationRuleId) {
            return this.alert.handleAndShowError('Ocorreu um erro ao salvar a regra, tente novamente');
          }

          this.alert.showSuccess('Regra de comunicação salva com sucesso');
          this.communicationRule.id = communicationRuleId;
          this.communicationRuleId = communicationRuleId;
          this.loading = false;
        },
        (err: any) => {
          this.alert.handleAndShowError(err);
          this.loading = false;
        }
      );
    }
  }

  templateIsReuse(): boolean {
    return this.communicationRule.whatsApp.active && this.communicationRule.whatsApp.reusePartnerTemplate;
  }

  templateIsNotSent(): boolean {
    return this.communicationRule.whatsApp.active && this.communicationRule.whatsApp.templateReviewStatus != 'NOT_SENT';
  }

  templateIsNotApproval(): boolean {
    return this.communicationRule.whatsApp.active && this.communicationRule.whatsApp.templateReviewStatus != 'APPROVED';
  }

  saveCommunicationRules() {
    if (!this.templateIsReuse) {
      if (this.templateIsNotSent() && this.templateIsNotApproval()) {
        return this.alert.showWarning('Whatsapp está ativo e o template não foi aprovado até o momento');
      }
    }

    if (this.campaignId) {
      this.loading = true;

      this.communicationRulesService.save(this.campaignId, this.communicationRule).subscribe(
        (communicationRules: any) => {
          this.loading = false;
          if (!communicationRules) {
            return this.alert.handleAndShowError('Ocorreu um erro ao salvar a regra, tente novamente');
          }

          this.alert.showSuccess('Regra de comunicação salva com sucesso');
        },
        (err: any) => {
          this.alert.handleAndShowError(err);
          this.loading = false;
        }
      );
    }
  }

  addToGridAndSave() {
    if (!this.day && !this.hour) {
      return this.alert.showError('Preencha os campos para prosseguir');
    }

    this.processingDates.push({
      when: `${this.day}-${this.hour}`,
      active: true,
      status: 'WAITING'
    });

    this.saveProcessingDates();
  }

  updateActiveProcessingDate(processingDate: any) {
    if (processingDate.active) {
      if (this.templateIsNotApproval()) {
        processingDate.active = processingDate.active ? true : false;
        return this.alert.showWarning('Whatsapp está ativo e o template não foi aprovado até o momento');
      }
    }
    const index = this.processingDates.findIndex(p => p.id == processingDate.id);
    this.processingDates[index].active = processingDate.active;
    this.saveProcessingDates();
  }

  saveProcessingDates() {
    if (!this.communicationRuleId) {
      return this.alert.showError('Regra de comunicação não identificada');
    }

    this.loading = true;
    if (!this.processingDates) {
      this.processingDates = [];
    }

    this.communicationRulesService
      .saveProcessingDates(this.campaignId, this.communicationRuleId, this.processingDates)
      .subscribe(
        (processingDates: any) => {
          if (!processingDates) {
            if (this.day && this.hour) this.processingDates.pop();

            return this.alert.handleAndShowError(
              'Ocorreu um erro ao atualizar as datas de processamento, tente novamente'
            );
          }

          if (this.day && this.hour) {
            this.gridProcessingDates.push({
              day: CommunicationRuleHelper.mapDayDescription(this.day),
              hour: this.hour,
              active: true,
              status: CommunicationRuleHelper.mapStatusDescription('WAITING')
            });
          }
          this.clearFormProcesssingDate();
          this.loading = false;
        },
        (err: any) => {
          this.loading = false;
          this.processingDates.pop();
          this.alert.handleAndShowError(err);
        }
      );
  }

  clearFormProcesssingDate(): void {
    this.day = '';
    this.hour = '';
  }

  clear() {
    this.communicationRuleId = '';
    this.communicationRule = {
      id: null,
      email: {},
      sms: {},
      whatsApp: {},
      targetAudience: {},
      processingDates: []
    };

    this.gridProcessingDates = [];
    this.processingDates = [];
  }

  clearDetails() {
    this.processingDetailValues = {};
  }

  onUploadError(error: any) {
    this.alert.handleAndShowError(error);
  }

  onUploadComplete() {
    this.alert.showInfo('Upload finalizado.');
  }

  get templateNotSendToApproval() {
    return (
      this.communicationRule.whatsApp.templateReviewStatus == 'NOT_SENT' ||
      this.communicationRule.whatsApp.templateReviewStatus == 'REJECTED'
    );
  }

  sendWhatsAppTemplateToApproval() {
    if (!this.communicationRule.whatsApp) {
      return this.alert.showError('Configurações de WhatsApp inválidas');
    }

    const whatsAppSendingTemplateReview: any = {
      name: this.communicationRule.name,
      components: {
        body: {
          text: this.communicationRule.whatsApp.template
        }
      },
      NotificationEmail: this.communicationRule.whatsApp.notificationTemplateApprovalEmail
    };

    if (this.communicationRule.whatsApp.imageUrl) {
      whatsAppSendingTemplateReview.components.header = {
        text: this.communicationRule.whatsApp.imageUrl
      };
    }

    this.sendingApproval = true;
    this.communicationRulesService
      .sendWhatsAppTemplateToApproval(this.campaignId, this.communicationRuleId, whatsAppSendingTemplateReview)
      .subscribe(
        sended => {
          this.sendingApproval = false;
          if (sended && sended.templatePartnerId) {
            this.communicationRule.whatsApp = sended;
            return this.alert.showSuccess('Template enviado para análise, aguarde a aprovação');
          } else {
            return this.alert.showSuccess(
              'Não foi possível enviar o template para análise, por favor, tente novamente'
            );
          }
        },
        err => {
          this.sendingApproval = false;
          this.alert.showError(err);
        }
      );
  }

  exportProcessingErrors() {
    this.loading = true;
    this.communicationRulesService.exportProcessingErrors(this.campaignId, this.communicationRuleId, this.processingDetailId).subscribe(res => {
      this.loading = false;
      if (res) {
        window.open(res, '_blank');
      }
    }, err => {
      this.loading = false;
      this.alert.handleAndShowError(err);
    });
  }
}
