import { Component, OnInit, ViewChild } from "@angular/core";
import { TabsetComponent } from "ng2-bootstrap";
import { CampaignEmailsNotificationsLogsSmsListComponent } from "./campaign-emails-notification-logs-sms-list/campaign-emails-notifications-logs-sms-list.component";
import { CampaignEmailsNotificationsLogsSmsEditComponent } from "./campaign-emails-notification-logs-sms-edit/campaign-emails-notifications-logs-sms-edit.component";

@Component({
    selector: 'campaig-email-notifications-logs-sms-view',
    templateUrl: './campaign-emails-notifications-logs-sms-view.component.html'
})
export class CampaignEmailsNotificationsLogsSmsViewComponent implements OnInit {

    @ViewChild('tabs') tabs: TabsetComponent;

    @ViewChild('list') list: CampaignEmailsNotificationsLogsSmsListComponent;

    @ViewChild('edit') edit: CampaignEmailsNotificationsLogsSmsEditComponent;

    ngOnInit(): void {
        this.tabs.tabs[1].disabled = true;
    }
    
    refreshGrid() {
        this.list.findSmsNotificationsLogs();
        this.edit.clear();
        this.tabs.tabs[1].disabled = true;
      }

    showSmsLogDetails(event) {
        this.edit.findCampaignNotificationSmsLogById(event.id);
        this.tabs.tabs[1].disabled = false;
        this.tabs.tabs[1].active = true;
      }
}