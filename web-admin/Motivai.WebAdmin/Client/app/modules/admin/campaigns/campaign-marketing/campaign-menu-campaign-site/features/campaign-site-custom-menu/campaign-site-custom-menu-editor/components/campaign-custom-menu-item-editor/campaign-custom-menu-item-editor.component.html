<gp-modal #modal title="Criação de Item no Menu" width="900px" (onClose)="clear()">
	<spinner [overlay]="true" [show]="loading"></spinner>

	<gp-card>
		<h4>Selecione o que deseja adicionar no menu</h4>
		<gp-form-row>
			<gp-form-col cols="12 6">
				<gp-input-radio name="featureGroupType" label="Página do Site Campanha" radioValue="CampaignSitePage"
					[(ngModel)]="featureGroupType">
				</gp-input-radio>

				<gp-input-radio name="featureGroupType" label="Feature Cadastrada na Campanha" radioValue="CampaignSiteFeature"
					[(ngModel)]="featureGroupType">
				</gp-input-radio>

				<gp-input-radio name="featureGroupType" label="Menu com Link Externo" radioValue="CustomMenuItemLink"
					[(ngModel)]="featureGroupType">
				</gp-input-radio>

				<gp-input-radio name="featureGroupType" label="Menu com Texto" radioValue="CustomMenuItem"
					[(ngModel)]="featureGroupType">
				</gp-input-radio>
			</gp-form-col>
		</gp-form-row>

		<div *ngIf="isCampaignPageSelected">
			<hr />
			<h4>Selecione a página do site campanha</h4>

			<gp-form-row>
				<gp-form-col cols="12 6">
					<gp-simple-input label="Página do site" [required]="false">
						<gp-select name="campaignSitePage" [multiple]="false" placeholder="Selecione a página"
							[items]="campaignSitePagesItems" [(ngModel)]="customItem.featureType" [required]="true"
							(change)="onCampaignSitePageItemChange()">
						</gp-select>
					</gp-simple-input>
				</gp-form-col>
			</gp-form-row>
		</div>

		<div *ngIf="isCampaignFeatureSelected">
			<hr />
			<h4>Selecione a feature cadastrada para exibição</h4>

			<gp-form-row>
				<gp-form-col cols="12 6 6">
					<gp-simple-input label="Feature da campanha">
						<gp-select name="featureType" [multiple]="false" placeholder="Selecione uma feature da campanha"
							[items]="campaignFeatureTypes" [(ngModel)]="customItem.featureType" [required]="true"
							(change)="onFeatureTypeChange()">
						</gp-select>
					</gp-simple-input>
				</gp-form-col>

				<gp-form-col cols="12 6">
					<gp-simple-input label="Registro" [required]="false">
						<gp-select name="campaignFeatureItem" [multiple]="false" placeholder="Selecione"
							[items]="campaignFeatureItems" [(ngModel)]="customItem.featureId" [required]="true"
							(change)="onCampaignFeatureItemChange()">
						</gp-select>
					</gp-simple-input>
				</gp-form-col>
			</gp-form-row>
		</div>

		<div *ngIf="isCampaignFeatureLinkSelected">
			<hr />
			<h4>Selecione a feature cadastrada para exibição</h4>

			<gp-form-row>
				<gp-form-col cols="12 6">
					<gp-simple-input label="URL" [required]="true">
						<input type="text" class="form-control" name="itemText" [(ngModel)]="customItem.featureId">
					</gp-simple-input>
				</gp-form-col>
			</gp-form-row>
		</div>

		<div *ngIf="!!featureGroupType">
			<hr />
			<h4>Configure o que será exibido no menu e para quem será exibido</h4>

			<gp-form-row>
				<gp-form-col cols="12 6 6">
					<gp-simple-input label="Texto no item de menu">
						<input type="text" class="form-control" name="itemText" [(ngModel)]="customItem.itemText" [required]="true">
					</gp-simple-input>
				</gp-form-col>

				<gp-form-col cols="12 6" *ngIf="canSelectAuthenticationState">
					<gp-simple-input label="Exibir para os usuários" [required]="true">
						<gp-select name="authenticationState" [allowClear]="false" [multiple]="false" placeholder="Selecione"
							[items]="participantAuthenticationStates" [(ngModel)]="customItem.authenticationState"
							[required]="true">
						</gp-select>
					</gp-simple-input>
				</gp-form-col>
			</gp-form-row>
		</div>
	</gp-card>

	<gp-card [last]="true">
		<gp-form-row>
			<gp-form-col cols="12" [inputGroup]="false">
				<gp-spinner-button type="submit" [pink]="true" text="Adicionar" pull="right" icon="plus" marginLeft="1em"
					(click)="addCustomItem()">
				</gp-spinner-button>

				<gp-spinner-button type="button" bootstrapClass="default" text="Voltar" pull="right" icon="arrow-left"
					marginRight="5px" (click)="closeModal()">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</gp-card>
</gp-modal>
