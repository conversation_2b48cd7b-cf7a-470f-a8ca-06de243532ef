<!-- TODO  EMAIL NOTIFICATION COMPONENT -->
<gp-card [last]="true">
<div class="row col-xs-12 bottom-m1">
	<h4>Configuração de notificações transacionais</h4>
</div>
<div class="row bottom-m1" *ngIf="isFullCampaign">
	<div class="col-xs-6">
		<label>Selecione um tipo de transação</label>
		<select name="transactionType" class="form-control" (change)="onTypeChange($event.target.value)">
			<option value="" selected disabled>Selecione</option>
			<option value="Participant">Novo Participante</option>
			<option value="FirstAccess">Primeiro Acesso</option>
			<option value="Points">Pontos Creditados</option>
			<option value="PointsDistributedNewParticipant">Distribuição de Pontos para Novo Participante</option>
			<option value="TokenIssue">Códigos de Segurança</option>
			<option value="PasswordRecovery">Recuperação de Senha</option>
			<option value="PointsPurchaseReceived">Pedido de Compra de Pontos</option>
			<option value="Order">Pedido para o Participante</option>
			<option value="RechargeOrderReceived">Pedido de Recarga de Celular</option>
			<option value="BillPaymentOrderReceived">Pedido de Pague Contas</option>
			<option value="PrepaidCardOrderReceived">Cartão Pré-Pago: Pedido Recebido</option>
			<option value="BankTransferOrderReceived">Transferência Bancária: Pedido Recebido</option>
			<option value="BankTransferOrderTransfered">Transferência Bancária: Transferência Efetuada</option>
			<option value="OrderRefund">Estorno do Pedido</option>
			<option value="OrderCancellation">Cancelamento do Pedido</option>
			<option value="PartnerOrder">Pedido para o Parceiro e Fabricante</option>
			<option value="OrderTracking">Situação de Entrega de Pedido</option>
			<option value="OrderDelivery">Pedido Entregue</option>
			<option value="OrderItemEvent">Tracking de Item do Pedido</option>
			<option value="OrderVirtualItemEvent">Link de Vale Virtual do Pedido</option>
			<option value="OrderPricing">Precificação de Pedido</option>
			<option value="ProductAvailable">Produto Disponível</option>
			<option value="Login">Login</option>
			<option value="ExpiringPoints">Pontos a Expirar</option>
			<option value="FeatureApproval">Aprovação de Feature</option>
			<option value="PaymentMethodConfirmation">Notificação de Pagamento</option>
		</select>
	</div>
</div>
<div class="row bottom-m1" *ngIf="isMakeYourCampaign">
	<div class="col-xs-6">
		<label>Selecione um tipo de transação</label>
		<select name="transactionType" class="form-control" (change)="onTypeChange($event.target.value)">
			<option value="" selected disabled>Selecione</option>
			<option value="Participant">Novo Participante</option>
			<option value="PasswordRecovery">Recuperação de Senha</option>
			<option value="Order">Pedido para o Participante</option>
			<option value="OrderItemEvent">Eventos no item do pedido</option>
		</select>
	</div>
</div>
<div *ngIf="showForm">
	<div class="row">
		<div class="col-xs-12">
			<label>Ativar E-mail:</label>
			<div>
				<gp-switch [(ngModel)]="notification.email.active"></gp-switch>
			</div>
		</div>
	</div>
	<br />
	<div class="row" *ngIf="headerImageUrl">
		<div class="col-xs-12">
			<img class="header-image" [src]="headerImageUrl" title="Imagem do header do e-mail" />
		</div>
	</div>
	<div class="row top-m1">
		<div class="col-xs-12">
			<div class="top-m1 text-help" *ngIf="availableVarsHelper?.length">
				<span>Textos dinâmicos disponíveis para este tipo de e-mail:</span>
				<ul>
					<li *ngFor="let item of availableVarsHelper"><strong>@@{{item.var}}: </strong>{{item.description}}</li>
				</ul>
			</div>
		</div>
		<div class="col-xs-12">
			<div class="form-group">
				<label>Assunto do E-mail</label>
				<input type="text" class="form-control" name="subject" [(ngModel)]="notification.email.subject" />
			</div>
		</div>
		<div class="col-xs-12">
			<gp-editor id="content" name="content" [(ngModel)]="notification.email.content" required></gp-editor>
		</div>
	</div>
	<hr *ngIf="_campaignStore.isFullCampaign" />
	<div class="row top-m1" *ngIf="_campaignStore.isFullCampaign">
		<div grid="12 12 12">
			<label>Ativar SMS:</label>
			<div>
				<gp-switch [(ngModel)]="notification.sms.active"></gp-switch>
			</div>
		</div>
	</div>
	<div class="row top-m1" *ngIf="_campaignStore.isFullCampaign">
		<div grid="12 12 12">
			<gp-simple-input label="Texto do SMS">
				<input type="text" class="form-control" name="text" [maxlength]="smsMaxLength" [(ngModel)]="notification.sms.text" />
			</gp-simple-input>
			<span class="pull-right">Caracteres Restantes: {{ smsMaxLength - (notification.sms.text?.length || 0) }}</span>
		</div>
	</div>
</div>
<div class="row">
	<spinner [show]="loading"></spinner>
	<div class="col-xs-12" style="margin-top: 10px">
		<button type="button" class="btn btn-default" [routerLink]="['/campanha', campaignId, 'edicao']">Voltar</button>
		<gp-spinner-button type="button" text="Salvar" bootstrapClass="success" loadingText="Salvando" [pink]="true" icon="send"
			[disabled]="disableButton" [loading]="sending" (click)="saveEmail()"></gp-spinner-button>
	</div>
	<div class="col-xs-12">
		<div class="div-alert">
			<gp-alert #gpAlert></gp-alert>
		</div>
	</div>
</div>
</gp-card>
