import { CampaignEmailsService } from '../../../services/campaign-emails.service';
import { Component, OnInit, ViewChild } from "@angular/core";
import { AuthStore } from '../../../../../../../../core/auth/auth.store';
import { CampaignStore } from '../../../../../campaign.store';
import { Subscription } from "rxjs";
import { GpAlertComponent } from "../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component";
import { getCampaignEventTypeDescription } from '../../../models/campaign-event-types';

@Component({
    selector: 'campaign-emails-notification-logs-sms-edit',
    templateUrl: './campaign-emails-notifications-logs-sms-edit.component.html'
})
export class CampaignEmailsNotificationsLogsSmsEditComponent implements OnInit {
    @ViewChild('alert') alert: GpAlertComponent;

    private _campaign$: Subscription;

    campaignId: string = '';

    loading: boolean = false;

    public smsLog =  {
        id: "",
        type: "",
        formattedType: "",
        mobilePhone: "",
        smsContent: ""
    };

    constructor(private _authStore: AuthStore, private _campaignStore: CampaignStore, private _service: CampaignEmailsService) { }


    ngOnInit(): void {
        this._campaign$ = this._campaignStore.asObservable
            .subscribe(id => this.campaignId = id);
    }

    get canResendNotification() {
        return this._authStore.role.PERMISSION_CAMPAIGNS_MARKETING_NOTIFICATION_LOGS_RESEND;
    }

    get canShowSmsContent(){
        return this.smsLog.type != "OrderVirtualItemEvent"
    }

    findCampaignNotificationSmsLogById(logId: string){
        this.clear();
        this.loading = true;
        this._service.getCampaignNotificationsEmailsLDetailsById(this.campaignId, logId)
            .subscribe(
                response => {
                    if(response)
                        this.smsLog = response;
                        this.smsLog.formattedType = getCampaignEventTypeDescription(response.type);
                        this.smsLog.mobilePhone = response.mobilePhone;
                        this.smsLog.type = response.type;
                    this.loading = false;
                },
                err => {
                    this.loading = false;
                    this.alert.showError(err);
                }
            );
    }

    resendSmsNotification() {
        this.loading = true;
        this._service.resendSmsNotification(this.campaignId, this.smsLog.id)
            .subscribe(
                response => {
                    if (response) {
                        this.alert.showSuccess('Notificação reenviada.');
                    } else {
                        this.alert.showWarning('Não foi possível enviar a notificação, por favor, tente novamente.');
                    }
                    this.loading = false;
                },
                err => {
                    this.loading = false;
                    this.alert.showError(err);
                }
            );
    }

    clear() {
        this.smsLog = {
            id: "",
            type: "",
            formattedType: "",
            mobilePhone: "",
            smsContent: ""
        };
    }

}