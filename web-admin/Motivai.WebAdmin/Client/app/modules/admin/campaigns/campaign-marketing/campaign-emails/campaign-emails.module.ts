import { CUSTOM_ELEMENTS_SCHEMA, LOCALE_ID, NgModule } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../../../../../shared/shared.module';
import { CampaignEmailsNotificationsLogsSmsViewComponent } from './features/campaign-emails-notification-logs-sms/campaign-emails-notifications-logs-sms-view.component';
import { CampaignEmailsNotificationsLogsSmsListComponent } from './features/campaign-emails-notification-logs-sms/campaign-emails-notification-logs-sms-list/campaign-emails-notifications-logs-sms-list.component';
import { CampaignEmailsNotificationsLogsSmsEditComponent } from './features/campaign-emails-notification-logs-sms/campaign-emails-notification-logs-sms-edit/campaign-emails-notifications-logs-sms-edit.component';
import { CampaignEmailsViewComponent } from './campaign-emails-view/campaign-emails-view.component';
import { CampaignEmailsParametrizationComponent } from './features/campaign-emails-notifications-parametrization/campaign-emails-notifications-parametrization.component';
import { CampaignEmailsNotificationsLogsViewComponent } from './features/campaign-emails-notifications-logs/campaign-emails-notifications-logs-view.component';
import { CampaignEmailsNotificationsLogsListComponent } from './features/campaign-emails-notifications-logs/campaign-emails-notifications-logs-list/campaign-emails-notifications-logs-list.component';
import { CampaignEmailsNotificationsLogsEditComponent } from './features/campaign-emails-notifications-logs/campaign-emails-notification-logs-edit/campaign-emails-notification-logs-edit.component';
import { CampaignEmailsService } from './services/campaign-emails.service';

const COMPONENTS = [
  CampaignEmailsViewComponent,
  CampaignEmailsParametrizationComponent,
  CampaignEmailsNotificationsLogsViewComponent,
  CampaignEmailsNotificationsLogsListComponent,
  CampaignEmailsNotificationsLogsEditComponent,
  CampaignEmailsNotificationsLogsSmsViewComponent,
  CampaignEmailsNotificationsLogsSmsListComponent,
  CampaignEmailsNotificationsLogsSmsEditComponent,
];

@NgModule({
  declarations: [COMPONENTS],
  imports: [
    CommonModule,
    FormsModule,
    SharedModule.forRoot(),
    RouterModule.forChild([
      {
        path: '',
        component: CampaignEmailsViewComponent,
        children: [
          {
            path: 'parametrizacao',
            component: CampaignEmailsParametrizationComponent
          },
          {
            path: 'logs',
            component: CampaignEmailsNotificationsLogsViewComponent
          },
          {
            path: 'logs-sms',
            component: CampaignEmailsNotificationsLogsSmsViewComponent
          }
        ]
      }
    ])
  ],
  exports: [RouterModule],
  providers: [
    CampaignEmailsService,
    DatePipe,
    [{ provide: LOCALE_ID, useValue: 'pt-br' }]
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class CampaignEmailsModule {}
