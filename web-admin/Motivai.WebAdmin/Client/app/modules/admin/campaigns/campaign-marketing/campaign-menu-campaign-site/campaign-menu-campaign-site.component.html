<gp-card [noPaddingTop]="true" [noPaddingBottom]="true">
	<div class="row">
		<accordion [closeOthers]="true">
			<accordion-group panelClass="b0 mb-sm panel-default no-shadow" [isOpen]="true">
				<div accordion-heading>
					<label><em class="fa fa-navicon"></em> Features do Menu</label>
				</div>
				<div class="row menublock top-p1">
					<gp-menublock-item size="xs" icon="gear" color="text-primary" text="Menu Principal"
						routerLinkActive="active" routerLink="menu-principal" *ngIf="canViewMainMenu">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="list-alt" color="text-primary" text="Menu de Botões do Header"
						routerLinkActive="active" routerLink="botoes-cabecalho" *ngIf="canCreateCampaignSiteHeaderLinks">
					</gp-menublock-item>
				</div>
			</accordion-group>
		</accordion>
	</div>
</gp-card>
<router-outlet></router-outlet>
