import { Component, OnInit, ViewChild } from '@angular/core';
import { CampaignService } from '../../campaign.service';
import { CampaignStore } from '../../campaign.store';
import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';

@Component({
  selector: 'campaign-social-medias',
  templateUrl: './campaign-social-medias.component.html'
})
export class CampaignSocialMediasComponent implements OnInit {
  @ViewChild('alert') alert: GpAlertComponent;

  loading: boolean = false;
  sending: boolean = false;
  socialMedias: any = {};

  constructor(private campaignService: CampaignService, private campaignStore: CampaignStore) { }

  ngOnInit() {
    this.getSocialMedias();
  }

  getSocialMedias() {
    this.loading = true;
    this.campaignService.getSocialMedias(this.campaignStore.id)
      .subscribe(
        socialmedias => {
          this.loading = false;
          this.socialMedias = socialmedias || {};
        },
        err => {
          this.loading = false;
          this.alert.handleAndShowError(err);
        }
      );
  }


  saveSocialMedias() {
    this.sending = true;
    this.campaignService.saveSocialMedias(this.campaignStore.id, this.socialMedias)
      .subscribe(
        response => {
          this.sending = false;
          if (response) {
            this.alert.showSuccess('Mídias Sociais salvas com sucesso.');
          }
        },
        err => {
          this.sending = false;
          this.alert.handleAndShowError(err);
        }
      );
  }

}
