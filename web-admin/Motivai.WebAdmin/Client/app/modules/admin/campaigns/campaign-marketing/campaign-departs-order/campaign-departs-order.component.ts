import { RxjsHelpers } from '../../../../../shared/helpers/rxjs-helpers';
import { Subscription } from 'rxjs/Subscription';
import { Component, OnInit, ViewChild, OnDestroy } from '@angular/core'
import { ActivatedRoute } from '@angular/router';

import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CategoriesService } from '../../../categories/categories.service';
import { CampaignService } from '../../campaign.service';
import { SpecialShopService } from '../campaign-specialshop/special-shop.service';
import { SegmentationsConfiguration } from '../../../../../shared/components/business/campaigns/segmentation-parametrization/segmentations-configuration';

@Component({
  selector: 'campaign-departs-order',
  templateUrl: 'campaign-departs-order.component.html',
  styles: [`
    .green-text {
      color: #4CAF50 !important;
    }
  `]
})
export class CampaignDepartsOrderComponent implements OnInit, OnDestroy {
  @ViewChild('gpAlert')gpAlert: GpAlertComponent;

  campaignId: string;

  loadingCategories: boolean = false;
  loadingMenuDeps: boolean = false;
  sending: boolean = false;
  updating: boolean = false;

  allDepartments: Array<{id: string, text: string}> = [];
  categories: Array<{id: string, text: string}> = [];

  departmentsWithProducts: any = {};
  specialShops: Array<any> = [];

  segmentationsConfiguration: SegmentationsConfiguration = {} as SegmentationsConfiguration;



  menuFilter: any = {
    onlyWithProducts: false,
    // selectedCategory: []
  };
  // classificações filtradas para mostrar na ordenação
  classificationToShow: Array<{id: string, text: string}> = [];
  departments: Array<any> = [];
  currenctCampaignMenuIds: Array<any> = [];

  private _route$: Subscription;

  constructor(private _categoriesService: CategoriesService, private _campaignService: CampaignService,
    private _specialShopService: SpecialShopService, private route: ActivatedRoute) {}

  ngOnInit(): void {
    this.loadingCategories = true;
    this.loadingMenuDeps = true;
    if (this.route.parent != null && this.route.parent.parent != null) {
      this._route$ = this.route.parent.parent.params.subscribe((params: any) => {
        this.campaignId = params['id'];
        this.loadCampaignDeps();
      });
    }
    this.loadDepartments();
  }

  ngOnDestroy() {
    RxjsHelpers.unsubscribe(this._route$);
  }

  get loading() {
    return this.loadingCategories || this.loadingMenuDeps;
  }

  get disableButton() {
    return this.sending || !this.departments || !this.departments.length;
  }

  get disableCategoriesSelect() {
    return !this.menuFilter.departmentId || !this.categories || !this.categories.length;
  }

  get useOnlyWithProducts() {
    return this.menuFilter.onlyWithProducts === true;
  }

  set useOnlyWithProducts(value) {
    this.menuFilter.onlyWithProducts = value === true;
    this.sortDepsUsing(this.classificationToShow);
  }

  private handleError(err) {
    let errorMessage = (err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao efetuar a operação.');
    this.gpAlert.showError(errorMessage);
    this.loadingCategories = false;
    this.loadingMenuDeps = false;
    this.sending = false;
    this.updating = false;
  }

  private verifyIfHasProducts(categoryId): boolean {
    if (!this.departmentsWithProducts || !this.departmentsWithProducts.departmentsIds) return false;
    let index = this.departmentsWithProducts.departmentsIds.indexOf(categoryId);
    if (index >= 0) return true;
    if (!this.departmentsWithProducts.categoriesIds) return false;
    return this.departmentsWithProducts.categoriesIds.indexOf(categoryId) >= 0;
  }

  private loadDepartments() {
    this.loadingCategories = true;
    this._campaignService.getDepartments(this.campaignId)
      .subscribe(
        departments => {
          if (departments) {
            this.allDepartments = [{id: 'todas', text: 'Todos'}].concat(departments.map(dep => {
              return { id: dep.id, text: dep.name };
            }));
            this.sortDeps();
          }
          this.loadingCategories = false;
        },
        err => this.handleError(err)
      );
  }

  private loadCategories(departmentId) {
    this.loadingCategories = true;
    this._categoriesService.getCombo(2, departmentId, true)
    .subscribe(
      categories => {
        if (categories) {
          const defaultCat = [{id: 'todas', text: 'Todas'}];
          this.menuFilter.selectedCategory = defaultCat;
          this.categories = defaultCat.concat(categories.map(dep => {
            return { id: dep.id, text: dep.name };
          }));
        } else {
          this.categories = [];
        }
        this.classificationToShow = this.categories;
        this.sortDepsUsing(this.classificationToShow);
        this.loadingCategories = false;
      },
      err => this.handleError(err)
    );
  }

  private loadSubategories(departmentId) {
    this.loadingCategories = true;
    this._categoriesService.getCombo(3, departmentId, true)
    .subscribe(
      subcategories => {
        if (subcategories) {
          this.classificationToShow = subcategories.map(dep => {
            return { id: dep.id, text: dep.name };
          });
        } else {
          this.classificationToShow = [];
        }
        this.sortDepsUsing(this.classificationToShow);
        this.loadingCategories = false;
      },
      err => this.handleError(err)
    );
  }

  private loadCampaignDeps() {
    this._campaignService.getDepartmentsWithProductsByCampaign(this.campaignId)
      .subscribe(
        departments => {
          if (departments) {
            this.departmentsWithProducts = departments;
          }
        },
        err => this.handleError(err)
      );

    this._campaignService.getDepartmentsOrderByCampaign(this.campaignId)
      .subscribe(
        departments => {
          if (departments) {
            if (departments) {
              this.currenctCampaignMenuIds = departments.menuOrder;
              if (departments.departmentId) {
                this.menuFilter.departmentId = departments.departmentId;
                this.loadCategories(departments.departmentId);
              }
              if (departments.segmentations) {
                this.segmentationsConfiguration = departments.segmentations as SegmentationsConfiguration;
              }
            } else {
              this.currenctCampaignMenuIds = [];
            }
            this.sortDeps();
          } else {
            this.currenctCampaignMenuIds = [];
          }
          this.loadingMenuDeps = false;
        },
        err => this.handleError(err)
      );

    this._specialShopService.getSpecialShopsByCampaign(this.campaignId, true)
      .subscribe(
        specialShops => {
          this.specialShops = specialShops || [];
        },
        err => this.handleError(err)
      );
  }

  private sortDeps() {
    if (this.allDepartments && this.allDepartments.length) {
      this.classificationToShow = this.allDepartments;
      this.sortDepsUsing(this.allDepartments);
    } else {
      this.departments = [];
    }
  }

  private sortDepsUsing(availableDeps: Array<any>) {
    if (!this.currenctCampaignMenuIds) {
      this.currenctCampaignMenuIds = [];
    }
    if (availableDeps) {
      let deps: Array<any> = [];
      for (const i in this.currenctCampaignMenuIds) {
        let menuDep = availableDeps.find(d => d.id == this.currenctCampaignMenuIds[i]);
        if (menuDep) {
          deps.push(menuDep);
          availableDeps = availableDeps.filter(d => d.id != this.currenctCampaignMenuIds[i]);
        } else {
          menuDep = this.specialShops.find(s => s.id == this.currenctCampaignMenuIds[i]);
          if (menuDep) {
            deps.push({ id: menuDep.id, text: menuDep.name, type: 'SpecialShop' });
          }
        }
      }
      availableDeps.forEach(d => { if (d.id != 'todas') deps.push(d); });
      this.departments = deps;

      if (this.departmentsWithProducts && this.menuFilter.onlyWithProducts === true) {
        this.departments = this.departments.filter(d => d.type == 'SpecialShop' || this.verifyIfHasProducts(d.id));
      }
    }

    if (this.specialShops && this.specialShops.length) {
      this.specialShops.forEach(shop => {
        if (this.currenctCampaignMenuIds.find(id => id == shop.id))
          return;

        this.departments.push({ id: shop.id, text: shop.name, type: 'SpecialShop' });
      });
    }
  }

  selectDepartment(item) {
    this.menuFilter.categoryId = null;
    this.menuFilter.selectedCategory = [];
    this.menuFilter.departmentId = null;
    this.menuFilter.department = [];
    this.categories = [];
    if (!item || !item.id) {
      return;
    } else if (item.id === 'todas') {
      this.sortDeps();
    } else {
      this.menuFilter.departmentId = item.id;
      this.menuFilter.department = item;
      this.loadCategories(item.id);
    }
  }

  selectCategory(item) {
    this.menuFilter.categoryId = null;
    if (!item || !item.id) {
      this.menuFilter.selectedCategory = [];
      return;
    } else if (item.id === 'todas') {
      this.loadCategories(this.menuFilter.departmentId);
    } else {
      this.menuFilter.categoryId = item.id;
      this.menuFilter.selectedCategory = [item];
      this.loadSubategories(item.id);
    }
  }

  saveDepartmentsOrder() {
    this.sending = true;
    this.gpAlert.clear();

    let campaignMenu: any = {
      departmentId: this.menuFilter.departmentId,
      categoryId: this.menuFilter.categoryId,
      menuOrder: this.departments.slice(0, 6).map(d => d.id),
      segmentations: this.segmentationsConfiguration
    };

    this._campaignService.saveDepartmentsOrder(this.campaignId, campaignMenu)
      .subscribe(
        result => {
          if (result == true) {
            this.gpAlert.showSuccess('Configurações dos departmentos salvas com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível salvar as configurações dos departamentos, por favor, tente novamente.');
          }
          this.sending = false;
        },
        err => this.handleError(err)
      );
  }

  updateMenu() {
    this.updating = true;
    this.gpAlert.clear();
    this._campaignService.updateCatalogMenu(this.campaignId)
      .subscribe(
        result => {
          if (result == true) {
            this.gpAlert.showSuccess('Menu do catálogo atualizado com sucesso. Pode levar até 1 minuto até que os caches se atualizem.');
          } else {
            this.gpAlert.showWarning('Não foi possível atualizar o menu, por favor, tente novamente.');
          }
          this.updating = false;
        },
        err => this.handleError(err)
      );
  }
}
