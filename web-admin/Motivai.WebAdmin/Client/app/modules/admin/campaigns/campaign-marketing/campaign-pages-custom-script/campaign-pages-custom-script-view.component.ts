import { Component, ViewChild } from '@angular/core';
import { TabsetComponent } from 'ng2-bootstrap';
import { CampaignPagesCustomScriptEditComponent } from './campaign-pages-custom-script-edit/campaign-pages-custom-script-edit.component';
import { CampaignPagesCustomScriptListComponent } from './campaign-pages-custom-script-list/campaign-pages-custom-script-list.component';
@Component({
  selector: 'campaign-script-view',
  templateUrl: 'campaign-pages-custom-script-view.component.html',
})
export class CampaignScriptViewComponent { 
    @ViewChild('tabs') tabs: TabsetComponent;
    @ViewChild('list') list: CampaignPagesCustomScriptListComponent;
    @ViewChild('edit') edit: CampaignPagesCustomScriptEditComponent;


    editCustomScript(event: any) {
      this.edit.clear();
      this.edit.showDetails(event);
      this.tabs.tabs[1].active = true;
    }

    refreshGrid() {
      this.list.findScripts();
    }
}