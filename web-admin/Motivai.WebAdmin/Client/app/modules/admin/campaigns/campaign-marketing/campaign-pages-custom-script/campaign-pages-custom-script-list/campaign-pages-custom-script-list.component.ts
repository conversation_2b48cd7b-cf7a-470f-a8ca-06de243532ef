import { Component, EventEmitter, OnInit, Output, ViewChild } from '@angular/core';
import { Subscription } from 'rxjs';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignStore } from '../../../campaign.store';
import { CampaignPagesCustomScriptsService } from '../services/campaign-pages-custom-scripts.service';
@Component({
  selector: 'campaign-script-list',
  templateUrl: 'campaign-pages-custom-script-list.component.html',
})
export class CampaignPagesCustomScriptListComponent implements OnInit {
  @ViewChild('alert') alert: GpAlertComponent;
  @Output('onEdit') onEdit: EventEmitter<any> = new EventEmitter();

  loading: boolean = false; 
  campaignId: string; 
  scripts: Array<any> = []; 
  private _campaign$: Subscription;

  constructor(
    private readonly campaignScriptsService: CampaignPagesCustomScriptsService, 
    private _campaignStore: CampaignStore
  ) {}

  ngOnInit(): void {
    this._campaign$ = this._campaignStore.asObservable
            .subscribe(campaignId => {
                this.campaignId = campaignId;
            });
    this.findScripts(); 
  } 

  findScripts() { 
    this.loading = true; 
    this.campaignScriptsService.getCustomScripts(this.campaignId)
      .subscribe(scripts => { 
        this.scripts = scripts || [];
        this.loading = false; 
    }, error => {
        this.alert.showError(error); 
        this.loading = false; 
    })
  }

  editCustomScript(event: any) { 
    if (event.id) this.onEdit.emit(event.id);
  }
}
