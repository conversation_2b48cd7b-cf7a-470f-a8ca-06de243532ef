import { Injectable } from '@angular/core';
import { Observable } from 'rxjs/Observable';
import { ApiService } from '../../../../../core/api/api.service';


@Injectable()
export class CampaignCommunicationRulesService {
  constructor(private _api: ApiService) { }

  getByCampaign(campaignId: string): Observable<any[]> {
    return this._api.get(`/api/campaigns/${campaignId}/communicationrules`);
  }

  getById(campaignId: string, id: string): any {
    return this._api.get(`/api/campaigns/${campaignId}/communicationrules/${id}`);
  }

  save(campaignId: string, communicationRules: any): any {
    if (communicationRules.id)
      return this._api.put(`/api/campaigns/${campaignId}/communicationrules/${communicationRules.id}`, communicationRules);

    return this._api.post(`/api/campaigns/${campaignId}/communicationrules`, communicationRules);
  }

  saveProcessingDates(campaignId: string, id: string, processingDates: any): Observable<boolean> {
      return this._api.put(`/api/campaigns/${campaignId}/communicationrules/${id}/processingdates`, processingDates);
  }

  getProcessingDetails(campaignId: string, ruleId: string, id: string) {
    return this._api.get(`/api/campaigns/${campaignId}/communicationrules/${ruleId}/schedules/${id}/counters/emails`);
  }

  sendWhatsAppTemplateToApproval(campaignId: string, communicationRuleId: string, whatsApp: any): Observable<any> {
    return this._api.put(`/api/campaigns/${campaignId}/communicationrules/${communicationRuleId}/whatsapp/template/approval`, whatsApp);
  }

  exportProcessingErrors(campaignId: string, ruleId: string, id: string) {
    return this._api.get(`/api/campaigns/${campaignId}/communicationrules/${ruleId}/processingdetails/${id}/logs/export`);
  }
}
