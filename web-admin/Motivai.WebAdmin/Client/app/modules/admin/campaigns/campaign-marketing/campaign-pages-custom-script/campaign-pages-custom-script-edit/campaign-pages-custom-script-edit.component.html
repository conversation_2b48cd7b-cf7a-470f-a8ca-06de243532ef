<gp-alert #alert [overlay]="true"></gp-alert>
<gp-card title="Cadastrar Script" [first]="true">
    <gp-form-row>
      <gp-form-col cols="12 12 12 12">
        <gp-form-row>
          <gp-form-col>
            <gp-simple-input label="Descrição" required="true">
              <input type="text" name="description" class="form-control" required
                [(ngModel)]="campaignScript.description" />
            </gp-simple-input>
          </gp-form-col>
          <gp-form-col cols="12 12 3 3">
            <label>Ativo</label>
            <div>
              <gp-switch name="active" [(ngModel)]="campaignScript.active"></gp-switch>
            </div>
          </gp-form-col>
        </gp-form-row>
      </gp-form-col>
    </gp-form-row>
</gp-card>

<gp-card title="Código com o Script">
    <gp-form-row>
      <gp-form-col cols="12 12 12 12">
          <gp-simple-input label="Script" required="true">
            <textarea name="script" class="form-control" [(ngModel)]="campaignScript.script" style="width: 100%;"
              rows="12"></textarea>
          </gp-simple-input>
      </gp-form-col>
    </gp-form-row>
</gp-card>

<gp-card [last]="true">
    <gp-spinner-button type="button" [pink]="true" icon="send" text="Salvar" loadingText="Processando" pull="right"
      [loading]="loading" (click)="createCampaignScript()" marginLeft="5px">
    </gp-spinner-button>
</gp-card>