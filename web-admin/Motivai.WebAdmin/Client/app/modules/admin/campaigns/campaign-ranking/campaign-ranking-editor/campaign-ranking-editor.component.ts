import { AuthStore } from '../../../../../core/auth/auth.store';
import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NgForm } from '@angular/forms';

import { GpFileUploadComponent } from '../../../../../shared/components/gp-fileupload/gp-fileupload.component';
import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { Item } from '../../../../../shared/models/item';
import { CampaignService } from '../../campaign.service';
import { CompanyService } from '../../../companies/company/company.service';
import { GpCategorySelectComponent } from '../../../categories/categories-util/categories-select.component';
import { PERMISSION_CAMPAIGNS_RANKINGS_INS } from '../../../../../core/auth/access-points';

@Component({
	selector: 'campaign-ranking-editor',
	templateUrl: 'campaign-ranking-editor.component.html'
})
export class CampaignRankingEditorComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @ViewChild('gpIcon') gpIcon: GpFileUploadComponent;
  @ViewChild('gpForm') gpForm: NgForm;
  @ViewChild('departmentComponent') departmentComponent: GpCategorySelectComponent;
  @ViewChild('categoryComponent') categoryComponent: GpCategorySelectComponent;
  @ViewChild('subcategoryComponent') subcategoryComponent: GpCategorySelectComponent;

  uploadPath: string;
  @Input() campaignId: string;

  private _parentId: string;
  @Input() set parentId(v: string) {
    if (v) {
      this._parentId = v;
      this.ranking.parentId = v;
      this.loadParentName();
    }
  }

  private _rankingId: string;
  @Input() set rankingId(v: string) {
    if (v) {
      this._rankingId = v;
      this.loadRanking();
    }
  }

  campaignPartners: Array<Item> = [];
  parentName: string;

	ranking: any = {};
  rules: any = {};
  product: any = {};

  loadingScreen: boolean = false;
  loadingParentName: boolean = false;
  loadingProduct: boolean = false;
  loading: boolean = false;
	sending: boolean = false;
	sendingRule: boolean = false;
  uploading: boolean = false;

  constructor(private _route: ActivatedRoute,
    private _campaignService: CampaignService,
    private _companyService: CompanyService,
    private _as: AuthStore) { }

	ngOnInit() {
    if (this.campaignId) {
      this.loadCombos();
    }
  }

  get disableButtons() {
    return this.loading || this.sending || this.sendingRule || this.uploading;
  }

  get hasSelectedIcon() {
    return this.gpIcon && this.gpIcon.hasSelectedFile();
  }

  private handleError(err) {
    let errorMessage = (err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao efetuar a operação.');
    this.gpAlert.showError(errorMessage);
    this.loading = false;
    this.sending = false;
  }

  private loadCombos() {
    this._campaignService.getLinkedPartnersDataByCampaign(this.campaignId)
      .subscribe(
        linkedPartners => {
          this.campaignPartners = linkedPartners.map(part => {
            return {id: part.partnerId, text: part.partnerName};
          });
        },
        err => this.handleError(err)
      );
  }

  private loadParentName() {
    if (this._parentId) {
      this.loadingParentName = true;
      this._campaignService.getParticipantsRankingById(this.campaignId, this._parentId)
        .subscribe(parent => {
          this.parentName = parent.name;
          this.loadingParentName = false;
        }, err => {
          this.handleError(err);
          this.loadingParentName = false;
        });
    }
  }

  private loadRanking() {
    if (this._rankingId) {
      this.loadingScreen = true;
      this._campaignService.getParticipantsRankingById(this.campaignId, this._rankingId)
        .subscribe(ranking => {
          this.ranking = ranking;
          if (this.ranking.iconUrl) {
            this.ranking.iconUrl = `${this.ranking.iconUrl}/100/100`;
          }
          if (ranking.parentId) {
            this.parentId = ranking.parentId;
          }

          if (ranking.rules) {
            if (ranking.rules.partners) {
              this.fillPartnersName(ranking.rules.partners);
            }
            if (ranking.rules.skusModels) {
              this.rules.skus = ranking.rules.skusModels;
            }
            if (ranking.rules.categoriesModels) {
              this.rules.categories = ranking.rules.categoriesModels;
            }
          }
          this.loadingScreen = false;
        }, err => {
          this.handleError(err);
          this.loadingScreen = false;
        });
    }
  }

  private fillPartnersName(partners: any[]) {
    if (this.campaignPartners && this.campaignPartners.length > 0) {
      partners.forEach(partnerId => {
        const partner = this.campaignPartners.find(p => p.id === partnerId);
        if (partner) {
          if (!this.rules.partners) this.rules.partners = [];
          (this.rules.partners as any[]).push({ partnerName: partner.text, partnerId });
        }
      });
    }
  }

  selectPartner(item) {
    if (!item || !item.id) {
      this.rules.partner = null
    } else {
      this.rules.partner = [item];
    }
  }

  selectPartnerForSku(item) {
    if (!item || !item.id) {
      this.rules.partnerForSku = null
    } else {
      this.rules.partnerForSku = [item];
    }
  }

	addPartner() {
    this.gpAlert.clear();
    if (!this.rules.partner || !this.rules.partner.length) {
      this.gpAlert.showWarning('Seleciona um parceiro.');
      return;
    }
    if (!this.rules.partners || !this.rules.partners.length) {
      this.rules.partners = [];
    }
    let partner = this.rules.partner[0];
    if (this.rules.partners.findIndex(p => p.partnerId === partner.id) >= 0) {
      this.rules.partner = null;
      this.gpAlert.showWarning('Parceiro já foi adicionado.');
      return;
    }
    this.rules.partners.push({ partnerId: partner.id, partnerName: partner.text });
    this.rules.partner = null;
	}

	removePartner(partner) {
    this.gpAlert.clear();
    if (!partner || !partner.partnerId || !this.rules.partners) {
      return;
    }
    this.rules.partners = this.rules.partners.filter(p => p.partnerId !== partner.partnerId);
	}

	addDepartment() {
    this.gpAlert.clear();
    if (!this.rules.departmentId) {
      this.gpAlert.showWarning('Selecione pelo menos um departamento.');
      return;
    }
    if (!this.rules.categories) this.rules.categories = [];

    let departmentName = '';
    if (this.rules.departmentId) {
      const department = this.departmentComponent.categories.find(x => x.id === this.rules.departmentId);
      if (department) departmentName = department.name;
    }

    let categoryName = '';
    if (this.rules.categoryId) {
      const category = this.categoryComponent.categories.find(x => x.id === this.rules.categoryId);
      if (category) categoryName = category.name;
    }

    let subcategoryName = '';
    if (this.rules.subcategoryId) {
      const subcategory = this.subcategoryComponent.categories.find(x => x.id === this.rules.subcategoryId);
      if (subcategory) subcategoryName = subcategory.name;
    }

    this.rules.categories.push({
      departmentId: this.rules.departmentId,
      departmentName: departmentName,
      categoryId: this.rules.categoryId || null,
      categoryName: categoryName,
      subcategoryId: this.rules.subcategoryId || null,
      subcategoryName: subcategoryName
    });
	}

	removeDepartment($event) {
    if ($event.departmentId) {
      let line = null;

      if ($event.subcategoryId) {
        line = this.rules.categories.find(
            x => x.departmentId === $event.departmentId &&
            x.categoryId === $event.categoryId &&
            x.subcategoryId === $event.subcategoryId
        );

      } else if ($event.categoryId) {
        line = this.rules.categories.find(
            x => x.departmentId === $event.departmentId &&
            x.categoryId === $event.categoryId &&
            !x.subcategoryId
        );

      } else {
        line = this.rules.categories.find(
            x => x.departmentId === $event.departmentId &&
            !x.categoryId &&
            !x.subcategoryId
        );
      }

      if (line) {
        this.rules.categories.splice(this.rules.categories.indexOf(line), 1);
      }
    }
	}

	addSku() {
    this.gpAlert.clear();
    if (!this.product.skuId)
      return this.gpAlert.showError('Busque um produto pelo sku para vincular ao ranking');

    if (!this.rules.skus) this.rules.skus = [];

    const skuFound = this.rules.skus.find(x => x.skuId === this.product.skuId);
    if (skuFound)
      return this.gpAlert.showError('Sku já vinculado a este ranking');

    this.rules.skus.push({
      skuId: this.product.skuId,
      partnerName: this.rules.partnerForSku[0].text,
      skuCode: this.rules.skuCode,
      productName: this.product.name
    });

    this.product = {};
    this.rules.skuCode = null;
	}

	removeSku($event) {
    if ($event.skuId) {
      let skuFound = this.rules.skus.find(x => x.skuId === $event.skuId);
      if (skuFound) this.rules.skus.splice(this.rules.skus.indexOf(skuFound), 1);
    }
  }

  private searchProduct() {
		if (this.rules.partnerForSku && this.rules.skuCode) {
      this.product = {};
			this.loadingProduct = true;
			this._companyService.findPartnerProductBySkuCode(this.rules.partnerForSku[0].id, this.rules.skuCode)
				.subscribe(product => {
					this.product = product;
					this.loadingProduct = false;
				}, err => {
          this.handleError(err);
          this.loadingProduct = false;
				});
		}
	}

  newRanking() {
    this.gpAlert.clear();
    this._rankingId = '';
    this._parentId = '';
    this.parentName = '';
    this.ranking = {};
    this.rules = {};
    this.gpForm.reset();
  }

  saveRanking() {
    this.gpAlert.clear();
    if (!this.gpForm.valid) {
      this.gpAlert.showWarning('Preencha os campos corretamente para prosseguir.');
      return;
    }
    if (this._rankingId) {
      this.ranking.id = this._rankingId;
    } else {
      this.ranking.id = null;
    }
    this.sending = true;
    this.ranking.rules = {};
    if (this.rules.partners && this.rules.partners.length) {
      this.ranking.rules.partners = this.rules.partners.map(p => p.partnerId);
    }
    if (this.rules.skus && this.rules.skus.length) {
      this.ranking.rules.skus = this.rules.skus.map(s => s.skuId);
    }
    if (this.rules.categories && this.rules.categories.length) {
      this.ranking.rules.categories = this.rules.categories.map(c => {
        return {
          departmentId: c.departmentId,
          categoryId: c.categoryId || null,
          subcategoryId: c.subcategoryId || null
        };
      });
    }

    this._campaignService.saveRanking(this.campaignId, this.ranking)
      .subscribe(
        result => {
          if (result) {
            this._rankingId = result;
            if (this.hasSelectedIcon) {
              this.gpAlert.showSuccess('Ranking salvo com sucesso, aguarde o upload do ícone.');
              this.uploadIcon();
            } else {
              this.gpAlert.showSuccess('Ranking salvo com sucesso.');
              this.sending = false;
            }
          } else {
            this.gpAlert.showWarning('Não foi possível salvar o ranking, por favor, novamente.');
            this.sending = false;
          }
        },
        err => this.handleError(err)
      );
  }

	uploadIcon() {
    this.gpAlert.clear();
    if (!this.hasSelectedIcon) {
      this.sending = false;
      return;
    }
    this.uploading = true;
    this.sending = false;
    this.uploadPath = `api/campaigns/${this.campaignId}/rankings/${this._rankingId}/icon`;
    if (this.gpIcon)
      this.gpIcon.path = this.uploadPath;
    this.gpIcon.uploadFile();
  }

  onComplete(event) {
    this.gpAlert.clear();
    if (event.response) {
      try {
        const apiReturn = JSON.parse(event.response);
        if (apiReturn['success'] === true || apiReturn['success'] === false) {
          event.success = apiReturn['success'];
          event.errorMessage = apiReturn['error'];
        }
      } catch (ex) { }
    }
    if (event.success) {
      this.gpAlert.showSuccess('Ícone enviado com sucesso.');
      this.gpIcon.removeAll();
    } else {
      this.gpAlert.showError(event.errorMessage);
    }
    this.sending = false;
    this.uploading = false;
  }

  canCreate() {
		return this._as.hasPermissionTo(PERMISSION_CAMPAIGNS_RANKINGS_INS);
	}
}
