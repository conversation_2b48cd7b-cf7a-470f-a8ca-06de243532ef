<form #gpForm="ngForm" (ngSubmit)="saveRanking()">
	<gp-card [first]="true" title="Dados do Ranking de Participante">
		<div class="row">
			<div grid="12 12" [group]="true">
				<gp-simple-input label="Nome do Ranking" [required]="true" errorMessage="Nome do ranking é obrigatório">
					<input type="text" class="form-control" name="name" required [(ngModel)]="ranking.name" />
				</gp-simple-input>
			</div>
		</div>
		<div class="row">
			<div grid="6 6" [group]="true">
				<gp-simple-input label="Ranking Pai">
					<input type="text" class="form-control" name="parentName" disabled [(ngModel)]="parentName" />
				</gp-simple-input>
			</div>
			<div grid="6 6" [group]="tue">
				<label>Ativo</label>
				<div>
					<label class="switch switch-lg">
						<input type="checkbox" checked="checked" id="active" name="active" [(ngModel)]="ranking.active" /><span></span>
					</label>
				</div>
			</div>
		</div>
		<div class="row">
			<div grid="12 12" [group]="true">
				<gp-fileupload name="icon" [path]="uploadPath" label="Selecione o ícone para o ranking" [images]="true"
					(oncomplete)="onComplete($event)" #gpIcon></gp-fileupload>
			</div>
		</div>
		<div class="row" *ngIf="ranking.iconUrl">
			<div grid="12 12" [group]="true">
				<img style="border: 2px solid #CCC; border-radius: 10px" [defaultImage]="'https://www.placecage.com/100/100'" [lazyLoad]="ranking.iconUrl" />
			</div>
		</div>
		<spinner [overlay]="true" [show]="loadingScreen || loadingParentName"></spinner>
	</gp-card>

	<gp-card title="Regras">
		<accordion [closeOthers]="true">
			<accordion-group panelClass="b0 mb-sm panel-default">
				<div accordion-heading>
					<label>Por Parceiro</label>
				</div>
				<div class="row">
					<div grid="12 6 6" [group]="true">
						<label>Parceiro:</label>
						<ng-select [allowClear]="true" [items]="campaignPartners" placeholder="Selecione um parceiro"
							[active]="rules.partner" (data)="selectPartner($event)">
						</ng-select>
					</div>
					<div grid="12 6 6" class="top-p2 pull-right">
						<gp-spinner-button type="button" [pink]="true" icon="plus" text="Adicionar" loadingText="Adicionando" pull="right" [loading]="sendingRule"
							[disabled]="!rules.partner" (click)="addPartner()">
						</gp-spinner-button>
					</div>
				</div>
				<div class="row">
					<div grid="12">
						<gp-grid name="partnersGrid" [rows]="rules.partners" [columns]="['Parceiro']" [fields]="['partnerName']" [loading]="false"
							[showEdit]="false" [showDelete]="true" emptyMessage="Nenhum parceiro adicionado." (onDelete)="removePartner($event)">
						</gp-grid>
					</div>
				</div>
			</accordion-group>

			<accordion-group panelClass="b0 mb-sm panel-default">
				<div accordion-heading>
					<label>Por Categorias (Departamento, Categoria e/ou Subcategoria)</label>
				</div>
				<div class="row">
					<div grid="12 3 3" [group]="true">
						<gp-simple-input label="Departamento">
							<gp-categories-select #departmentComponent id="department" name="department" [active]="true" [level]="1" [(ngModel)]="rules.departmentId">
							</gp-categories-select>
						</gp-simple-input>
					</div>
					<div grid="12 3 3" [group]="true">
						<gp-simple-input label="Categoria">
							<gp-categories-select #categoryComponent id="category" name="category" [active]="true" [level]="2" emptyMessage="Sem categorias"
								[parentId]="rules.departmentId" [disabled]="!rules.departmentId" [(ngModel)]="rules.categoryId">
							</gp-categories-select>
						</gp-simple-input>
					</div>
					<div grid="12 3 3" [group]="true">
						<gp-simple-input label="Subcategoria">
							<gp-categories-select #subcategoryComponent id="category" name="subcategory" [active]="true" [level]="3" emptyMessage="Sem subcategorias"
								[parentId]="rules.categoryId" [disabled]="!rules.categoryId" [(ngModel)]="rules.subcategoryId">
							</gp-categories-select>
						</gp-simple-input>
					</div>
					<div grid="12 3 3" class="top-p2 pull-right">
						<gp-spinner-button type="button" [pink]="true" icon="plus" text="Adicionar" loadingText="Adicionando" pull="right"
							[loading]="sendingRule" [disabled]="!rules.departmentId" (click)="addDepartment()">
						</gp-spinner-button>
					</div>
				</div>
				<div class="row">
					<div grid="12">
						<gp-grid name="departmentsGrid" [rows]="rules.categories"
							[columns]="['Departamento', 'Categoria', 'Subcategoria']" [fields]="['departmentName', 'categoryName', 'subcategoryName']"
							[loading]="false" [showActive]="false" [showEdit]="false" [showDelete]="true" emptyMessage="Nenhum departamento adicionado."
							(onDelete)="removeDepartment($event)">
						</gp-grid>
					</div>
				</div>
			</accordion-group>

			<accordion-group panelClass="b0 mb-sm panel-default">
				<div accordion-heading>
					<label>Por SKU</label>
				</div>
				<div class="row">
					<div grid="12 7" [group]="true">
						<label>Parceiro:</label>
						<ng-select [allowClear]="true" [items]="campaignPartners" (data)="selectPartnerForSku($event)" placeholder="Selecione um parceiro">
						</ng-select>
					</div>
					<div grid="12 3" [group]="true">
						<gp-simple-input label="Código SKU">
							<input type="text" class="form-control" name="skuCode" [(ngModel)]="rules.skuCode" (blur)="searchProduct()" />
						</gp-simple-input>
					</div>
					<div grid="12 10" [group]="true">
						<input type="text" class="form-control" [value]="product.name || ''" disabled />
					</div>
					<div grid="12 2" class="pull-right">
						<gp-spinner-button type="button" [pink]="true" icon="plus" text="Adicionar" loadingText="Adicionando" pull="right"
							[loading]="sendingRule || loadingProduct" [disabled]="!product.id" (click)="addSku()">
						</gp-spinner-button>
					</div>
				</div>
				<div class="row">
					<div grid="12">
						<gp-grid name="productsGrid" [rows]="rules.skus"
							[columns]="['Parceiro', 'Código SKU', 'Produto']" [fields]="['partnerName', 'skuCode', 'productName']"
							[loading]="false" [showActive]="false" [showEdit]="false" [showDelete]="true" emptyMessage="Nenhum produto adicionado."
							(onDelete)="removeSku($event)">
						</gp-grid>
					</div>
				</div>
			</accordion-group>
		</accordion>
	</gp-card>

	<gp-card [last]="true">
		<div class="row" *ngIf="canCreate()">
			<div class="col-sm-12">
				<gp-spinner-button text="Novo" icon="plus" bootstrapClass="default" [disabled]="disableButtons" (click)="newRanking()"></gp-spinner-button>
				<gp-spinner-button type="submit" text="Salvar Ranking" icon="send" [pink]="true" loadingText="Processando"
					[disabled]="disableButtons" [loading]="sending">
				</gp-spinner-button>
			</div>
		</div>
		<div class="row">
			<div class="col-sm-12">
				<gp-alert #gpAlert></gp-alert>
			</div>
		</div>
	</gp-card>
</form>
