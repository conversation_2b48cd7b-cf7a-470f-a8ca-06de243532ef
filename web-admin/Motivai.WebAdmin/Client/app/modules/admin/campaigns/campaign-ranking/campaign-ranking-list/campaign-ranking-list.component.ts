import { Component, OnInit, ViewChild, Output, EventEmitter } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { TreeComponent, TreeNode } from 'angular2-tree-component';
import { ContextMenuService, ContextMenuComponent } from 'ngx-contextmenu';

import { CampaignService } from '../../campaign.service';
import { AuthStore } from '../../../../../core/auth/auth.store';
import { PERMISSION_CAMPAIGNS_RANKINGS_INS, PERMISSION_CAMPAIGNS_RANKINGS_TREE } from '../../../../../core/auth/access-points';

@Component({
	selector: 'campaign-ranking-list',
	templateUrl: 'campaign-ranking-list.component.html'
})
export class CampaignRankingListComponent implements OnInit {
	@ViewChild('tree') tree: TreeComponent;
	@ViewChild('contextMenu') contextMenu: ContextMenuComponent;

	@Output('onedit') onedit: EventEmitter<any> = new EventEmitter();
	@Output('oncreate') oncreate: EventEmitter<any> = new EventEmitter();

	nodes: any[] = [];
	options: any = {
		allowDrag: false,
		allowDrop: false,
		actionMapping: {
			mouse: {
				contextMenu: (tree, node, $event) => this.onContextMenu($event, node)
			}
		}
	};

	campaignId: string;
	rankings: Array<any> = [];
	loading: boolean = false;

	constructor(private _route: ActivatedRoute,
		private contextMenuService: ContextMenuService,
		private _as: AuthStore,
		private _campaignService: CampaignService) { }

	ngOnInit() {
		if (this._route.parent != null && this._route.parent.parent != null) {
			this._route.parent.params.subscribe((params: any) => {
				this.campaignId = params['id'];
				this.loadRankings();
			});
		}
	}

	public loadRankings() {
		this.fetchTree();
	}

	private fetchTree() {
		this.loading = true;
		this._campaignService.fetchRankingTree(this.campaignId)
			.subscribe(rankings => {
				this.nodes = rankings;
				this.tree.treeModel.update();
				this.loading = false;
			}, err => {
				console.log(err);
				this.loading = false;
			});
	}

	private onContextMenu($event, node: TreeNode): void {
		this.contextMenuService.show.next({
			contextMenu: this.contextMenu,
			event: $event,
			item: node,
		});

		$event.preventDefault();
	}

	contextMenuCreateChild($event): void {
		if ($event.item) {
			this.oncreate.emit($event.item.data);
		}
	}

	contextMenuEdit($event): void {
		if ($event.item) {
			this.onedit.emit($event.item.data);
		}
	}

	canCreate() {
		return this._as.hasPermissionTo(PERMISSION_CAMPAIGNS_RANKINGS_INS);
	}
}
