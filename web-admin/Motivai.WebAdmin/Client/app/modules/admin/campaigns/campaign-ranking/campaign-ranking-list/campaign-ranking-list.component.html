<gp-card [first]="true" [last]="true" title="Visão Geral dos Rankings de Participantes">
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<Tree #tree [nodes]="nodes" [options]="options"></Tree>
			<p *ngIf="!nodes || !nodes.length">Nenhum ranking cadastrado.</p>
			<spinner [overlay]="true" [show]="loading"></spinner>
		</gp-form-col>
	</gp-form-row>
	<hr />
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<p class="text-bold">Intruções de Uso:</p>
			<p>1. Crie um novo ranking na aba de cadastro</p>
			<p>2. Clique com o botão direito em cima de um ranking para visualizar as opções</p>
			<p>3. Clique com o botão direito em cima de um ranking para criar registros em forma de hierarquia</p>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<context-menu #contextMenu>
	<ng-template *ngIf="canCreate()" contextMenuItem (execute)="contextMenuEdit($event)">
		<div style="border-bottom: 1px solid #F0F0F0; height: 30px; line-height: 30px;">
			<i class="fa fa-edit"></i> Editar
		</div>
	</ng-template>
	<ng-template *ngIf="canCreate()" contextMenuItem (execute)="contextMenuCreateChild($event)">
		<div style="height: 30px; line-height: 25px">
			<i class="fa fa-indent"></i> Criar Filho
		</div>
	</ng-template>
</context-menu>