import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from '../../../../../../core/api/api.service';
import { CampaignsELearningsEvaluationsForms } from '../e-learning-evaluation/models/campaigns-elearnings-evaluations-forms';
import { EvaluationContent } from '../e-learning-evaluation/models/evaluation-content';
import { GenericEvaluation } from '../e-learning-evaluation/models/generic-evaluation';

@Injectable()
export class ELearningModuleContentService {

    constructor(private _api: ApiService) { }

	findByELearningFormId(campaignId, eLearningFormId: string, moduleId: string, skip: number, limit: number): Observable<Array<any>> {
		return this._api.get(`/api/campaigns/${campaignId}/features/lms/elearnings/${eLearningFormId}/modules/${moduleId}/contents`, { name, skip: skip || 0, limit: limit || 20 });
	}

	findById(campaignId: string, eLearningFormId: string, moduleId: string, contentId: string): Observable<any> {
		return this._api.get(`/api/campaigns/${campaignId}/features/lms/elearnings/${eLearningFormId}/modules/${moduleId}/contents/${contentId}`);
    }

	findContentsTitles(campaignId: string, eLearningFormId: string, moduleId: string): Observable<any> {
		return this._api.get(`/api/campaigns/${campaignId}/features/lms/elearnings/${eLearningFormId}/modules/${moduleId}/contents/titles`);
    }

	create(campaignId: string, eLearningFormId: string, moduleId: string, content: any): Observable<any> {
		return this._api.post(`/api/campaigns/${campaignId}/features/lms/elearnings/${eLearningFormId}/modules/${moduleId}/contents`, content);
    }

	update(campaignId: string, eLearningFormId: string, moduleId: string, content: any): Observable<any> {
		return this._api.put(`/api/campaigns/${campaignId}/features/lms/elearnings/${eLearningFormId}/modules/${moduleId}/contents/${content.id}`, content);
    }

	uploadVideo(campaignId: string, eLearningFormId: string, moduleId: string, content: any): Observable<any> {
		return this._api.post(`/api/campaigns/${campaignId}/features/lms/elearnings/${eLearningFormId}/modules/${moduleId}/contents/${content.id}/videos`, content);
    }

	findGenericEvaluationBy(campaignId: string, eLearningFormId: string, moduleId: string, contentId: string): Observable<CampaignsELearningsEvaluationsForms> {
		return this._api.get(`/api/campaigns/${campaignId}/features/lms/elearnings/${eLearningFormId}/modules/${moduleId}/contents/${contentId}/evaluations`);
	}

	saveGenereicEvaluationForm(campaignId: string, eLearningFormId: string, moduleId: string, contentId: string, evaluation: CampaignsELearningsEvaluationsForms): Observable<CampaignsELearningsEvaluationsForms> {
		return this._api.post(`/api/campaigns/${campaignId}/features/lms/elearnings/${eLearningFormId}/modules/${moduleId}/contents/${contentId}/evaluations`, evaluation);
	}

	updateGenereicEvaluationForm(campaignId: string, eLearningFormId: string, moduleId: string, contentId: string, evaluation: CampaignsELearningsEvaluationsForms): Observable<CampaignsELearningsEvaluationsForms> {
		return this._api.put(`/api/campaigns/${campaignId}/features/lms/elearnings/${eLearningFormId}/modules/${moduleId}/contents/${contentId}/evaluations/${evaluation.id}`, evaluation);
	}

	addGenericEvaluationQuestion(campaignId: string, eLearningFormId: string, moduleId: string, contentId: string, evaluationId: string, question: GenericEvaluation): Observable<boolean> {
		return this._api.put(`/api/campaigns/${campaignId}/features/lms/elearnings/${eLearningFormId}/modules/${moduleId}/contents/${contentId}/evaluations/${evaluationId}/questions`, question);
	}

	updateGenericEvaluationQuestion(campaignId: string, eLearningFormId: string, moduleId: string, contentId: string, evaluationId: string, question: GenericEvaluation): Observable<boolean> {
		return this._api.put(`/api/campaigns/${campaignId}/features/lms/elearnings/${eLearningFormId}/modules/${moduleId}/contents/${contentId}/evaluations/${evaluationId}/questions/${question.id}`, question);
	}

	findElearningContentAndGenericEvaluationFormBy(campaignId: string, eLearningFormId: string, moduleId: string, contentId: string): Observable<any> {
		return Observable.zip(
			this.findById(campaignId, eLearningFormId, moduleId, contentId),
			this.findGenericEvaluationBy(campaignId, eLearningFormId, moduleId, contentId)
		  )
		  .map(response => {
			if (!response || response.length < 2) {
			  return null;
			}
			return response;
		});
	}

	updateElearningContentAndGenericEvalutationForm(campaignId: string, eLearningFormId: string, moduleId: string, content: EvaluationContent, evaluation: CampaignsELearningsEvaluationsForms): Observable<any> {
		return Observable.zip(
			this.update(campaignId, eLearningFormId, moduleId, content),
			this.updateGenereicEvaluationForm(campaignId, eLearningFormId, moduleId, content.id, evaluation)
		  )
		  .map(response => {
			if (!response || response.length < 2) {
			  return null;
			}
			return response;
		});
	}
}
