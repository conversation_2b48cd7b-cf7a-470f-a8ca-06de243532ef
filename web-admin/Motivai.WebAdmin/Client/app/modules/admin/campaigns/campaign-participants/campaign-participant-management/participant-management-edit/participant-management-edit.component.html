<gp-card title="Participante" [first]="true">
  <gp-form-row>
    <gp-form-col cols="12 3">
      <label>CPF/CNPJ: </label>
      <span>{{ ((participantData.cpf || participantData.cnpj) | document ) || 'Não informado'}}</span>
    </gp-form-col>
    <gp-form-col cols="12 3">
      <label>Login: </label>
      <span>{{ participantData.login }}</span>
    </gp-form-col>
    <gp-form-col cols="12 3" *ngIf="participantData.clientUserId">
      <label>Código: </label>
      <span>{{ participantData.clientUserId }}</span>
    </gp-form-col>
    <gp-form-col cols="12 3">
      <label>Data de Criação: </label>
      <span>{{ participantData.createDate | datetimezone:participantData.timezone }}</span>
    </gp-form-col>
  </gp-form-row>

  <gp-form-row>
    <gp-form-col cols="12 3">
      <label>Ativo: </label>
      <span>{{ participantData.active ? 'Sim' : 'Não' }}</span>
    </gp-form-col>
    <gp-form-col cols="12 3">
      <label>Primeiro Acesso: </label>
      <span>{{ participantData.firstAccess ? 'Sim' : 'Não' }}</span>
    </gp-form-col>
    <gp-form-col cols="12 3" *ngIf="participantData.firstAccessDate">
      <label>Data do Primeiro Acesso: </label>
      <span>{{ participantData.firstAccessDate | datetimezone }}</span>
    </gp-form-col>
    <gp-form-col cols="12 3">
      <label>Data do Último Acesso: </label>
      <span>{{ participantData.lastAccessDate | datetimezone }}</span>
    </gp-form-col>
  </gp-form-row>

  <gp-form-row>
    <gp-form-col cols="12 12">
      <gp-spinner-button text="Vincular CPF/CNPJ" pull="right" [pink]="true" (click)="openDocumentLinkerModal()"
        *ngIf="canFillDocument">
      </gp-spinner-button>

      <gp-spinner-button text="Vincular Participante Pai" pull="right" bootstrapClass="primary" icon="link"
        (click)="openParentLinkerModal()" *ngIf="canEditParentParticipant">
      </gp-spinner-button>
    </gp-form-col>
  </gp-form-row>

  <spinner [overlay]="true" [show]="loading"></spinner>
</gp-card>

<participant-linker-document-modal [campaignId]="campaignId" [userId]="participantData.userId" (close)="resetForms()"
  #linkDocumentModal>
</participant-linker-document-modal>

<app-participant-parent-linker-modal [campaignId]="campaignId" [userId]="participantData.userId" (close)="resetForms()"
  #parentLinkerModal>
</app-participant-parent-linker-modal>

<tabset #tabsParticipantData class="bg-white p0 tab-no-border" [justified]="true">
  <gp-alert [overlay]="true" #alert></gp-alert>
  <tab>
    <ng-template tabHeading>Dados Cadastrais</ng-template>
    <gp-card [first]="true">
      <gp-form-row>
        <gp-form-col cols="12 3 3">
          <label>Ativo</label>
          <div>
            <gp-switch name="active" [disabled]="canOnlyRead" [(ngModel)]="participantData.active"></gp-switch>
          </div>
        </gp-form-col>

        <gp-form-col cols="12 3 3">
          <label>Bloqueado</label>
          <div>
            <gp-switch name="blocked" [disabled]="canOnlyRead" [(ngModel)]="participantData.blocked"></gp-switch>
          </div>
        </gp-form-col>

        <gp-form-col cols="12 3 3">
          <label>Habilitar Login no Site</label>
          <div>
            <gp-switch name="allowSiteLogin" [disabled]="canOnlyRead" [(ngModel)]="participantData.allowLoginSite"></gp-switch>
          </div>
        </gp-form-col>

        <gp-form-col *ngIf="!firstAccess" cols="12 3 3">
          <label>Primeiro Acesso</label>
          <div>
            <gp-switch name="firstAccess" [disabled]="canOnlyRead" [(ngModel)]="participantData.firstAccess"></gp-switch>
          </div>
        </gp-form-col>
      </gp-form-row>

      <div class="clear" *ngIf="isPF">
        <div *ngIf="participantData.blocked">
          <h4>Informações de Bloqueio</h4>
          <gp-form-row>
            <gp-form-col cols="12 6 6">
              <gp-simple-input label="Motivo">
                <input type="text" [disabled]="canOnlyRead" class="form-control" [(ngModel)]="participantData.blockingDetails.reason" readonly />
              </gp-simple-input>
            </gp-form-col>
          </gp-form-row>
        </div>
        <h3>Dados pessoais</h3>
        <gp-form-row>
          <gp-form-col cols="12 12 12">
            <gp-simple-input label="Nome completo">
              <input type="text" [disabled]="canOnlyRead" class="form-control" [(ngModel)]="participantData.name" />
            </gp-simple-input>
          </gp-form-col>
        </gp-form-row>
        <gp-form-row>
          <gp-form-col cols="12 6 6">
            <gp-simple-input label="RG">
              <input name="rg" disabled class="form-control" [(ngModel)]="participantData.rg" />
            </gp-simple-input>
          </gp-form-col>
          <gp-datepicker [disabled]="canOnlyRead" cols="12 6" label="Data de nascimento" name="date" [disableSince]="today"
            [(ngModel)]="participantData.birthDate">
          </gp-datepicker>
        </gp-form-row>
        <gp-form-row>
          <gp-form-col cols="12 6">
            <gp-simple-input label="Estado Civil">
              <select class="form-control" name="type" [disabled]="canOnlyRead" [(ngModel)]="participantData.maritalStatus">
                <option disabled>Selecione</option>
                <option value="Casado(a)">Casado(a)</option>
                <option value="Divorciado(a)">Divorciado(a)</option>
                <option value="Solteiro(a)">Solteiro(a)</option>
                <option value="Viúvo(a)">Viúvo(a)</option>
                <option value="União estável(a)">União Estável</option>
              </select>
            </gp-simple-input>
          </gp-form-col>
          <gp-form-col cols="12 6 6">
            <gp-simple-input label="Sexo">
              <select class="form-control" name="type" [disabled]="canOnlyRead" [(ngModel)]="participantData.gender">
                <option disabled>Selecione</option>
                <option value="M">Masculino</option>
                <option value="F">Feminino</option>
              </select>
            </gp-simple-input>
          </gp-form-col>
        </gp-form-row>
      </div>

      <div class="clear" *ngIf="isPJ">
        <div *ngIf="participantData.blocked">
          <h4>Informações de Bloqueio</h4>
          <gp-form-row>
            <gp-form-col cols="12 6 6">
              <gp-simple-input label="Motivo">
                <input type="text" [disabled]="canOnlyRead" class="form-control" [(ngModel)]="participantData.blockingDetails.reason" readonly />
              </gp-simple-input>
            </gp-form-col>
          </gp-form-row>
        </div>

        <h3>Dados da empresa</h3>
        <gp-form-row>
          <gp-form-col cols="12 6 6">
            <gp-simple-input label="Nome Fantasia">
              <input type="text" [disabled]="canOnlyRead" class="form-control" [(ngModel)]="participantData.name" />
            </gp-simple-input>
          </gp-form-col>
          <gp-form-col cols="12 6 6">
            <gp-simple-input label="Razão Social">
              <input type="text" [disabled]="canOnlyRead" class="form-control" [(ngModel)]="participantData.companyName" />
            </gp-simple-input>
          </gp-form-col>
        </gp-form-row>

        <gp-form-row>
          <gp-form-col cols="12 12">
            <label>Inscrição estadual isenta?</label>
            <div>
              <gp-switch name="stateInscriptionExempt" [disabled]="canOnlyRead" [(ngModel)]="participantData.stateInscriptionExempt"></gp-switch>
            </div>
          </gp-form-col>
        </gp-form-row>
        <gp-form-row>
          <gp-form-col cols="12 6 6">
            <gp-simple-input label="Inscrição Estadual">
              <gp-input-mask name="stateInscription" [readonly]="canOnlyRead" mask="00000000000000"
                [(ngModel)]="participantData.stateInscription" [disabled]="participantData.stateInscriptionExempt">
              </gp-input-mask>
            </gp-simple-input>
          </gp-form-col>
          <gp-form-col cols="12 6 6">
            <gp-simple-input label="Estado da Inscrição Estadual (UF)">
				<gp-brazil-state-selector name="stateInscriptionUf" [useUf]="true" [disabled]="participantData.stateInscriptionExempt || canOnlyRead"
					[(ngModel)]="participantData.stateInscriptionUf">
				</gp-brazil-state-selector>
            </gp-simple-input>
          </gp-form-col>
        </gp-form-row>
      </div>

      <hr />
      <div class="clear">
        <h3>Telefones</h3>
        <gp-form-row>
          <gp-form-col cols="12 6 6">
            <gp-simple-input label="Telefone Principal">
              <gp-input-mask name="mainPhone" [readonly]="canOnlyRead" mask="(00)0000-0000" [(ngModel)]="participantData.contact.mainPhone">
              </gp-input-mask>
            </gp-simple-input>
          </gp-form-col>
          <gp-form-col cols="12 6 6">
            <gp-simple-input label="Telefone Residencial">
              <gp-input-mask name="homePhone" [readonly]="canOnlyRead" mask="(00)0000-0000" [(ngModel)]="participantData.contact.homePhone">
              </gp-input-mask>
            </gp-simple-input>
          </gp-form-col>
        </gp-form-row>
        <gp-form-row>
          <gp-form-col cols="12 6 6">
            <gp-simple-input label="Telefone Comercial">
              <gp-input-mask name="businessPhone" [readonly]="canOnlyRead" mask="(00)0000-0000"
                [(ngModel)]="participantData.contact.commercialPhone">
              </gp-input-mask>
            </gp-simple-input>
          </gp-form-col>
          <gp-form-col cols="12 6">
            <gp-simple-input>
              <input type="text" [disabled]="canOnlyRead" class="form-control" name="talkTo" [(ngModel)]="participantData.contact.talkTo" />
            </gp-simple-input>
          </gp-form-col>
        </gp-form-row>
        <gp-form-row>
          <gp-form-col cols="12 6 6">
            <gp-simple-input label="Telefone Celular">
              <gp-input-mask name="mobilePhone" [readonly]="canOnlyRead"  mask="(00)00000-0000" [(ngModel)]="participantData.contact.mobilePhone">
              </gp-input-mask>
            </gp-simple-input>
          </gp-form-col>
          <gp-form-col cols="12 6 6">
            <gp-simple-input label="Operadora do Celular">
              <select class="form-control" name="type" [disabled]="canOnlyRead" [(ngModel)]="participantData.contact.mobileOperator">
                <option disabled>Selecione</option>
                <option value="Claro">Claro</option>
                <option value="CTBC Telecom">CtbcTelecom</option>
                <option value="Nextel">Nextel</option>
                <option value="Oi">Oi</option>
                <option value="Porto Seguro Conecta">Porto Seguro Conecta</option>
                <option value="Sercomtel">Sercomtel</option>
                <option value="TIM">Tim</option>
                <option value="Vivo">Vivo</option>
              </select>
            </gp-simple-input>
          </gp-form-col>
        </gp-form-row>
      </div>

      <hr />
      <div class="clear">
        <h3>Formas de contato</h3>
        <gp-form-row>
          <gp-form-col cols="12 6 6">
            <gp-simple-input label="Email Principal">
              <input type="text" [disabled]="canOnlyRead" class="form-control" [(ngModel)]="participantData.contact.mainEmail" />
            </gp-simple-input>
          </gp-form-col>
          <gp-form-col cols="12 6 6">
            <gp-simple-input label="Email Pessoal">
              <input type="text" [disabled]="canOnlyRead" class="form-control" [(ngModel)]="participantData.contact.personalEmail" />
            </gp-simple-input>
          </gp-form-col>
        </gp-form-row>
        <gp-form-row>
          <gp-form-col cols="12 6 6">
            <gp-simple-input label="Email Comercial">
              <input type="text" [disabled]="canOnlyRead" class="form-control" [(ngModel)]="participantData.contact.commercialEmail" />
            </gp-simple-input>
          </gp-form-col>
        </gp-form-row>
      </div>

      <hr />
      <div class="clear">
        <h3>Ranking</h3>
        <gp-form-row>
          <gp-form-col cols="12 6 6">
            <gp-simple-input label="Ranking">
              <gp-select name="rankings" [disabled]="canOnlyRead" placeholder="Rankings" [multiple]="false" [items]="rankings"
                [(ngModel)]="participantData.rankingId">
              </gp-select>
            </gp-simple-input>
          </gp-form-col>
        </gp-form-row>
      </div>

      <div class="clear" *ngIf="participantData.authenticationMfaSettings">
        <h3>Dados de autenticação MFA</h3>
        <gp-form-row>
          <gp-form-col cols="12 6 6">
            <gp-simple-input label="Formato de envio do Token">
              <gp-select name="authenticationMfaFormat" disabled="true" [items]="participantAuthenticationMfaFormats"
                [(ngModel)]="participantData.authenticationMfaSettings.authenticationMfaFormat"></gp-select>
            </gp-simple-input>
          </gp-form-col>
        </gp-form-row>

        <gp-form-row>
          <gp-form-col cols="12 6 6" *ngIf="participantData.authenticationMfaSettings.authenticationMfaFormat != 'SMS_ONLY'">
            <gp-simple-input label="E-mail">
              <input type="text" [disabled]="canOnlyRead" readonly class="form-control" [(ngModel)]="participantData.authenticationMfaSettings.email" />
            </gp-simple-input>
          </gp-form-col>

          <gp-form-col cols="12 6 6" *ngIf="participantData.authenticationMfaSettings.authenticationMfaFormat != 'EMAIL_ONLY'">
            <gp-simple-input label="Celular">
              <gp-input-mask name="authenticationMfaSettingsMobilePhone" [readonly]="canOnlyRead" mask="(00) 00000-0000"
                [(ngModel)]="participantData.authenticationMfaSettings.mobilePhone">
              </gp-input-mask>
            </gp-simple-input>
          </gp-form-col>
        </gp-form-row>
      </div>

      <gp-form-row *ngIf="canUpdateRegistrationData || canResetFirstAccess">
        <gp-form-col cols="12 12" [inputGroup]="false">
          <gp-spinner-button type="submit" text="Salvar" pull="right" [pink]="true" icon="send"
            loadingText="Processando" [loading]="sending" (click)="saveParticipantData()"
            *ngIf="canUpdateRegistrationData">
          </gp-spinner-button>

          <gp-spinner-button type="submit" text="Resetar Cartões do Participante" marginRight="1em" pull="right"
            bootstrapClass="primary" loadingText="Processando" [loading]="sending" (click)="resetParticipantWallet()"
            *ngIf="canResetFirstAccess">
          </gp-spinner-button>

          <gp-spinner-button type="submit" text="Desbloquear Participante" marginRight="1em" pull="right"
            bootstrapClass="primary" [loading]="sending" (click)="openModal()" *ngIf="participantData.blocked && canUnblockParticipant">
          </gp-spinner-button>

        </gp-form-col>
      </gp-form-row>
        <participant-management-modal (close)="modalClose()" #participantModal [campaignId]="campaignId"></participant-management-modal>
    </gp-card>
  </tab>

  <tab>
    <ng-template tabHeading>Campos Dinâmicos</ng-template>
    <app-participant-metadata-fields name="metadata" [firstCard]="true" [readOnly]="canOnlyRead"
      [userId]="userId" [(ngModel)]="participantData.metadata">
    </app-participant-metadata-fields>
  </tab>

  <tab>
      <ng-template tabHeading>Grupos</ng-template>
      <gp-card [first]="true" *ngIf="!canOnlyRead">
        <gp-campaign-participants-group-selector name="participantGroupsCodes"
          [enableEmpty]="false" [campaignId]="campaignId" [userId]="userId" (select)="onAddedGroups($event)" >
        </gp-campaign-participants-group-selector>

        <gp-spinner-button type="submit" text="Adicionar" pull="right" [pink]="true" icon="send"
          loadingText="Processando" [loading]="sending" (click)="saveParticipantToGroup()" marginTop="36px">
        </gp-spinner-button>
      </gp-card>


      <gp-campaign-participants-group-list name="participantGroupsList" [canDelete]="!canOnlyRead" [campaignId]="campaignId"
        [userId]="userId" [selectedGroup]="selectedGroup" #participantGroupsList>
      </gp-campaign-participants-group-list>
  </tab>

  <tab (select)="participantAddress.findParticipantAddresses()">
    <participant-management-address #participantAddress></participant-management-address>
  </tab>

  <tab>
    <ng-template tabHeading>Operadores</ng-template>
    <participant-management-account-operator [_userId]="userId" [_accountDocument]="accountDocument" #participantAccountOperator>
    </participant-management-account-operator>
  </tab>

  <tab (select)="participantHistoryFiles.findParticipantHistoryFiles()">
    <ng-template tabHeading>Histórico de Arquivos</ng-template>
    <participant-history-files [_userId]="userId" #participantHistoryFiles></participant-history-files>
  </tab>
</tabset>

<!-- <gp-card title="Grupo do participante">
  <gp-form-row>
     <campaign-participants-group-selector name="participantGroup" [(ngModel)]="participantGroup.id"
        (select)="setSelectedGroup($event)"></campaign-participants-group-selector>
<campaign-participants-group-selector name="participantGroup"></campaign-participants-group-selector>
</gp-form-row>
</gp-card>  -->
