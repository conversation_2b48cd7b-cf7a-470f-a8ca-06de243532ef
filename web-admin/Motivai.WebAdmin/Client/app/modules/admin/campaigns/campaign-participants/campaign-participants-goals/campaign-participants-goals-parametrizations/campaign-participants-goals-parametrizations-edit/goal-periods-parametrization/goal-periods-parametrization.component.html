<gp-modal title="Período" width="700px" #goalPeriodParams>
	<gp-alert #alert [overlay]="true"></gp-alert>
	<spinner [overlay]="true" [show]="loading"></spinner>

	<gp-form-row>
		<gp-form-col cols="12 6 6">
			<label>Ativo</label>
			<div>
				<gp-switch name="active" [disabled]="hasStarted" [(ngModel)]="goalPeriod.active"></gp-switch>
			</div>
		</gp-form-col>

		<gp-form-col cols="12 6 6">
			<label>Exibir no site campanha
				<gp-tooltip spanClass="text-primary">
					<div style="text-align:justify">
					  Se habilitado, será exibido no site para selecionar o período de consulta
					</div>
				  </gp-tooltip>
			</label>
			<div>
				<gp-switch name="enableOnCampaignSite" [disabled]="hasStarted" [(ngModel)]="goalPeriod.enableOnCampaignSite"></gp-switch>
			</div>
		</gp-form-col>
	</gp-form-row>

	<gp-form-row>
		<gp-form-col cols="12 6 6">
		  <label>Selecione o formato</label>
		  <gp-select name="periodFormat" [disabled]="hasStarted" [items]="periodFormats" placeholder="Selecione o formato"
			[(ngModel)]="goalPeriod.periodFormat">
		  </gp-select>
		</gp-form-col>
	</gp-form-row>

	<gp-form-row>
		<gp-form-col cols="12 6 6">
			<gp-simple-input label="Descrição">
				<input type="text" name="description" class="form-control" [(ngModel)]="goalPeriod.description" />
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>

	<gp-form-row *ngIf="goalPeriod.periodFormat !== 'UNIQUE'">
		<gp-datepicker cols="12 6 6" label="Data inicial" name="initialDate" 
			[(ngModel)]="goalPeriod.initialDate">
		</gp-datepicker>
		<gp-datepicker cols="12 6 6" label="Data final" name="endDate" 
			[(ngModel)]="goalPeriod.endDate">
		</gp-datepicker>
	</gp-form-row>

	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<gp-spinner-button bootstrapClass="primary" icon="send" pull="right" [text]="textButton" (click)="addPeriod()">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
</gp-modal>
