import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild
} from '@angular/core';
import * as moment from 'moment';
import { GpAlertComponent } from '../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpFileUploadComponent } from '../../../../../../../../shared/components/gp-fileupload/gp-fileupload.component';
import { GpModalComponent } from '../../../../../../../../shared/components/gp-modal/gp-modal.component';
import { Item } from '../../../../../../../../shared/models/item';
import { RewardMethod } from '../../../models/reward-method';
import { CampaignsParticipantsGoalsActionsService } from '../campaigns-participants-goals-actions.service';

@Component({
  selector: 'interval-goal-editing',
  templateUrl: './interval-goal-editing.component.html'
})
export class intervalGoalEditingComponent implements OnInit {
  @ViewChild('intervalGoalModal') intervalGoalModal: GpModalComponent;
  @ViewChild('gpFile') gpFile: GpFileUploadComponent;
  @ViewChild('alert') alert: GpAlertComponent;

  @Output() refreshIntervalsGoals: EventEmitter<any> = new EventEmitter();
  @Input() campaignId: string;
  @Input() goalId: string;
  @Input() hasStarted: string;

  @Input() scoringMethod: string;

  saving: boolean = false;
  removing: boolean;
  uploading: boolean = false;

  intervalGoalId: string;
  intervalGoal: any = {
    active: true,
    rewardMethod: RewardMethod.CASHBACK
  };

  get isCashBackRewardMethod() {
    return this.intervalGoal.rewardMethod == RewardMethod.CASHBACK;
  }

  get isPointsRewardMethod() {
    return this.intervalGoal.rewardMethod == RewardMethod.FIXED_REWARD;
  }

  rewardMethods: Array<Item> = [
    Item.of(RewardMethod.FIXED_REWARD, 'Pontuação Fixa'),
    Item.of(RewardMethod.CASHBACK, 'Cashback sobre Resultado (%)')
  ];

  constructor(
    private goalsActionsService: CampaignsParticipantsGoalsActionsService
  ) { }

  ngOnInit() { }

  get isEditing() {
    return this.intervalGoal && this.intervalGoal.id;
  }

  get textButtonSave() {
    if (this.isEditing) {
      return 'Salvar';
    }
    return 'Adicionar';
  }

  get icon() {
    if (this.isEditing) {
      return 'send';
    }
    return 'plus';
  }

  get hasSelectedFile() {
    return this.gpFile && this.gpFile.hasSelectedFile();
  }

  private clearForm() {
    this.intervalGoal = {
      active: true,
      rewardMethod: RewardMethod.CASHBACK
    };
  }

  newIntervalGoal() {
    this.clearForm();
    this.showModal();
  }

  editIntervalGoal(event: any) {
    this.intervalGoal = event;
    this.intervalGoalId = event.id;
    this.showModal();
  }

  showModal() {
    this.intervalGoalModal.show();
  }

  hideModal() {
    this.intervalGoalModal.hide();
  }

  saveGoal() {
    this.saving = true;
    if (this.isEditing) {
      return this.updateIntervalGoal();
    }
    return this.saveIntervalGoal();
  }


  saveIntervalGoal() {
    this.goalsActionsService
      .saveIntervalGoal(this.campaignId, this.goalId, this.intervalGoal)
      .subscribe(
        response => {
          if (response) {
            if (typeof response === 'string') {
              this.intervalGoal.id = response;
              this.intervalGoalId = response;
              this.alert.showSuccess('Intervalo salvo com sucesso');
              this.refreshIntervalsGoals.emit();
              this.hideModal();
            } else {
              this.alert.showError('Não foi possível salvar intervalo, tente novamente');
            }
          }
          this.saving = false;
        },
        err => {
          this.saving = false;
          this.alert.showError(err);
        }
      );
  }

  updateIntervalGoal() {
    this.goalsActionsService
      .updateIntervalGoal(this.campaignId, this.goalId, this.intervalGoal)
      .subscribe(
        response => {
          if (response) {
            this.intervalGoal.id = response;
            this.intervalGoalId = response;
            this.alert.showSuccess('Intervalo atualizado com sucesso');
            this.refreshIntervalsGoals.emit();
            this.hideModal();
          } else {
            this.alert.showError('Não foi possível atualizar intervalo, tente novamente');
          }
          this.saving = false;
        },
        err => {
          this.saving = false;
          this.alert.showError(err);
        }
      );
  }
}
