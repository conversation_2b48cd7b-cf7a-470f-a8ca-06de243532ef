<tabset #tabs class="bg-white p0 tab-no-border" [justified]="true">
	<tab (select)="listFields()">
		<ng-template tabHeading>Campos</ng-template>
		<metadata-fields-list #list (onEdit)="editPage($event)"></metadata-fields-list>
	</tab>
	<tab *ngIf="canCreateOrUpdate()" [disabled]="!canCreateMetadataFields()">
		<ng-template tabHeading>Cadastrar</ng-template>
		<metadata-fields-editor #edit></metadata-fields-editor>
	</tab>
</tabset>