import { Component, OnInit, ViewChild } from '@angular/core';

import * as moment from 'moment';

import { GpFileUploadComponent } from '../../../../../../../shared/components/gp-fileupload/gp-fileupload.component';
import { FileUploadEvent } from '../../../../../../../shared/components/gp-fileupload/file-upload-event';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { FormatHelper } from '../../../../../../../shared/formatters/format-helper';
import { Item } from '../../../../../../../shared/models/item';

import { CampaignStore } from '../../../../campaign.store';
import { CampaignParticipantsGoalsService } from '../../campaign-participants-goals.service';
import { PeriodType } from '../../campaign-participants-goals-parametrizations/actions/models/period-type';
import { isNullOrEmpty } from '../../../../../../../shared/helpers/comparators';

@Component({
  selector: 'campaign-participants-goals-import-edit',
  templateUrl: './campaign-participants-goals-import-edit.component.html'
})
export class CampaignParticipantsGoalsImportEditComponent implements OnInit {
  @ViewChild('alert') alert: GpAlertComponent;
  @ViewChild('gpFile') gpFile: GpFileUploadComponent;

  campaignGoals: Array<Item> = [];
  campaignParticipantsGoals: any[] = [];
  batchCampaignParticipantsGoals: any = {};
  participantsGoals: any = []

  selectedCampaignGoal: any = {};
  goalsPeriods: Array<Item> = [];
  periodKeys: Array<Item> = [];

  campaignId: string = '';
  batchId: string = '';

  successCount: number = 0;
  errorsCount: number = 0;

  periodType: PeriodType = PeriodType.NONE;

  loading: boolean = false;

  constructor(private campaignStore: CampaignStore, private campaignParticipantsGoalsService: CampaignParticipantsGoalsService) { }

  ngOnInit() {
    this.campaignId = this.campaignStore.id;
    this.findCampaignParticipantsGoalsParametrizations();
  }

  get hasSelectedFile() {
    return this.gpFile && this.gpFile.hasSelectedFile();
  }

  get hasPeriod() {
    return this.periodType == PeriodType.WEEKLY || this.periodType == PeriodType.MONTHLY || this.periodType == PeriodType.YEARLY || this.isCustomPeriod;
  }

  get isCustomPeriod(): boolean {
    return this.periodType === PeriodType.CUSTOM;
  }

  get hasPeriodKeys(): boolean {
    return this.periodKeys && this.periodKeys.length > 0;
  }

  get isError() {
    return this.batchCampaignParticipantsGoals.errorOccurred;
  }

  get canExportBatchErrors() {
    return this.errorsCount > 0;
  }

  get activeCampaignGoals(){
    return this.participantsGoals
      .filter(goal => goal.active == true)
      .map(goal => new Item(goal.id, `${goal.description || goal.descriptionForSite} (${goal.code})`));
  }

  uploadBatchFile() {
    if (!this.batchCampaignParticipantsGoals.campaignGoalId) {
      return this.alert.showWarning('Selecione a meta da importação.');
    }

    if (!this.gpFile || !this.gpFile.hasSelectedFile()) {
      return this.alert.showWarning('Selecione um arquivo para upload.');
    }

    this.loading = true;
    this.gpFile.path = `api/campaigns/${this.campaignId}/participants/goals/batches/upload`;
    this.batchCampaignParticipantsGoals.fileName = this.gpFile.selectedFilename;
    this.gpFile.uploadFile();
  }

  onFileUploaded(event: FileUploadEvent) {
    if (event.success && event.result) {
      this.batchCampaignParticipantsGoals.url = event.result;
      const paths = event.result.split('/');
      this.batchCampaignParticipantsGoals.storageFileName = paths[paths.length - 1];
      this.saveCampaignParticipantsGoalsBatch();
      this.loading = false;
    } else {
      this.loading = false;
      this.alert.showError('Ocorreu um erro ao realizar o upload do arquivo');
    }
  }

  private saveCampaignParticipantsGoalsBatch() {
    this.campaignParticipantsGoalsService.saveCampaignParticipantsGoalsBatch(this.campaignId, this.batchCampaignParticipantsGoals).subscribe(
      imported => {
        if (imported) {
          this.batchId = imported;
          this.findCampaignGoalsBatchById();
          this.alert.showSuccess('Arquivo importado com sucesso.');
        }
      },
      err => {
        this.loading = false;
        this.alert.showError(err);
      }
    );
  }

  findCampaignGoalsBatchById() {
    this.loading = true;
    this.campaignParticipantsGoalsService.findCampaignParticipantsGoalsBatchById(this.campaignId, this.batchId).subscribe(
      batch => {
        if (batch) {
          batch.formattedStatus = this.handleStatus(batch.status);
        }
        this.batchCampaignParticipantsGoals = batch;
        this.batchCampaignParticipantsGoals.formmatedGoalName = this.getFormattedGoalNameById(batch.campaignGoalId);
        this.batchCampaignParticipantsGoals.formattedFileReferenceDate = FormatHelper.formatDate(batch.fileReferenceDate);

        this.getCounters();
        this.loading = false;
      },
      err => {
        this.loading = false;
        this.alert.showError(err);
      }
    );
  }

  findCampaignParticipantsGoalsParametrizations() {
    this.campaignParticipantsGoalsService.findCampaignParticipantsGoalsParametrizations(this.campaignStore.id).subscribe(
      response => {
        if (response) {
          this.participantsGoals = response;
        }
      },
      err => {
        this.loading = false;
        this.alert.showError(err);
      }
    );
  }

  findCampaignParticipantsGoalsByBatchId() {
    this.campaignParticipantsGoalsService.findCampaignParticipantsGoalsByBatchId(this.campaignStore.id, this.batchId).subscribe(
      response => {
        if (response) {
          response.forEach(campaignParticipantsGoal => {
            campaignParticipantsGoal.formattedCreateDate = FormatHelper.formatDateWithTimezone(campaignParticipantsGoal.createDate);
          });
          this.participantsGoals = response;
        }
        this.campaignParticipantsGoals = response;
      },
      err => {
        this.alert.showError(err);
      }
    );

  }

  exportBatchErrors() {
    this.campaignParticipantsGoalsService.exportBatchErrors(this.campaignStore.id, this.batchId).subscribe(
      response => {
        window.open(response, '_blank');
      }, err => {
        this.alert.showError(err);
      }
    );
  }

  clear() {
    this.batchId = '';
    this.periodType = PeriodType.NONE;
    this.campaignParticipantsGoals = [];
    this.batchCampaignParticipantsGoals = {};
  }


  getCounters() {
    this.successCount = this.batchCampaignParticipantsGoals.totalLinesProcessingSuccess;
    this.errorsCount = this.batchCampaignParticipantsGoals.totalLinesProcessingErrors;
  }

  handleStatus(status: string): string {
    switch (status) {
      case 'CREATED':
        return 'Criado';
      case 'PROCESSING':
        return 'Processando';
      case 'COMPLETED':
        return 'Processado';
      case 'ERROR':
        return 'Erro durante o processamento';
      default:
        return 'Status inválido';
    }
  }

  configPeriod() {
    this.selectedCampaignGoal = this.participantsGoals.find(x => x.id == this.batchCampaignParticipantsGoals.campaignGoalId);

    const hadPeriod = this.selectedCampaignGoal && this.selectedCampaignGoal.settings
      && this.selectedCampaignGoal.settings.periodType;

    if (!hadPeriod) {
      this.periodType = PeriodType.NONE;
      return;
    }
    this.periodType = this.selectedCampaignGoal.settings.periodType;
    this.configureComboBoxWithPeriodsIfNeeded();
  }

  private configureComboBoxWithPeriodsIfNeeded(): void {
    if (!this.isCustomPeriod || !this.hasGoalPeriods())
      return;

    this.goalsPeriods = this.selectedCampaignGoal.settings.goalPeriods
      .filter(p => p.active)
      .map(p => new Item(p.id, p.description));
  }

  setPeriodKey(): void {
    const goalPeriod = this.findGoalPeriodById();
    if (!goalPeriod)
      return;

    if (goalPeriod.periodFormat === 'UNIQUE') {
      this.batchCampaignParticipantsGoals.periodFormat = 'UNIQUE';
      this.batchCampaignParticipantsGoals.periodKey = 'ALL';
      this.periodKeys = [];
      return;
    }
    if (goalPeriod.periodFormat === 'MULTI_WITHOUT_MONTHLY_RESULT') {
      this.batchCampaignParticipantsGoals.periodFormat = 'MULTI_WITHOUT_MONTHLY_RESULT';
      this.batchCampaignParticipantsGoals.periodKey = goalPeriod.periodKey;
      this.periodKeys = [];
      return;
    }
    this.batchCampaignParticipantsGoals.periodFormat = 'MULTI_WITH_MONTHLY_RESULT'

    const periodKeys = goalPeriod.periodKey.split('#');
    this.periodKeys = periodKeys.map(p => this.buildItemWithPeriodKey(p));

    if (this.periodKeys && this.periodKeys.length === 1) {
      this.batchCampaignParticipantsGoals.periodKey = this.periodKeys[0].id;
    }
  }

  private findGoalPeriodById(): any {
    if (!this.hasGoalPeriods())
      return null;

    return this.selectedCampaignGoal.settings.goalPeriods.find(p => p.id === this.batchCampaignParticipantsGoals.goalPeriodId);
  }

  private hasGoalPeriods(): boolean {
    return this.selectedCampaignGoal.settings.goalPeriods && this.selectedCampaignGoal.settings.goalPeriods.length > 0;
  }

  private buildItemWithPeriodKey(periodKey: string): Item {
    const splitPeriodKey = periodKey.split("-");
    const month = (Number(splitPeriodKey[1]) - 1).toString();
    const year = splitPeriodKey[0];

    return new Item(periodKey,
      moment().month(month).locale('pt-br').format('MMMM')
      .concat(' / '.concat(year))
    );
  }

  getFormattedGoalNameById(id: string) : string {
    if(isNullOrEmpty(id)){
      return ""
    }

    for (var goal of this.participantsGoals) {
      if(goal.id == id){
        return goal.name;
      }
    }

    return "";
  }
}
