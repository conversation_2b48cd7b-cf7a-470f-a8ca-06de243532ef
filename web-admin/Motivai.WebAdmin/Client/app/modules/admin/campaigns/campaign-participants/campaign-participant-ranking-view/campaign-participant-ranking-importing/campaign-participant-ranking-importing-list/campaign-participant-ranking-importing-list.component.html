<gp-card [first]="true">
  <gp-form-row>
    <gp-form-col cols="12 6 6">
      <gp-simple-input label="Número do Lote">
        <input type="text" class="form-control" name="batchNumber" [(ngModel)]="params.batchNumber" />
      </gp-simple-input>
    </gp-form-col>

    <gp-form-col cols="12 6 6">
      <gp-simple-input label="Status do Lote">
        <gp-select name="batchStatus" [allowClear]="false" [items]="batchStatuses" [(ngModel)]="params.batchStatus">
        </gp-select>
      </gp-simple-input>
    </gp-form-col>

    <!-- <gp-datepicker cols="12 4 4" label="Data de Importação" name="importDate" [(ngModel)]="params.importDate">
    </gp-datepicker> -->
  </gp-form-row>

  <gp-form-row>
    <gp-form-col cols="12 12">
      <gp-spinner-button [search]="true" pull="right" text="Pesquisar" marginTop="27px" [loading]="loading"
        loadingText="Processando" (click)="findBatchesRanking()"></gp-spinner-button>
    </gp-form-col>
  </gp-form-row>
</gp-card>

<gp-card title="Registros encontrados" [last]="true">
  <gp-form-row>
    <gp-form-col cols="12">
      <gp-grid name="grid" [rows]="batchesRankings"
        [columns]="['Número do Lote', 'Data da Importação', 'Ranking', 'Nome do Arquivo', 'Status']"
        [fields]="['batchNumber', 'formattedCreateDate', 'rankingName', 'fileName', 'formattedStatus']" [showActive]="false"
        [showPagination]="true" [totalRecords]="totalRecords" [pageSize]="params.limit" (onPageChanged)="onPageChanged($event)" [showDelete]="false" [showEdit]="true" (onEdit)="showDetailsBatchRanking($event)">
      </gp-grid>
    </gp-form-col>
  </gp-form-row>
</gp-card>

<spinner [overlay]="true" [show]="loading"></spinner>
<gp-alert #alert [overlay]="true"></gp-alert>
