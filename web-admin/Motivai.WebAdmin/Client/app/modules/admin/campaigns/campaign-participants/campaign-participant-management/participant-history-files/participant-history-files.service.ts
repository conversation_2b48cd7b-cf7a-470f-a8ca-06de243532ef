import { Injectable } from '@angular/core';
import { Observable } from 'rxjs/Observable';
import { ApiService } from '../../../../../../core/api/api.service';


@Injectable()
export class ParticipantHistoryFilesService {

  constructor(private api: ApiService) { }


  public getParticipantHistoryFiles(userId: string, campaignId: string): any {
    return this.api.get(`/api/users/${userId}/campaigns/${campaignId}/historyfiles`);
  }

  public unactiveParticipantHistoryFiles(userId: string, campaignId: string, id: string): Observable<any>  {
    return this.api.put(`/api/users/${userId}/campaigns/${campaignId}/historyfiles/${id}`);
  }

  public save(userId: string, campaignId: string, historyFile: any): Observable<any> {
    return this.api.post(`/api/users/${userId}/campaigns/${campaignId}/historyfiles`, historyFile);
  }

}
