import { PERMISSION_CAMPAIGNS_PARTICIPANTS_MANAGEMENT } from '../../../../../core/auth/access-points';
import { CampaignService } from '../../campaign.service';
import { AuthStore } from '../../../../../core/auth/auth.store';
import { ActivatedRoute, Router } from '@angular/router';
import { Component, OnInit } from '@angular/core';
import { Campaign } from '../../campaign';
import { CampaignStore } from '../../campaign.store';

@Component({
  selector: 'campaign-participants-view',
  templateUrl: 'campaign-participants-view.component.html'
})
export class GpParticipantsViewComponent implements OnInit {
  campaignId: string;
  campaign: Campaign;

  loading: boolean = false;

  constructor(private _authStore: AuthStore, private _campaignService: CampaignService,
    private route: ActivatedRoute, private router: Router,private _campaignStore: CampaignStore) { }

  ngOnInit() {
    if (this.route.parent != null) {
      this.route.parent.params.subscribe((params: any) => {
        this.campaignId = params['id'];
        if (!this.campaignId) {
          this.router.navigate(['/campanha']);
          return;
        }
      });
    } else {
      this.router.navigate(['/campanha']);
    }
  }



  get showMenublock() {
    return this._campaignStore.isFullCampaignOrUserWithGpBu;
  }

  get canListGroups() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_PARTICIPANTS_GROUPS_LIST;
  }

  get canListImports() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_IMPORT_PARTICIPANTS_VIEW;
  }

  get canListPreregister() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_PREREGISTER_VIEW;
  }

  get canViewParticipantsMetadataFields() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_PARTICIPANTS_METADATA_FIELDS_VIEW;
  }

  private canViewParticipantManagament() {
    return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_PARTICIPANTS_MANAGEMENT);
  }

  get canViewMetadataPages() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_PARTICIPANTS_METADATA_FIELDS_VIEW;
  }

  get canViewRankingParametrization() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_PARTICIPANTS_RANKING_PARAMETRIZATION;
  }

  get canViewParticipantsRegisterReport() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_PARTICIPANTS_REGISTERS_REPORT;
  }

  get canViewParticipantsCredify() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_PARTICIPANTS_CREDIFY;
  }

  get canViewParticipantsGoals() {
    // TODO: criar permissao
    return this._authStore.role.PERMISSION_CAMPAIGNS_PARTICIPANTS_RANKING_PARAMETRIZATION;
  }
}
