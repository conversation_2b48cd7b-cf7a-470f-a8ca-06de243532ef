<gp-alert #alert [overlay]="true"></gp-alert>
<spinner [overlay]="true" [show]="loading"></spinner>

<gp-card title="Meta" [first]="true">
  <gp-form-row>
    <gp-form-col cols="12 6">
      <gp-simple-input label="Nome">
        <gp-tooltip message="Identificação interna">(?)</gp-tooltip>
        <input type="text" name="name" class="form-control" [disabled]="hasStarted && !!goalParametrizations.name"
          [(ngModel)]="goalParametrizations.name" />
      </gp-simple-input>
    </gp-form-col>

    <gp-form-col cols="12 6">
      <div>
        <label>Ativo</label>
      </div>
      <gp-switch name="active" [(ngModel)]="goalParametrizations.active"></gp-switch>
    </gp-form-col>
  </gp-form-row>

  <gp-form-row>
    <gp-form-col cols="12 6">
      <gp-simple-input label="Descrição Site">
        <gp-tooltip message="Será exibido no site campanha para o participante">(?)</gp-tooltip>
        <input type="text" name="descriptionForSite" class="form-control"
          [disabled]="hasStarted && !!goalParametrizations.descriptionForSite"
          [(ngModel)]="goalParametrizations.descriptionForSite" />
      </gp-simple-input>
    </gp-form-col>

    <gp-form-col cols="12 6">
      <gp-simple-input label="Código">
        <input type="text" name="code" class="form-control" [disabled]="hasStarted"
          [(ngModel)]="goalParametrizations.code" />
      </gp-simple-input>
    </gp-form-col>
  </gp-form-row>

  <gp-form-row>
    <gp-form-col cols="12 6">
      <gp-simple-input label="Descrição da Transação de Crédito">
        <gp-tooltip message="Exibido na transação de crédito no extrato do participante">(?)</gp-tooltip>
        <input type="text" name="descriptionForTransaction" class="form-control"
          [(ngModel)]="goalParametrizations.descriptionForTransaction" />
      </gp-simple-input>
    </gp-form-col>

    <gp-form-col cols="12 6">
      <label>Selecione a Mecânica</label>
      <gp-select name="mechanicId" [items]="campaignsMechanics" placeholder="Selecione a mecânica"
        [(ngModel)]="goalParametrizations.mechanicId">
      </gp-select>
    </gp-form-col>
  </gp-form-row>

  <hr>
  <h4 class="font-weight">Configuração de exibição</h4>
  <gp-form-row>
    <gp-form-col cols="12 6">
      <div>
        <label>Exibir meta no site campanha</label>
      </div>
      <gp-switch name="showAtCampaignSite" [(ngModel)]="goalParametrizations.parametrizations.showAtCampaignSite">
      </gp-switch>
    </gp-form-col>
    <gp-form-col cols="12 6" *ngIf="groupedGoalsLayout">
      <div>
        <label>Exibir no agrupamento de metas</label>
      </div>
      <gp-switch name="enableInGroupedGoalsLayout"
        [(ngModel)]="goalParametrizations.parametrizations.enableInGroupedGoalsLayout">
      </gp-switch>
    </gp-form-col>
    <gp-form-col cols="12 6">
      <div>
        <label>Retornar meta na API Cadastros</label>
      </div>
      <gp-switch name="showAtApiCampaignsIntegrations"
        [(ngModel)]="goalParametrizations.parametrizations.showAtApiCampaignsIntegrations">
      </gp-switch>
    </gp-form-col>
  </gp-form-row>
</gp-card>

<gp-card title="Segmentações">
  <gp-form-row>
    <gp-form-col cols="12 6">
      <label>Habilitar segmentação por grupo</label>
      <div>
        <gp-switch [disabled]="hasStarted" [(ngModel)]="goalParametrizations.enableParticipantsGroups"></gp-switch>
      </div>
    </gp-form-col>

    <gp-campaign-participants-group-selector *ngIf="goalParametrizations.enableParticipantsGroups"
      name="participantsGroups" cols="12 6" [enableEmpty]="false" [campaignId]="campaignId" [disabled]="hasStarted"
      [(ngModel)]="goalParametrizations.participantsGroups">
    </gp-campaign-participants-group-selector>
  </gp-form-row>

  <gp-form-row>
    <gp-form-col cols="12 12 12">
      <label>Habilitar segmentação por participante</label>
      <div>
        <gp-switch name="enableSegmentationByParticipants"
          [(ngModel)]="goalParametrizations.enableSegmentationByParticipants" [disabled]="hasStarted">
        </gp-switch>
      </div>
    </gp-form-col>

    <gp-form-col cols="12 12 12" *ngIf="goalParametrizations.enableSegmentationByParticipants">
      <campaign-participants-selector [showGrid]="true"
        [participantsList]="goalParametrizations.participantsSegmentation"
        (selectedParticipant)="onSelectedParticipant($event)" [disabled]="hasStarted">
      </campaign-participants-selector>
    </gp-form-col>
  </gp-form-row>
</gp-card>

<gp-card title="Parametrizações">
  <gp-form-row>
    <gp-form-col cols="12 4">
      <label>Formato da meta</label>
      <gp-select name="goalFormat" [items]="goalFormats" placeholder="Selecione o formato"
        [(ngModel)]="goalParametrizations.parametrizations.goalFormat"
        (change)="updateAvailableProgressViewer()">
      </gp-select>
    </gp-form-col>
  </gp-form-row>

  <div *ngIf="isExclusiveLevel">
    <hr>
    <h4>Eventos</h4>
    <gp-form-row>
      <gp-form-col cols="12 4">
        <label>Tipo de processamento
          <gp-tooltip spanClass="text-primary">
            <div style="text-align:justify">
              <ul>
                <li>
                  Processar Metas: Processa somente as faixas cadastradas (Comportamento padrão).
                </li>
                <li>
                  Processar Eventos: Processa somente os eventos cadastrados.
                </li>
                <li>
                  Processar Metas e Eventos: Processa as faixas e eventos cadastrados.
                </li>
              </ul>
            </div>
          </gp-tooltip>
        </label>
        <gp-select name="processType" [items]="processTypes"
          [(ngModel)]="goalParametrizations.parametrizations.processType">
        </gp-select>
      </gp-form-col>

      <gp-form-col cols="12 4" *ngIf="hasExclusiveLevelToProcess">
        <label>Tipo de ordenação das metas
          <gp-tooltip spanClass="text-primary">
            <div style="text-align:justify">
              <ul>
                <li>
                  Decrescente: Na verificação da faixa atingida, a comparação do resultado com o valor base é dada pela
                  regra maior ou igual ( >= ),
                  começando a verificação sempre pelo maior valor base cadastrado.
                </li>
                <li>
                  Crescente: Na verificação da faixa atingida, a comparação do resultado com o valor base é dada pela
                  regra menor ou igual ( <= ), começando a verificação sempre pelo menor valor base cadastrado. </li>
              </ul>
            </div>
          </gp-tooltip>
        </label>
        <gp-select name="goalsOrdinationType" [items]="eventTypes"
          [(ngModel)]="goalParametrizations.parametrizations.goalsOrdinationType">
        </gp-select>
      </gp-form-col>
    </gp-form-row>
  </div>

  <hr />
  <h4>Features da Campanha</h4>
  <gp-form-row>
    <gp-form-col cols="12 6">
      <label>Habilitar features da campanha</label>
      <div>
        <gp-switch name="enableCampaignFeatures"
          [(ngModel)]="goalParametrizations.parametrizations.enableCampaignFeatures">
        </gp-switch>
      </div>
    </gp-form-col>

    <gp-form-col cols="12 6" additionalClasses="col-with-button">
      <gp-spinner-button bootstrapClass="primary" text="Selecionar Feature" icon="search"
        (click)="showFeatureSelectModal()">
      </gp-spinner-button>
    </gp-form-col>
  </gp-form-row>

  <gp-grid name="campaignFeatures" [loading]="loading" [rows]="goalParametrizations.parametrizations.campaignFeatures"
    [columns]="['Tipo Feature', 'Nome Feature']" [fields]="['featureTypeDescription', 'featureName']" [showEdit]="false"
    [showActive]="true" [showDelete]="canSaveParametrization" (onDelete)="removeCampaignFeature($event)"
    *ngIf="hasCampaignFeatureLinked">
  </gp-grid>
</gp-card>

<gp-card title="Configurações de Menu">
  <gp-form-row>
    <gp-form-col cols="12 3 4">
      <label>Habilitar no menu</label>
      <div>
        <gp-switch name="enableOnCampaignSiteMenu" [(ngModel)]="goalParametrizations.settings.enableOnCampaignSiteMenu">
        </gp-switch>
      </div>
    </gp-form-col>
  </gp-form-row>
  <campaign-site-page-param [ngModelOptions]="{standalone: true}" [showRequireAuth]="false" [requireAuth]="true"
    [showEnable]="false" [enable]="true" [showVisibleMenu]="false" [visibleMenu]="true" [showMenuPosition]="true"
    [(ngModel)]="goalParametrizations.settings.campaignSiteMenuItem">
  </campaign-site-page-param>
</gp-card>

<gp-card title="Configurações de Processamento">
  <gp-form-row>
    <gp-form-col cols="12 4">
      <label>Criar participantes caso não exista</label>
      <div>
        <gp-switch [(ngModel)]="goalParametrizations.parametrizations.createParticipantWhenNotFound"></gp-switch>
      </div>
    </gp-form-col>

    <gp-form-col cols="12 4">
      <label>Configurar meta do participante caso não exista</label>
      <div>
        <gp-switch [(ngModel)]="goalParametrizations.parametrizations.createParticipantGoalsWhenNotConfigured"
          [disabled]="disabledCreateParticipantGoalsWhenNotConfigured">
        </gp-switch>
      </div>
    </gp-form-col>

    <gp-form-col cols="12 4">
      <label>Habilitar notificação de crédito <gp-tooltip
          message="Habilitar notificação de crédito de pontos liberados para o participante. É utilizado o mesmo template de crédito de pontos da campanha.">(?)</gp-tooltip></label>
      <div>
        <gp-switch [(ngModel)]="goalParametrizations.parametrizations.enablePointsCreditNotification">
        </gp-switch>
      </div>
    </gp-form-col>
  </gp-form-row>

  <gp-form-row>
    <gp-form-col cols="12 4" *ngIf="isExclusiveLevel || goalFormatByTargetAwarded && !hasEventsToProcess && !isRewardResultProcessTypeViewOnly">
      <div>
        <label>Permitir controlar liberação em cada linha
          <gp-tooltip spanClass="text-primary">
            <div style="text-align:justify">
              Se habilitado e existir a coluna Liberado no arquivo com valor Sim, o bonus da faixa alcançada será
              creditado.
            </div>
          </gp-tooltip>
        </label>
      </div>
      <gp-switch name="enableSimulationControlByLine"
        [(ngModel)]="goalParametrizations.settings.enableSimulationControlByLine"></gp-switch>
    </gp-form-col>

    <gp-form-col cols="12 4">
      <label>Desabilitar pontos bloqueados <gp-tooltip message="Desabilitar pontos bloqueados.">(?)</gp-tooltip></label>
      <div>
        <gp-switch [(ngModel)]="goalParametrizations.parametrizations.disableBlockedPoints">
        </gp-switch>
      </div>
    </gp-form-col>

    <gp-form-col cols="12 4">
      <label>Desabilitar arredondamento <gp-tooltip
          message="Desabilitar arredondamento do valor base da meta e do resultado.">(?)</gp-tooltip></label>
      <div>
        <gp-switch [(ngModel)]="goalParametrizations.parametrizations.disableRoundingValues">
        </gp-switch>
      </div>
    </gp-form-col>
  </gp-form-row>

  <gp-form-row *ngIf="isExclusiveLevel && !hasEventsToProcess && !isRewardResultProcessTypeViewOnly">
    <gp-form-col cols="12 4">
      <div>
        <label>Habilitar bônus das faixas inferiores à alcançada
          <gp-tooltip spanClass="text-primary">
            <div style="text-align:justify">
              Se habilitado e delta habilitado -> Calcula o delta entre as faixas
              <ul>
                <li>Se delta habilitado e a flag pra usar o excedente habilitada -> Calcula a faixa atingida com o
                  resultado - o valor base da faixa anterior</li>
                <li>Se delta habilitado e a flag pra usar o excedente desabilitada -> Calcula o delta usando a base
                  atual - base anterior</li>
                <li>Se delta desabilitado e a flag excedente habilitada -> Calcula o valor do bônus para o nível
                  atingido com o resultado atual, e o demais níveis com o teto -1</li>
                <li>Se delta desabilitado e a flag excedente desabilitada -> Calcula o valor do bônus para nível
                  atingido e os níveis anteriores, com o valor mínimo atingido no nível</li>
              </ul>
            </div>
          </gp-tooltip>
        </label>
      </div>
      <gp-switch name="enablePreviousRecheadLevelsBonus"
        [(ngModel)]="goalParametrizations.settings.enablePreviousRecheadLevelsBonus"></gp-switch>
    </gp-form-col>

    <gp-form-col cols="12 4">
      <div>
        <label>Habilitar crédito parcial das faixas atingidas
          <gp-tooltip spanClass="text-primary">
            <div style="text-align:justify">
              Permite continuar creditando a mesma meta ao atingir os próximos níveis
            </div>
          </gp-tooltip>
        </label>
      </div>
      <gp-switch name="enablePartialLevelsCredits"
        [(ngModel)]="goalParametrizations.settings.enablePartialLevelsCredits"></gp-switch>
    </gp-form-col>

    <gp-form-col cols="12 4" *ngIf="isExclusiveLevel && !hasEventsToProcess && !isRewardResultProcessTypeViewOnly">
      <div>
        <label>Habilitar deltas entre as faixas no cálculo
          <gp-tooltip spanClass="text-primary">
            <div style="text-align:justify">
              Se habilitado o bônus da faixa é calculado utilizando a diferença entre o piso da faixa atual e o piso da
              faixa anterior, multiplicado pelo cashback da faixa atual.
              <ul>
                <li>Se habilitado: Valor pontos = (Piso Faixa Atual - Piso Faixa Anterior) * Cashback (%)</li>
                <li>Se desabilitado: Valor pontos = Piso Faixa Atual * Cashback (%)</li>
              </ul>
            </div>
          </gp-tooltip>
        </label>
      </div>
      <gp-switch name="useDeltaBetweenLevelsInCalculation"
        [(ngModel)]="goalParametrizations.settings.useDeltaBetweenLevelsInCalculation"></gp-switch>
    </gp-form-col>
  </gp-form-row>

  <gp-form-row *ngIf="goalFormatByTargetAwarded">
    <gp-form-col cols="12 4">
      <label>Permitir configuração contínua
        <gp-tooltip spanClass="text-primary">
          <div style="text-align:justify">
              Se habilitado, o valor da meta poderá ser atualizado enquanto não houver crédito.
          </div>
      </gp-tooltip>
      </label>
      <div>
        <gp-switch [(ngModel)]="goalParametrizations.parametrizations.enableContinuousSetup">
        </gp-switch>
      </div>
    </gp-form-col>
  </gp-form-row>
</gp-card>

<campaign-participants-goals-reward-parametrizations [hasStarted]="hasStarted"
  [params]="goalParametrizations.parametrizations.reward" (rewardParametrizations)="addRewardParametrizations($event)"
  (validationError)="showRewardParamsErrorMessage($event)" #rewardParams>
</campaign-participants-goals-reward-parametrizations>

<gp-card title="Configurações de Período">
  <gp-form-row>
    <gp-form-col cols="12 4">
      <div>
        <label>Período de ativação</label>
      </div>
      <gp-switch name="period" [(ngModel)]="goalParametrizations.settings.period"></gp-switch>
    </gp-form-col>
  </gp-form-row>
  <gp-form-row *ngIf="hasPeriod">
    <gp-datepicker cols="12 6 6 6" label="Data do pedido (de)" name="initialDate"
      [(ngModel)]="goalParametrizations.settings.initialDate"></gp-datepicker>
    <gp-datepicker cols="12 6 6 6" label="Data do pedido (até)" name="endDate"
      [(ngModel)]="goalParametrizations.settings.endDate"></gp-datepicker>
  </gp-form-row>

  <gp-form-row>
    <gp-form-col cols="12 6" *ngIf="isAllowPeriod">
      <label>Período de limitação</label>
      <gp-select name="periodType" [disabled]="hasStarted" [items]="PeriodTypes" placeholder="Selecione o formato"
        [(ngModel)]="goalParametrizations.settings.periodType" (change)="onChangeSelectedPeriod()">
      </gp-select>
    </gp-form-col>
  </gp-form-row>

  <hr *ngIf="isPeriodCustomType" />
  <gp-gp-form-row *ngIf="isPeriodCustomType">
    <gp-spinner-button bootstrapClass="primary" icon="plus" [inline]="false" pull="right" text="Adicionar período"
      (click)="newGoalPeriod()" *ngIf="!hasStarted">
    </gp-spinner-button>
    <gp-form-row>
      <gp-form-col cols="12">
        <gp-grid [rows]="customGoalPeriods" [columns]="['Descrição', 'Data Inicial', 'Data Final']"
          [fields]="['description', 'formattedInitialDate', 'formattedEndDate']" [loading]="false" [showActive]="true"
          [showEdit]="true" (onEdit)="editGoalPeriod($event)" [showDelete]="false">
        </gp-grid>
      </gp-form-col>
    </gp-form-row>
  </gp-gp-form-row>
</gp-card>

<gp-card title="Exibir no Site Campanha">
  <gp-form-row>
    <gp-form-col cols="12 2 2">
      <div>
        <label>Valor da meta</label>
      </div>
      <gp-switch name="displayTargetValue" [(ngModel)]="goalParametrizations.settings.displayTargetValue"></gp-switch>
    </gp-form-col>

    <gp-form-col cols="12 4 4" *ngIf="goalParametrizations.settings.displayTargetValue">
      <gp-simple-input label="Nome da coluna">
        <input type="text" name="targetValueLabel" class="form-control"
          [(ngModel)]="goalParametrizations.settings.targetValueLabel" />
      </gp-simple-input>
    </gp-form-col>
  </gp-form-row>

  <gp-form-row>
    <gp-form-col cols="12 2 2">
      <div>
        <label>Valor ganho</label>
      </div>
      <gp-switch name="displayBonusReward" [(ngModel)]="goalParametrizations.settings.displayBonusReward"></gp-switch>
    </gp-form-col>

    <gp-form-col cols="12 4 4" *ngIf="goalParametrizations.settings.displayBonusReward">
      <gp-simple-input label="Nome da coluna">
        <input type="text" name="bonusRewardLabel" class="form-control"
          [(ngModel)]="goalParametrizations.settings.bonusRewardLabel" />
      </gp-simple-input>
    </gp-form-col>
  </gp-form-row>

  <gp-form-row>
    <gp-form-col cols="12 2 2">
      <div>
        <label>Resultado do participante</label>
      </div>
      <gp-switch name="displayParticipantResultValue"
        [(ngModel)]="goalParametrizations.settings.displayParticipantResultValue"></gp-switch>
    </gp-form-col>

    <gp-form-col cols="12 4 4" *ngIf="goalParametrizations.settings.displayParticipantResultValue">
      <gp-simple-input label="Nome da coluna">
        <input type="text" name="participantResultValueLabel" class="form-control"
          [(ngModel)]="goalParametrizations.settings.participantResultValueLabel" />
      </gp-simple-input>
    </gp-form-col>
  </gp-form-row>

  <gp-form-row *ngIf="goalParametrizations.settings?.enableHistoricDetails">
    <gp-form-col cols="12 2 2">
      <div>
        <label>Período da {{ goalParametrizations.settings?.targetValueLabel || 'Meta' }}</label>
      </div>
      <gp-switch name="DisplayPeriod" [(ngModel)]="goalParametrizations.settings.displayPeriod"></gp-switch>
    </gp-form-col>

    <gp-form-col cols="12 4 4" *ngIf="goalParametrizations.settings?.displayPeriod">
      <gp-simple-input label="Nome da coluna">
        <input type="text" name="PeriodLabel" class="form-control"
          [(ngModel)]="goalParametrizations.settings.periodLabel" />
      </gp-simple-input>
    </gp-form-col>
  </gp-form-row>

  <hr />

  <gp-form-row>
    <gp-form-col cols="12 6">
      <div>
        <label>Exibir em dashboard</label>
      </div>
      <gp-switch name="enableDashboard" [(ngModel)]="goalParametrizations.settings.showDashboard">
      </gp-switch>
    </gp-form-col>
    <gp-form-col cols="12 6">
      <div>
        <label>Exibir Progresso</label>
      </div>
      <gp-switch name="showProgressThermometer" [(ngModel)]="goalParametrizations.settings.showProgressViewer">
      </gp-switch>
    </gp-form-col>
    <gp-form-col cols="12 6" *ngIf="goalParametrizations.settings.showProgressViewer">
      <gp-simple-input label="Tipo de Visualização de Progresso" [required]="true"
        errorMessage="Tipo de Visualização de Progresso é obrigatória">
        <gp-select name="factoryId" [items]="progressViewerTypes"
          [disabled]="!goalParametrizations.parametrizations.goalFormat"
          [(ngModel)]="goalParametrizations.settings.progressViewerType"
          placeholder="Tipo de Visualização de Progresso">
        </gp-select>
      </gp-simple-input>
    </gp-form-col>
  </gp-form-row>
  <hr />

  <gp-form-row *ngIf="hasPeriodType">
    <gp-form-col cols="12 12">
      <div>
        <label>Exibir histórico de metas</label>
      </div>
      <gp-switch name="enableDashboard" [(ngModel)]="goalParametrizations.settings.enableHistoricDetails">
      </gp-switch>
    </gp-form-col>
  </gp-form-row>

  <gp-form-row *ngIf="goalParametrizations.settings?.enableHistoricDetails">
    <gp-form-col cols="12 2 2">
      <div>
        <label>Nome da {{ goalParametrizations.settings?.targetValueLabel || 'Meta' }}</label>
      </div>
      <gp-switch name="DisplayTargetGoalName"
        [(ngModel)]="goalParametrizations.settings.displayTargetGoalName"></gp-switch>
    </gp-form-col>

    <gp-form-col cols="12 4 4" *ngIf="goalParametrizations.settings?.displayTargetGoalName">
      <gp-simple-input label="Nome da coluna">
        <input type="text" name="TargetGoalNameLabel" class="form-control"
          [(ngModel)]="goalParametrizations.settings.targetGoalNameLabel" />
      </gp-simple-input>
    </gp-form-col>
  </gp-form-row>

  <gp-form-row *ngIf="goalParametrizations.settings?.enableHistoricDetails">
    <gp-form-col cols="12 2 2">
      <div>
        <label>Indicador de simulação</label>
      </div>
      <gp-switch name="DisplaySimulation" [(ngModel)]="goalParametrizations.settings.displaySimulation"></gp-switch>
    </gp-form-col>
  </gp-form-row>

  <gp-form-row *ngIf="goalParametrizations.settings?.enableHistoricDetails">
    <gp-form-col cols="12 2 2">
      <div>
        <label>{{ goalParametrizations.settings?.targetValueLabel || 'Meta' }} Atingida</label>
      </div>
      <gp-switch name="DisplayTargetReached"
        [(ngModel)]="goalParametrizations.settings.displayTargetReached"></gp-switch>
    </gp-form-col>
  </gp-form-row>

  <gp-form-row *ngIf="goalParametrizations.settings?.enableHistoricDetails">
    <gp-form-col cols="12 2 2">
      <div>
        <label>Data Atingida</label>
      </div>
      <gp-switch name="displayReachDate" [(ngModel)]="goalParametrizations.settings.displayReachDate"></gp-switch>
    </gp-form-col>
  </gp-form-row>
</gp-card>

<gp-card title="Conteúdo da Meta">
  <gp-form-row>
    <gp-form-col cols="12">
      <gp-editor id="goalHeaderContent" [(ngModel)]="goalParametrizations.goalHeaderContent"></gp-editor>
    </gp-form-col>
  </gp-form-row>
</gp-card>

<gp-card title="Eventos da Meta" *ngIf="parametrizationId && hasEventsToProcess">
  <gp-spinner-button *ngIf="!hasStarted" bootstrapClass="primary" icon="plus" [inline]="false" pull="right"
    text="Adicionar evento" (click)="newGoalEvent()">
  </gp-spinner-button>
  <gp-form-row>
    <gp-form-col cols="12">
      <gp-grid [rows]="events" [columns]="['Código', 'Descrição', 'Tipo', 'Valor']" [loading]="loadingIntervalsGoals"
        [fields]="['code', 'description', 'formatterEventType', 'value']" [showEdit]="true" [showDelete]="false"
        (onEdit)="editGoalEvent($event)">
      </gp-grid>
    </gp-form-col>
  </gp-form-row>
</gp-card>

<gp-card *ngIf="parametrizationId && goalFormatByTargetAwarded">
  <awarded-target-goal-editing [campaignId]="campaignId" [goalId]="parametrizationId"
    [_goalAchievedAwarded]="goalAchievedAwarded" [hasStarted]="hasStarted"
    (goalAchievedAwarded)="addGoalAchievedAwarded($event)"
    (validationError)="showGoalAchievedAwardedErrorMessage($event)" #awardedTargetGoalModal>
  </awarded-target-goal-editing>
</gp-card>

<gp-card title="Faixas Acumulativa da Meta" *ngIf="showGoalLevels">
  <gp-spinner-button *ngIf="!hasStarted" bootstrapClass="primary" icon="plus" [inline]="false" pull="right"
    text="Adicionar faixa" (click)="showGoalLevel()">
  </gp-spinner-button>
  <gp-form-row>
    <gp-form-col cols="12">
      <gp-grid [rows]="goalsLevels" [columns]="['Descrição', 'Meta da Faixa', 'Valor Ganho', 'Tipo da Pontuação']"
        [fields]="['name', 'targetValue', 'value', 'formattedRewardType']" [showEdit]="false" [showDelete]="false">
      </gp-grid>
    </gp-form-col>
  </gp-form-row>
</gp-card>

<gp-card title="Faixas Exclusívas da Meta" *ngIf="parametrizationId && isExclusiveLevel">
  <gp-spinner-button *ngIf="!hasStarted" bootstrapClass="primary" icon="plus" [inline]="false" pull="right"
    text="Adicionar faixa" (click)="newExclusiveLevelGoal()">
  </gp-spinner-button>
  <gp-form-row>
    <gp-form-col cols="12">
      <gp-grid [rows]="exclusiveLevelsGoals"
        [columns]="['Descrição', 'Faturamento (Base)', 'Fator de Crescimento (%)' , 'Variação', 'Atingimento Mínimo', 'Valor Ganho', 'Tipo da Pontuação','Método de Pontuação']"
        [loading]="loadingExclusiveLevelGoals"
        [fields]="['name', 'baseTargetValue', 'levelThresholdPercentage', 'levelThresholdVariationValue', 'minimumTargetValue', 'rewardValue', 'formattedRewardType', 'formattedRewardMethod']"
        [showEdit]="true" [showDelete]="false" (onEdit)="editExclusiveLevelGoal($event)">
      </gp-grid>
    </gp-form-col>
  </gp-form-row>
</gp-card>

<gp-card title="Faixas de Intervalos da Meta" *ngIf="parametrizationId && isIntervalsFormat">
  <gp-spinner-button *ngIf="!hasStarted" bootstrapClass="primary" icon="plus" [inline]="false" pull="right"
    text="Adicionar faixa" (click)="newIntervalGoal()">
  </gp-spinner-button>
  <gp-form-row>
    <gp-form-col cols="12">
      <gp-grid [rows]="intervalsGoals"
        [columns]="['Descrição', 'Faturamento (Piso)', 'Intervalo', 'Valor Ganho', 'Tipo da Pontuação', 'Método de Pontuação']"
        [loading]="loadingIntervalsGoals"
        [fields]="['name', 'baseTargetValue', 'interval', 'rewardValue', 'formattedRewardType', 'formattedRewardMethod']"
        [showEdit]="true" [showDelete]="false" (onEdit)="editIntervalGoal($event)">
      </gp-grid>
    </gp-form-col>
  </gp-form-row>
</gp-card>

<gp-card [last]="true">
  <gp-form-row>
    <gp-form-col cols="12 12 12" [inputGroup]="false">
      <gp-spinner-button *ngIf="canInitiateGoal" bootstrapClass="primary" icon="send" pull="right" text="Iniciar meta"
        marginLeft="5px" [disabled]="starting" [loading]="starting" loadingText="Processando..." (click)="startGoal()">
      </gp-spinner-button>

      <gp-spinner-button [pink]="true" icon="send" pull="right" text="Salvar" marginLeft="5px" [disabled]="saving"
        [loading]="saving" loadingText="Salvando" (click)="saveCampaignParticipantsGoal()">
      </gp-spinner-button>

      <gp-spinner-button bootstrapClass="default" icon="plus" pull="right" text="Nova meta" (click)="clear()">
      </gp-spinner-button>
    </gp-form-col>
  </gp-form-row>
</gp-card>


<gp-modal title="Faixa de Objetivo" #goalModal>
  <form>
    <gp-form-row>
      <gp-form-col cols="12 12">
        <gp-simple-input label="Descrição">
          <input type="text" name="name" name="levelDescription" class="form-control" [(ngModel)]="goalLevel.name" />
        </gp-simple-input>
      </gp-form-col>
    </gp-form-row>

    <gp-form-row>
      <gp-form-col cols="12 12">
        <label>Valor à ser atingido</label>
        <gp-input-mask required name="targetValue" [onlyDecimal]="true" [integers]="6" [decimais]="2" placeholder="0,00"
          [(ngModel)]="goalLevel.targetValue"></gp-input-mask>
      </gp-form-col>
    </gp-form-row>

    <gp-form-row>
      <gp-form-col cols="12 6">
        <gp-simple-input label="Tipo da pontuação">
          <select name="rewardType" class="form-control" [(ngModel)]="goalLevel.rewardType">
            <option value="Currency">Reais (R$)</option>
            <option value="Points">Pontos</option>
          </select>
        </gp-simple-input>
      </gp-form-col>
      <gp-form-col cols="12 6">
        <label>Valor ganho</label>
        <gp-input-mask required name="value" [onlyDecimal]="true" [integers]="6" [decimais]="2" placeholder="0,00"
          [(ngModel)]="goalLevel.value"></gp-input-mask>
      </gp-form-col>
    </gp-form-row>

    <gp-form-row>
      <gp-form-col cols="12 12 12 12">
        <gp-spinner-button bootstrapClass="primary" icon="plus" pull="right" text="Adicionar" (click)="addGoalLevel()">
        </gp-spinner-button>
      </gp-form-col>
    </gp-form-row>
  </form>
</gp-modal>

<goal-event-editing [campaignId]="campaignId" [goalId]="parametrizationId" [hasStarted]="hasStarted"
  (refreshEvents)="findGoalEvents()" #eventModal>
</goal-event-editing>

<exclusive-level-goal-editing [campaignId]="campaignId" [goalId]="parametrizationId" [hasStarted]="hasStarted"
  (refreshExclusiveLevelGoals)="findExclusiveLevelGoals()" #exclusiveLevelGoalModal>
</exclusive-level-goal-editing>

<interval-goal-editing [campaignId]="campaignId" [goalId]="parametrizationId" [hasStarted]="hasStarted"
  (refreshIntervalsGoals)="findIntervalGoals()" #intervalGoalModal>
</interval-goal-editing>

<campaign-feature-selector [campaignId]="campaignId" (select)="onFeatureSelect($event)" (error)="onError($event)"
  text="Selecione a feature para vincular no recebimento de um NF válida" [allowedFeatureTypes]="['RANKING']"
  #featureSelectorModal>
</campaign-feature-selector>

<goal-periods-parametrization [customGoalPeriods]="customGoalPeriods" #goalPeriodParamsModal
  (goalPeriods)="updateCustomGoalPeriods($event)">
</goal-periods-parametrization>