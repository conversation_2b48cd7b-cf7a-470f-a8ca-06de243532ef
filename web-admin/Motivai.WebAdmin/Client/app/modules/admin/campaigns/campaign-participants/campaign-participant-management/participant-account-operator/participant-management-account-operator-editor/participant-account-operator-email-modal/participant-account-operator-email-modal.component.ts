import { Component, EventEmitter, Output, ViewChild } from '@angular/core';

import { GpAlertComponent } from '../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpModalComponent } from '../../../../../../../../shared/components/gp-modal/gp-modal.component';
import { CampaignParticipantManagementService } from '../../../campaign-participant-management.service';
import { AuthStore } from '../../../../../../../../core/auth/auth.store';
import { CampaignStore } from '../../../../../campaign.store';
import { TimezoneHelper } from '../../../../../../../../shared/helpers/timezone-helper';

@Component({
  selector: 'participant-account-operator-email-modal',
  templateUrl: 'participant-account-operator-email-modal.component.html'
})
export class ParticipantAccountOperatorEmailModalComponent {
  @ViewChild('modal') modal: GpModalComponent;
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @Output('update') updateEmitter: EventEmitter<any> = new EventEmitter();

  accountOperator: any = {};
  newEmail: string = null;
  processing: boolean = false;

  constructor(private _authStore: AuthStore, private _campaignStore: CampaignStore,
    private _participantService: CampaignParticipantManagementService) { }

  openModal(accountOperator: any) {
    if (!accountOperator) {
      return;
    }
    this.accountOperator = accountOperator;
    this.newEmail = '';
    this.modal.show();
  }

  updateEmail() {
    if (!this.newEmail) {
      this.gpAlert.showWarning('Preencha o novo e-mail.');
      return;
    }
    this.processing = true;
    const payload: any = {}

    payload.fieldValue = this.newEmail;
    payload.timezone = TimezoneHelper.getTimezone();

    this._participantService.updateAccountOperatorEmail(this._authStore.loggedUser.id, this._campaignStore.id, this.accountOperator.accountOperatorId, this.accountOperator.accountOperatorLoginId, payload)
      .subscribe(
        result => {
          this.processing = false;
          if (result) {
            this.accountOperator.email = this.newEmail;
            this.accountOperator.login = this.newEmail;
            this.updateEmitter.emit({});
            this.gpAlert.showSuccess('E-mail atualizado com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível atualizar o e-mail, por favor, tente novamente.');
          }
        },
        err => {
          this.processing = false;
          this.gpAlert.handleAndShowError(err);
        }
      );
  }
}
