<gp-card *ngIf="showMenublock" [noPaddingTop]="true" [noPaddingBottom]="true">
	<div class="row">
		<accordion [closeOthers]="true">
			<accordion-group panelClass="b0 mb-sm panel-default no-shadow" [isOpen]="true">
				<div accordion-heading>
					<label><em class="fa fa-navicon"></em>Features de Metas</label>
				</div>

				<div class="row menublock text-center">
					<gp-menublock-item
						size="xs"
						icon="cogs"
						color="text-warning"
						text="Configurações"
						routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'participantes', 'metas', 'configuracoes']">
					</gp-menublock-item>

					<gp-menublock-item
						size="xs"
						icon="cloud-upload"
						color="text-warning"
						text="Importação"
						routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'participantes', 'metas', 'importacao']">
					</gp-menublock-item>

					<gp-menublock-item
						size="xs"
						icon="money"
						color="text-warning"
						text="Resultados"
						routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'participantes', 'metas', 'resultados']">
					</gp-menublock-item>

				</div>
			</accordion-group>
		</accordion>
	</div>
</gp-card>
<router-outlet></router-outlet>
