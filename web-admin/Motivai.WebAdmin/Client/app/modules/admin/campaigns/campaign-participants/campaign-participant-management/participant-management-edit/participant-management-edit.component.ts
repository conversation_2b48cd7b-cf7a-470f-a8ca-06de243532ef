import { Component, OnInit, ViewChild, OnDestroy, EventEmitter, Output } from '@angular/core';
import { NgForm } from '@angular/forms';
import { TabsetComponent } from 'ng2-bootstrap';
import { Subscription } from 'rxjs';

import { ParticipantParentLinkerModalComponent } from '../modals/participant-parent-linker-modal/participant-parent-linker-modal.component';
import { CampaignStore } from '../../../campaign.store';
import { ParticipantManagementAddressComponent } from '../participant-address/participant-management-address.component';
import { Item } from '../../../../../../shared/models/item';
import { CampaignService } from '../../../campaign.service';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignParticipantManagementService } from '../campaign-participant-management.service';
import { EMPTY } from '../../../../../../core/constants/constants-value';
import { RxjsHelpers } from '../../../../../../shared/helpers/rxjs-helpers';
import { AuthStore } from '../../../../../../core/auth/auth.store';
import { ParticipantLinkerDocumentModalComponent } from '../modals/participant-linker-document-modal/participant-linker-document-modal.component';
import { ParticipantManagementAccountOperatorComponent } from '../participant-account-operator/participant-management-account-operator.component';
import { ParticipantService } from '../../../../backoffice/participant.service';
import { CampaignParticipantsGroupListComponent } from '../../../../../../shared/components/business/campaigns/campaign-participants-group-list/campaign-participants-group-list.component';
import { ParticipantMenagementModalComponent } from './modal/partiicpant-management-modal';

@Component({
  selector: 'participant-management-edit',
  templateUrl: './participant-management-edit.component.html'
})
export class ParticipantManagementEditComponent implements OnInit, OnDestroy {
  @Output('error') errorEmitter: EventEmitter<any> = new EventEmitter<any>();
  @ViewChild('alert') alert: GpAlertComponent;
  @ViewChild('tabsParticipantData') tabsParticipantData: TabsetComponent;
  @ViewChild('participantForm') participantForm: NgForm;
  @ViewChild('participantAddress') participantAddress: ParticipantManagementAddressComponent;
  @ViewChild('linkDocumentModal') linkDocumentModal: ParticipantLinkerDocumentModalComponent;
  @ViewChild('parentLinkerModal') parentLinkerModal: ParticipantParentLinkerModalComponent;
  @ViewChild('participantAccountOperator') participantAccountOperator: ParticipantManagementAccountOperatorComponent;
  @ViewChild('participantGroupsList') participantGroupsList: CampaignParticipantsGroupListComponent;
  @ViewChild('participantModal') participantModal: ParticipantMenagementModalComponent;

  today: Date = new Date();

  loading: boolean = false;
  sending: boolean = false;
  loadingDelete: boolean = false;

  participantGroups: Array<Item> = [];
  selectedGroup: Item;

  campaignId: string;
  participant: any;

  participantData: any = {
    contact: {},
    metadata: {}
  };

  firstAccess: boolean = false;

  participantAuthenticationMfaFormats: Array<Item> = [
    Item.of('BOTH', 'Ambos'),
    Item.of('SMS_ONLY', 'Somente SMS'),
    Item.of('EMAIL_ONLY', 'Somente e-mail'),
  ];

  public rankings: any = [];

  private campaign$: Subscription;

  constructor(private campaignStore: CampaignStore, private _campaignService: CampaignService,
    private campaignParticipantService: CampaignParticipantManagementService,
    private _authStore: AuthStore, private _participantService: ParticipantService) { }

  get userId() {
    return this.participant ? this.participant.userId : null;
  }

  get isPF() {
    return this.participantData.type == 'Fisica';
  }

  get isPJ() {
    return this.participantData.type == 'Juridica';
  }

  get accountDocument() {
    return this.participant ? this.participant.document : null;
  }

  get canUpdateRegistrationData() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_PARTICIPANTS_MANAGEMENT_UPDATE_REGISTRATION_DATA;
  }

  get canUnblockParticipant() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_UNBLOCK_PARTICIPANT;
  }

  get canResetFirstAccess() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_PARTICIPANTS_MANAGEMENT_RESET_WALLET_CARDS;
  }

  get canFillDocument() {
    return this.participantData && this.participantData.fillDocument;
  }

  get canOnlyRead() {
    return !this._authStore.role.PERMISSION_CAMPAIGNS_PARTICIPANTS_MANAGEMENT_UPDATE_REGISTRATION_DATA;
  }

  get canEditParentParticipant() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_PARTICIPANTS_MANAGEMENT_PARENT_USER;
  }

  ngOnInit() {
    this.campaign$ = this.campaignStore.asObservable
      .subscribe(
        campaignId => {
          if (campaignId) {
            this.campaignId = campaignId;
            this.loadRankings();
          }
        }
      );
  }

  private loadRankings() {
    this._campaignService.getRankings(this.campaignId)
      .subscribe(rankings => {
        if (rankings && rankings.length) {
          this.rankings = [Item.of(EMPTY, 'Nenhum ranking selecionado')].concat(rankings.map(r => Item.of(r.id, r.name)));
        } else {
          this.rankings = [];
        }
      }, err => this.alert.handleAndShowError(err));
  }

  public editParticipant(participant: any) {
    this.clearEditFormParticipant();
    if (!participant || !participant.userId) {
      return;
    }
    this.participant = participant;
    this.participantAddress.participant = participant;
    this.getParticipantById();
  }

  ngOnDestroy() {
    RxjsHelpers.unsubscribe(this.campaign$);
  }

  openDocumentLinkerModal() {
    this.linkDocumentModal.openModal();
  }

  openParentLinkerModal() {
    this.parentLinkerModal.openModal();
  }

  resetAfterAddressSubmit() {
    this.participantAddress.findParticipantAddresses();
    this.participantAddress.clearEditFormAddress();
    this.tabsParticipantData.tabs[0].active = true;
  }

  getParticipantById() {
    if (this.participant.userId && this.campaignId) {
      this.loading = true;

      this.campaignParticipantService.getParticipantDataById(this.participant.userId, this.campaignId)
        .subscribe(
          participant => {
            this.loading = false;
            if (participant) {
              if (!participant.contact) {
                participant.contact = {};
              }

              if (!participant.blockingDetails) {
                participant.blockingDetails = {};
              }

              this.participantData = participant;
              this.firstAccess = participant.firstAccess;
            } else {
              this.alert.showError('Ocorreu um erro ao buscar os dados do participante, tente novamente');
            }
          },
          err => {
            this.alert.handleAndShowError(err);
            this.loading = false;
          }
        );
    }
  }

  saveParticipantData() {
    this.sending = true;
    this.participantData.Id = this.participant.userId;
    this.campaignParticipantService.updateUserManagemenet(this.participant.userId, this.campaignId, this.participantData)
      .subscribe((participant: any) => {
        this.sending = false;
        if (participant) {
          this.firstAccess = participant.firstAccess;
          this.alert.showSuccess('Participante salvo com sucesso');
        } else {
          this.alert.showError('Ocorreu um erro ao gravar o participante');
        }
      }, err => {
        this.alert.handleAndShowError(err);
        this.sending = false;
      });
  }

  saveParticipantToGroup() {
    if (!this.selectedGroup) {
      this.alert.showWarning('Selecione o grupo para adicionar');
      return;
    }
    this.loading = true;
    this._campaignService
      .saveParticipantToGroup(this.campaignId, this.selectedGroup.id, this.userId)
      .subscribe(
        response => {
          if (response) {
            this.alert.showSuccess('Participante adionado com sucesso ao grupo');
            if (this.participantGroupsList) {
              this.participantGroupsList.loadParticipantsGroupsDetails();
            }
          } else {
            this.alert.showError(
              'Ocorreu um erro ao adicionar o participante ao grupo, tente novamente.'
            );
          }

          this.loading = false;
        },
        err => {
          this.alert.showError(err, true);
          this.loading = false;
        }
      );
  }

  resetParticipantWallet() {
    this.loading = true;
    this.campaignParticipantService.resetParticipantWallet(this.campaignId, this.participant.userId)
      .subscribe(
        response => {
          this.loading = false;
          if (!response) {
            return this.alert.showWarning('Ocorreu um erro ao resetar Wallet do participante.');
          }

          this.alert.showSuccess('Cartões do participante resetada com sucesso.');
        }, err => {
          this.alert.handleAndShowError(err);
          this.loading = false;
        }
      )
  }

  openModal() {
    this.participantModal.showModal(this.participant);
  }

  modalClose() {
    this.getParticipantById();
  }

  clearEditFormParticipant() {
    this.participantData = {
      contact: {},
      metadata: {},
      groupsCodes: []
    };
  }

  onAddedGroups(selectedGroup: any) {
    this.selectedGroup = selectedGroup || {};
  }

  resetForms() {
    if (this.participantAddress)
      this.participantAddress.clearEditFormAddress();
    if (this.participantAccountOperator)
      this.participantAccountOperator.clear();
    this.clearEditFormParticipant();
  }
}
