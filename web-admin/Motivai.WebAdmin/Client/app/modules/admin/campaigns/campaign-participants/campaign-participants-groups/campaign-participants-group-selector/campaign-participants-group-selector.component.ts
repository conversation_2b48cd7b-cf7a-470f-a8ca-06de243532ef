import { Component, Input, ViewChild, forwardRef, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';

import { Item } from '../../../../../../shared/models/item';
import { CampaignStore } from '../../../campaign.store';
import { ParticipantsGroupService } from '../participants-groups.service';
import { EMPTY } from '../../../../../../core/constants/constants-value';
import { NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';
import { SelectItem } from 'ng2-select';
import { Subscription } from 'rxjs';
import { RxjsHelpers } from '../../../../../../shared/helpers/rxjs-helpers';

@Component({
  selector: 'campaign-participants-group-selector',
  template: `
    <gp-form-col [cols]="cols">
      <label for="participantGroup">Grupo Pai</label>
      <gp-select [name]="name" [multiple]="multiple" placeholder="Selecione o grupo de participantes ou o padrão"
        [items]="participantsGroups" [(ngModel)]="value" [required]="required">
      </gp-select>
    </gp-form-col>
  `,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => CampaignParticipantsGroupSelectorComponent),
      multi: true
    }
  ]
})
export class CampaignParticipantsGroupSelectorComponent implements ControlValueAccessor, OnInit, OnDestroy {
  @Output('error') errorEmitter: EventEmitter<any> = new EventEmitter<any>();
  @Output('select') selectEmitter: EventEmitter<any> = new EventEmitter<any>();

  @Input() cols: string = '12 6';
  @Input() name: string;
  @Input('enableEmpty') enableEmpty: boolean = true;
  @Input() required: boolean = false;
  @Input() multiple: boolean = false;
  @Input() campaignDefault: string = 'Padrão da Campanha';

  loading: boolean = false;
  participantsGroups: Array<Item> = [];
  
  private _campaign$: Subscription;
  
  private _value: any;

  onChange: any = () => { };
  onTouched: any = () => { };

  constructor(private _campaignStore: CampaignStore, private _participantsGroupService: ParticipantsGroupService) {}

  get value() {
    return this._value;
  }

  set value(val: any) {
    if (this._value !== val) {
      this._value = val;
      let value = val;
      if (val && val.length && val.map) {
        value = val.map(v => v && v.id ? v.id : v);
      } else if (val instanceof SelectItem) {
        value = val.id;
      }
      this.onChange(value);
      this.onTouched();
      this.emitSelectedGroup(value);
    }
  }

  ngOnInit() {
    this._campaign$ = this._campaignStore.asObservable
      .subscribe(id => this.loadParticipantsGroups());
  }

  ngOnDestroy() {
    RxjsHelpers.unsubscribe(this._campaign$);
  }

  private loadParticipantsGroups() {
    this.loading = true;
    this._participantsGroupService.search(this._campaignStore.id)
      .subscribe(
        groups => {
          this.loading = false;
          if (groups) {
            if (this.enableEmpty) {
              this.participantsGroups = [ Item.of(EMPTY, this.campaignDefault) ].concat(groups.map(g => Item.of(g.id, g.name)));
            } else {
              this.participantsGroups = groups.map(g => Item.of(g.id, g.name));
            }
          }
        },
        err => {
          this.loading = false;
          this.errorEmitter.emit(err);
        }
      );
  }

  private emitSelectedGroup(value: any) {
    let groupName = null;
    if (value) {
      const group = this.participantsGroups.find(g => g.id === value);
      if (group) {
        groupName = group.text;
      }
    }
    this.selectEmitter.emit({
      id: value,
      name: groupName
    });
  }

  registerOnChange(fn) {
    this.onChange = fn;
  }

  registerOnTouched(fn) {
    this.onTouched = fn;
  }

  writeValue(value) {
    if (value !== this._value) {
      this._value = value;
    }
  }
}