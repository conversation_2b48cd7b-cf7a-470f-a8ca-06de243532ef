<gp-card [first]="true" title="Filtrar Campos Dinâmicos">
    <div class="row">
        <gp-form-col cols="12 5 5 5">
            <label>Página</label>
            <gp-select name="page" [items]="pages" [(ngModel)]="platformPage" [placeholder]="page"></gp-select>
        </gp-form-col>
        <div style="padding-top: 26px;">
            <gp-form-col cols="12 2 2 2">
                <gp-spinner-button text="Pesquisar" [search]="true" (click)="loadMetadataFields()" [loading]="loading"
                    loadingText="Pesquisando"></gp-spinner-button>
            </gp-form-col>
        </div>
    </div>
</gp-card>

<gp-card title="Campos Dinâmicos Cadastradas">
    <div class="row">
        <div class="col-xs-12">
            <gp-grid name="metadataFieldsGrid" [loading]="loading" [rows]="metadatafields"
                [columns]="['Código do Campo', 'Descrição do Campo']" [fields]="['property', 'description']"
                [showActive]="true" [showPagination]="false" [showEdit]="true" [showDelete]="false"
                (onEdit)="editPage($event)">
            </gp-grid>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12" style="padding-top:5px;">
            <div class="div-alert">
                <gp-alert #alert [overlay]="true"></gp-alert>
            </div>
        </div>
    </div>
</gp-card>