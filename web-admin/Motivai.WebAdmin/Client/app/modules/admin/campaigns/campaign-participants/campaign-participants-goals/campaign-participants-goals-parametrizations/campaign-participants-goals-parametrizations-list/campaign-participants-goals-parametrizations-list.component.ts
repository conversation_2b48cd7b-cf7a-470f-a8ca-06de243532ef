import {
  Component,
  OnInit,
  ViewChild,
  EventEmitter,
  Output
} from '@angular/core';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignStore } from '../../../../campaign.store';
import { CampaignParticipantsGoalsService } from '../../campaign-participants-goals.service';

@Component({
  selector: 'campaign-participants-goals-parametrizations-list',
  templateUrl:
    './campaign-participants-goals-parametrizations-list.component.html'
})
export class CampaignParticipantsGoalsParametrizationsListComponent
  implements OnInit {
  @ViewChild('alert') alert: GpAlertComponent;
  @Output('onEdit') onEdit: EventEmitter<any> = new EventEmitter<any>();

  campaignId: string = '';
  loading: boolean = false;

  params: any = {};
  campaignParticipantsGoalsParametrizations: any[] = [];

  constructor(
    private campaignStore: CampaignStore,
    private campaignParticipantsGoalsService: CampaignParticipantsGoalsService
  ) {}

  ngOnInit() {
    this.campaignId = this.campaignStore.id;
    this.findCampaignParticipantsGoalsParametrizations();
  }

  findCampaignParticipantsGoalsParametrizations() {
    this.loading = true;
    this.campaignParticipantsGoalsService
      .findCampaignParticipantsGoalsParametrizations(
        this.campaignId,
        this.params.code
      )
      .subscribe(
        response => {
          if (response && response.length > 0) {
            response.forEach(goal => {
              goal.formattedStarted = goal.started ? 'Sim' : 'Não';
            });
          }

          this.campaignParticipantsGoalsParametrizations = response || [];
          this.handleParametrizations();
          this.loading = false;
        },
        err => {
          this.loading = false;
          this.alert.showError(err);
        }
      );
  }

  private handleParametrizations(): void {
    if (!this.campaignParticipantsGoalsParametrizations || this.campaignParticipantsGoalsParametrizations.length < 1)
      return;
    this.campaignParticipantsGoalsParametrizations.forEach(p => {
      p.description = p.descriptionForSite ? p.descriptionForSite : p.description;
    }); 
  }

  findCampaignParticipantsGoalParametrizations(event) {
    this.onEdit.emit(event);
  }

  inactiveCampaignParticipantsGoalParametrizations(event: any) {
    this.loading = true;
    this.campaignParticipantsGoalsService
      .inactiveCampaignParticipantsGoalParametrizations(
        this.campaignId,
        event.id
      )
      .subscribe(
        response => {
          this.loading = false;
          if (response) {
            this.alert.showSuccess('A meta foi inativada com sucesso.');
            return this.findCampaignParticipantsGoalsParametrizations();
          }

          return this.alert.showError('Erro ao inativar meta');
        },
        err => {
          this.loading = false;
          this.alert.showError(err);
        }
      );
  }
}
