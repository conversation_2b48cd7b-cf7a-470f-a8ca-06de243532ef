<gp-alert #alert [overlay]="true"></gp-alert>
<spinner [overlay]="true" [show]="loading"></spinner>

<div *ngIf="!batchId">
	<gp-card title="Importar Ranking" [first]="true">
		<gp-form-row>
			<gp-form-col cols="12 4 4">
				<gp-simple-input label="Selecione o ranking para importação">
					<gp-select name="campaignRankingId" [items]="campaignRankings"
						[(ngModel)]="batchRanking.campaignRankingId" #rankingSelect ></gp-select>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 9">
				<label>Selecione o arquivo</label>
				<gp-fileupload name="batchFile" (oncomplete)="onFileUploaded($event)" #gpFile></gp-fileupload>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card *ngIf="hasSelectedFile">
		<gp-form-row>
			<gp-form-col cols="12 12 12" [inputGroup]="false">
				<gp-spinner-button type="button" [pink]="true" pull="right" text="Importar Ranking" icon="upload"
					(click)="uploadBatchFile()" [disabled]="loading">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card title="Instruções de Uso" [last]="true">
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<p>1. O formato do arquivo deve ser <u>[.csv]</u>, com as colunas separadas por [;].</p>
				<p>2. O cabeçalho deve ser formado por: posicao, nome, documento, producao e regional.</p>
			</gp-form-col>
			<gp-form-col cols="12 4 4">
				<ul>
					<li>
						<strong>posicao: </strong>Posição do participante no ranking
					</li>
					<li>
						<strong>nome: </strong>Nome do participante
					</li>
					<li>
						<strong>documento: </strong>Documento do participante
						<ul>
							<li>Cabeçalhos aceitos: documento, cnpj, cpf</li>
						</ul>
					</li>
					<li>
						<strong>producao: </strong>Valor de produção consolidado do documento
						<ul>
							<li>Casas decimais: o valor precisa ser formatado como texto e com ponto <u>[.]</u> no lugar da virgula <u>[,]</u>.</li>
							<li>Exemplo: um crédito de <u>1.152,50</u> pontos precisar estar salvo como <u>1152.50</u>.</li>
						</ul>
					</li>
					<li>
						<strong>regional: </strong>Regional vinculada ao documento
					</li>
				</ul>
			</gp-form-col>
			<gp-form-col cols="12 8 8">
				<img src="/assets/img/imports/modelo_planilha_ranking_dinamico.png" style="width: 500px;" /> <br /><br />
				<a href="/assets/files/modelo_planilha_ranking_dinamico.csv">Clique aqui para baixar um modelo de planilha</a>
			</gp-form-col>
			<gp-form-col cols="12 12 12">
				<p>3. Os campos obrigatórios são: posicao, nome, documento, producao e regional.</p>
				<p>4. O documento deve conter: 11 digitos para CPF; 14 digitos para CNPJ, sem formatação. <strong>Se atente a formatar a coluna de documento como <u>texto</u>, para manter os zeros a esquerda.</strong></p>
			</gp-form-col>
		</gp-form-row>
	</gp-card>
</div>

<div *ngIf="batchId">
	<gp-card title="Detalhes da Importação" [first]="true">
		<gp-form-row>
			<gp-form-col cols="12 12 12 12">
				<gp-form-row>
					<gp-form-col cols="12 6 6">
						<gp-simple-input label="Código do Lote">
							<input type="text" name="batchNumber" class="form-control" disabled
								[(ngModel)]="batchRanking.batchNumber" />
						</gp-simple-input>
					</gp-form-col>
					<gp-form-col cols="12 6 6">
						<gp-simple-input label="Status">
							<input type="text" name="status" class="form-control" disabled
								[(ngModel)]="batchRanking.formattedStatus" />
						</gp-simple-input>
					</gp-form-col>
				</gp-form-row>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row *ngIf="isError">
			<gp-form-col cols="12 12 12 12">
				<gp-form-row>
					<gp-form-col cols="12 12 12">
						<gp-simple-input label="Erro">
							<input type="text" name="error" class="form-control" disabled
								[(ngModel)]="batchRanking.errorMessage" />
						</gp-simple-input>
					</gp-form-col>
				</gp-form-row>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<div *ngIf="!isError">
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-card>
					<gp-form-row>
						<gp-form-col cols="12 12 12">
							<h3>Sucessos:
								<span class="text-success">{{ successCount }}</span>
							</h3>
						</gp-form-col>
					</gp-form-row>
				</gp-card>
			</gp-form-col>

			<gp-form-col cols="12 6 6">
				<gp-card>
					<gp-form-row>
						<gp-form-col cols="12 4">
							<h3>Erros:
								<span class="text-danger">{{ errorsCount }}</span>
							</h3>
						</gp-form-col>
						<gp-form-col cols="12 8">
							<gp-spinner-button  *ngIf="canExportBatchErrors" type="button" pull="right" marginTop="24px" icon="download"
								text="Exportar Erros do lote" loadingText="Processando..." bootstrapClass="success" [disabled]="loading || !batchId"
								(click)="exportBatchErrors()" ></gp-spinner-button>
						</gp-form-col>
					</gp-form-row>
				</gp-card>
			</gp-form-col>
		</gp-form-row>

		<gp-card title="Registros com Sucesso">
			<gp-form-col cols="12">
				<gp-grid name="grid" [rows]="participantsRakings"
					[columns]="['Posição', 'Documento', 'Nome', 'Produção', 'Data de Criação']"
					[fields]="['position', 'userDocument', 'name', 'formattedResultValue', 'formattedCreateDate']"
					[showActive]="false" [showPagination]="false" [showDelete]="false" [showEdit]="false">
				</gp-grid>
			</gp-form-col>
		</gp-card>
	</div>
</div>
