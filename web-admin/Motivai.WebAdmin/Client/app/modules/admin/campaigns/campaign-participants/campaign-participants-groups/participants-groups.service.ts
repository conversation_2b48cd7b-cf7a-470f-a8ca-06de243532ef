import { Observable } from 'rxjs/Observable';
import { ParticipantsGroup } from './participantsGroup';
import { ApiService } from '../../../../../core/api/api.service';
import { Injectable } from '@angular/core';

@Injectable()
export class ParticipantsGroupService {
    constructor(private _api: ApiService) { }

    public search(campaignId: string, name?: string): Observable<ParticipantsGroup[]> {
        let params: any = {};
        if (name) params.name = name;
        return this._api.get(`/api/campaigns/${campaignId}/participantsgroups`, params);
    }

    public findParticipantsGroupsDetails(campaignId: string, userId: string): Observable<Array<any>> {
        return this._api.get(`/api/campaigns/${campaignId}/participantsgroups/${userId}/participantgroups`);
    }

    public save(campaignId: string, group: ParticipantsGroup): Observable<ParticipantsGroup> {
        if (group.id)
            return this._api.put(`/api/campaigns/${campaignId}/participantsgroups/${group.id}`, group);
        else
            return this._api.post(`/api/campaigns/${campaignId}/participantsgroups`, group);
    }

    public getById(campaignId: string, groupId: string): Observable<ParticipantsGroup> {
        return this._api.get(`/api/campaigns/${campaignId}/participantsgroups/${groupId}`);
    }

    public searchParticipants(campaignId: string, nameOrDocument: string): Observable<any[]> {
        let params: any = {};
        if (nameOrDocument) params.term = nameOrDocument;

        return this._api.get(`/api/campaigns/${campaignId}/participants`, params);
    }

    public addParticipant(campaignId: string, groupId: string, userId: string): Observable<boolean> {
        return this._api.put(`/api/campaigns/${campaignId}/participantsgroups/${groupId}/participants/${userId}`);
    }

    public getParticipants(campaignId: string, groupId: string, userId?: string, skip?: number, limit?: number): Observable<any[]> {
        let params: any = {};
        if (userId) params.userId = userId;
        params.skip = skip;
        if (limit) params.limit = limit;
        return this._api.get(`/api/campaigns/${campaignId}/participantsgroups/${groupId}/participants`, params);
    }

    public removeParticipant(campaignId: string, groupId: string, participantId: string): Observable<boolean> {
        return this._api.delete(`/api/campaigns/${campaignId}/participantsgroups/${groupId}/participants/${participantId}`);
    }

    public addProduct(campaignId: string, groupId: string, productId: string): Observable<boolean> {
        return this._api.put(`/api/campaigns/${campaignId}/participantsgroups/${groupId}/products/${productId}`);
    }

    public getProducts(campaignId: string, groupId: string): Observable<any[]> {
        return this._api.get(`/api/campaigns/${campaignId}/participantsgroups/${groupId}/products`);
    }

    public removeProducts(campaignId: string, groupId: string, productId: string): Observable<boolean> {
        return this._api.delete(`/api/campaigns/${campaignId}/participantsgroups/${groupId}/products/${productId}`);
    }
}
