import { CampaignParticipantRankingParametrizationListComponent } from './campaign-participant-ranking-parametrization-list/campaign-participant-ranking-parametrization-list.component';
import { Component, OnInit, ViewChild } from '@angular/core';
import { CampaignParticipantRankingParametrizationEditComponent } from './campaign-participant-ranking-parametrization-edit/campaign-participant-ranking-parametrization-edit.component';
import { TabsetComponent } from 'ng2-bootstrap';

@Component({
    selector: 'campaign-participant-ranking-parametrization-view',
    templateUrl: './campaign-participant-ranking-parametrization-view.component.html'
})
export class CampaignParticipantRankingParametrizationViewComponent implements OnInit {
    @ViewChild('list') list: CampaignParticipantRankingParametrizationListComponent;
    @ViewChild('edit') edit: CampaignParticipantRankingParametrizationEditComponent;
    @ViewChild('tabs') tabs: TabsetComponent;

    constructor() { }

    ngOnInit(): void { }

    showRankingParametrizationDetails(event) {
        this.edit.showRanking(event);
        this.tabs.tabs[1].active = true;
    }

    reloadRankingParametrizations() {
        this.edit.clear();
        this.list.findRankingParametrizations();
    }


}
