import { Component, OnInit, ViewChild, EventEmitter, Output } from '@angular/core';
import { FormatHelper } from '../../../../../../../shared/formatters/format-helper';
import { Item } from '../../../../../../../shared/models/item';
import { CampaignStore } from '../../../../campaign.store';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignParticipantsGoalsService } from '../../campaign-participants-goals.service';
import { isNullOrEmpty } from '../../../../../../../shared/helpers/comparators';

@Component({
  selector: 'campaign-participants-goals-import-list',
  templateUrl: './campaign-participants-goals-import-list.component.html'
})
export class CampaignParticipantsGoalsImportListComponent implements OnInit {
  @ViewChild('alert') alert: GpAlertComponent;
  @Output('onEdit') onEdit: EventEmitter<any> = new EventEmitter<any>();

  loading: boolean = false;


  batchStatuses: Array<Item> = [
    Item.of(' ', 'Todos'),
    Item.of('CREATED', 'Criado'),
    Item.of('PROCESSING', 'Em Processamento'),
    Item.of('COMPLETED', 'Processado'),
    Item.of('REJECTED', 'Reprovado'),
    Item.of('ERROR', 'Erro')
  ];

  campaignGoals : Array<Item>;

  batchesCampaignParticipantsGoals: any[] = [];
  params: any = {};

  campaignId: string = '';

  skip: number = 0;
  limit: number = 20;


  constructor(private campaignStore: CampaignStore, private campaignParticipantsGoalsService: CampaignParticipantsGoalsService) { }

  ngOnInit() {
    this.findCampaignParticipantsGoalsBatches();
    this.findCampaignParticipantsGoalsParametrizationsActive();
  }

  findCampaignParticipantsGoalsBatches() {
    this.loading = true;
    this.campaignParticipantsGoalsService.findCampaignParticipantsGoalsBatches(this.campaignStore.id, this.params, this.skip, this.limit).subscribe(
      response => {
        if (response) {
            response.forEach(x => {
              if (x.status)
                  x.formattedStatus = this.handleStatus(x.status);
              if (x.createDate)
                  x.formattedCreateDate = FormatHelper.formatDate(x.createDate);
              x.formattedGoalName = this.getFormattedGoalNameById(x.campaignGoalId);
            });
        }

        this.batchesCampaignParticipantsGoals = response;
        this.loading = false;
      }, err => {
        this.loading = false;
        this.alert.showError(err);
      }
    );
  }

  findCampaignParticipantsGoalsParametrizationsActive() {
    this.campaignParticipantsGoalsService.findCampaignParticipantsGoalsParametrizationsActiveAll(this.campaignStore.id).subscribe(
      response => {
        if (response) {
          this.campaignGoals = response.map(goal => new Item(goal.id, `${goal.description || goal.descriptionForSite} (${goal.code})`));
        }
      },
      err => {
        this.loading = false;
        this.alert.showError(err);
      }
    );
  }


  showBatchCampaignParticipantsGoalDetails(event: any) {
    this.onEdit.emit(event);
  }

  handleStatus(status: string): string {
    switch (status) {
      case 'COMPLETED':
        return 'Processado';
      case 'CREATED':
        return 'Criado';
      case 'PROCESSING':
        return 'Processando';
      case 'REJECTED':
        return 'Reprovado';
      case 'ERROR':
        return 'Erro durante o processamento';
      default:
        return 'Status inválido';
    }
  }

  changePage(event) {
      if (event) {
        this.skip = event.skip;
        this.limit = event.limit;
        this.findCampaignParticipantsGoalsBatches();
    }
  }

  getFormattedGoalNameById(id: string) : string {
    if(isNullOrEmpty(id)){
      return ""
    }

    for (var goal of this.campaignGoals) {
      if(goal.id == id){
        return goal.text;
      }
    }

    return "";
  }


}
