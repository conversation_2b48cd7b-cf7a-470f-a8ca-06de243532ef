import { CampaignStore } from '../../campaign.store';
import {
  PERMISSION_CAMPAIGNS_IMPORT_PARTICIPANTS_IMPORT,
  PERMISSION_CAMPAIGNS_IMPORT_PARTICIPANTS_VIEW,
  PERMISSION_CAMPAIGNS_IMPORT_PARTICIPANTS_APPROVE
} from '../../../../../core/auth/access-points';
import { AuthStore } from '../../../../../core/auth/auth.store';
import { TabsetComponent } from 'ng2-bootstrap/tabs';
import { GpCampaignImportParticipantsEditComponent } from './campaign-import-participants-edit/campaign-import-participants-edit.component';
import { GpCampaignImportBatchlistComponent } from '../../common/campaign-batches/campaign-import-batchlist.component';
import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { ActivatedRoute } from '@angular/router';
import { CampaignService } from '../../campaign.service';
import { Component, OnInit, ViewChild } from '@angular/core';
import { Subscription } from 'rxjs/Subscription';
import { RxjsHelpers } from '../../../../../shared/helpers/rxjs-helpers';

@Component({
  selector: 'campaign-import-participants',
  templateUrl: 'campaign-import-participants.component.html'
})
export class GpCampaignImportParticipantsComponent implements OnInit {

  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @ViewChild('batchList') batchList: GpCampaignImportBatchlistComponent;
  @ViewChild('editComponent') editComponent: GpCampaignImportParticipantsEditComponent;
  @ViewChild('tabs') tabs: TabsetComponent;

  campaignId: string;
  loading: boolean = false;
  batchId: string;

  private _campaign$: Subscription;

  constructor(private _campaignService: CampaignService, private route: ActivatedRoute, private _as: AuthStore, private cs: CampaignStore) { }

  ngOnInit() {
		this._campaign$ = this.cs.asObservable
        .subscribe(id => this.campaignId = id);

    }

	ngOnDestroy() {
		RxjsHelpers.unsubscribe(this._campaign$);
	}

  handleError(err) {
    let errorMessage = err
      ? err.message ? err.message : err.toString()
      : 'Ocorreu um erro ao efetuar a operação.';
    this.gpAlert.showError(errorMessage);
  }

  refreshGrid() {
    this.batchList.getBatches();
    this.editComponent.reset();
    this.batchId = '';
  }

  editBatchImport($event) {
    if ($event) {
      this.batchId = $event.id;
      this.tabs.tabs[1].active = true;
    }
  }

  canImportOrViewOrApprove() {
    return (
      this._as.hasPermissionTo(
        PERMISSION_CAMPAIGNS_IMPORT_PARTICIPANTS_IMPORT
      ) ||
      this._as.hasPermissionTo(PERMISSION_CAMPAIGNS_IMPORT_PARTICIPANTS_VIEW) ||
      this._as.hasPermissionTo(PERMISSION_CAMPAIGNS_IMPORT_PARTICIPANTS_APPROVE)
    );
  }
}
