<gp-card [first]="true">
    <gp-form-row>
        <gp-form-col cols="12">
            <gp-spinner-button type="button" icon="plus" bootstrapClass="primary" text="Novo Operador" loadingText="Aguarde"
                pull="right" [loading]="loading" [disabled]="loading" (click)="newOperator()">
            </gp-spinner-button>

            <gp-spinner-button type="button" icon="search" bootstrapClass="primary" text="Atualizar Lista" loadingText="Aguarde"
                pull="right" marginRight="1em" [loading]="loading" [disabled]="loading" (click)="findOperators()">
            </gp-spinner-button>
        </gp-form-col>
    </gp-form-row>
	
    <gp-form-row>
        <gp-form-col cols="12">
            <gp-grid name="representativesGrid" [rows]="operators" [loading]="loading"
				[columns]="['Nome', 'CPF', 'Email', 'Login', 'Perfil', 'Bloqueado']"
                [fields]="['name', 'document', 'email', 'login', 'roleDescription', 'blockedStatusDescription']"
				[showActive]="false" [showPagination]="false"
                [showEdit]="true" [showDelete]="false" (onEdit)="showDetails($event)" emptyMessage="Nenhum Operador encontrado.">
            </gp-grid>
        </gp-form-col>
    </gp-form-row>
</gp-card>
<gp-alert [overlay]="true" #alert></gp-alert>
