import { Component, OnInit, ViewChild } from '@angular/core';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpFileUploadComponent } from '../../../../../../../shared/components/gp-fileupload/gp-fileupload.component';
import { CampaignStore } from '../../../../campaign.store';
import { CampaignParticipantRankingService } from '../../campaign-participant-ranking.service';
import { Item } from '../../../../../../../shared/models/item';
import { FormatHelper } from '../../../../../../../shared/formatters/format-helper';
import { AmountPipe } from '../../../../../../../shared/pipes/amount.pipe';
import { GpSelectComponent } from '../../../../../../../shared/components/gp-select/gp-select.component';

@Component({
  selector: 'campaign-participant-ranking-importing-edit',
  templateUrl: './campaign-participant-ranking-importing-edit.component.html'
})
export class CampaignParticipantRankingImportingEditComponent implements OnInit {
  @ViewChild('alert') alert: GpAlertComponent;
  @ViewChild('gpFile') gpFile: GpFileUploadComponent;
  @ViewChild('rankingSelect') rankingSelect: GpSelectComponent;

  private amountPipe = new AmountPipe();

  campaignRankings: Array<Item> = [];
  participantsRakings: any[] = [];
  batchRanking: any = {};

  campaignId: string = '';
  batchId: string = '';

  successCount: number = 0;
  errorsCount: number = 0;

  loading: boolean = false;



  constructor(private campaignStore: CampaignStore, private rankingParticipantService: CampaignParticipantRankingService) { }

  ngOnInit() {
    this.campaignId = this.campaignStore.id;
    this.findCampaignRankings();
  }

  get canExportBatchErrors() {
    return this.errorsCount > 0;
  }


  get hasSelectedFile() {
    return this.gpFile && this.gpFile.hasSelectedFile();
  }

  uploadBatchFile() {
    if (!this.batchRanking.campaignRankingId) {
      return this.alert.showWarning('Selecione o ranking.');
    }

    if (!this.gpFile || !this.gpFile.hasSelectedFile()) {
      return this.alert.showWarning('Selecione um arquivo para upload.');
    }

    this.loading = true;
    this.gpFile.path = `api/campaigns/${this.campaignId}/rankings/batches/upload`;
    this.batchRanking.fileName = this.gpFile.selectedFilename;
    this.gpFile.uploadFile();
  }

  onFileUploaded(event) {
    if (event.success && event.response) {
      const response = JSON.parse(event.response);
      this.batchRanking.storageFileName = response.return.filename;
      this.batchRanking.url = response.return.url;
      this.saveBatchRanking();
      this.loading = false;
    } else {
      this.loading = false;
      this.alert.showError('Ocorreu um erro ao realizar o upload do arquivo');
    }
  }

  private saveBatchRanking() {
    if (!this.batchRanking.campaignRankingId) {
      return this.alert.showInfo('Selecione o ranking da importação');
    }

    const rankingSelected = this.rankingSelect.getItemById(this.batchRanking.campaignRankingId);
    if (!rankingSelected) {
      return this.alert.showError('Ranking selecionado inválido');
    }

    this.batchRanking.rankingName = rankingSelected.text;
    this.rankingParticipantService.saveCampaignParticipantsRankingBatch(this.campaignId, this.batchRanking).subscribe(
      imported => {
        if (imported) {
          this.batchId = imported;
          this.findBatchRankingById();
          this.alert.showSuccess('Arquivo importado com sucesso.');
        }
      },
      err => {
        this.loading = false;
        this.alert.showError(err);
      }
    );
  }

  findBatchRankingById() {
    this.loading = true;
    this.rankingParticipantService.findCampaignParticipantsRankingBatchById(this.campaignId, this.batchId).subscribe(
      batchRanking => {
        if (batchRanking) {
          batchRanking.formattedStatus = this.handleStatus(batchRanking.status);
        }
        this.batchRanking = batchRanking;
        this.findCampaignParticipantsRankingByBatchId();
        this.getCounters();
        this.loading = false;
      },
      err => {
        this.loading = false;
        this.alert.showError(err);
      }
    );
  }

  findCampaignRankings() {
    this.rankingParticipantService.findCampaingParticipantRankingParametrizations(this.campaignStore.id, null, true)
      .subscribe(
        response => {
          if (response) {
            this.campaignRankings = response.map(ranking => new Item(ranking.id, ranking.name));
          }
        },
        err => {
          this.loading = false;
          this.alert.showError(err);
        }
      );
  }

  findCampaignParticipantsRankingByBatchId() {
    this.rankingParticipantService.findCampaignParticipantsRankingByBatchId(this.campaignStore.id, this.batchId).subscribe(
      response => {
        if (response) {
          response.forEach(participantRanking => {
            participantRanking.formattedResultValue = this.amountPipe.transform(participantRanking.resultValue);
            participantRanking.formattedCreateDate = FormatHelper.formatDateWithTimezone(participantRanking.createDate);
          });
        }
        this.participantsRakings = response;
      },
      err => {
        this.alert.showError(err);
      }
    );

  }

  exportBatchErrors() {
    this.rankingParticipantService.exportBatchErrors(this.campaignStore.id, this.batchId).subscribe(
      response => {
        window.open(response, '_blank');
      }, err => {
        this.alert.showError(err);
      }
    );
  }


  getCounters() {
    this.successCount = this.batchRanking.totalLinesProcessingSuccess;
    this.errorsCount = this.batchRanking.totalLinesProcessingErrors;
  }

  handleStatus(status: string): string {
    switch (status) {
      case 'CREATED':
        return 'Criado';
      case 'PROCESSING':
        return 'Processando';
      case 'PROCESSING_APPROVAL':
        return 'Processando Aprovação';
      case 'COMPLETED':
        return 'Finalizado';
      case 'REJECTED':
        return 'Rejeitado';
      case 'ERROR':
        return 'Erro durante o processamento';
      default:
        return 'Status inválido';
    }
  }

  get isError() {
    return this.batchRanking.errorOccurred;
  }

  clear() {
    this.batchId = '';
    this.batchRanking = {};
    this.participantsRakings = [];
  }

}
