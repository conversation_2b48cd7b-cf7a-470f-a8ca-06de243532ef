import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignImportService } from '../../../campaign-import.service';
import { AuthStore } from '../../../../../../core/auth/auth.store';
import { Component, Input, ViewChild } from '@angular/core';

import { BatchImport } from '../../../campaign-import';
import { CampaignStore } from '../../../campaign.store';

@Component({
    selector: 'campaign-import-participants-edit',
    templateUrl: 'campaign-import-participants-edit.component.html'
})

export class GpCampaignImportParticipantsEditComponent {
    @ViewChild('alert') alert: GpAlertComponent;

    // Page configuration
    parameters: any = {};
    batchImport: BatchImport = new BatchImport();
    loading: boolean = false;
    loadingApproval: boolean = false;
    loadingRefuse: boolean = false;
    loadingExport: boolean = false;

    // Grid configuration
    loadingGrid: boolean = false;
    imports: any[];
    skip: number = 0;
    limit: number = 100;

    private _campaignId: string;
    @Input() set campaignId(v: string) {
        if (v) {
            this._campaignId = v;
        }
    }

    _batchId: string;
    @Input() set batchImportId(v: string) {
        this._batchId = v;
        this.getBatchImport();
        this.loadTemporaryParticipants();
    }

    // Upload file configuration
    private countItemsQueue: number = 0;
    private countItemsProcessed: number = 0;
    get uploadPath(): string {
        return `api/campaigns/${this._campaignId}/batchimport/participants/upload`;
    }

    constructor(private _campaignImportService: CampaignImportService, private _authStore: AuthStore, public _campaignStore: CampaignStore) { }

    // Define rules for approval link visibility
    get showApprovalLink(): boolean {
      return this.canApprove && this.batchImport.status === 'Aguardando Aprovação';
    }

    // Define rules for export users link visibility
    get showExportUsers(): boolean {
        return this.canApprove && this.batchImport.status === 'Finalizado';
    }

    get isRefused() {
        return this.batchImport.status === 'Reprovado';
    }

    get isImporting() {
        return this.batchImport.status === 'Processando';
    }

    // Define fules for Processing text visibility
    get showProcessing(): boolean {
        if (this.batchImport.status === 'Processando') return true;
        if (this.batchImport.status === 'Processando Aprovação') return true;

        return false;
    }

    get showRefuseButton() {
      return this.showApprovalLink && this.canApprove;
    }

    // Define rules for delete button on grid
    get enableDeleteButton() {
        return this.showApprovalLink && this.canRemoveTemporary;
    }

    get canApprove() {
        return this._authStore.role.PERMISSION_CAMPAIGNS_IMPORT_PARTICIPANTS_APPROVE;
    }

    get showTemporaryParticipants() {
      return this.canViewTemporary || this.canRemoveTemporary;
    }

    get canImport() {
        return this._authStore.role.PERMISSION_CAMPAIGNS_IMPORT_PARTICIPANTS_IMPORT && !this._batchId;
    }

    get canView() {
        return this._authStore.role.PERMISSION_CAMPAIGNS_IMPORT_PARTICIPANTS_VIEW && this._batchId;
    }

    get canViewTemporary() {
        return this._authStore.role.PERMISSION_CAMPAIGNS_IMPORT_PARTICIPANTS_VIEWTEMPORARY;
    }

    get canRemoveTemporary() {
        return this._authStore.role.PERMISSION_CAMPAIGNS_IMPORT_PARTICIPANTS_REMOVETEMPORARY;
    }

    // Reset to default values
    reset() {
        this.imports = [];
        this.skip = 0;
        this.limit = 10;
        this.batchImport = new BatchImport();
    }

    // On upload complete
    onItemComplete($event: any) {
        if ($event) {
            this.countItemsProcessed += Number(1);
            if (this.countItemsProcessed === this.countItemsQueue) {
                this.alert.showSuccess('Arquivos enviados para a fila de processamento.');
            }
        }
    }

    // Get batchimport detail information
    getBatchImport() {
        if (this._batchId) {
            this.loading = true;
            this._campaignImportService.getBatchById(this._campaignId, this._batchId)
                .subscribe(batch => {
                    this.batchImport = batch;
                    this.batchImport.status = this.setStatus(batch.status);
                    this.loading = false;
                }, () => {
                    this.loading = false;
                });
        }
    }

    // Load temporary participants grid
    loadTemporaryParticipants() {
        if (this._batchId) {
            this.loadingGrid = true;
            this.imports = [];
            this._campaignImportService.getTemporaryParticipantsByBatchId(this._campaignId, this._batchId, undefined, true, this.skip, this.limit)
                .subscribe(temps => {
                    if (temps)
                        this.imports = temps;

                    this.loadingGrid = false;
                }, () => {
                    this.loadingGrid = false;
                });
        }
    }

    // Grid pagination
    onPageChanged($event) {
        if ($event) {
            this.skip = $event.skip;
            this.limit = $event.limit;
            this.loadTemporaryParticipants();
        }
    }

    // Helper to translate batch status
    setStatus(status: string): string {
        switch (status) {
            case 'Waiting':
                return 'Aguardando';
            case 'Running':
                return 'Processando';
            case 'Waiting_Approval':
                return 'Aguardando Aprovação';
            case 'Running_Approval':
                return 'Processando Aprovação';
            case 'Completed':
                return 'Finalizado';
            case 'Refused':
                return 'Reprovado';
            default:
                return 'Status indefinido';
        }
    }

    // Approve batch import
    approval() {
        if (this._batchId && !this.loadingApproval) {
            this.loadingApproval = true;
            this._campaignImportService.approveTemporaryParticipants(this._campaignId, this._batchId)
                .subscribe(() => {
                    this.getBatchImport();
                    this.loadTemporaryParticipants();

                    this.loadingApproval = false;
                }, () => {
                    this.loadingApproval = false;
                });
        }
    }

    // Export success csv file
    exportSuccess() {
        window.open(this.batchImport.successFileUrl, '_blank');
        // axios
        //     .get(`/api/campaigns/${this._campaignId}/batchimport/${this._batchId}/temporaryparticipants/csv?success=true&processed=true`, {
        //         responseType: 'arraybuffer',
        //         headers: {
        //             access_token: this._authStore.getToken()
        //         }
        //     })
        //     .then(response => {
        //         if (response.data) {
        //             const blob = new Blob([response.data], { type: 'text/csv' });
        //             const url = window.URL.createObjectURL(blob);
        //             window.location.href = url;
        //         }
        //     });
    }

    // Export errors csv file
    exportErrors() {
        window.open(this.batchImport.errorFileUrl, '_blank');
        // axios
        // .get(`/api/campaigns/${this._campaignId}/batchimport/${this._batchId}/temporaryparticipants/csv?success=false`, {
        //     responseType: 'arraybuffer',
        //     headers: {
        //         access_token: this._authStore.getToken()
        //     }
        // })
        // .then(response => {
        //     if (response.data) {
        //         const blob = new Blob([response.data], { type: 'text/csv' });
        //         const url = window.URL.createObjectURL(blob);
        //         window.location.href = url;
        //     }
        // });
    }

    // Delete temporary participant data
    deleteTemporaryParticipant($event) {
        this.loadingGrid = true;
        this._campaignImportService.removeTemporaryParticipant(this._campaignId, this._batchId, $event.id)
            .subscribe(() => {
                this.loadTemporaryParticipants();
            }, err => {
                console.log(err);
            });
    }

    public refuseBatch() {
      if (this._batchId) {
      }

      this.alert.confirm('Atenção: Ao prosseguir com esta ação, o lote será reprovado e os pontos não poderão ser creditados aos participantes. Deseja realmente REPROVAR o lote?')
        .then(result => {
          if (result && result.value) {
            this.loadingRefuse = true;
            this._campaignImportService.refuseBatch(this._campaignId, this._batchId)
                .subscribe(
                    response => {
                        if (response) {
                            this.alert.showSuccess('Lote reprovado com sucesso');
                            this.getBatchImport();
                        } else {
                            this.alert.showError('Ocorreu um erro ao reprovar o lote, tente novamente!');
                        }

                        this.loadingRefuse = false;
                    },
                    err => {
                        this.alert.showError(err, true);
                        this.loadingRefuse = false;
                    }
                );
          }
        });
    }
}
