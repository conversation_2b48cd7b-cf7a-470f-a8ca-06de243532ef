import { NgModule, LOCALE_ID, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';

import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';

import { SharedModule } from '../../../../../shared/shared.module';

import { MetadataFieldsEditorComponent } from './metadata-fields-editor/metadata-fields-editor.component';
import { MetadataFieldsListComponent } from './metadata-fields-list/metadata-fields.list.component';
import { MetadataFieldsViewComponent } from './metadata-fields-view/metadata-fields-view.component';
import { MetadataFieldService } from './metadata-fields.service';

@NgModule({
	imports: [
		CommonModule,
		SharedModule.forRoot(),
		FormsModule,
		RouterModule.forChild([
			{
				path: '', component: MetadataFieldsViewComponent
			}
		])
	],
	exports: [
		RouterModule
	],
	declarations: [
		MetadataFieldsEditorComponent,
		MetadataFieldsViewComponent,
		MetadataFieldsListComponent
	],
	providers: [
		[{ provide: LOCALE_ID, useValue: 'pt-br' }],
		DatePipe,
		MetadataFieldService
	],
	schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class MetadataFieldsModule {

}
