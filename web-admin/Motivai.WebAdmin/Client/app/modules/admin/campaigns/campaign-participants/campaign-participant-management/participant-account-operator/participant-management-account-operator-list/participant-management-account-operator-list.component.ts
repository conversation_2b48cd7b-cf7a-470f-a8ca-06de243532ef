import { CampaignParticipantManagementService } from '../../campaign-participant-management.service';
import { CampaignStore } from '../../../../campaign.store';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { Component, ViewChild, Output, EventEmitter, Input } from '@angular/core';
import { ParticipantManagementAccountOperatorComponent } from '../participant-management-account-operator.component';

@Component({
    selector: 'participant-management-account-operator-list',
    templateUrl: './participant-management-account-operator-list.component.html'
})
export class ParticipantManagementAccountOperatorListComponent {
  @Output('edit') edit: EventEmitter<any> = new EventEmitter();
  @ViewChild('alert') alert: GpAlertComponent;

  @ViewChild('participantAccountOperatorEditor') participantAccountOperatorEditor: ParticipantManagementAccountOperatorComponent;

  @Input()
  set _userId(id: string) {
    if (!id) {
      return;
    }
    this.userId = id;
  }

  operators: any[] = [];

  userId: string = '';

  loading: boolean = false;

  constructor(private _campaignStore: CampaignStore, private _participantService: CampaignParticipantManagementService) { }

  newOperator() {
      this.edit.emit({ isEditing: true });
  }

  findOperators() {
    if (!this.userId || !this.userId.length) {
      this.operators = [];
      return;
    }

    // this.participantAccountOperatorEditor.clear();
    this.loading = true;
    this._participantService.getAccountOperators(this.userId, this._campaignStore.id).subscribe(
      operators => {
        if (operators != null) {
            operators.forEach(operator => {
            operator.roleDescription = this.getRole(operator.role);
            operator.blockedStatusDescription = this.getBlockedDescription(operator.blocked);
          });
        }
        this.operators = operators;
        this.loading = false;
      },
      err => {
        this.loading = false;
        this.alert.showError(err);
      }
    );
  }

  showDetails(event) {
    this.edit.emit({ isEditing: true, operator: event });
  }

  getRole(operatorRole: string) {
    switch (operatorRole) {
      case 'MASTER':
        return 'Master';
      case 'MANAGER':
        return 'Gerente';
      case 'ADMIN':
          return 'Administrador';
      default:
        return 'Indefinido';
    }
  }

  getBlockedDescription(operatorBlocked: any) {
    if (operatorBlocked === true) {
      return 'Sim';
    }
    if (operatorBlocked === false) {
      return'Não';
    }
    return '';
  }
}
