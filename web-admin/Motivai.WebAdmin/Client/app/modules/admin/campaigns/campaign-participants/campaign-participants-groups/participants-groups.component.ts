import {
    PERMISSION_CAMPAIGNS_PARTICIPANTS_GROUPS_CREATE,
    PERMISSION_CAMPAIGNS_PARTICIPANTS_GROUPS_EDIT,
    PERMISSION_CAMPAIGNS_PARTICIPANTS_GROUPS_VIEW
} from '../../../../../core/auth/access-points';
import { TabsetComponent } from 'ng2-bootstrap/tabs';
import { ActivatedRoute } from '@angular/router';
import { Component, OnInit, ViewChild, Input } from '@angular/core';

import { GpParticipantsGroupEditComponent } from './participants-groups-edit/participants-groups-edit.component';
import { GpParticipantsGroupsListComponent } from './participants-groups-list/participants-groups-list.component';
import { AuthStore } from '../../../../../core/auth/auth.store';

@Component({
    selector: 'participants-groups',
    templateUrl: 'participants-groups.component.html'
})
export class GpParticipantsGroupComponent implements OnInit {
    @ViewChild('tabs') tabs: TabsetComponent;
    @ViewChild('listComponent') listComponent: GpParticipantsGroupsListComponent;
    @ViewChild('editComponent') editComponent: GpParticipantsGroupEditComponent;
    groupId: string;

    constructor(private route: ActivatedRoute, private _as: AuthStore) { }

    ngOnInit() { }

    onEdit($event: any) {
        if ($event) {
            this.groupId = $event;
            this.tabs.tabs[1].active = true;
        }
    }

    clearEditForm() {
        this.groupId = '';
        this.listComponent.getGroups();
        this.editComponent.reset();
    }

    canCreateOrEditOrView() {
        return this._as.hasPermissionTo(PERMISSION_CAMPAIGNS_PARTICIPANTS_GROUPS_CREATE) ||
            this._as.hasPermissionTo(PERMISSION_CAMPAIGNS_PARTICIPANTS_GROUPS_EDIT) ||
            this._as.hasPermissionTo(PERMISSION_CAMPAIGNS_PARTICIPANTS_GROUPS_VIEW);
    }
}
