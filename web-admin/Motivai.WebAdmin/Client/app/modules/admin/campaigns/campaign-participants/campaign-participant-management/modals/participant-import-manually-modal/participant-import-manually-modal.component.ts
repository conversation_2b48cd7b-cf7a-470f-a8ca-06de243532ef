import { Component, ViewChild } from '@angular/core';

import { CampaignParticipantsRegistrationComponent } from '../../../../../../../shared/components/business/campaigns/campaign-participants-registration/campaign-participants-registration.component';

@Component({
  selector: 'participant-invite-modal',
  templateUrl: './participant-import-manually-modal.component.html'
})
export class ParticipantImportManuallyComponent {
  @ViewChild('participantRegistration') participantRegistration: CampaignParticipantsRegistrationComponent;
  
  constructor() {}

  openModal() {
    this.participantRegistration.openModal();
  }

  hide() {
    this.participantRegistration.hide();
  }

}
