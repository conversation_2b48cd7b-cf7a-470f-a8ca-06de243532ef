<gp-alert #alert [overlay]="true"></gp-alert>
<spinner [overlay]="true" [show]="loading"></spinner>
<campaign-participants-goals-results-details-popup #goalDetails></campaign-participants-goals-results-details-popup>

<gp-card title="Filtro resultados" [first]="true">
	<gp-form-row>
		<gp-form-col cols="12 6">
			<gp-simple-input label="Selecione a Meta" [required]="false">
				<gp-select [items]="campaignGoals" name="campaignGoal" [(ngModel)]="filters.campaignGoalId"
					[allowClear]="true" [includeEmptyOption]="true" placeholder="Selecione"
					[required]="true"></gp-select>
			</gp-simple-input>
		</gp-form-col>

		<gp-form-col cols="12 6">
			<gp-simple-input label="Período de limitação" [required]="true">
				<gp-select [items]="periodTypes" name="periodType" [(ngModel)]="filters.periodType"
					[allowClear]="true" [includeEmptyOption]="true" placeholder="Selecione"
					[required]="true"></gp-select>
			</gp-simple-input>
		  </gp-form-col>
	</gp-form-row>

	<gp-form-row>
		<gp-datepicker cols="12 6" label="Data de referência" name="consultReferenceDate"
			[(ngModel)]="filters.consultReferenceDate" [required]="true">
		</gp-datepicker>

		<gp-form-col cols="12 6">
			<gp-simple-input label="CPF/CNPJ">
				<gp-input-mask name="document" mask="00000000000000" [(ngModel)]="filters.document"></gp-input-mask>
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>

	<gp-form-row cols="12 12">
		<gp-form-col cols="12 12">
			<gp-spinner-button [search]="true" pull="right" text="Pesquisar" [loading]="loading" loadingText="Buscando"
				(click)="onClickSearch()"></gp-spinner-button>

			<gp-spinner-button text="Exportar" loadingText="Processando" icon="file-excel-o" marginRight="12px"
				[disabled]="!hasGoals()" pull="right" bootstrapClass="success" (click)="exportGoals()">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card *ngIf="hasGoals()" [last]="true">
	<gp-form-col cols="12 12 12">
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-grid #grid name="grid" [rows]="campaignParticipantsGoals"
					[columns]="['Meta', 'Data de Criação', 'Simulacão', 'Pontos', 'Periodo']"
					[fields]="['name', 'formattedDate', 'formattedSimulation', 'formattedPoints', 'periodKey']"
					[loading]="loadingConsult"
					[showCustom]="true" customText="Detalhes" customTitle="Detalhes" customIcon="list" (onCustom)="openDetailsPopup($event)"
					[showActive]="false"  [showPagination]="true" [showTotalPages]="false" [showDelete]="false" [showEdit]="false"
					(onPageChanged)="onPageChanged($event)">
				</gp-grid>
			</gp-form-col>
		</gp-form-row>
	</gp-form-col>
</gp-card>
