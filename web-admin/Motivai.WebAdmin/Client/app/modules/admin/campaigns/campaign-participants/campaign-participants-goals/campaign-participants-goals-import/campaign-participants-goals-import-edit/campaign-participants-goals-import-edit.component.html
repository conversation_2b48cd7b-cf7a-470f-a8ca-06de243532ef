<gp-alert #alert [overlay]="true"></gp-alert>
<spinner [overlay]="true" [show]="loading"></spinner>

<div *ngIf="!batchId">
	<gp-card title="Importar meta de participantes" [first]="true">
		<gp-form-row>
			<gp-form-col cols="12 4 4">
				<gp-simple-input label="Selecione a Meta para importação">
					<gp-select name="campaignGoalId" [items]="activeCampaignGoals"
						[(ngModel)]="batchCampaignParticipantsGoals.campaignGoalId" (change)="configPeriod()">
					</gp-select>
				</gp-simple-input>
			</gp-form-col>

			<gp-form-col cols="12 4 4" *ngIf="isCustomPeriod">
				<gp-simple-input label="Selecione o período">
					<gp-select name="goalPeriodId" [items]="goalsPeriods"
						[(ngModel)]="batchCampaignParticipantsGoals.goalPeriodId" (change)="setPeriodKey()">
					</gp-select>
				</gp-simple-input>
			</gp-form-col>

			<gp-form-col cols="12 4 4" *ngIf="hasPeriodKeys">
				<gp-simple-input label="Selecione o mês">
					<gp-select name="periodKey" [items]="periodKeys"
						[(ngModel)]="batchCampaignParticipantsGoals.periodKey">
					</gp-select>
				</gp-simple-input>
			</gp-form-col>

			<gp-datepicker cols="12 4 4" label="Data de referência do arquivo" name="fileReferenceDate" *ngIf="hasPeriod"
				[(ngModel)]="batchCampaignParticipantsGoals.fileReferenceDate" [required]="false">
			</gp-datepicker>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 12">
				<label>Selecione o arquivo</label>
				<gp-fileupload name="batchFile" (oncomplete)="onFileUploaded($event)" #gpFile></gp-fileupload>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card>
		<gp-form-row>
			<gp-form-col cols="12 12 12" [inputGroup]="false">
				<gp-spinner-button type="button" icon="send" [pink]="true" pull="right" text="Importar Metas"
						(click)="uploadBatchFile()" [disabled]="loading">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</gp-card>
</div>

<div *ngIf="batchId">
	<gp-card title="Detalhes da Importação" [first]="true">
		<gp-form-row>
			<gp-form-col cols="12 12 12 12">
				<gp-form-row>
					<gp-form-col cols="12 6 6">
						<gp-simple-input label="Código do Lote">
							<input type="text" name="batchNumber" class="form-control" disabled
								[(ngModel)]="batchCampaignParticipantsGoals.batchNumber" />
						</gp-simple-input>
					</gp-form-col>
					<gp-form-col cols="12 6 6">
						<gp-simple-input label="Status">
							<input type="text" name="status" class="form-control" disabled
								[(ngModel)]="batchCampaignParticipantsGoals.formattedStatus" />
						</gp-simple-input>
					</gp-form-col>
				</gp-form-row>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Meta">
					<input type="text" name="formmatedGoalName" class="form-control" disabled
						[ngModel]="batchCampaignParticipantsGoals.formmatedGoalName" />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Arquivo">
					<input type="text" name="fileName" class="form-control" disabled
						[(ngModel)]="batchCampaignParticipantsGoals.fileName" />
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Data importação:">
					<input type="text" name="fileReferenceDate" class="form-control" disabled
						[ngModel]="batchCampaignParticipantsGoals.formattedFileReferenceDate " />
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row *ngIf="isError">
			<gp-form-col cols="12 12 12 12">
				<gp-form-row>
					<gp-form-col cols="12 12 12">
						<gp-simple-input label="Erro">
							<input type="text" name="error" class="form-control" disabled
								[(ngModel)]="batchCampaignParticipantsGoals.errorMessage" />
						</gp-simple-input>
					</gp-form-col>
				</gp-form-row>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<div *ngIf="!isError">
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-card>
					<gp-form-row>
						<gp-form-col cols="12 12 12">
							<h3>Sucessos:
								<span class="text-success">{{ successCount }}</span>
							</h3>
						</gp-form-col>
					</gp-form-row>
				</gp-card>
			</gp-form-col>

			<gp-form-col cols="12 6 6">
				<gp-card>
					<gp-form-row>
						<gp-form-col cols="12 4">
							<h3>Erros:
								<span class="text-danger">{{ errorsCount }}</span>
							</h3>
						</gp-form-col>
						<gp-form-col cols="12 8">
							<gp-spinner-button  *ngIf="canExportBatchErrors" type="button" pull="right" marginTop="24px" text="Exportar Erros do lote" loadingText="Processando..." bootstrapClass="success" [disabled]="loading || !batchId" (click)="exportBatchErrors()" ></gp-spinner-button>
						</gp-form-col>
					</gp-form-row>
				</gp-card>
			</gp-form-col>
		</gp-form-row>

		<!-- <gp-card title="Registros com Sucesso">
			<gp-form-col cols="12">
				<gp-grid name="grid" [rows]="campaignParticipantsGoals"
					[columns]="['Data de Criação']"
					[fields]="['formattedCreateDate']"
					[showActive]="false" [showPagination]="false" [showDelete]="false" [showEdit]="false">
				</gp-grid>
			</gp-form-col>
		</gp-card> -->
	</div>
</div>

<gp-card title="Instruções de Uso" [last]="true">
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<p>1. O formato do arquivo deve ser <u>[.csv]</u>, com as colunas separadas por [;].</p>
			<p>2. O cabeçalho deve ser formado por: documento, resultado e campos extras. Os campos extras podem ser nomeados de acordo com seus valores, como está sendo feito na imagem de exemplo.</p>
			<p>3. Os cabeçalhos que não forem identificados como documento e/ou resultado, também serão salvos em nosso sistema.</p>
		</gp-form-col>
		<gp-form-col cols="12 4 4">
			<ul>
				<li>
					<strong>documento: </strong>CPF/CNPF do participante, usado para identificar o mesmo em nosso
					sistema
				</li>
				<li>
					<strong>resultado: </strong>Valor alcançado pelo participante
					<ul>
						<li>Casas decimais: o valor precisa ser formatado como texto e com ponto <u>[.]</u> no lugar da virgula <u>[,]</u>.</li>
						<li>Exemplo: um crédito de <u>1.152,50</u> pontos precisar estar salvo como <u>1152.50</u>.</li>
					</ul>
				</li>
				<li>
					<strong>codigo, produto: </strong>Código ou produto da meta
					<ul>
						<li>Utilizado no caso da meta ser por bateu levou</li>
						<li>Obs.: Caso tenha apenas um alvo configurado na meta, não é necessário informar código</li>
					</ul>
				</li>
				<li>
					<strong>meta, esperado: </strong>Valor da meta do participante para configuração inicial
					<ul>
						<li>Utilizado no caso da meta ser por produto, por faixa exclusíva ou intervalo</li>
						<li>
							Somente será lida na primeira importação de cada participante para configuração inicial
							(caso não tenha sido feita pela tela)
						</li>
					</ul>
				</li>
				<li>
					<strong>bonus, pontos: </strong>Valor do bônus da meta
					<ul>
						<li>Utilizado no caso da meta ser por produto</li>
						<li>
							Somente será lida na primeira importação de cada participante para configuração inicial
							(caso não tenha sido feita pela tela)
						</li>
					</ul>
				</li>
				<li>
					<strong>intervalo: </strong>Faturamento base e valor do intervalo
					<ul>
						<li>Utilizado no caso da meta ser por intervalos</li>
						<li>
							Somente será lida na primeira importação de cada participante para configuração inicial
							(caso não tenha sido feita pela tela)
						</li>
					</ul>
				</li>
				<li>
					<strong>previa: </strong>Valor alcançado pelo participante (Simulação)
					<ul>
						<li>Utilizado no caso da meta ser por intervalos e Exclusívos</li>
						<li>
							Utilizado para simulação da meta.
						</li>
					</ul>
				</li>
				<li>
					<strong>liberado: </strong>Define se o crédito é ou não Simulação
					<ul>
						<li>Utilizado para metas do tipo Faixa Exclusiva e Bateu Levou</li>
						<li>
							Valores esperados: Sim ou Nao
						</li>
					</ul>
				</li>
			</ul>
		</gp-form-col>
		<gp-form-col cols="12 8 8">
			<img src="/assets/img/imports/modelo-importacao-metas.png" style="width: 600px;" /> <br /><br />
			<a href="/assets/files/modelo_planilha_importacao_metas.csv">Clique aqui para baixar um modelo de
				planilha</a>
		</gp-form-col>
		<gp-form-col cols="12 12 12">
			<p>4. Os campos obrigatórios são: documento e resultado.</p>
			<p>5. O documento deve conter: 11 digitos para CPF; 14 digitos para CNPJ, sem formatação. <strong>Se atente
					a formatar a coluna de documento como <u>texto</u>, para manter os zeros a esquerda.</strong></p>
		</gp-form-col>
	</gp-form-row>
</gp-card>
