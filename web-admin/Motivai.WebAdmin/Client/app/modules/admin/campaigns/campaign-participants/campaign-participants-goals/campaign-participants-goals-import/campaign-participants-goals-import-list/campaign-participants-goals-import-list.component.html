<gp-card [first]="true">
  <gp-form-row>
    <gp-form-col cols="12 5">
      <gp-simple-input label="Número do Lote">
        <input type="text" class="form-control" name="batchNumber" [(ngModel)]="params.batchNumber" />
      </gp-simple-input>
    </gp-form-col>

    <gp-form-col cols="12 5">
      <gp-simple-input label="Status do Lote">
        <gp-select name="batchStatus" [allowClear]="false" [items]="batchStatuses" [(ngModel)]="params.batchStatus">
        </gp-select>
      </gp-simple-input>
    </gp-form-col>

    <gp-form-col cols="12 2">
      <gp-spinner-button [search]="true" pull="left" text="Pesquisar" marginLeft="5px" marginTop="27px" [loading]="loading"
        loadingText="Processando" (click)="findCampaignParticipantsGoalsBatches()"></gp-spinner-button>
    </gp-form-col>
    <!-- <gp-datepicker cols="12 4 4" label="Data de Importação" name="importDate" [(ngModel)]="params.importDate">
    </gp-datepicker> -->
  </gp-form-row>
</gp-card>

<gp-card title="Registros encontrados" [last]="true">
  <gp-form-row>
    <gp-form-col cols="12">
      <gp-grid name="grid" [rows]="batchesCampaignParticipantsGoals"
        [columns]="['Número do Lote', 'Meta',  'Data da Importação', 'Nome do Arquivo', 'Status']"
        [fields]="['batchNumber', 'formattedGoalName', 'formattedCreateDate', 'fileName', 'formattedStatus']" [showActive]="false"
        [showPagination]="true" [pageSize]="limit" [showTotalPages]="false" (onPageChanged)="changePage($event)"
        [showDelete]="false" [showEdit]="true" (onEdit)="showBatchCampaignParticipantsGoalDetails($event)">
      </gp-grid>
    </gp-form-col>
  </gp-form-row>
</gp-card>

<spinner [overlay]="true" [show]="loading"></spinner>
<gp-alert #alert [overlay]="true"></gp-alert>
