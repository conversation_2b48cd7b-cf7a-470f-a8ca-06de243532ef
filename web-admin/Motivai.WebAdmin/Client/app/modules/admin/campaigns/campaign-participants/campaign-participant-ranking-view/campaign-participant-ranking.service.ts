import { Injectable } from '@angular/core';
import { ApiService } from '../../../../../core/api/api.service';
import { Observable } from 'rxjs';

@Injectable()
export class CampaignParticipantRankingService {

    constructor(private _api: ApiService) { }

    saveCampaignParticipantRankingParametrization(campaignId: string, rankingParametrization: any): Observable<any> {
        if (rankingParametrization.id)
            return this._api.putWithStaticContent(`/api/campaigns/${campaignId}/participants/dynamicrankings/parametrization/${rankingParametrization.id}`, rankingParametrization)
        return this._api.post(`/api/campaigns/${campaignId}/participants/dynamicrankings/parametrization`, rankingParametrization);
    }

    findCampaingParticipantRankingParametrizations(campaignId: string, code?: string, onlyActive: boolean = false): Observable<any[]> {
        const params: any = {
          onlyActive: onlyActive
        };
        if (code)
            params.code = code;
        return this._api.get(`/api/campaigns/${campaignId}/participants/dynamicrankings/parametrization`, params);
    }

    inactiveRankingParametrization(campaignId: string, parametrizationId: string): Observable<any> {
        return this._api.put(`/api/campaigns/${campaignId}/participants/dynamicrankings/parametrization/${parametrizationId}/inactive`);
    }

    findCampaignParticipantsRankingBatchById(campaignId: string, batchId: string): Observable<any> {
        return this._api.get(`/api/campaigns/${campaignId}/rankings/batches/${batchId}`);
    }

    findCampaignParticipantsRankingsBatches(campaignId: string, parameters: any): Observable<any[]> {
        const params: any = {};

        if (parameters.batchNumber) {
            params.batchNumber = parameters.batchNumber;
        }

        if (parameters.batchStatus) {
            params.batchStatus = parameters.batchStatus;
        }

        params.skip = parameters.skip;
        params.limit = parameters.limit;

        return this._api.get(`/api/campaigns/${campaignId}/rankings/batches`, params);
    }

    getCampaignParticipantsRankingsBatchesCounter(campaignId: string, parameters: any): Observable<any> {
        const params: any = { };

        if (parameters.batchNumber) {
            params.batchNumber = parameters.batchNumber;
        }

        if (parameters.batchStatus) {
            params.batchStatus = parameters.batchStatus;
        }

        return this._api.get(`/api/campaigns/${campaignId}/rankings/batches/counter`, params);
    }

    saveCampaignParticipantsRankingBatch(campaignId: string, data: any): Observable<any> {
        return this._api.post(`/api/campaigns/${campaignId}/rankings/batches`, data);
    }

    findCampaignParticipantsRankingByBatchId(campaignId: string, batchId: string): Observable<any[]> {
        return this._api.get(`/api/campaigns/${campaignId}/participantsrankings/batches/${batchId}`);
    }

    exportBatchErrors(campaignId: string, batchId: string): Observable<any> {
        return this._api.get(`/api/campaigns/${campaignId}/rankings/batches/${batchId}/export/errors`);
    }

}
