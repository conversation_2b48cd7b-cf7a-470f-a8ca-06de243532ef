<gp-alert [overlay]="true" #alert></gp-alert>

<gp-card [first]="true">
    <gp-form-row>
        <gp-form-col cols="12 6">
            <gp-simple-input label="CPF">
                <gp-input-mask name="document" mask="99999999999" [disabled]="disableFields" [(ngModel)]="operator.document" (onblur)="findOperatorById()"></gp-input-mask>
            </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 6">
            <gp-simple-input label="Nome">
                <input type="text" class="form-control" [disabled]="disableFields || foundOperator" [(ngModel)]="operator.name" />
            </gp-simple-input>
        </gp-form-col>
    </gp-form-row>
    <gp-form-row>
        <gp-form-col cols="12 6">
            <gp-simple-input label="Email">
                <input type="text" class="form-control" [disabled]="disableFields || foundOperator" [(ngModel)]="operator.email" />
            </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 6">
            <gp-simple-input label="Login">
                <input type="text" class="form-control" [disabled]="disableFields || foundOperator" [ngModel]="operator.login" />
            </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 6">
			<gp-simple-input label="Perfil">
				<gp-select name="rolesAccount" [allowClear]="false" [disabled]="disableOperatorRoleField" [items]="rolesAccount" [(ngModel)]="operator.role">
				</gp-select>
			</gp-simple-input>
		</gp-form-col>
        <gp-form-col cols="12 6">
            <gp-simple-input label="Operador está bloqueado">
                <input type="text" [(ngModel)]="valueOfLabel" readonly class="form-control" name="name" required value="label" />
            </gp-simple-input>
        </gp-form-col>
    </gp-form-row>
    <gp-form-row *ngIf="operator.isMigrated">
        <gp-form-col cols="12 6">
            <gp-simple-input label="Usuário Super Portal Empregador">
                <input type="text" [(ngModel)]="operator.isMigratedLabel" readonly class="form-control" name="name" />
            </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 6">
            <gp-simple-input label="Data de Migração">
                <input type="text" [(ngModel)]="operator.migrationDate" readonly class="form-control" name="name" />
            </gp-simple-input>
        </gp-form-col>
    </gp-form-row>
</gp-card>
<spinner [overlay]="true" [show]="loading"></spinner>

<gp-card>
    <gp-form-row>
        <gp-form-col cols="12" [inputGroup]="false">

            <gp-spinner-button type="button" [actionBack]="true" text="Voltar" loadingText="Aguarde"
                pull="right" [loading]="loading" [disabled]="loading" (click)="back()" marginLeft="5px">
            </gp-spinner-button>

            <gp-spinner-button type="button" [pink]="true" icon="send" text="Criar Operador" loadingText="Processando"
                pull="right" [loading]="loading" [disabled]="loading" (click)="createOperator()" *ngIf="isCreating">
            </gp-spinner-button>

			<gp-spinner-button type="button" icon="trash" bootstrapClass="danger" text="Desativar" loadingText="Processando"
				pull="right" [loading]="loading" [disabled]="loading" marginLeft="5px"
				(click)="disableOperator()" *ngIf="canBlock">
			</gp-spinner-button>

			<gp-spinner-button type="button" icon="check" bootstrapClass="success" text="Ativar" loadingText="Processando"
				pull="right" [loading]="loading" [disabled]="loading" marginLeft="5px"
				(click)="activeOperator()" *ngIf="canUnblock">
			</gp-spinner-button>

            <gp-spinner-button type="button" icon="refresh" bootstrapClass="warning" text="Resetar Senha" loadingText="Processando"
                pull="right" [loading]="loading" [disabled]="loading" marginLeft="5px"
                (click)="resetOperatorPassword()" *ngIf="canResetPassword">
            </gp-spinner-button>

            <gp-spinner-button type="button" icon="envelope-o" bootstrapClass="primary" text="Alterar E-mail" loadingText="Processando"
                pull="right" [loading]="loading" [disabled]="loading" marginLeft="5px"
                (click)="updateOperatorEmail()" *ngIf="canChangeEmail">
            </gp-spinner-button>

            <gp-spinner-button type="button" icon="user" bootstrapClass="primary" text="Alterar Perfil" loadingText="Processando"
                pull="right" [loading]="loading" [disabled]="loading" marginLeft="5px"
                (click)="updateOperatorRole()" *ngIf="canChangeRole">
            </gp-spinner-button>
        </gp-form-col>
    </gp-form-row>
</gp-card>

<participant-account-operator-modal #partcipantAccountOperatorModal
    [_userId]="userId"
    [_campaignId]="campaignId"
    [_participant]="participant"
	(update)="onAccountOperatorUpdate()"
>
</participant-account-operator-modal>

<participant-account-operator-email-modal #accountOperatorEmail>
</participant-account-operator-email-modal>
