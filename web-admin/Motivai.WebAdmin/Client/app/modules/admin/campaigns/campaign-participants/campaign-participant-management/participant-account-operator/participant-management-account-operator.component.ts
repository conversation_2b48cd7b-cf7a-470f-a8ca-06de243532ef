import { Component, OnInit, ViewChild, Input } from '@angular/core';
import { ParticipantManagementAccountOperatorListComponent } from './participant-management-account-operator-list/participant-management-account-operator-list.component';
import { ParticipantManagementAccountOperatorEditorComponent } from './participant-management-account-operator-editor/participant-management-account-operator-editor.component';

@Component({
    selector: 'participant-management-account-operator',
    templateUrl: './participant-management-account-operator.component.html'
})
export class ParticipantManagementAccountOperatorComponent implements OnInit {
    @ViewChild('participantAccountOperatorList') participantAccountOperatorList: ParticipantManagementAccountOperatorListComponent;
    @ViewChild('participantAccountOperatorEditor') participantAccountOperatorEditor: ParticipantManagementAccountOperatorEditorComponent;

    operator: any = {};

    userId: string = '';

    isEditing: boolean = false;
    accountDocument: string = '';

    constructor() { }

    ngOnInit(): void { }

    get show() {
        return this.isEditing;
    }

    @Input()
    set _userId(id: string) {
        if (!id) {
            return;
        }
        this.participantAccountOperatorList.userId = id;
        this.participantAccountOperatorEditor.userId = id;
        this.findOperators();
    }

    @Input()
    set _accountDocument(accountDocument: string) {
        if (!accountDocument) {
            return;
        }
        this.accountDocument = accountDocument;
    }

    public clear() {
      this.userId = '';
      this.findOperators();
    }

    onEdit(event) {
        if (!event) {
            return;
        }
        this.isEditing = event.isEditing;
        this.operator = event.operator;
    }

    onList(event) {
        if (!event) {
            return;
        }
        this.isEditing = event.isEditing;
        this.findOperators();
    }

    findOperators() {
        this.isEditing = false;
        this.operator = {};
        this.participantAccountOperatorEditor.clear();
        this.participantAccountOperatorList.findOperators();
    }
}
