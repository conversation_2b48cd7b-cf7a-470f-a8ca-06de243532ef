<form *ngIf="canView()" #participantsGroupForm="ngForm" (ngSubmit)="saveGroup()" novalidate>
	<gp-card [first]="true" title="Dados do Grupo">
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Descrição do Grupo" [required]="true"
					errorMessage="Descrição do grupo é obrigatório">
					<input type="text" class="form-control" name="name" required [(ngModel)]="group.name" />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Identificador do Grupo"
					tooltip="Identificador único do grupo, utilizado na importação de participantes" [required]="true"
					errorMessage="Identificador é obrigatório">
					<input type="text" class="form-control" name="identifier" required [(ngModel)]="group.identifier" />
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<campaign-participants-group-selector cols="12 6" [(ngModel)]="group.parentGroupId" [multiple]="false"
				name="parentGroupId" [ngModelOptions]="{standalone: true}"></campaign-participants-group-selector>

			<gp-form-col cols="12 3">
				<label>Gerar Público Alvo Automaticamente</label>
				<div>
					<gp-switch name="generateTargetAudience" [(ngModel)]="group.generateTargetAudience">
					</gp-switch>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 3">
				<label>Ativo</label>
				<div>
					<gp-switch name="active" [(ngModel)]="group.active"></gp-switch>
				</div>
			</gp-form-col>
			
		</gp-form-row>
		<div class="row">
			<div class="col-xs-12">
				<gp-spinner-button type="submit" text="Salvar Grupo" pull="right" [pink]="true" icon="send"
					loadingText="Processando" [loading]="loading" [disabled]="!participantsGroupForm.valid"
					*ngIf="canCreate() || canEdit()">
				</gp-spinner-button>

				<gp-spinner-button text="Novo" bootstrapClass="default" icon="plus" pull="right" marginRight="10px"
					[disabled]="loading" (click)="reset()" [showSpinner]="false" *ngIf="canCreate()">
				</gp-spinner-button>
			</div>
		</div>
	</gp-card>
	<spinner [overlay]="true" [show]="loadingScreen || loadingParticipants"></spinner>
	<gp-alert [overlay]="true" #alert></gp-alert>
</form>

<gp-card title="Adicionar um participante no grupo" *ngIf="group.id && canView() && !group.generateTargetAudience">
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<p>Adicione manualmente participantes ao grupo: Pesquise pelo documento ou nome do participante, selecione
				na lista um registro encontrado e clique em Adicionar.</p>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="CPF/CNPJ">
				<input type="text" name="nameOrDocument" class="form-control" (blur)="searchParticipants()"
					[(ngModel)]="participantSearch.nameOrDocument" />
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 6 6">
			<gp-simple-input label="Participantes encontrados">
				<select [disabled]="!participantsFound || !participantsFound.length" class="form-control" name="userId"
					[(ngModel)]="selectedUserId">
					<option value="">{{ !participantsFound.length ? "Nenhum participante encontrado" : "Selecione" }}
					</option>
					<option *ngFor="let p of participantsFound" [value]="p.userId"
						[selected]="participantsFound.length === 1">{{ p.name }}</option>
				</select>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 2 2">
			<gp-spinner-button type="button" text="Adicionar" [pink]="true" icon="plus" pull="right" marginTop="26px"
				[loading]="loading" [disabled]="!participantsFound || !participantsFound.length"
				(click)="addParticipant()" *ngIf="canEdit()">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card>
	<gp-form-row>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Consultar pelo CPF/CNPJ">
				<input type="text" name="searchDocument" class="form-control" [(ngModel)]="userDocument" />
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 2 2">
			<gp-spinner-button type="button" text="Buscar" [actionSearch]="true" pull="right" marginTop="26px"
				[loading]="loading" (click)="searchParticipant()" *ngIf="canEdit()">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card title="Participantes dos grupos">
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<gp-grid #grid name="participantsGrid" [rows]="participants"
				[columns]="[ 'Nome do Participante', 'Documento' ]" [fields]="[ 'name', 'document' ]"
				[loading]="loadingDelete || loadingParticipants" [showActive]="false" [showPagination]="true"
				[showTotalPages]="false" [pageSize]="limit" [showEdit]="false" [showDelete]="canEdit()"
				emptyMessage="Nenhum participante vinculado a este grupo" (onPageChanged)="pageChanged($event)"
				(onDelete)="removeParticipant($event)">
			</gp-grid>
		</gp-form-col>
	</gp-form-row>
</gp-card>