import { Component, OnInit, Input, ViewChild, forwardRef } from '@angular/core';
import { Item } from '../../../../../shared/models/item';
import { ParticipantsGroupService } from './participants-groups.service';
import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';

@Component({
    selector: 'gp-participants-groups',
    template: `
        <ng-select [id]="inputName" [name]="inputName" [multiple]="multiple"
            [allowClear]="allowClear" [items]="items" [required]="required" [disabled]="disabled"
            [active]="currentItems" [placeholder]="placeholder" [campaignId]="campaignId" [(ngModel)]="value"> 
        </ng-select>
  `,
    providers: [{
        provide: NG_VALUE_ACCESSOR,
        useExisting: forwardRef(() => GpParticipantsGroupsSelectComponent),
        multi: true
    }]
})

export class GpParticipantsGroupsSelectComponent implements OnInit, ControlValueAccessor {
    @ViewChild('alert') alert: GpAlertComponent;

    @Input('name')
    inputName: string;
    @Input()
    public required: boolean = false;
    @Input()
    public placeholder: string;
    @Input()
    public disabled: boolean;
    @Input()
    public multiple: boolean = false;
    @Input()
    public allowClear: boolean = true;
    @Input()
    public campaignId: string;

    items: Array<Item> = [];
    currentItems: Array<Item> = [];

    loading: boolean = false;

    private valueToSet?: string = undefined;
    private _value: string;
    private onTouched = () => { };
    private propagateChange = (_: any) => { };

    get value(): any {
        return this._value;
    }

    set value(v: any) {
        if (v) {
          if (v !== this._value) {
            this._value = v.id;
            this.propagateChange(v);
          }
          this.onTouched();
        }
    }

    constructor(private groupsService: ParticipantsGroupService) { }

    ngOnInit(): void {
        this.loadGroups();
    }

    private loadGroups() {
        if (!this.campaignId)
            return;
        this.loading = true;
        this.groupsService.search(this.campaignId)
            .subscribe(
                groups => {
                    if (groups) {
                        this.items = groups.map(g => Item.of(g.id, g.name));
                    }
                    if (this.valueToSet) {
                        this.value = this.valueToSet;
                    }
                    this.loading = false;
                },
                err => {
                    this.loading = false;
                    this.alert.showError(err);
                }
            );
    }

    writeValue(value: any): void {
        if (value !== this._value) {
            if (this.loading || !this.items) {
                this.valueToSet = value;
            } else {
                this.value = value;
            }
        }
    }

    registerOnChange(fn: any): void {
        this.propagateChange = fn;
    }

    registerOnTouched(fn: any): void {
        this.registerOnTouched = fn;
    }

    onChange(data: string) {
        this.value = data;
        this.propagateChange(data);
    }


}
