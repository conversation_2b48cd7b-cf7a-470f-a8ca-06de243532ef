import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild
} from '@angular/core';
import { GpAlertComponent } from '../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpFileUploadComponent } from '../../../../../../../../shared/components/gp-fileupload/gp-fileupload.component';
import { GpModalComponent } from '../../../../../../../../shared/components/gp-modal/gp-modal.component';
import { Item } from '../../../../../../../../shared/models/item';
import { AmountType } from '../../../models/amount-type';
import { EventTypes } from '../../../models/event-types';
import { CampaignsParticipantsGoalsActionsService } from '../campaigns-participants-goals-actions.service';

@Component({
  selector: 'goal-event-editing',
  templateUrl: './goal-event-editing.component.html'
})
export class GoalEventEditingComponent implements OnInit {
  @ViewChild('eventModal') eventModal: GpModalComponent;
  @ViewChild('gpFile') gpFile: GpFileUploadComponent;
  @ViewChild('alert') alert: GpAlertComponent;

  @Output() refreshEvents: EventEmitter<any> = new EventEmitter();
  @Input() campaignId: string;
  @Input() goalId: string;
  @Input() hasStarted: string;

  saving: boolean = false;
  removing: boolean;
  uploading: boolean = false;

  eventId: string;
  event: any = {
    active: true,
    eventType: EventTypes.BONUS
  };

  eventTypes: Array<Item> = [
    Item.of(EventTypes.BONUS, 'Bônus'),
    Item.of(EventTypes.PENALTY, 'Penalidade')
  ];

  amountTypes: Array<Item> = [
    Item.of(AmountType.CURRENCY, 'Reais'),
    Item.of(AmountType.POINTS, 'Pontos')
  ];

  constructor(
    private goalsActionsService: CampaignsParticipantsGoalsActionsService
  ) { }

  ngOnInit() { }

  get isEditing() {
    return this.event && this.event.id;
  }

  get textButtonSave() {
    if (this.isEditing) {
      return 'Salvar';
    }
    return 'Adicionar';
  }

  get rewardTypeFieldTitle() {
    if (this.event.eventType == EventTypes.PENALTY) {
      return "Tipo de penalidade"
    } 
    return "Tipo de bônus";
  }

  get icon() {
    if (this.isEditing) {
      return 'send';
    }
    return 'plus';
  }

  get hasSelectedFile() {
    return this.gpFile && this.gpFile.hasSelectedFile();
  }

  private clearForm() {
    this.event = {
      active: true
    };
  }

  newEvent() {
    this.clearForm();
    this.showModal();
  }

  editEvent(event: any) {
    this.event = event;
    this.eventId = event.id;
    this.showModal();
  }

  showModal() {
    this.eventModal.show();
  }

  hideModal() {
    this.eventModal.hide();
  }

  saveEvent() {
    this.saving = true;
    if (this.isEditing) {
      return this.updateGoalEvent();
    }
    return this.createEvent();
  }

  createEvent() {
    this.goalsActionsService
      .saveGoalEvent(this.campaignId, this.goalId, this.event)
      .subscribe(
        response => {
          if (response) {
            if (typeof response === 'string') {
              this.eventId = response;
              this.event.id = response;
              this.alert.showSuccess('Evento salvo com sucesso');
              this.refreshEvents.emit();
              this.hideModal();
            } else {
              this.alert.showError('Não foi possível salvar, tente novamente');
            }

          }
          this.saving = false;
        },
        err => {
          this.saving = false;
          this.alert.showError(err);
        }
      );
  }

  updateGoalEvent() {
    this.goalsActionsService
      .updateGoalEvent(this.campaignId, this.goalId, this.event)
      .subscribe(
        response => {
          if (response) {
              this.alert.showSuccess('Faixa atualizada com sucesso');
              this.refreshEvents.emit();
              this.hideModal();
          } else {
              this.alert.showError('Não foi possível atualizar, tente novamente');
          }
          this.saving = false;
        },
        err => {
          this.saving = false;
          this.alert.showError(err);
        }
      );
  }
}
