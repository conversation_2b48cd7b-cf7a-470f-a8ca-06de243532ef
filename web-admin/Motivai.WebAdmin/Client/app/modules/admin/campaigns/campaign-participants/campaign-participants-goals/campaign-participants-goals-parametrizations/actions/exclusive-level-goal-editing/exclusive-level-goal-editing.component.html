<gp-modal title="Faixa Exclusíva da Meta" width="800px" #exclusiveLevelGoalModal  (onClose)="onClose()">
    <gp-alert #alert [overlay]="true"></gp-alert>
    <spinner [overlay]="true" [show]="saving"></spinner>

    <gp-form-row>
        <gp-form-col cols="12 6">
            <gp-simple-input label="Nome">
                <input type="text" name="name" class="form-control" [(ngModel)]="exclusiveLevelGoal.name" />
            </gp-simple-input>
        </gp-form-col>

        <gp-form-col cols="12 6">
            <label>Ativo</label>
            <div>
                <gp-switch name="active" [disabled]="hasStarted" [(ngModel)]="exclusiveLevelGoal.active"></gp-switch>
            </div>
        </gp-form-col>
    </gp-form-row>

    <gp-form-row>
        <gp-form-col cols="12 6">
            <gp-simple-input label="Código">
                <input type="text" name="code" class="form-control" [disabled]="hasStarted"
                    [(ngModel)]="exclusiveLevelGoal.code" />
            </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 6">
            <label>Faturamento (Base)</label>
            <gp-input-mask required name="baseTargetValue" [onlyDecimal]="true"
                [decimais]="exclusiveLevelGoal.decimalPlacesQuantity"
                [(ngModel)]="exclusiveLevelGoal.baseTargetValue"
                (change)="updateMinimumTargetValue()" *ngIf="showChild"
                (change)="onDecimalPlacesQuantityUpdate()"></gp-input-mask>
        </gp-form-col>
    </gp-form-row>

    <gp-form-row>
        <gp-form-col cols="12 6">
            <label>Habilitar Fator de Crescimento</label>
            <div>
                <gp-switch name="active" [disabled]="hasStarted" [(ngModel)]="enableLevelThresholdPercentage" (change)="onEnableLevelThressholdPercentageChange()"></gp-switch>
            </div>
        </gp-form-col>
        <gp-form-col cols="12 6" *ngIf="enableLevelThresholdPercentage">
            <label>Fator de Crescimento (%)</label>
            <gp-input-mask required name="levelThresholdPercentage" [onlyDecimal]="true" [integers]="7" [decimais]="2"
                placeholder="0.00" [disabled]="hasStarted" [(ngModel)]="exclusiveLevelGoal.levelThresholdPercentage"
                (change)="updateMinimumTargetValue()"></gp-input-mask>
        </gp-form-col>
        <gp-form-col cols="12 6" *ngIf="enableLevelThresholdPercentage">
            <label>Variação</label>
            <gp-input-mask required name="levelThresholdVariationValue" [disabled]="true" [onlyDecimal]="true"
                [integers]="7" [decimais]="2" placeholder="0.00" [disabled]="hasStarted"
                [(ngModel)]="exclusiveLevelGoal.levelThresholdVariationValue">
            </gp-input-mask>
        </gp-form-col>
        <gp-form-col cols="12 6" *ngIf="enableLevelThresholdPercentage">
            <label>Atingimento Mínimo da Meta</label>
            <gp-input-mask required name="minimumTargetValue" [disabled]="true" [onlyDecimal]="true" [integers]="6"
                [decimais]="2" placeholder="0,00" [disabled]="hasStarted"
                [(ngModel)]="exclusiveLevelGoal.minimumTargetValue"></gp-input-mask>
        </gp-form-col>
    </gp-form-row>

    <gp-form-row>
        <gp-form-col cols="12 6">
            <gp-simple-input label="Método de pontuação">
                <gp-select [disabled]="hasStarted" [items]="rewardMethods"
                    [(ngModel)]="exclusiveLevelGoal.rewardMethod">
                </gp-select>
            </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 6" *ngIf="isCashBackRewardMethod">
            <label>Cashback (%)</label>
            <gp-input-mask required name="cashback" [onlyDecimal]="true" [integers]="6" [decimais]="2"
                placeholder="0,00" [disabled]="hasStarted" [(ngModel)]="exclusiveLevelGoal.rewardValue"></gp-input-mask>
        </gp-form-col>

        <gp-form-col cols="12 6" *ngIf="isCashBackRewardMethod && !enableLevelThresholdPercentage">
            <label>
                Habilitar cashback proporcional do excedente
                <gp-tooltip spanClass="text-primary">
                    <div style="text-align:justify">
                        Habilita o cálculo do cashback com o resultado excedente além do valor de atingimento mínimo da faixa.
                        <ul>
                            <li>Se habilitado: Valor pontos = Resultado Total * Cashback (%)</li>
                            <li>Se desabilitado: Valor pontos = Atingimento Mínimo * Cashback (%)</li>
                        </ul>
                    </div>
                </gp-tooltip>
            </label>
            <div>
                <gp-switch name="allowProportionalBonusForExceedingLevel" [disabled]="hasStarted"
                    [(ngModel)]="exclusiveLevelGoal.allowProportionalBonusForExceedingLevelResult">
                </gp-switch>
            </div>
        </gp-form-col>
    </gp-form-row>

    <gp-form-row *ngIf="isPointsRewardMethod">
        <gp-form-col cols="12 6">
            <gp-simple-input label="Tipo da pontuação">
                <select class="form-control" [disabled]="hasStarted" [(ngModel)]="exclusiveLevelGoal.rewardType">
                    <option value="Currency">Reais (R$)</option>
                    <option value="Points">Pontos</option>
                </select>
            </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 6">
            <label>Valor do ganho</label>
            <gp-input-mask required name="points" [onlyDecimal]="true" [integers]="6" [decimais]="2" placeholder="0,00"
                [disabled]="hasStarted" [(ngModel)]="exclusiveLevelGoal.rewardValue">
            </gp-input-mask>
        </gp-form-col>
    </gp-form-row>

    <gp-form-row>
        <gp-form-col cols="12 6">
          <gp-simple-input label="Quantidade de casas decimais">
            <gp-tooltip message="Especificar a quantidade de casas decimais do valor base da meta e do resultado.">(?)</gp-tooltip>
            <gp-input-mask required name="decimalPlacesQuantity" [onlyInteger]="true" placeholder="2" [(ngModel)]="exclusiveLevelGoal.decimalPlacesQuantity"
            (change)="onDecimalPlacesQuantityUpdate()"></gp-input-mask>
          </gp-simple-input>
        </gp-form-col>

        <gp-form-col cols="12 6">
            <label>Considerar valor 0 como resultado</label>
            <div>
                <gp-switch name="active" [(ngModel)]="exclusiveLevelGoal.considerValueZeroAsValidResult" (change)="considerValueZeroAsValidResultUpdate()"></gp-switch>
            </div>
        </gp-form-col>
      </gp-form-row>

    <gp-form-row>
        <gp-form-col cols="12 12 12 12">
            <gp-spinner-button [actionPrimary]="true" [icon]="icon" pull="right" [text]="textButtonSave" (click)="saveGoal()">
            </gp-spinner-button>
        </gp-form-col>
    </gp-form-row>

</gp-modal>
