import { AuthStore } from '../../../../../core/auth/auth.store';
import { CampaignStore } from '../../campaign.store';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
    selector: 'campaign-participant-ranking-view',
    templateUrl: './campaign-participant-ranking-view.component.html'
})
export class CampaignParticipantRankingViewComponent implements OnInit {
    campaignId: string = '';

    constructor(private _campaignStore: CampaignStore, private _authStore: AuthStore, private route: ActivatedRoute, private router: Router) { }

    ngOnInit(): void {
        this.campaignId = this._campaignStore.id;
    }

    get showMenublock() {
        return this._campaignStore.isFullCampaignOrUserWithGpBu;
    }

    get canViewRankingParametrization() {
        return this._authStore.role.PERMISSION_CAMPAIGNS_PARTICIPANTS_RANKING_PARAMETRIZATION;
    }
}
