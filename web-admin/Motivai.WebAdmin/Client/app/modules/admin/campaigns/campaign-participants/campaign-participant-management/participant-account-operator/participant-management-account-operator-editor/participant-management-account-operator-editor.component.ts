import { Component, ViewChild, Output, EventEmitter, Input, OnInit } from '@angular/core';

import { CampaignParticipantManagementService } from '../../campaign-participant-management.service';
import { CampaignStore } from '../../../../campaign.store';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { ParticipantAccountOperatorEmailModalComponent } from './participant-account-operator-email-modal/participant-account-operator-email-modal.component';
import { ParticipantAccountModalComponent } from './participant-account-operator-modal/participant-account-operator-modal.component';
import { Item } from '../../../../../../../shared/models/item';
import { AuthStore } from '../../../../../../../core/auth/auth.store';
import { TimezoneHelper } from '../../../../../../../shared/helpers/timezone-helper';
import * as moment from 'moment';


@Component({
    selector: 'participant-management-account-operator-editor',
    templateUrl: './participant-management-account-operator-editor.component.html'
})
export class ParticipantManagementAccountOperatorEditorComponent implements OnInit {
  @ViewChild('partcipantAccountOperatorModal') partcipantAccountOperatorModal: ParticipantAccountModalComponent;
  @ViewChild('accountOperatorEmail') accountOperatorEmail: ParticipantAccountOperatorEmailModalComponent;

  @Output('list') list: EventEmitter<any> = new EventEmitter();

  @ViewChild('alert') alert: GpAlertComponent;

  operator: any = {};
  accountDocument: string = '';
  isEditing: boolean = false;
  userId: string = '';
  campaignId: string = '';
  label: string = '';
  loading: boolean = false;
  rolesAccount: Array<Item> = [
    Item.of('MANAGER', 'Gerente'),
    Item.of('MASTER', 'Master'),
    Item.of('ADMIN', 'Administrador'),
  ];

  constructor(private authStore: AuthStore, private _campaignStore: CampaignStore, private _participantService: CampaignParticipantManagementService) { }

  ngOnInit() {
    this.campaignId = this._campaignStore.id;
  }
  get valueOfLabel() {
    return this.isBlocked ? this.label = "Sim" : this.label = "Não";
  }
  get isCreating() {
    return !this.isEditing;
  }

  get isBlocked() {
    return this.operator.blocked;
  }

  get disableFields() {
    return this.isEditing;
  }

  get disableOperatorRoleField() {
    return !this.canChangeRole;
  }

  get foundOperator() {
    return this.operator.accountOperatorId && this.operator.accountOperatorLoginId;
  }

  get canResetPassword() {
    return this.isEditing && !this.operator.isMigrated && this.authStore.role.PERMISSION_CAMPAIGNS_PARTICIPANTS_MANAGEMENT_OPERATORS_RESET_PASSOWRD;
  }

  get canChangeRole() {
    return this.isEditing && this.authStore.role.PERMISSION_CAMPAIGNS_PARTICIPANTS_MANAGEMENT_OPERATORS_CHANGE_ROLE;
  }

  get canChangeEmail() {
    return this.isEditing && !this.operator.isMigrated && this.authStore.role.PERMISSION_CAMPAIGNS_PARTICIPANTS_MANAGEMENT_OPERATORS_CHANGE_EMAIL;
  }

  get canRemoveOperator() {
    return this.isEditing && this.authStore.role.PERMISSION_CAMPAIGNS_PARTICIPANTS_MANAGEMENT_OPERATORS_REMOVE;
  }

  get canBlock() {
    return this.canRemoveOperator && !this.isBlocked;
  }

  get canUnblock() {
    return this.canRemoveOperator && this.isBlocked;
  }

  @Input()
  set _operator(operator: any) {
    if (!operator) {
      this.clear();
      return;
    }
    this.isEditing = true;
    this.setOperator(operator);
  }

  @Input()
  set _userId(id: string) {
    if (!id) {
      return;
    }
    this.userId = id;
  }

  @Input()
  set _accountDocument(accountDocument: string) {
    if (!accountDocument) {
      return;
    }
    this.accountDocument = accountDocument;
  }

  disableOperator() {
    this.partcipantAccountOperatorModal.openModal(this.operator);
  }

  activeOperator() {
    this.partcipantAccountOperatorModal.openActiveModal(this.operator);
  }

  findOperatorById() {
    if (this.operator.id || !this.operator.document) return;
    this._participantService.findAccountOperatorByDocument(this.userId, this._campaignStore.id, this.operator.document)
      .subscribe(
        operator => {
          if (operator) {
            operator.document = this.operator.document;
            operator.role = this.operator.role;
            this.operator = operator;
          }
        },
        err => {
          this.alert.handleAndShowError(err);
        }
      );
  }

  createOperator() {
    this.loading = true;
    this.operator.accountDocument = this.accountDocument;
    this._participantService.createAccountOperator(this.userId, this._campaignStore.id, this.operator).subscribe(
      response => {
        this.alert.showSuccess('Operador cadastrado com sucesso.');
        this.loading = false;
        this.back();
      },
      err => {
        this.loading = false;
        this.alert.showError(err);
      }
    );
  }

  resetOperatorPassword() {
    this.alert.confirm('Confirma o reset de senha deste operador?')
      .then(result => {
        if (result && result.value) {
          this.loading = true;
          this._participantService.resetAccountOperatorPassword(this.userId, this._campaignStore.id,
              this.operator.accountOperatorId, this.operator.accountOperatorLoginId)
            .subscribe(
              response => {
                this.alert.showSuccess('Senha resetada com sucesso, será enviada por email.');
                this.loading = false;
                this.back();
              },
              err => {
                this.loading = false;
                this.alert.showError(err);
              }
            );
        }
      });
  }

  updateOperatorRole() {
    this.loading = true;
    const payload: any = {}

    payload.fieldValue = this.operator.role;
    payload.timezone = TimezoneHelper.getTimezone();

    this._participantService.updateOperatorRole(this.userId, this._campaignStore.id,
      this.operator.accountOperatorId, this.operator.accountOperatorLoginId, payload).subscribe(
      response => {
        if (response)
          this.alert.showSuccess('Perfil do operador alterado com sucesso.')
        this.loading = false;
      },err => {
        this.loading = false;
        this.alert.showError(err);
      }
    )
  }

  clear() {
    this.operator = {};
    this.isEditing = false;
  }

  back() {
    this.list.emit({ isEditing: false });
    this.clear();
  }

  updateOperatorEmail() {
    this.accountOperatorEmail.openModal(this.operator);
  }

  onAccountOperatorUpdate() {
    this.findOperatorById();
  }

  setOperator(operator: any) {
    if (operator) {
      operator.isMigrated ? operator.isMigratedLabel = "Sim" : operator.isMigratedLabel = "Não";
      operator.migrationDate = moment(operator.migrationDate).format("DD/MM/YYYY");
    }
    this.operator = operator;
  }
}
