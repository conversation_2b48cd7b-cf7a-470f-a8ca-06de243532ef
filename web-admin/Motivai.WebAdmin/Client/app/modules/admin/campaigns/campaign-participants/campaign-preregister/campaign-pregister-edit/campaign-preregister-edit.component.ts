import { Component, Input, ViewChild } from '@angular/core';
import { CampaignPreRegisterService } from '../campaign-preregister.service';
import { DateTimezonePipe } from '../../../../../../shared/pipes/datetime-format.pipe';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { AuthStore } from '../../../../../../core/auth/auth.store';

@Component({
    selector: 'campaign-preregister-edit',
    templateUrl: './campaign-preregister-edit.component.html'
})
export class CampaignPreRegisterEditComponent {
    private _preRegisterId: string;
    private _campaignId: string;

    @ViewChild('alert') alert: GpAlertComponent;
    @ViewChild('refuseAlert') refuseAlert: GpAlertComponent;
    private dateFormat = new DateTimezonePipe();

    preRegister: any = {};
    refuseReason: string;
    loading: boolean = false;
    loadingApprove: boolean = false;
    loadingRefuse: boolean = false;
    loadingReintegration: boolean = false;


    metadata: Array<any> = [];

    constructor(private _preRegisterService: CampaignPreRegisterService, private _as: AuthStore) { }

    @Input()
    set campaignId(v: string) {
        if (v) {
            this._campaignId = v;
            this.loadMetadata();
        }
    }

    @Input()
    set preRegisterId(v: string) {
        if (v) {
            this._preRegisterId = v;
            this.findPreRegister();
        }
    }

    get canApprove() {
        return this.preRegister && this.preRegister.canApprove
          && this._as.role.PERMISSION_CAMPAIGNS_PREREGISTER_APPROVE;
    }

    get canRefuse() {
        return this.preRegister && this.preRegister.canRefuse
          && this._as.role.PERMISSION_CAMPAIGNS_PREREGISTER_REFUSE;
    }

    get hasIntegratedData() {
      return this.preRegister.integrated || this.preRegister.integratedDate || this.preRegister.integrationErrorMessage;
    }

    get hasIntegrationError() {
      return this.preRegister.integrationErrorMessage;
    }

    get canReintegrate() {
      return this.hasIntegratedData && !this.preRegister.integrated || this.hasIntegrationError;
    }

    private loadMetadata() {
      this._preRegisterService.getPreRegisterMetadata(this._campaignId)
        .subscribe(
          metadata => {
            if (metadata && metadata.length) {
              this.metadata = metadata;
            }
          }
        );
    }

    userMetadataFieldValue(fieldProperty: string) {
      if (!this.preRegister || !this.preRegister.userMetadata)
        return '';
      return this.preRegister.userMetadata[fieldProperty] || '';
    }

    findPreRegister() {
        if (this._preRegisterId) {
            this.loading = true;
            this._preRegisterService.findById(this._campaignId, this._preRegisterId).subscribe(
                preRegister => {
                    if (preRegister) {
                        preRegister.formattedCreationDate = this.dateFormat.transform(preRegister.createDate);
                        if (preRegister.approvedOn) {
                            preRegister.formattedApprovedOn = this.dateFormat.transform(preRegister.approvedOn);
                        }
                        if (preRegister.refusedOn) {
                            preRegister.formattedRefusedOn = this.dateFormat.transform(preRegister.refusedOn);
                        }

                        preRegister.formmatedIntegrated = (preRegister.integrated) ? "Sim" : "Não";

                        if(preRegister.integratedDate){
                          preRegister.formmatedIntegratedDate = this.dateFormat.transform(preRegister.integratedDate);
                        }
                    }

                    this.preRegister = preRegister || {};
                    this.loading = false;
                },
                err => {
                    this.alert.showSuccess(this.handleError(err));
                    this.loading = false;
                }
            );
        }
    }

    approve() {
        if (this.preRegister && this.preRegister.canApprove) {
          this.alert.confirm('ATENÇÃO: Ao aprovar o participante, seu acesso será liberado imediatamente à campanha. Deseja prosseguir?')
            .then(result => {
              if (result && result.value) {
                this.loadingApprove = true;
                this._preRegisterService.approve(this._campaignId, this._preRegisterId)
                  .subscribe(
                    approved => {
                        if (approved === true) {
                            this.alert.showSuccess('Participante aprovado com sucesso');
                        } else {
                            this.alert.showError('Ocorreu um erro ao aprovar o participante');
                        }
                        this.loadingApprove = false;
                    },
                    err => {
                        this.alert.showError(this.handleError(err));
                        this.loadingApprove = false;
                    }
                  );
              }
            });
        }
    }

    refuse() {
        if (this.preRegister && this.preRegister.canRefuse) {
          this.alert.confirm('ATENÇÃO: Ao recusar o participante, seu acesso não será liberado e o mesmo não conseguirá se cadastrar novamente na campanha. Deseja prosseguir?')
          .then(result => {
            if (result && result.value) {
              this.loadingRefuse = true;
              this._preRegisterService.refuse(this._campaignId, this._preRegisterId, this.refuseReason)
                .subscribe(
                  refused => {
                      if (refused === true) {
                          this.alert.showSuccess('Participante recusado com sucesso');
                      } else {
                          this.alert.showError('Não foi possível recusar o participante. Tente novamente!');
                      }
                      this.loadingRefuse = false;
                  },
                  err => {
                      this.alert.showError(this.handleError(err));
                      this.loadingRefuse = false;
                  }
                );
            }
          });
        }
    }

    integrationRetry() {
      if(this.canReintegrate){
        this.loadingReintegration = true;
        this._preRegisterService.integrationRetry(this._campaignId, this._preRegisterId)
          .subscribe(
            response =>{
              this.loadingReintegration = false;
              this.alert.showSuccess('Integração realizada com sucesso');
            },
            err => {
              this.alert.showError(this.handleError(err));
              this.loadingReintegration = false;
            }
          )
      }
    }

    closeAlert($event) {
        if ($event) {
            this.findPreRegister();
        }
    }

    handleError(err: any) {
        return err ? (err.message ? err.message : err.toString()) : 'Ocorreu um problema ao processar sua requisição';
    }
}
