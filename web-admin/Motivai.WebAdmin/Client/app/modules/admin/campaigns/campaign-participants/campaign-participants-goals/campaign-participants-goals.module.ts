import { CommonModule } from '@angular/common';
import { LOCALE_ID, NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { RouterModule } from '@angular/router';
import LazyLoadImageModule from 'ng-lazyload-image';
import { SharedModule } from '../../../../../shared/shared.module';
import { CampaignParticipantsGoalsImportEditComponent } from './campaign-participants-goals-import/campaign-participants-goals-import-edit/campaign-participants-goals-import-edit.component';
import { CampaignParticipantsGoalsImportListComponent } from './campaign-participants-goals-import/campaign-participants-goals-import-list/campaign-participants-goals-import-list.component';
import { CampaignParticipantsGoalsImportComponent } from './campaign-participants-goals-import/campaign-participants-goals-import.component';
import { CampaignsParticipantsGoalsActionsService } from './campaign-participants-goals-parametrizations/actions/campaigns-participants-goals-actions.service';
import { ExclusiveLevelGoalEditingComponent } from './campaign-participants-goals-parametrizations/actions/exclusive-level-goal-editing/exclusive-level-goal-editing.component';
import { AwardedTargetGoalEditingComponent } from './campaign-participants-goals-parametrizations/actions/awarded-target-goal-editing/awarded-target-goal-editing.component';
import { intervalGoalEditingComponent } from './campaign-participants-goals-parametrizations/actions/interval-goal-editing/interval-goal-editing.component';
import { CampaignParticipantsGoalsParametrizationsEditComponent } from './campaign-participants-goals-parametrizations/campaign-participants-goals-parametrizations-edit/campaign-participants-goals-parametrizations-edit.component';
import { CampaignParticipantsGoalsParametrizationsListComponent } from './campaign-participants-goals-parametrizations/campaign-participants-goals-parametrizations-list/campaign-participants-goals-parametrizations-list.component';
import { CampaignParticipantsGoalsParametrizationsComponent } from './campaign-participants-goals-parametrizations/campaign-participants-goals-parametrizations.component';
import { CampaignParticipantsGoalsRewardParametrizationsComponent } from './campaign-participants-goals-parametrizations/campaign-participants-goals-parametrizations-edit/campaign-participants-goals-reward-parametrizations/campaign-participants-goals-reward-parametrizations.component';
import { CampaignParticipantsGoalsComponent } from './campaign-participants-goals.component';
import { CampaignParticipantsGoalsService } from './campaign-participants-goals.service';
import { GoalEventEditingComponent } from './campaign-participants-goals-parametrizations/actions/goal-event-editing/goal-event-editing.component';
import { CampaignParticipantsGoalsResultsComponent } from './campaign-participants-goals-results/campaign-participants-goals-results.component';
import { CampaignParticipantsGoalsResultsDetailsPopup } from './campaign-participants-goals-results/campaign-participants-goals-results-details-popup/campaign-participants-goals-results-details-popup.component';


import { GoalPeriodsParametrizationComponent } from './campaign-participants-goals-parametrizations/campaign-participants-goals-parametrizations-edit/goal-periods-parametrization/goal-periods-parametrization.component';

@NgModule({
  declarations: [
    CampaignParticipantsGoalsComponent,
    CampaignParticipantsGoalsParametrizationsComponent,
    CampaignParticipantsGoalsParametrizationsListComponent,
    CampaignParticipantsGoalsParametrizationsEditComponent,
    CampaignParticipantsGoalsImportComponent,
    CampaignParticipantsGoalsImportListComponent,
    CampaignParticipantsGoalsImportEditComponent,
    CampaignParticipantsGoalsRewardParametrizationsComponent,
    CampaignParticipantsGoalsResultsComponent,
    CampaignParticipantsGoalsResultsDetailsPopup,
    AwardedTargetGoalEditingComponent,
    ExclusiveLevelGoalEditingComponent,
    intervalGoalEditingComponent,
    GoalEventEditingComponent,
    GoalPeriodsParametrizationComponent,
    CampaignParticipantsGoalsRewardParametrizationsComponent
  ],
  imports: [
    CommonModule,
    LazyLoadImageModule,
    SharedModule.forRoot(),
    RouterModule.forChild([
      {
        path: '', component: CampaignParticipantsGoalsComponent,
        children: [
          { path: 'configuracoes', component: CampaignParticipantsGoalsParametrizationsComponent },
          { path: 'importacao', component: CampaignParticipantsGoalsImportComponent },
          { path: 'resultados', component: CampaignParticipantsGoalsResultsComponent },
          { path: '', redirectTo: 'configuracoes', pathMatch: 'full' }
        ]
      }
    ])],
  exports: [RouterModule],
  providers: [
    [{ provide: LOCALE_ID, useValue: 'pt-br' }],
    CampaignParticipantsGoalsService,
    CampaignsParticipantsGoalsActionsService
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})

export class CampaignParticipantsGoalsModule { }
