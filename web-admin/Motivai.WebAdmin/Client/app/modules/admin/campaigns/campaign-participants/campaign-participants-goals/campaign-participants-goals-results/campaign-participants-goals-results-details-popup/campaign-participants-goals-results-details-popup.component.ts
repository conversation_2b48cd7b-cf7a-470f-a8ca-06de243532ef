import { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { GpModalComponent } from '../../../../../../../shared/components/gp-modal/gp-modal.component';

@Component({
    selector: 'campaign-participants-goals-results-details-popup',
    templateUrl: './campaign-participants-goals-results-details-popup.component.html'
})
export class CampaignParticipantsGoalsResultsDetailsPopup {
    @ViewChild('modal') modal: GpModalComponent;
    columns: Array<string> = [];
    fields: Array<string> = [];
    rows: Array<any> = [];
    public showDetails(ev: any){
        this.rows       = ev.nestedData;
        this.columns    = ev.nestedColumns;
        this.fields     = ev.nestedFields;
        this.modal.show();
    }
}