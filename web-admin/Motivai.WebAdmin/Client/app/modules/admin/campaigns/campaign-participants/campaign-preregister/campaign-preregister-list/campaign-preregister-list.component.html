<gp-card [first]="true" title="Pesquisa">
	<gp-form-row>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="CPF/CNPJ">
				<gp-input-mask name="document" mask="00000000000000" [(ngModel)]="parameters.document">
				</gp-input-mask>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4">
			<label>Status integração:</label>
			<gp-select [allowClear]="true" [items]="integrationStatus" [(ngModel)]="selectedIntegrationStatus" placeholder="Status integração" (change)="onIntegrationStatusChange()">
			</gp-select>
		</gp-form-col>
		<gp-form-col cols="12 3 3">
			<gp-spinner-button [search]="true" text="Pesquisar" icon="search" [loading]="loading" marginTop="26px" (click)="search()"></gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
	<div class="row">
		<div class="col-md-12">
			<gp-alert #alert></gp-alert>
		</div>
	</div>
</gp-card>

<gp-card title="Resultados encontrados">
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<gp-grid name="grid" [rows]="preRegisters" [columns]="[ 'Cadastro', 'CPF/CNPJ', 'Nome', 'Status' ]" [fields]="[ 'formattedCreateDate', 'document', 'name', 'status' ]"
				[showActive]="false" [showPagination]="true" [showTotalPages]="false"
				[showEdit]="true" [showDelete]="false" [loading]="loading"
				(onEdit)="editPreRegister($event)" (onPageChanged)="onPageChanged($event)" [pageSize]="20">
			   </gp-grid>
		</gp-form-col>
	</gp-form-row>
</gp-card>
