import { AuthStore } from '../../../../../../core/auth/auth.store';
import { ParticipantService } from '../../../../backoffice/participant.service';
import { CampaignStore } from '../../../campaign.store';
import { CampaignParticipantManagementService } from '../campaign-participant-management.service';
import { CorreiosService } from '../../../../../../core/services/correios.service';
import { ParticipantManagementEditComponent } from '../participant-management-edit/participant-management-edit.component';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpModalComponent } from '../../../../../../shared/components/gp-modal/gp-modal.component';
import { Component, ViewChild, OnInit, Input } from '@angular/core';
import { NgForm } from '@angular/forms';


@Component({
    selector: 'participant-management-address',
    templateUrl: './participant-management-address.component.html'
})
export class ParticipantManagementAddressComponent implements OnInit {
    @ViewChild('modal') modal: GpModalComponent;
    @ViewChild('addressForm') addressForm: NgForm;
    @ViewChild('alert') alert: GpAlertComponent;
    @ViewChild('participantManagement') participantManagement: ParticipantManagementEditComponent;

    addresses: any = [];
    address: any = {};
    participant: any;
    campaignId: string = null;
    loading: boolean = false;
    loadingModal: boolean = false;
    loadingAddress: boolean = false;
    loadingCep: boolean = false;
    addressFound: boolean = false;
    sending: boolean = false;


    constructor(private _correiosService: CorreiosService, private campaignParticipantService: CampaignParticipantManagementService,
        private _participantService: ParticipantService, private _campaignStore: CampaignStore, private _authStore: AuthStore) { }

    get canUpdateAddresses() {
        return this._authStore.role.PERMISSION_CAMPAIGNS_PARTICIPANTS_MANAGEMENT_UPDATE_ADDRESSES;
    }

    @Input()
    set _participantId(id: string) {
        if (!id) {
            return;
        }
        this.participant = id;
    }

    ngOnInit(): void {
        this.campaignId = this._campaignStore.id;
    }


    get renderCepStatus() {
        if (this.loadingCep)
            return 'Pesquisando CEP...';
        if (!this.addressFound)
            return 'CEP não encontrado. Termine de preencher o endereço!';
        return '';
    }

    findParticipantAddresses() {
        if (this.participant.userId && this.campaignId) {
            this.loading = true;

            this.campaignParticipantService.getAllAddressesParticipant(this.participant.userId, this.campaignId).subscribe(
                (addresses: any) => {
                    this.loading = false;
                    if (addresses) {
                      this.addresses = addresses;
                    }
                },
                (err: any) => {
                    this.alert.handleAndShowError(err);
                    this.loading = false;
                }
            );
        }
    }

    newAddress() {
        this.address = { active: true };
        this.showModal();
    }

    private findParticipantAddress() {
        this.loadingModal = true;

        if (!this.address || !this.address.id) {
            return;
        }
        this.loadingAddress = true;
        this._participantService.findAddress(this.participant.userId, this.campaignId, this.address.id)
            .subscribe(address => {
                this.loadingAddress = false;
                this.address = address;
            }, err => {
                this.loadingAddress = false;
                this.alert.handleAndShowError(err);
            });

        this.loadingModal = false;
    }

    editAddress(event: any) {
        this.address = event;
        this.findParticipantAddress();
        this.showModal();
    }

    removeAddressSelected(address: any) {
        if (!address || !address.id) {
            return;
        }
        this.alert.confirm(`Deseja remover este endereço: ${address.addressName}`)
            .then(result => {
                if (result.value) {
                    this.loading = true;
                    this._participantService.removeAddress(this.participant.userId, this.campaignId, address.id)
                        .subscribe(removed => {
                            this.findParticipantAddresses();
                        }, err => {
                            this.loading = false;
                            this.alert.handleAndShowError(err);
                        });
                }
            });
    }

    searchAddressByZipcode() {
        if (!this.address || !this.address.cep) {
            return;
        }
        this.loadingCep = true;
        this._correiosService.searchAddressByCep(this.address.cep)
            .subscribe(address => {
                if (address) {
                    if (!this.address)
                        this.address = {};

                    this.address.street = address.address;
                    this.address.city = address.city;
                    this.address.state = address.state;
                    this.address.uf = address.uf;
                    this.address.neighborhood = address.neighborhood;
                    this.handleAddress();
                } else {
                    this.addressFound = false;
                }
                this.loadingCep = false;
            }, err => {
                this.alert.handleAndShowError(err);
                this.loadingCep = false;
                this.addressFound = false;
            });
    }

    private handleAddress(): void {
        if ((this.isNullOrEmpty(this.address.street) || this.isNullOrEmpty(this.address.city))
            || (this.isNullOrEmpty(this.address.state) || this.isNullOrEmpty(this.address.uf))
            || this.isNullOrEmpty(this.address.neighborhood)) {
            this.addressFound = false;
        } else {
            this.addressFound = true;
        }
    }

    saveAddress() {
        this.sending = true;
        this.alert.clear();
        this._participantService.saveAddress(this.participant.userId, this.campaignId, this.address)
            .subscribe(address => {
                this.sending = false;
                if (address) {
                    this.address = address;
                    this.alert.showSuccess('Endereço salvo com sucesso');
                    this.resetAfterAddressSubmit();
                } else {
                    this.alert.showError('Ocorreu um erro ao gravar o endereço');
                }
            }, err => {
                this.alert.handleAndShowError(err);
                this.sending = false;
            });
    }

    public resetAddress() {
        this.address = {};
        this.alert.clear();
        this.addressForm.reset();

        Object.keys(this.addressForm.controls).forEach(key => {
            this.addressForm.controls[key].reset();
        });
    }

    clearEditFormAddress() {
        this.address = {};
    }

    resetAfterAddressSubmit() {
        this.findParticipantAddresses();
        this.clearEditFormAddress();
        this.hideModal();
    }

    private showModal() {
        this.modal.show();
    }

    private hideModal() {
        this.modal.hide();
    }

    private isNullOrEmpty(value: string) {
        return !(typeof value === 'string' && value.trim().length > 0);
    }
}
