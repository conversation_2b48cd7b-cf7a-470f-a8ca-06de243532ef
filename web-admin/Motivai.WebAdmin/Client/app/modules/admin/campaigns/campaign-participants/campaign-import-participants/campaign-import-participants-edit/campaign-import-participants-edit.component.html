<gp-card [first]="true" [last]="true" title="Importar Participantes" *ngIf="canImport">
	<gp-form-row>
		<gp-form-col cols="12 12 12 12">
			<gp-form-row>
				<gp-form-col cols="12 4 4">
					<gp-simple-input label="A primeira linha é um cabeçalho?">
						<select class="form-control" name="headerOnFirstLine" [(ngModel)]="parameters.headerOnFirstLine">
							<option value="1">Sim - colunas serão identificadas automaticamente</option>
							<option value="0">Não - colunas precisam estar na ordem correta</option>
						</select>
					</gp-simple-input>
				</gp-form-col>
			</gp-form-row>
			<gp-form-row>
				<gp-form-col cols="12 12 12">
					<gp-fileupload name="files" [multiple]="true" [data]="parameters" [csv]="true" [path]="uploadPath" (oncomplete)="onItemComplete($event)" #gpFile>
					</gp-fileupload>
				</gp-form-col>
			</gp-form-row>
			<gp-form-row>
				<gp-form-col cols="12 12 12">
					<gp-alert #alert></gp-alert>
				</gp-form-col>
			</gp-form-row>
			<hr />
			<gp-form-row>
				<gp-form-col cols="12 12 12">
					<p>Instruções de uso:</p>
					<p>1. O formato do arquivo deve ser <u>[.csv]</u>, com as colunas separadas por [;], <u>com ou sem cabeçalho</u> de acordo com o campo <strong>A primeira linha é um cabeçalho</strong>.</p>
					<p>2. O <u>[documento]</u> deve conter: 11 digitos para CPF; 14 digitos para CNPJ, sem formatação. <strong>Se atente a formatar a coluna de documento como <u>texto</u>, para manter os zeros a esquerda.</strong></p>
					<p>3. Caso o <u>[login]</u> não seja informado, o login será gerado com o documento do participante.</p>
					<p>4. Uma senha aleatória será gerada caso a coluna <u>[senha]</u> não seja preenchida. Um email será enviado para o participante com a senha gerada, e um arquivo estará disponível ao final da importação com todos os usuários e senhas geradas.</p>
					<p>5. Caso a primeira linha do arquivo seja um cabeçalho, automaticamente identificaremos as colunas através de sua respectiva descrição. Abaixo seguem as palavras reservadas:</p>
				</gp-form-col>
				<gp-form-col cols="12 4 4">
					<ul>
						<li>
							<strong>documento: </strong>documento, cpf, cnpj
						</li>
						<li>
							<strong>login: </strong>login, usuario
						</li>
						<li>
							<strong>senha: </strong>senha
						</li>
						<li>
							<strong>nome: </strong>nome, nome fantasia
						</li>
						<li>
							<strong>razão social: </strong>razao social
						</li>
						<li>
							<strong>e-mail: </strong>email, e-mail
						</li>
						<li>
							<strong>número de celular: </strong>celular
						</li>
						<li>
							<strong>matricula: </strong>matricula
						</li>
						<li *ngIf="_campaignStore.isFullCampaign">
							<strong>ranking: </strong>ranking
						</li>
						<li *ngIf="_campaignStore.isFullCampaign">
							<strong>grupo de participante: </strong>grupo
						</li>
						<li>
							<strong>CPF representante: </strong>cpf representante, cpf rep, representante
						</li>
						<li>
							<strong>status: </strong>ativo, status
							<br>Valores permitidos:
							<ul>
								<li>Ativo: ativo, sim, s</li>
								<li>Inativo: inativo, não, n</li>
							</ul>
						</li>
						<li>
							<strong>matriz: </strong>cnpj matriz, matriz
						</li>
						<li>
							<strong>sexo: </strong>sexo
							<br>Valores permitidos:
							<ul>
								<li>Masculino: M, masculino</li>
								<li>Feminino: F, feminio</li>
							</ul>
						</li>
						<li>
							<strong>estado civil: </strong>estado civil
							<br>Valores:
							<ul>
								<li>Casado(a)</li>
								<li>Solteiro(a)</li>
								<li>Divorciado(a)</li>
								<li>Viuvo(a), Viúvo(a)</li>
								<li>Uniao Estavel, União Estável</li>
							</ul>
						</li>
					</ul>
				</gp-form-col>
				<gp-form-col cols="12 8 8">
					<img src="/assets/img/imports/importacao_participante_dinamica.png" style="width: 650px;" alt="Exemplo de arquivo de participantes com cabeçalho" /> <br /><br />
				</gp-form-col>
				<gp-form-col cols="12 12 12">
					<p>Importante: as colunas não identificadas pelo sistema também serão salvas e apresentadas em relatórios e outras funcionalidades do sistema.</p>
				</gp-form-col>
				<gp-form-col cols="12 4 4">
					<p>6. Caso o arquivo seja importado sem cabeçalho, as colunas devem seguir a seguinte ordem:</p>
					<ul>
						<li>
							<strong>A: </strong>documento
						</li>
						<li>
							<strong>B: </strong>login
						</li>
						<li>
							<strong>C: </strong>senha
						</li>
						<li>
							<strong>D: </strong>nome/nome fantasia
						</li>
						<li>
							<strong>E: </strong>razão social
						</li>
						<li>
							<strong>F: </strong>email
						</li>
						<li *ngIf="_campaignStore.isFullCampaign">
							<strong>G: </strong>ranking
						</li>
						<li *ngIf="_campaignStore.isFullCampaign">
							<strong>H: </strong>grupo do participante
						</li>
					</ul>
				</gp-form-col>
				<gp-form-col cols="12 8 8">
					<img src="/assets/img/imports/modelo_planilha_participante.png" style="width: 650px;" alt="Exemplo de arquivo de participantes" /> <br /><br />
					<a href="/assets/files/modelo_importacao_participante.csv">Clique aqui para baixar um modelo de planilha</a>
				</gp-form-col>
				<gp-form-col cols="12 12 12">
					<p>7. Apenas o <u>[documento]</u> é obrigatório (CPF ou CNPJ)</p>
				</gp-form-col>
			</gp-form-row>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-tab-panel headerText="Detalhes da Importação" [noMargin]="true" *ngIf="canView">
	<gp-alert [overlay]="true" #alert></gp-alert>
	<gp-form-row>
		<gp-form-col cols="12 12">
			<gp-card>
				<gp-form-row>
					<gp-form-col cols="12 4">
						<gp-simple-input label="Código do Lote">
							<input type="text" name="batchNumber" class="form-control" disabled [ngModel]="batchImport.batchNumber" />
						</gp-simple-input>
					</gp-form-col>
					<gp-form-col cols="12 6 4">
						<gp-simple-input label="A primeira linha é um cabeçalho?">
							<input type="text" name="headerOnFirstLine" class="form-control" disabled [ngModel]="batchImport.headerOnFirstLine ? 'Sim' : 'Não'" />
						</gp-simple-input>
					</gp-form-col>
					<gp-form-col cols="12 4">
						<gp-simple-input label="Status">
							<input type="text" name="status" class="form-control" disabled [ngModel]="batchImport.status" />
						</gp-simple-input>
					</gp-form-col>
				</gp-form-row>
			</gp-card>
		</gp-form-col>
	</gp-form-row>

	<gp-form-row>
		<gp-form-col cols="12">
			<gp-card>
				<gp-form-row>
					<gp-form-col cols="12 4">
						<gp-simple-input label="Total de linhas do lote">
							<input type="text" name="totalLines" class="form-control" disabled [ngModel]="batchImport.totalLines | number" />
						</gp-simple-input>
					</gp-form-col>
					<gp-form-col cols="12 4" *ngIf="!isImporting">
						<gp-simple-input label="Quantidade de linhas lidas sem erros">
							<input type="text" name="totalLines" class="form-control" disabled [ngModel]="batchImport.totalSuccessImported | number" />
						</gp-simple-input>
					</gp-form-col>
				</gp-form-row>
			</gp-card>
		</gp-form-col>
	</gp-form-row>

	<gp-form-row *ngIf="showRefuseButton">
		<gp-form-col cols="12" [inputGroup]="false">
			<gp-card>
				<gp-spinner-button type="button" [pink]="true" icon="close" text="Reprovar lote" pull="right"
					[loading]="loadingRefuse" (click)="refuseBatch()">
				</gp-spinner-button>
			</gp-card>
		</gp-form-col>
	</gp-form-row>

	<gp-form-row>
		<gp-form-col cols="12 4 4">
			<gp-card>
				<gp-form-row>
					<gp-form-col cols="12 12 12">
						<h3 *ngIf="isImporting">
							Lendo Arquivo: <span class="text-success" title="Quantidade de linhas lidas sem erros do arquivo">{{ batchImport.totalSuccessImported | number }}</span>
						</h3>
						<h3 *ngIf="!isImporting">
							Sucesso: <span class="text-success" title="Total de participantes que serão importados na aprovação">{{ batchImport.totalSuccessCompleted | number }}</span>
						</h3>
						<hr />
						<a *ngIf="showApprovalLink" style="text-decoration: none;" (click)="approval()">
							<h4 class="text-success">{{ !loadingApproval ? 'APROVAR' : 'PROCESSANDO...' }}</h4>
						</a>
						<a *ngIf="showExportUsers" style="text-decoration: none;" (click)="exportSuccess()">
							<h4 class="text-success">{{ !loadingExport ? 'EXPORTAR USUÁRIOS' : 'PROCESSANDO...' }}</h4>
						</a>
						<h4 *ngIf="showProcessing" style="color: #CCC">PROCESSANDO...</h4>
						<h4 *ngIf="isRefused" style="color: #CCC">REPROVADO</h4>
					</gp-form-col>
				</gp-form-row>
			</gp-card>
		</gp-form-col>

		<gp-form-col cols="12 4 4">
			<gp-card>
				<gp-form-row>
					<gp-form-col cols="12 12 12">
						<h3>Avisos:
							<span class="text-warning">0</span>
						</h3>
						<hr />
						<h4 *ngIf="canApprove" style="color: #CCC">DETALHES</h4>
					</gp-form-col>
				</gp-form-row>
			</gp-card>
		</gp-form-col>

		<gp-form-col cols="12 4 4">
			<gp-card>
				<gp-form-row>
					<gp-form-col cols="12 12 12">
						<h3>Erros:
							<span class="text-danger">{{ batchImport.totalErrorImported | number }}</span>
						</h3>
						<hr />
						<h4 *ngIf="batchImport.totalErrorImported === 0 && canApprove" style="color: #CCC">DETALHES</h4>
						<a *ngIf="batchImport.totalErrorImported > 0 && canApprove" style="text-decoration: none;" (click)="exportErrors()">
							<h4 class="text-danger">DETALHES</h4>
						</a>
					</gp-form-col>
				</gp-form-row>
			</gp-card>
		</gp-form-col>
	</gp-form-row>

	<gp-form-row *ngIf="showTemporaryParticipants">
		<gp-form-col cols="12 12 12">
			<gp-card title="Registros com sucesso" [last]="true">
				<gp-form-row>
					<gp-form-col cols="12 12 12">
						<gp-grid name="grid" [rows]="imports" [showActive]="true" activeHeaderText="Processado"
							[columns]="[ 'Documento', 'Nome', 'Login', 'E-mail' ]" [fields]="[ 'document', 'name', 'login', 'email' ]"
							activeField="processed" activeText="Sim" inactiveText="Não" [showPagination]="true" [pageSize]="limit" [showTotalPages]="false" [showEdit]="false"
							[showDelete]="enableDeleteButton" [loading]="loadingGrid" (onDelete)="deleteTemporaryParticipant($event)"
							(onPageChanged)="onPageChanged($event)">
						</gp-grid>
					</gp-form-col>
				</gp-form-row>
			</gp-card>
		</gp-form-col>
	</gp-form-row>
</gp-tab-panel>
