<gp-alert #alert [overlay]="true"></gp-alert>
<spinner [overlay]="true" [show]="loading"></spinner>

<gp-card title="Parametrizações dos Alvos">
    <gp-form-row>    
        <gp-form-col cols="12 6">
            <label>Habilitar bônus ao atingir a quantidade mínima de alvos
                <gp-tooltip spanClass="text-primary">
                    <div style="text-align:justify">
                        Se habilitado, o bônus creditado será o configurado nessa seção quando a quantidade mínima de alvos for atingida.
                    </div>
                </gp-tooltip>
            </label>
            <div>
                <gp-switch name="enableMinimumTargetAmountToAchieve" [disabled]="hasStarted" 
                    [(ngModel)]="goalAchievedAwarded.parametrizations.enableMinimumTargetAmountToAchieve">
                </gp-switch>
            </div>
        </gp-form-col>
    </gp-form-row>

    <gp-form-row>
        <gp-form-col cols="12 6" *ngIf="goalAchievedAwarded.parametrizations.enableMinimumTargetAmountToAchieve">
            <label>Quantidade mínima de alvos</label>
            <gp-input-mask required name="minimumTargetAmountToAchieve" [onlyInteger]="true" 
                [(ngModel)]="goalAchievedAwarded.parametrizations.minimumTargetAmountToAchieve">
            </gp-input-mask>
        </gp-form-col>
    </gp-form-row>

    <gp-form-row *ngIf="goalAchievedAwarded.parametrizations.enableMinimumTargetAmountToAchieve">
        <gp-form-col cols="12 6">
            <gp-simple-input label="Tipo da pontuação">
                <select class="form-control" [disabled]="hasStarted" [(ngModel)]="goalAchievedAwarded.parametrizations.rewardType">
                    <option value="Currency">Reais (R$)</option>
                    <option value="Points">Pontos</option>
                </select>
            </gp-simple-input>
        </gp-form-col>
    
        <gp-form-col cols="12 6">
            <label>Valor do bônus</label>
            <gp-input-mask required name="rewardValue" [onlyDecimal]="true" [integers]="6" [decimais]="2" placeholder="0,00"
                [disabled]="hasStarted" [(ngModel)]="goalAchievedAwarded.parametrizations.rewardValue">
            </gp-input-mask>
        </gp-form-col>
    </gp-form-row>
</gp-card>

<gp-card title="Alvos da Meta">
    <gp-spinner-button bootstrapClass="primary" icon="plus" [inline]="false" pull="right" text="Adicionar alvo"
        (click)="newGoalTargetAwarded()" *ngIf="!hasStarted">
    </gp-spinner-button>
    
    <gp-form-row>
        <gp-form-col cols="12">
            <gp-grid [rows]="awardedTargetGoals" [columns]="['Código', 'Descrição', 'Porcentagem Atingimento', 'Meta Configurada', 'Meta Para Atingir', 'Bônus']"
                [fields]="['code', 'description', 'minimumPercentageToReachOfGoalTargetValueToAward', 'configuredTargetValue', 'targetValue', 'rewardValue']"
                [loading]="loading" [showActive]="true" [showEdit]="true" (onEdit)="editGoalTargetAwarded($event)"
                [showDelete]="false">
            </gp-grid>
        </gp-form-col>
    </gp-form-row>
    
    <gp-modal title="Alvo da Meta" width="700px" #goalTargetAwarded>
        <gp-form-row>
            <gp-form-col cols="12 6">
                <label>Ativo</label>
                <div>
                    <gp-switch name="active" [disabled]="hasStarted" [(ngModel)]="targetAwarded.active"></gp-switch>
                </div>
            </gp-form-col>

            <gp-form-col cols="12 6">
                <label>Habilitar bônus
                    <gp-tooltip spanClass="text-primary">
                        <div style="text-align:justify">
                            Se habilitado, ao atingir o alvo o bônus será creditado.
                        </div>
                    </gp-tooltip>
                </label>
                <div>
                    <gp-switch name="enableReward" [disabled]="hasStarted" [(ngModel)]="targetAwarded.enableReward"></gp-switch>
                </div>
            </gp-form-col>
        </gp-form-row>

        <gp-form-row>
            <gp-form-col cols="12 6">
                <label>Permitir creditar o bônus dos demais alvos
                    <gp-tooltip spanClass="text-primary">
                        <div style="text-align:justify">
                           Faz a liberação do bônus dos demais alvos que foram atingidos, mas estão com o bônus desabilitados.
                        </div>
                    </gp-tooltip>
                </label>
                <div>
                    <gp-switch name="enableRewardFromOtherTargets" [disabled]="hasStarted" [(ngModel)]="targetAwarded.enableRewardFromOtherTargets"></gp-switch>
                </div>
            </gp-form-col>

            <gp-form-col cols="12 6">
                <label>Ocultar no site campanha
                    <gp-tooltip spanClass="text-primary">
                        <div style="text-align:justify">
                           Oculta o alvo na tabela no site da campanha.
                        </div>
                    </gp-tooltip>
                </label>
                <div>
                    <gp-switch name="hideFromView" [disabled]="hasStarted" [(ngModel)]="targetAwarded.hideFromView"></gp-switch>
                </div>
            </gp-form-col>
        </gp-form-row>

        <gp-form-row>
            <gp-form-col cols="12 6">
                <gp-simple-input label="Código">
                    <input type="text" name="code" class="form-control" [disabled]="hasStarted"
                    [(ngModel)]="targetAwarded.code" />
                </gp-simple-input>
            </gp-form-col>
            
            <gp-form-col cols="12 6">
                <gp-simple-input label="Descrição">
                    <input type="text" name="description" class="form-control" [(ngModel)]="targetAwarded.description" />
                </gp-simple-input>
            </gp-form-col>
        </gp-form-row>
        
        <gp-form-row>
            <gp-form-col cols="12 6">
                <label>Valor da meta</label>
                <gp-input-mask required name="configuredTargetValue" [onlyDecimal]="true" [integers]="7" [decimais]="2"
                    placeholder="0.00" [disabled]="hasStarted" [(ngModel)]="targetAwarded.configuredTargetValue"></gp-input-mask>
            </gp-form-col>
    
            <gp-form-col cols="12 6">
                <gp-simple-input label="Porcentagem mínima para atingimento">
                    <gp-input-mask name="minimumPercentageToReachOfGoalTargetValueToAward" 
                        [onlyDecimal]="true" [integers]="7" [decimais]="2" [disabled]="hasStarted"
                        [(ngModel)]="targetAwarded.minimumPercentageToReachOfGoalTargetValueToAward">
                    </gp-input-mask>
                </gp-simple-input>
            </gp-form-col>
        </gp-form-row>
    
        <gp-form-row>
            <gp-form-col cols="12 6">
                <gp-simple-input label="Tipo da pontuação">
                    <select class="form-control" [disabled]="hasStarted" [(ngModel)]="targetAwarded.rewardType">
                        <option value="Currency">Reais (R$)</option>
                        <option value="Points">Pontos</option>
                    </select>
                </gp-simple-input>
            </gp-form-col>
    
            <gp-form-col cols="12 6">
                <label>Valor do bônus</label>
                <gp-input-mask required name="points" [onlyDecimal]="true" [integers]="6" [decimais]="2" placeholder="0,00"
                    [disabled]="hasStarted" [(ngModel)]="targetAwarded.rewardValue"></gp-input-mask>
            </gp-form-col>
        </gp-form-row>
    
        <div *ngIf="isEditing">
            <gp-form-row>
                <gp-form-col cols="12 6">
                    <label>Selecione a imagem do alvo</label>
                    <gp-fileupload name="fileImage" label="Selecionar imagem" [images]="true"
                        (oncomplete)="onComplete($event)" #gpFile>
                    </gp-fileupload>
                </gp-form-col>
    
                <gp-form-col cols="12 6">
                    <gp-spinner-button type="button" *ngIf="gpFile.hasSelectedFile()" [inline]="true"
                        bootstrapClass="primary" text="Upload da imagem" icon="send" pull="right"
                        loadingText="Salvando imagem..." [loading]="uploading" (click)="uploadImage()">
                    </gp-spinner-button>
                    <gp-spinner-button type="button" *ngIf="hasImageUrl" [inline]="true"
                        bootstrapClass="danger" text="Remover Imagem" icon="close" pull="right"
                        loadingText="Removendo imagem..." [loading]="removing" (click)="removeImage()">
                    </gp-spinner-button>
                </gp-form-col>
            </gp-form-row>
    
            <gp-form-row>
                <gp-form-col cols="12 12 12">
                    <div class="row bottom-m1" *ngIf="hasImageUrl">
                        <div grid="12 12">
                            <img class="banner" defaultImage="/assets/img/img-loader.gif" [lazyLoad]="imageUrl"
                                title="Imagem do item" style="width:100%;" />
                        </div>
                    </div>
                </gp-form-col>
            </gp-form-row>
        </div>
    
        <gp-form-row>
            <gp-form-col cols="12 12 12 12">
                <gp-spinner-button bootstrapClass="primary" icon="send" pull="right" [text]="textButtonSave"
                    [disabled]="loading" (click)="addOrUpdate()">
                </gp-spinner-button>
            </gp-form-col>
        </gp-form-row>
    </gp-modal>
</gp-card>
