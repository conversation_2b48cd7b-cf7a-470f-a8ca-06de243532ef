import { Component, OnInit, ViewChild } from '@angular/core';
import { TabsetComponent } from 'ng2-bootstrap';
import { CampaignParticipantsGoalsImportListComponent } from './campaign-participants-goals-import-list/campaign-participants-goals-import-list.component';
import { CampaignParticipantsGoalsImportEditComponent } from './campaign-participants-goals-import-edit/campaign-participants-goals-import-edit.component';

@Component({
  selector: 'campaign-participants-goals-import',
  templateUrl: './campaign-participants-goals-import.component.html'
})
export class CampaignParticipantsGoalsImportComponent implements OnInit {
  @ViewChild('tabs') tabs: TabsetComponent;
  @ViewChild('list') list: CampaignParticipantsGoalsImportListComponent;
  @ViewChild('edit') edit: CampaignParticipantsGoalsImportEditComponent;

  constructor() { }

  ngOnInit() {
  }


  showBatchCampaignParticipantsGoalDetails(event: any) {
    this.edit.batchId = event.id;
    this.edit.findCampaignGoalsBatchById();
    this.tabs.tabs[1].active = true;
  }

  reloadBatchesCampaignParticipantsGoals() {
    this.edit.clear();
    this.list.params = { };
    this.list.findCampaignParticipantsGoalsBatches();
  }


}
