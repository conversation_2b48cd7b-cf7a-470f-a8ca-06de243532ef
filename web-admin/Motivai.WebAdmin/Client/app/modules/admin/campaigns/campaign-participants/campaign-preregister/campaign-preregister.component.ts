import { ActivatedRoute } from '@angular/router';
import { Component, OnInit, ViewChild } from '@angular/core';
import { TabsetComponent } from 'ng2-bootstrap';

import { AuthStore } from '../../../../../core/auth/auth.store';
import { CampaignPreRegisterListComponent } from './campaign-preregister-list/campaign-preregister-list.component';
import { CampaignPreRegisterEditComponent } from './campaign-pregister-edit/campaign-preregister-edit.component';

@Component({
    selector: 'campaign-preregister',
    templateUrl: './campaign-preregister.component.html',
})
export class CampaignPreRegisterComponent implements OnInit {
    campaignId: string;
    preRegisterId: string;

    @ViewChild('tabs') tabs: TabsetComponent;
    @ViewChild('listComponent') listComponent: CampaignPreRegisterListComponent;
    @ViewChild('editComponent') editComponent: CampaignPreRegisterEditComponent;

    constructor(private route: ActivatedRoute, private _as: AuthStore) { }

    ngOnInit() {
        this.tabs.tabs[1].disabled = true;
        if (this.route.parent != null && this.route.parent.parent != null) {
            this.route.parent.parent.params.subscribe((params: any) => {
                this.campaignId = params['id'];
            });
        }
    }

    onEdit($event) {
        if ($event) {
            this.preRegisterId = $event.id;
            this.tabs.tabs[1].disabled = false;
            this.tabs.tabs[1].active = true;
        }
    }

    refreshGrid() {
        this.tabs.tabs[1].disabled = true;
    }
}
