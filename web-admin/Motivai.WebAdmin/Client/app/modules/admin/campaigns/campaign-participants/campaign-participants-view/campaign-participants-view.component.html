<gp-card *ngIf="showMenublock" [noPaddingTop]="true" [noPaddingBottom]="true">
	<div class="row">
		<accordion [closeOthers]="true">
			<accordion-group panelClass="b0 mb-sm panel-default no-shadow" [isOpen]="true">
				<div accordion-heading>
					<label><em class="fa fa-navicon"></em> Features de Participantes</label>
				</div>

				<div class="row menublock text-center">
					<gp-menublock-item *ngIf="canListGroups" size="xs" icon="sitemap" color="text-warning" text="Grupos"
						routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'participantes', 'grupos-de-participantes']">
					</gp-menublock-item>

					<gp-menublock-item *ngIf="canViewParticipantsCredify" text="Credify" icon="search" size="xs"
						color="text-warning" routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'participantes', 'credify']">
					</gp-menublock-item>

					<gp-menublock-item *ngIf="canViewMetadataPages" text="Campos Dinâmicos" icon="code" size="xs"
						color="text-warning" routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'participantes', 'campos-dinamicos']">
					</gp-menublock-item>

					<gp-menublock-item *ngIf="canListImports" size="xs" icon="cloud-upload" color="text-warning"
						text="Importação" routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'participantes', 'importar-participantes']">
					</gp-menublock-item>

					<gp-menublock-item *ngIf="canListPreregister" size="xs" icon="address-book" color="text-warning"
						text="Pré-Cadastro" routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'participantes', 'pre-cadastro']">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="id-card" color="text-warning" text="Gestão de Participantes"
						routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'participantes', 'controle-gestao']">
					</gp-menublock-item>

					<gp-menublock-item *ngIf="canViewRankingParametrization" size="xs" icon="sort-numeric-asc"
						color="text-warning" text="Rankings" routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'participantes', 'ranking']">
					</gp-menublock-item>

					<gp-menublock-item *ngIf="canViewParticipantsGoals" size="xs" icon="trophy" color="text-warning" text="Metas"
						routerLinkActive="active" [routerLink]="['/campanha', campaignId, 'participantes', 'metas']">
					</gp-menublock-item>
				</div>
			</accordion-group>
		</accordion>
	</div>
</gp-card>
<router-outlet></router-outlet>
