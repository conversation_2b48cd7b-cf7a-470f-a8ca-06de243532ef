import { <PERSON><PERSON>nent, OnInit, ViewChild, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs/Subscription';

import { CampaignStore } from '../../../../campaign.store';

import { CampaignFeatureSelectorComponent } from '../../../../../../../shared/components/business/campaigns/campaign-feature-selector/campaign-feature-selector/campaign-feature-selector.component';
import { getFeatureTypeDescription } from '../../../../../../../shared/components/business/campaigns/campaign-feature-selector/models/campaign-feature-types.enum';
import { CampaignFeature } from '../../../../../../../shared/components/business/campaigns/campaign-feature-selector/models/campaign-feature.model';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpModalComponent } from '../../../../../../../shared/components/gp-modal/gp-modal.component';
import { FormatHelper } from '../../../../../../../shared/formatters/format-helper';
import { RxjsHelpers } from '../../../../../../../shared/helpers/rxjs-helpers';
import { Item } from '../../../../../../../shared/models/item';

import { GoalFormat } from '../../models/goal-format';
import { RewardMethod } from '../../models/reward-method';
import { EventTypes } from '../../models/event-types';

import { CampaignParticipantsGoalsRewardParametrizationsComponent } from './campaign-participants-goals-reward-parametrizations/campaign-participants-goals-reward-parametrizations.component';
import { GoalPeriodsParametrizationComponent } from './goal-periods-parametrization/goal-periods-parametrization.component';

import { ExclusiveLevelGoalEditingComponent } from '../actions/exclusive-level-goal-editing/exclusive-level-goal-editing.component';
import { AwardedTargetGoalEditingComponent } from '../actions/awarded-target-goal-editing/awarded-target-goal-editing.component';
import { intervalGoalEditingComponent } from '../actions/interval-goal-editing/interval-goal-editing.component';
import { GoalEventEditingComponent } from '../actions/goal-event-editing/goal-event-editing.component';
import { PeriodType } from '../actions/models/period-type';

import { CampaignParticipantsGoalsService } from '../../campaign-participants-goals.service';
import { ProgressViewerType } from '../../models/progress-viewer-type';

@Component({
  selector: 'campaign-participants-goals-parametrizations-edit',
  templateUrl: './campaign-participants-goals-parametrizations-edit.component.html'
})
export class CampaignParticipantsGoalsParametrizationsEditComponent implements OnInit, OnDestroy {
  @ViewChild('alert') alert: GpAlertComponent;
  @ViewChild('goalModal') goalModal: GpModalComponent;
  @ViewChild('eventModal') eventModal: GoalEventEditingComponent;
  @ViewChild('awardedTargetGoalModal') awardedTargetGoalModal: AwardedTargetGoalEditingComponent;
  @ViewChild('exclusiveLevelGoalModal') exclusiveLevelGoalModal: ExclusiveLevelGoalEditingComponent;
  @ViewChild('intervalGoalModal') intervalGoalModal: intervalGoalEditingComponent;
  @ViewChild('featureSelectorModal') featureSelectorModal: CampaignFeatureSelectorComponent;
  @ViewChild('rewardParams') rewardParams: CampaignParticipantsGoalsRewardParametrizationsComponent;
  @ViewChild('goalPeriodParamsModal') goalPeriodParamsModal: GoalPeriodsParametrizationComponent;

  saving: boolean = false;
  loading: boolean = false;
  starting: boolean = false;
  loadingEvents: boolean = false;
  loadingGoalsTargetsAwarded: boolean = false;
  loadingExclusiveLevelGoals: boolean = false;
  loadingIntervalsGoals: boolean = false;
  rewardType: string = "";

  campaignId: string = '';

  goalFormats: Array<Item> = [
    Item.of(GoalFormat.BY_GOAL_LEVELS, 'Por Faixa Acumulativa'),
    Item.of(GoalFormat.BY_TARGET_AWARDED, 'Por Bateu Levou'),
    Item.of(GoalFormat.BY_EXCLUSIVE_LEVEL, 'Por Faixa Exclusiva'),
    Item.of(GoalFormat.BY_INTERVALS, 'Por Faixa com Intervalos')
  ];

  PeriodTypes: Array<Item> = [
    Item.of(PeriodType.NONE, 'Nenhum'),
    Item.of(PeriodType.WEEKLY, 'Semanal'),
    Item.of(PeriodType.MONTHLY, 'Mensal'),
    Item.of(PeriodType.YEARLY, 'Anual'),
    Item.of(PeriodType.CUSTOM, 'Customizado')
  ];

  progressViewerTypes: Array<Item> = [];

  eventTypes: Array<Item> = [
    Item.of("CRESC", 'Crescente'),
    Item.of("DESC", 'Descrecente')
  ];

  processTypes: Array<Item> = [
    Item.of("PROCESS_ONLY_GOALS", 'Processar Metas'),
    Item.of("PROCESS_ONLY_EVENTS", 'Processar Eventos'),
    Item.of("PROCESS_BOTH", 'Processar Metas e Eventos')
  ];

  campaignsMechanics: Array<Item> = [];

  parametrizationId: string = '';

  goalLevel: any = {
    rewardAmount: {}
  };
  events: any[] = [];
  goalsLevels: any[] = [];
  intervalsGoals: any[] = [];
  exclusiveLevelsGoals: any[] = [];
  goalAchievedAwarded: any = {};

  customGoalPeriods: any[] = [];

  goalParametrizations: any = {
    settings: {
      period: false,
      accumulation: false,
      enablePreviousRecheadLevelsBonus: false,
      campaignSiteMenuItem: {},
      enableOnCampaignSiteMenu: false
    },
    parametrizations: {
      processType: 'PROCESS_ONLY_GOALS',
      goalsOrdinationType: 'DESC'
    }
  };
  private disabledCreateParticipantGoalsWhenNotConfigured: boolean = false;
  private campaign$: Subscription;

  constructor(private campaignStore: CampaignStore, private campaignParticipantsGoalsService: CampaignParticipantsGoalsService) { }

  ngOnInit() {
    this.campaign$ = this.campaignStore.asObservable
      .subscribe(id => {
        this.campaignId = id;
        this.loadCampaignMechanics(this.campaignId);
      });
  }

  ngOnDestroy(): void {
    RxjsHelpers.unsubscribe(this.campaign$);
  }

  get hasPeriod() {
    return this.goalParametrizations.settings.period;
  }

  get hasPeriodType() {
    return this.periodType == PeriodType.WEEKLY || this.periodType == PeriodType.MONTHLY || this.periodType == PeriodType.YEARLY;
  }

  get canInitiateGoal() {
    return this.parametrizationId && !this.goalParametrizations.started;
  }

  get hasGoalsLevels() {
    return this.goalParametrizations.goalsLevels && this.goalParametrizations.goalsLevels.length > 0;
  }

  get hasStarted() {
    return this.goalParametrizations && this.goalParametrizations.started;
  }

  get goalFormatByLevels() {
    return this.goalParametrizations.parametrizations.goalFormat == GoalFormat.BY_GOAL_LEVELS;
  }

  get goalFormatByTargetAwarded() {
    return this.goalParametrizations.parametrizations.goalFormat == GoalFormat.BY_TARGET_AWARDED;
  }

  get isExclusiveLevel() {
    return this.goalParametrizations.parametrizations.goalFormat == GoalFormat.BY_EXCLUSIVE_LEVEL;
  }

  get isIntervalsFormat() {
    return this.goalParametrizations.parametrizations.goalFormat == GoalFormat.BY_INTERVALS;
  }

  get isAllowPeriod() {
    return this.goalParametrizations.parametrizations.goalFormat === GoalFormat.BY_INTERVALS ||
      this.goalParametrizations.parametrizations.goalFormat === GoalFormat.BY_EXCLUSIVE_LEVEL ||
      this.goalParametrizations.parametrizations.goalFormat === GoalFormat.BY_TARGET_AWARDED;
  }

  get groupedGoalsLayout() {
    return this.goalParametrizations.parametrizations.goalFormat == GoalFormat.BY_EXCLUSIVE_LEVEL
      || this.goalParametrizations.parametrizations.goalFormat == GoalFormat.BY_INTERVALS
      || this.goalParametrizations.parametrizations.goalFormat == GoalFormat.BY_TARGET_AWARDED;
  }

  get showGoalLevels() {
    return this.parametrizationId && (this.goalFormatByLevels || this.hasGoalsLevels);
  }

  get periodType() {
    if (this.goalParametrizations.settings && this.goalParametrizations.settings.periodType) {
      return this.goalParametrizations.settings.periodType
    }
    return PeriodType.NONE;
  }

  get hasPeriodTypeForGoal() {
    return this.periodType != PeriodType.NONE;
  }

  get isPeriodCustomType(): boolean {
    return this.periodType === PeriodType.CUSTOM;
  }

  get hasCampaignFeatureLinked(): boolean {
    return this.goalParametrizations && this.goalParametrizations.parametrizations && this.goalParametrizations.parametrizations.campaignFeatures
      && this.goalParametrizations.parametrizations.campaignFeatures.length > 0;
  }

  get hasExclusiveLevelToProcess(): boolean {
    if (!this.goalParametrizations.parametrizations.processType)
      return false;
    return this.goalParametrizations.parametrizations.processType != 'PROCESS_ONLY_EVENTS';
  }

  get hasEventsToProcess(): boolean {
    if (!this.goalParametrizations.parametrizations.processType)
      return false;
    return this.goalParametrizations.parametrizations.processType != 'PROCESS_ONLY_GOALS';
  }

  get isRewardResultProcessTypeViewOnly(): boolean {
    if (!this.goalParametrizations.parametrizations.rewardResultProcessType)
      return false;
    return this.goalParametrizations.parametrizations.rewardResultProcessType === 'VIEW_ONLY';
  }

  findCampaignParticipantsGoal() {
    this.loading = true;
    this.campaignParticipantsGoalsService.findCampaignParticipantsGoalParametrizations(this.campaignId, this.parametrizationId).subscribe(
      response => {
        if (response) {
          this.goalParametrizations = response;
          if (this.hasCampaignFeatureLinked) {
            response.parametrizations.campaignFeatures.forEach(f => {
              f.featureTypeDescription = getFeatureTypeDescription(f.featureType);
            });
          }
          if (!this.goalParametrizations.settings) {
            this.goalParametrizations.settings = {};
          }
          this.handleParametrizations();
          if (response.goalsLevels && response.goalsLevels.length > 0) {
            if (!response.goalFormat) {
              response.parametrizations.goalFormat = GoalFormat.BY_GOAL_LEVELS;
            }
            this.formattedGoalsLevels(response.goalsLevels);
          }
          if (response.exclusivesLevelsGoals && response.exclusivesLevelsGoals.length > 0) {
            if (!response.goalFormat) {
              response.parametrizations.goalFormat = GoalFormat.BY_EXCLUSIVE_LEVEL;
            }
            this.formatExclusivesLevelsGoals(response.exclusivesLevelsGoals);
          }
          if (response.intervalsGoals && response.intervalsGoals.length > 0) {
            if (!response.goalFormat) {
              response.parametrizations.goalFormat = GoalFormat.BY_INTERVALS;
            }
            this.formatIntervalsGoals(response.intervalsGoals);
          }
          if (response.goalAchievedAwarded) {
            this.goalAchievedAwarded = response.goalAchievedAwarded;
          }
          this.handleParametrizationsSettings();
          if (this.isExclusiveLevel)
            this.findGoalEvents();
        }
        this.handleSegmentations()
        this.loading = false;
      },
      err => {
        this.loading = false;
        this.alert.showError(err);
      }
    );
  }

  private handleSegmentations() {
    if (!this.goalParametrizations.participantsSegmentation) {
      this.goalParametrizations.participantsSegmentation = [];
    }
  }

  onSelectedParticipant(participantList: any[]) {
    this.goalParametrizations.participantsSegmentation = participantList || [];
  }

  formattedGoalsLevels(goalsLevels: any[]) {
    goalsLevels.forEach(level => {
      level.formattedRewardType = level.rewardType == 'Points' ? 'Pontos' : 'Reais (R$)';
      level.value = level.rewardType == 'Pointos' ? level.rewardAmount.points : level.rewardAmount.currency;
    });
    this.goalsLevels = goalsLevels;
  }

  private handleParametrizations(): void {
    if (!this.goalParametrizations.descriptionForSite) {
      this.goalParametrizations.descriptionForSite = this.goalParametrizations.name;
    }
    if (!this.goalParametrizations.descriptionForTransaction) {
      this.goalParametrizations.descriptionForTransaction = this.goalParametrizations.description;
    }
    if (!this.goalParametrizations.parametrizations) {
      this.goalParametrizations.parametrizations = {};
    }
    if (!this.goalParametrizations.parametrizations.processType) {
      this.goalParametrizations.parametrizations.processType = 'PROCESS_ONLY_GOALS'
    }
    if (!this.goalParametrizations.parametrizations.goalsOrdinationType) {
      this.goalParametrizations.parametrizations.goalsOrdinationType = 'DESC'
    }
    if (!this.goalParametrizations.parametrizations.reward) {
      this.goalParametrizations.parametrizations.reward = {
        rewardResultProcessType: 'COMPLETE',
        rewardCreditProcessType: 'CURRENT_GOAL'
      }
    }
  }

  private handleParametrizationsSettings() {
    this.updateAvailableProgressViewer();
    if (!this.goalParametrizations.settings.targetValueLabel) {
      this.goalParametrizations.settings.targetValueLabel = 'Meta';
    }
    if (!this.goalParametrizations.settings.bonusRewardLabel) {
      this.goalParametrizations.settings.bonusRewardLabel = 'Realizado';
    }
    if (!this.goalParametrizations.settings.participantResultValueLabel) {
      this.goalParametrizations.settings.participantResultValueLabel = 'Pontos';
    }
    this.customGoalPeriods = this.goalParametrizations.settings.goalPeriods || [];
    this.customGoalPeriods.forEach(c => {
      c.formattedInitialDate = FormatHelper.formatDate(c.initialDate);
      c.formattedEndDate = FormatHelper.formatDate(c.endDate);
    });
  }

  formatExclusivesLevelsGoals(goals: any[]) {
    goals.forEach(goal => {
      goal.formattedRewardType = goal.rewardType == 'Points' ? 'Pontos' : 'Reais (R$)';
      goal.formattedRewardMethod = goal.rewardMethod == RewardMethod.CASHBACK ? 'Cashback' : (goal.rewardMethod == RewardMethod.FIXED_REWARD ? 'Fixa' : '');
    });
    this.exclusiveLevelsGoals = goals;
  }

  formatIntervalsGoals(goals: any[]) {
    goals.forEach(goal => {
      goal.formattedRewardType = goal.rewardType == 'Points' ? 'Pontos' : 'Reais (R$)';
      goal.formattedRewardMethod = goal.rewardMethod == RewardMethod.CASHBACK ? 'Cashback' : (goal.rewardMethod == RewardMethod.FIXED_REWARD ? 'Fixa' : '');
    });
    this.intervalsGoals = goals;
  }

  saveCampaignParticipantsGoal() {
    if (!this.validate())
      return;

    this.saving = true;

    if (this.goalParametrizations.enableParticipantsGroups) {
      if (typeof (this.goalParametrizations.participantsGroups) == 'string') {
        this.goalParametrizations.participantsGroups = [this.goalParametrizations.participantsGroups];
      }
    }

    if (this.goalsLevels && this.goalsLevels.length && !this.goalParametrizations.started) {
      this.goalParametrizations.goalsLevels = this.goalsLevels;
    }

    this.campaignParticipantsGoalsService.saveCampaignParticipantsGoalParametrizations(this.campaignId, this.parametrizationId, this.goalParametrizations)
      .subscribe(
        response => {
          this.saving = false;
          if (!response) {
            this.alert.showWarning('Não foi possível salvar a meta, por favor, tente novamente.');
            return;
          }

          if (this.parametrizationId) {
            this.alert.showSuccess('Meta salva com sucesso.');
          } else {
            this.parametrizationId = response;
          }
        },
        err => {
          this.saving = false;
          this.alert.showError(err);
        }
      );
  }

  private validate(): boolean {
    if (!this.goalParametrizations.mechanicId) {
      this.alert.showWarning('Selecione uma mecânica para continuar.');
      return false;
    }
    if (!this.validateLimitationPeriod())
      return false;

    if (!this.validateGoalAchievedAwardedIfNeeded())
      return false;

    return this.validateRewardParametrizations();
  }

  private validateLimitationPeriod(): boolean {
    if (!this.isExclusiveLevel)
      return true;
    if (!this.hasPeriodTypeForGoal) {
      this.alert.showInfo("Período de limitação obrigatório");
      return false;
    }
    return true;
  }

  private validateRewardParametrizations(): boolean {
    if (!this.rewardParams) {
      this.alert.showError("Erro na parametrizações do bônus");
      return false;
    }
    return this.rewardParams.validateAndEmitParams();
  }

  private validateGoalAchievedAwardedIfNeeded(): boolean {
    if (!this.goalFormatByTargetAwarded || this.awardedTargetGoalModal == null)
      return true;

    return this.awardedTargetGoalModal.validateAndEmitParams();
  }

  addGoalAchievedAwarded(goalAchievedAwarded: any): void {
    this.goalParametrizations.goalAchievedAwarded = goalAchievedAwarded;
  }

  showGoalAchievedAwardedErrorMessage(errorMessage: string): void {
    this.alert.showWarning(errorMessage || 'Erro na parametrização dos alvos');
  }

  addRewardParametrizations(rewardParametrizations: any): void {
    this.goalParametrizations.parametrizations.reward = rewardParametrizations;
  }

  showRewardParamsErrorMessage(errorMessage: string): void {
    this.alert.showWarning(errorMessage || 'Erro na parametrização do bônus');
  }

  saveGoalLevels() {
    this.loading = true;
    this.campaignParticipantsGoalsService.saveGoalLevels(this.campaignId, this.parametrizationId, this.goalsLevels)
      .subscribe(
        response => {
          this.loading = false;
          if (response) {
            this.findCampaignParticipantsGoal();
            return this.alert.showSuccess('Meta cadastrada com sucesso.');
          }
        },
        err => {
          this.loading = false;
          this.alert.showError(err);
        }
      );
  }

  startGoal() {
    this.alert.confirm('Confirma o início da meta?')
      .then(result => {
        if (result && result.value) {
          this.starting = true;
          this.campaignParticipantsGoalsService.startGoal(this.campaignId, this.parametrizationId)
            .subscribe(response => {
              this.starting = false;
              if (response) {
                this.alert.showSuccess('Meta iniciada com sucesso.');
                this.findCampaignParticipantsGoal();
              } else {
                this.alert.showWarning('Não foi possível iniciar a meta, por favor, tente novamente.');
              }
            }, err => {
              this.starting = false;
              this.alert.showError(err);
            });
        }
      })
  }

  public loadCampaignMechanics(campaignId: string) {
    this.campaignParticipantsGoalsService.getCampaignMechanics(campaignId)
      .subscribe(
        mechanics => {
          let campaignsMechanics = [Item.of(campaignId, 'Mecânica Padrão')];
          if (mechanics && mechanics.length) {
            mechanics.forEach(m => campaignsMechanics.push(Item.of(m.id, m.description)));
          }
          this.campaignsMechanics = campaignsMechanics;
        },
        err => {
          this.alert.showError(err);
        }
      );
  }

  showGoalLevel() {
    this.goalModal.show();
  }

  addGoalLevel() {
    if (!this.goalLevel.targetValue || this.goalLevel.targetValue < 0) {
      return this.alert.showWarning('Valor é obrigatório');
    }
    if (this.goalLevel.rewardType) {
      if (this.goalLevel.rewardType == 'Points') {
        this.goalLevel.rewardAmount.points = this.goalLevel.value;
        this.goalLevel.formattedRewardType = 'Pontos';
      } else {
        this.goalLevel.rewardAmount.currency = this.goalLevel.value;
        this.goalLevel.formattedRewardType = 'Reais (R$)';
      }
    } else {
      return this.alert.showWarning('Tipo do Valor é obrigatório');
    }

    this.goalsLevels.push(this.goalLevel);
    this.goalLevel = {
      rewardAmount: {}
    };
    this.goalModal.hide();
    return this.alert.showSuccess('Nível adicionado com sucesso.');
  }

  newExclusiveLevelGoal() {
    this.exclusiveLevelGoalModal.newExclusiveLevelGoal();
  }

  editExclusiveLevelGoal(event: any) {
    if (!event || !event.id) {
      return this.alert.showWarning('Faixa está inválido');
    }
    this.exclusiveLevelGoalModal.editExclusiveLevelGoal(event);
  }

  newGoalEvent() {
    this.eventModal.newEvent();
  }

  editGoalEvent(event: any) {
    if (!event || !event.id) {
      return this.alert.showWarning('Evento está inválido');
    }
    this.eventModal.editEvent(event);
  }

  newIntervalGoal() {
    this.intervalGoalModal.newIntervalGoal();
  }

  editIntervalGoal(event: any) {
    if (!event || !event.id) {
      return this.alert.showWarning('Intervalo está inválido');
    }
    this.intervalGoalModal.editIntervalGoal(event);
  }

  newGoalPeriod() {
    this.goalPeriodParamsModal.newGoalPeriod();
  }

  editGoalPeriod(goalPeriod: any): void {
    this.goalPeriodParamsModal.editGoalPeriod(goalPeriod);
  }

  updateCustomGoalPeriods(customGoalPeriods: any[]): void {
    this.customGoalPeriods = customGoalPeriods;
    this.goalParametrizations.settings.goalPeriods = customGoalPeriods;
  }

  findGoalEvents() {
    this.loadingEvents = true;
    this.campaignParticipantsGoalsService
      .findEvents(this.campaignId, this.parametrizationId)
      .subscribe(
        response => {
          if (response) {
            response.forEach((event: any) => {
              event.formattedCreateDate = FormatHelper.formatDate(event.createDate);
              event.formatterEventType = event.eventType == EventTypes.BONUS ? "Bônus" : "Penalidade";
            });
          }
          this.events = response;
          this.loadingEvents = false;
        }, err => {
          this.loadingEvents = false;
          this.alert.showError(err);
        }
      );
  }

  findExclusiveLevelGoals() {
    this.loadingExclusiveLevelGoals = true;
    this.campaignParticipantsGoalsService
      .findExclusiveLevelGoals(this.campaignId, this.parametrizationId)
      .subscribe(
        response => {
          if (response) {
            response.forEach((productGoal: any) => {
              productGoal.formattedCreateDate = FormatHelper.formatDate(productGoal.createDate);
            });
            this.formatExclusivesLevelsGoals(response);
          }
          this.loadingExclusiveLevelGoals = false;
        }, err => {
          this.loadingExclusiveLevelGoals = false;
          this.alert.showError(err);
        }
      );
  }

  findIntervalGoals() {
    this.loadingIntervalsGoals = true;
    this.campaignParticipantsGoalsService
      .findIntervalGoalGoals(this.campaignId, this.parametrizationId)
      .subscribe(
        response => {
          if (response) {
            response.forEach((intervalGoal: any) => {
              intervalGoal.formattedCreateDate = FormatHelper.formatDate(intervalGoal.createDate);
            });
            this.formatIntervalsGoals(response);
          }
          this.loadingIntervalsGoals = false;
        }, err => {
          this.loadingIntervalsGoals = false;
          this.alert.showError(err);
        }
      );
  }

  clear() {
    this.goalAchievedAwarded = {};
    this.goalsLevels = [];
    this.intervalsGoals = [];
    this.exclusiveLevelsGoals = [];
    this.goalLevel = {
      rewardAmount: {}
    };
    this.goalParametrizations = {
      settings: {
        period: false,
        accumulation: false,
        enablePreviousRecheadLevelsBonus: false
      },
      parametrizations: {
        processType: 'PROCESS_ONLY_GOALS',
        goalsOrdinationType: 'DESC'
      }
    };
    this.parametrizationId = '';
    this.disabledCreateParticipantGoalsWhenNotConfigured = false;
    this.events = [];
    this.customGoalPeriods = [];
  }

  onChangeSelectedPeriod(): void {
    if (!this.validateLimitationPeriod())
      return;

    if (!this.isPeriodCustomType)
      this.handleCreateParticipantGoalsWhenNotConfigured();

    if (this.isPeriodCustomType)
      this.goalPeriodParamsModal.newGoalPeriod();
  }

  private handleCreateParticipantGoalsWhenNotConfigured() {
    if (this.goalParametrizations.settings && this.hasPeriodTypeForGoal) {
      var oldValue = this.goalParametrizations.parametrizations.createParticipantGoalsWhenNotConfigured
      this.goalParametrizations.parametrizations.createParticipantGoalsWhenNotConfigured = true;
      this.disabledCreateParticipantGoalsWhenNotConfigured = true;
      if (!oldValue) {
        this.alert.showInfo("opção 'Configurar meta do participante caso não exista' foi ativado para meta com período.");
      }
    } else {
      this.disabledCreateParticipantGoalsWhenNotConfigured = false;
    }
  }

  onFeatureSelect(feature: CampaignFeature) {
    if (!feature) {
      return;
    }
    if (!this.goalParametrizations.parametrizations.campaignFeatures) {
      this.goalParametrizations.parametrizations.campaignFeatures = [feature];
      return;
    }

    const alreadySelected = this.goalParametrizations.parametrizations.campaignFeatures
      .findIndex(f => f.featureId == feature.featureId) >= 0;
    if (alreadySelected) {
      this.alert.showWarning('Feature selecionada já existe na lista');
      return;
    }
    this.goalParametrizations.parametrizations.campaignFeatures = this.goalParametrizations.parametrizations.campaignFeatures.concat([feature]);
  }

  removeCampaignFeature(feature: CampaignFeature) {
    this.goalParametrizations.parametrizations.campaignFeatures = this.goalParametrizations.parametrizations.campaignFeatures
      .filter(f => f.featureId !== feature.featureId);
  }

  showFeatureSelectModal() {
    this.featureSelectorModal.showModal();
  }

  updateAvailableProgressViewer() {
    if (!this.goalParametrizations || !this.goalParametrizations.parametrizations)
      return;
    var goalFormat = this.goalParametrizations.parametrizations.goalFormat;
    this.progressViewerTypes = this.setAvailableProgressViewerTypesBy(goalFormat);
  }

  private setAvailableProgressViewerTypesBy(goalFormat: GoalFormat) {
    switch (goalFormat) {
      case GoalFormat.BY_GOAL_LEVELS:
        return [
          Item.of(ProgressViewerType.GOALS_MAP, 'Mapa de Pontos e Metas')
        ];

      case GoalFormat.BY_TARGET_AWARDED:
        return [
          Item.of(ProgressViewerType.GOALS_SUMMARY_TABLES, 'Tabelas de resumo de metas Bateu Levou'),
          Item.of(ProgressViewerType.GOALS_CARD, 'Níveis de Metas em Cards'),
        ];

      case GoalFormat.BY_EXCLUSIVE_LEVEL:
        return [
          Item.of(ProgressViewerType.SIMPLE_PROGRESS_THERMOMETER, 'Termômetro de Progresso Simples de Pontos'),
          Item.of(ProgressViewerType.PERCENTAGE_PROGRESS_THERMOMETER, 'Termômetro de Progresso com Porcentagem de Pontos'),
          Item.of(ProgressViewerType.GOALS_AND_POINTS_PROGRESS_THERMOMETER, 'Termômetro de Progresso de Pontos e Metas'),
          Item.of(ProgressViewerType.GOALS_SUMMARY_PANEL, 'Painel resumido das metas')
        ];

      case GoalFormat.BY_INTERVALS:
        return [
          Item.of(ProgressViewerType.SIMPLE_PROGRESS_THERMOMETER, 'Termômetro de Progresso Simples de Pontos'),
          Item.of(ProgressViewerType.PERCENTAGE_PROGRESS_THERMOMETER, 'Termômetro de Progresso com Porcentagem de Pontos'),
          Item.of(ProgressViewerType.GOALS_AND_POINTS_PROGRESS_THERMOMETER, 'Termômetro de Progresso de Pontos e Metas')
        ];

      default:
        return [];
    }
  }
}
