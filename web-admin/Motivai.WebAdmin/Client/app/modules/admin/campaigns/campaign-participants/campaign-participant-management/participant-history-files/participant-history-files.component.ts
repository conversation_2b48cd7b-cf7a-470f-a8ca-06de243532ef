import { Component, ViewChild, Input } from '@angular/core';

import { AuthStore } from '../../../../../../core/auth/auth.store';
import { ParticipantHistoryFilesEditComponent } from './participant-history-files-edit/participant-history-files-edit.component';
import { ParticipantHistoryFilesListComponent } from './participant-history-files-list/participant-history-files-list.component';

@Component({
  selector: 'participant-history-files',
  templateUrl: './participant-history-files.component.html'
})
export class ParticipantHistoryFilesComponent {
  @ViewChild('participantHistoryFilesList') participantHistoryFilesListComponent: ParticipantHistoryFilesListComponent;
  @ViewChild('participantHistoryFilesEdit') participantHistoryFilesEditComponent: ParticipantHistoryFilesEditComponent;

  userId: string;

  constructor(private _authStore: AuthStore) {}

  get canUpdateHistoricFiles() {
    return this._authStore.role .PERMISSION_CAMPAIGNS_PARTICIPANTS_MANAGEMENT_UPDATE_HISTORIC_FILES;
  }

  @Input()
  set _userId(id: string) {
    if (!id) {
      return;
    }
    this.userId = id;
    this.participantHistoryFilesListComponent.userId = id;
  }

  findParticipantHistoryFiles() {
    this.participantHistoryFilesListComponent.findParticipantHistoryFiles();
  }

  onView(event: any) {
    this.findParticipantHistoryFiles();
  }
}
