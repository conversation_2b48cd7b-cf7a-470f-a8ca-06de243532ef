import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild
} from '@angular/core';
import { GpAlertComponent } from '../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpFileUploadComponent } from '../../../../../../../../shared/components/gp-fileupload/gp-fileupload.component';
import { GpInputMaskComponent } from '../../../../../../../../shared/components/gp-input-mask/gp-input-mask.component';
import { GpModalComponent } from '../../../../../../../../shared/components/gp-modal/gp-modal.component';
import { Item } from '../../../../../../../../shared/models/item';
import { RewardMethod } from '../../../models/reward-method';
import { CampaignsParticipantsGoalsActionsService } from '../campaigns-participants-goals-actions.service';

@Component({
  selector: 'exclusive-level-goal-editing',
  templateUrl: './exclusive-level-goal-editing.component.html'
})
export class ExclusiveLevelGoalEditingComponent implements OnInit {
  @ViewChild('exclusiveLevelGoalModal') exclusiveLevelGoalModal: GpModalComponent;
  @ViewChild('gpFile') gpFile: GpFileUploadComponent;
  @ViewChild('alert') alert: GpAlertComponent;

  @Output() refreshExclusiveLevelGoals: EventEmitter<any> = new EventEmitter();
  @Input() campaignId: string;
  @Input() goalId: string;
  @Input() hasStarted: string;

  saving: boolean = false;
  removing: boolean;
  uploading: boolean = false;
  childVisible: boolean = true;

  enableLevelThresholdPercentage: boolean = true;

  exclusiveLevelGoalId: string;
  exclusiveLevelGoal: any = {
    active: true,
    rewardMethod: RewardMethod.CASHBACK,
    decimalPlacesQuantity: 2
  };

  showChild: boolean = true;

  constructor(
    private changeDetector : ChangeDetectorRef,
    private goalsActionsService: CampaignsParticipantsGoalsActionsService
  ) { }

  ngOnInit() { }

  get hasLevelThresholdPercentage() {
    return this.exclusiveLevelGoal && this.exclusiveLevelGoal.levelThresholdPercentage && this.exclusiveLevelGoal.levelThresholdPercentage > 0;
  }

  get isCashBackRewardMethod() {
    return this.exclusiveLevelGoal.rewardMethod == RewardMethod.CASHBACK;
  }

  get isPointsRewardMethod() {
    return this.exclusiveLevelGoal.rewardMethod == RewardMethod.FIXED_REWARD;
  }

  rewardMethods: Array<Item> = [
    Item.of(RewardMethod.FIXED_REWARD, 'Pontuação Fixa'),
    Item.of(RewardMethod.CASHBACK, 'Cashback (%)')
  ];

  get isEditing() {
    return this.exclusiveLevelGoal && this.exclusiveLevelGoal.id;
  }

  get textButtonSave() {
    if (this.isEditing) {
      return 'Salvar';
    }
    return 'Adicionar';
  }

  get icon() {
    if (this.isEditing) {
      return 'send';
    }
    return 'plus';
  }

  get hasSelectedFile() {
    return this.gpFile && this.gpFile.hasSelectedFile();
  }

  private clearForm() {
    this.exclusiveLevelGoal = {
      active: true,
      rewardMethod: RewardMethod.CASHBACK
    };
    this.enableLevelThresholdPercentage = true;
  }

  newExclusiveLevelGoal() {
    this.clearForm();
    this.showModal();
  }

  editExclusiveLevelGoal(event: any) {
    this.exclusiveLevelGoal = event;
    this.exclusiveLevelGoalId = event.id;
    this.enableLevelThresholdPercentage = this.hasLevelThresholdPercentage;
    this.onDecimalPlacesQuantityUpdate();
    this.showModal();
  }

  showModal() {
    this.exclusiveLevelGoalModal.show();
  }

  hideModal() {
    this.exclusiveLevelGoalModal.hide();
  }

  saveGoal() {
    this.saving = true;
    if (this.isEditing) {
      return this.updateExclusiveLevelGoal();
    }
    return this.saveExclusiveLevelGoal();
  }

  saveExclusiveLevelGoal() {
    this.goalsActionsService
      .saveExclusiveLevelGoal(this.campaignId, this.goalId, this.exclusiveLevelGoal)
      .subscribe(
        response => {
          if (response) {
            if (typeof response === 'string') {
              this.exclusiveLevelGoalId = response;
              this.exclusiveLevelGoal.id = response;
              this.alert.showSuccess('Faixa salva com sucesso');
              this.refreshExclusiveLevelGoals.emit();
              this.hideModal();
            } else {
              this.alert.showError('Não foi possível salvar, tente novamente');
            }

          }
          this.saving = false;
        },
        err => {
          this.saving = false;
          this.alert.showError(err);
        }
      );
  }

  updateExclusiveLevelGoal() {
    this.goalsActionsService
      .updateExclusiveLevelGoal(this.campaignId, this.goalId, this.exclusiveLevelGoal)
      .subscribe(
        response => {
          if (response) {
              this.alert.showSuccess('Faixa atualizada com sucesso');
              this.refreshExclusiveLevelGoals.emit();
              this.hideModal();
          } else {
              this.alert.showError('Não foi possível atualizar, tente novamente');
          }
          this.saving = false;
        },
        err => {
          this.saving = false;
          this.alert.showError(err);
        }
      );
  }

  updateMinimumTargetValue() {
    if (!this.exclusiveLevelGoal.baseTargetValue || !this.exclusiveLevelGoal.levelThresholdPercentage) {
      this.exclusiveLevelGoal.minimumTargetValue = 0;
      this.exclusiveLevelGoal.levelThresholdVariationValue = 0;
      return;
    }

    this.exclusiveLevelGoal.levelThresholdVariationValue = this.exclusiveLevelGoal.baseTargetValue * this.exclusiveLevelGoal.levelThresholdPercentage * 0.01;

    this.exclusiveLevelGoal.minimumTargetValue = Number(this.exclusiveLevelGoal.baseTargetValue) + Number(this.exclusiveLevelGoal.levelThresholdVariationValue);
  }

  onEnableLevelThressholdPercentageChange() {
    if (!this.enableLevelThresholdPercentage) {
      this.exclusiveLevelGoal.levelThresholdPercentage = null;
    }
  }

  onClose() {
    this.refreshExclusiveLevelGoals.emit();
  }

  onDecimalPlacesQuantityUpdate() {
    if (this.exclusiveLevelGoal.decimalPlacesQuantity != 0 && !this.exclusiveLevelGoal.decimalPlacesQuantity) {
      this.exclusiveLevelGoal.decimalPlacesQuantity = 2;
    }

    if (this.exclusiveLevelGoal.baseTargetValue != undefined) {
      this.exclusiveLevelGoal.baseTargetValue = (new Number(this.exclusiveLevelGoal.baseTargetValue)).toFixed(this.exclusiveLevelGoal.decimalPlacesQuantity);
    }
    this.reinitChildComponent();
   
  }

  private reinitChildComponent(): void{
    this.showChild = false;
    this.changeDetector.detectChanges();
    this.showChild = true;
    this.changeDetector.detectChanges();
  }

  considerValueZeroAsValidResultUpdate() {
    if (this.exclusiveLevelGoal.considerValueZeroAsValidResult && !this.exclusiveLevelGoal.baseTargetValue) {
       this.exclusiveLevelGoal.baseTargetValue = 0;
    } else if (this.exclusiveLevelGoal.baseTargetValue == 0) {
      this.exclusiveLevelGoal.baseTargetValue = null;
    }
    this.onDecimalPlacesQuantityUpdate();
  }
}
