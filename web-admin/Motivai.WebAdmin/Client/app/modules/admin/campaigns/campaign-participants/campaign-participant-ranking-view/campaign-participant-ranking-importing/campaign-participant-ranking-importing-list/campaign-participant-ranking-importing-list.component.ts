import { Component, OnInit, ViewChild, Output, EventEmitter } from '@angular/core';
import { CampaignStore } from '../../../../campaign.store';
import { CampaignParticipantRankingService } from '../../campaign-participant-ranking.service';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { FormatHelper } from '../../../../../../../shared/formatters/format-helper';
import { Item } from '../../../../../../../shared/models/item';

@Component({
  selector: 'campaign-participant-ranking-importing-list',
  templateUrl: './campaign-participant-ranking-importing-list.component.html'
})
export class CampaignParticipantRankingImportingListComponent implements OnInit {
  @ViewChild('alert') alert: GpAlertComponent;
  @Output('onEdit') onEdit: EventEmitter<any> = new EventEmitter<any>();



  batchesRankings: any[] = [];
  params: any = {
    skip: 0,
    limit: 10
  };

  totalRecords: number = 0;

  campaignId: string = '';

  loading: boolean = false;


  batchStatuses: Array<Item> = [
    Item.of(' ', 'Todos'),
    Item.of('CREATED', 'Criado'),
    Item.of('PROCESSING', 'Em Processamento'),
    Item.of('COMPLETED', 'Finalizado'),
    Item.of('ERROR', 'Erro')
  ];

  constructor(private campaignStore: CampaignStore, private rankingParticipantService: CampaignParticipantRankingService) { }

  ngOnInit() {
    this.findBatchesRanking();
  }

  findBatchesRanking() {
    this.loading = true;
    this.rankingParticipantService.findCampaignParticipantsRankingsBatches(this.campaignStore.id, this.params).subscribe(
      response => {
        if (response) {
          this.getBatchesRankingsCounter();
          response.forEach(x => {
            x.formattedStatus = this.handleStatus(x.status);
            x.formattedCreateDate = FormatHelper.formatDateWithTimezone(x.createDate);
          });
        }

        this.batchesRankings = response;
        this.loading = false;
      }, err => {
        this.loading = false;
        this.alert.showError(err);
      }
    );
  }

  getBatchesRankingsCounter() {
    this.loading = true;
    this.rankingParticipantService.getCampaignParticipantsRankingsBatchesCounter(this.campaignStore.id, this.params).subscribe(
      response => {
        this.totalRecords = response;
        this.loading = false;
      }, err => {
        this.loading = false;
        this.alert.showError(err);
      }
    );
  }

  showDetailsBatchRanking(event) {
    this.onEdit.emit(event);
  }

  onPageChanged(event) {
    this.params.skip = event.skip;
    this.params.limit = event.limit;
    this.findBatchesRanking();
  }

  handleStatus(status: string): string {
    switch (status) {
      case 'COMPLETED':
        return 'Finalizado';
      case 'CREATED':
        return 'Criado';
      case 'PROCESSING':
        return 'Processando';
      case 'ERROR':
        return 'Erro';
      default:
        return 'Status inválido';
    }
  }
}
