import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignStore } from '../../../../campaign.store';
import { Component, OnInit, ViewChild } from '@angular/core';
import { CampaignParticipantRankingService } from '../../campaign-participant-ranking.service';

@Component({
    selector: 'campaign-participant-ranking-parametrization-edit',
    templateUrl: './campaign-participant-ranking-parametrization-edit.component.html'
})
export class CampaignParticipantRankingParametrizationEditComponent implements OnInit {
    @ViewChild('alert') alert: GpAlertComponent;

    loading: boolean = false;

    campaignId: string;
    rankingParametrization: any = {
        columnsParametrizations: {}
    };

    constructor(private _campaignStore: CampaignStore, private _rankingParametrizationService: CampaignParticipantRankingService) { }

    ngOnInit(): void {
      this.campaignId = this._campaignStore.id;
    }

    get hasRankingParametrization() {
        return this.rankingParametrization && this.rankingParametrization.description && this.rankingParametrization.code;
    }

    public showRanking(ranking: any) {
      if (ranking) {

        if (!ranking.columnsParametrizations) {
            ranking.columnsParametrizations = {
                showResultValue: true,
                resultValueHeaderText: "PRODUÇÃO"
            };
        }

        this.rankingParametrization = ranking;
        if (typeof (this.rankingParametrization.participantsGroups) == 'string') {
          this.rankingParametrization.participantsGroups = [this.rankingParametrization.participantsGroups];
        }
      } else {
        this.rankingParametrization = {};
      }
    }

    saveDynamicRankingParametrization() {
        if (this.rankingParametrization.enableParticipantsGroups) {
            if (typeof (this.rankingParametrization.participantsGroups) == 'string') {
                this.rankingParametrization.participantsGroups = [this.rankingParametrization.participantsGroups];
            }
        }

        if (!this.validateColumnsParametrizations()) {
            this.alert.showWarning("Verifique se o nome da coluna está preenchido para os campos que estão ativos");
            return;
        }

        this.loading = true;
        this._rankingParametrizationService.saveCampaignParticipantRankingParametrization(this._campaignStore.id, this.rankingParametrization)
            .subscribe(
                response => {
                    if (response) {
                        if (response.id) {
                            this.rankingParametrization.id = response.id;
                        }
                        this.alert.showSuccess('Parametrização do ranking salva com sucesso.');
                    }
                    this.loading = false;
                },
                err => {
                    this.loading = false;
                    this.alert.showError(err);
                }
            );
    }

    validateColumnsParametrizations(): boolean {
        if (this.rankingParametrization.columnsParametrizations.showName && !this.rankingParametrization.columnsParametrizations.nameHeaderText)
            return false;
        if (this.rankingParametrization.columnsParametrizations.showDocument && !this.rankingParametrization.columnsParametrizations.documentHeaderText)
            return false;
        if (this.rankingParametrization.columnsParametrizations.showResultValue && !this.rankingParametrization.columnsParametrizations.resultValueHeaderText)
            return false;
        return true;
    }

    clear() {
        this.rankingParametrization = {
            columnsParametrizations: {}
        };
    }
}
