export class GoalLevelProgress {
    public goalName: string;
    public index: number;
    public targetReached: string;
    public estimatedRewardPointsAmount: number;
    public baseTargetValue: number;
    public currentResult: number;
    public deltaReward: string;
    public deltaValue: string; 

    static buildFromLevel(level: any,) : GoalLevelProgress {
        let goalLevel = new GoalLevelProgress();

        goalLevel.goalName = level.name;
        goalLevel.index = level.index;
        goalLevel.targetReached = (level.targetReached) ? "Sim" : "Não";
        goalLevel.estimatedRewardPointsAmount = (level.estimatedRewardPointsAmount != null) ? level.estimatedRewardPointsAmount.toFixed(0) : 0;
        goalLevel.baseTargetValue = level.baseTargetValue || 0;
        goalLevel.currentResult = level.currentResult || 0;

        return goalLevel;
    } 

    static buildFromLevelAwardedTargetGoal(level: any,) : GoalLevelProgress {
        let goalLevel = new GoalLevelProgress();

        goalLevel.goalName = level.code;
        goalLevel.index = level.index;
        goalLevel.estimatedRewardPointsAmount = (level.estimatedRewardPointsAmount != null) ? level.estimatedRewardPointsAmount.toFixed(0) : 0;
        
        goalLevel.targetReached = (level.targetReached) ? "Sim" : "Não";
        goalLevel.baseTargetValue = level.targetValue || 0;
        goalLevel.currentResult = level.currentResult || 0;

        return goalLevel;
    }
}