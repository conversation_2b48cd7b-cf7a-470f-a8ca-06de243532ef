import { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';

import { FormatHelper } from '../../../../../../../shared/formatters/format-helper';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpModalComponent } from '../../../../../../../shared/components/gp-modal/gp-modal.component';
import { CampaignParticipantManagementService } from '../../campaign-participant-management.service';

@Component({
  selector: 'app-participant-parent-linker-modal',
  templateUrl: './participant-parent-linker-modal.component.html'
})
export class ParticipantParentLinkerModalComponent {
  @Output() closeEmitter: EventEmitter<any> = new EventEmitter<any>();

  @ViewChild('modal') modal: GpModalComponent;
  @ViewChild('alert') alert: GpAlertComponent;

  @Input() userId: string;
  @Input() campaignId: string;

  currentParent: any = null;

  participants: any[] = [];

  userDocument: string = null;

  loadingScreen: boolean = false;
  loading: boolean = false;

  constructor(
    private campaignParticipantManagementService: CampaignParticipantManagementService
  ) {}

  get hasParent() {
    return this.currentParent && this.currentParent.userId;
  }

  openModal() {
    this.clear();
    this.modal.show();
    this.loadCurrentParent();
  }

  private loadCurrentParent() {
    this.loadingScreen = true;
    this.campaignParticipantManagementService.getParticipantParent(this.userId, this.campaignId)
      .subscribe(
        parent => {
          this.loadingScreen = false;
          if (parent && parent.userId) {
            this.currentParent = parent;
          }
        },
        err => {
          this.loadingScreen = false;
          this.alert.handleAndShowError(err);
        }
      );
  }

  search() {
    this.loading = true;
    this.participants = [];
    this.campaignParticipantManagementService
      .findUserCampaignsByDocument(this.userDocument, this.campaignId)
      .subscribe(
        participant => {
          if (participant) {
            participant.formattedDocument = FormatHelper.formatDocument(
              participant.document
            );
            this.participants = [participant];
          } else {
            this.alert.showWarning(
              'Participante não encontrado pelo CPF/CNPJ informado.'
            );
          }
          this.loading = false;
        },
        err => {
          this.alert.showError(err);
          this.loading = false;
        }
      );
  }

  linkParent(selectedUser) {
    if (!selectedUser || !selectedUser.userId) {
      return;
    }

    this.loadingScreen = true;
    this.campaignParticipantManagementService
      .linkParent(this.userId, this.campaignId, {
        userId: selectedUser.userId,
        name: selectedUser.name,
        document: selectedUser.document
      })
      .subscribe(
        response => {
          this.loadingScreen = false;
          if (response) {
            this.alert.showSuccess('Participante pai vinculado.');
            this.closeModal();
          } else {
            this.alert.showWarning(
              'Não foi possível vincular o participante pai, por favor, tente novamente.'
            );
          }
        },
        err => {
          this.loadingScreen = false;
          this.alert.showError(err);
        }
      );
  }

  closeModal() {
    this.modal.hide();
    this.clear();
    this.closeEmitter.emit({});
  }

  clear() {
    this.userDocument = null;
    this.currentParent = null;
    this.participants = [];
  }
}
