<gp-modal #modal title="Vincular Participante Pai">
	<gp-alert [overlay]="true" #alert></gp-alert>
	<spinner [overlay]="true" [show]="loadingScreen"></spinner>

	<div *ngIf="hasParent">
		<h4>Participante pai atual:</h4>
		<gp-form-row>
			<gp-form-col cols="12">
				<div>
					<label>Nome: </label> {{ currentParent.name }}
				</div>
				<div>
					<label>CPF/CNPJ: </label> {{ currentParent.document | document }}
				</div>
			</gp-form-col>
		</gp-form-row>
		<hr />
	</div>

	<gp-form-row>
		<gp-form-col cols="12 6">
			<gp-simple-input label="CPF/CNPJ">
				<gp-input-mask
					name="document"
					mask="00000000000000"
					[(ngModel)]="userDocument"
				></gp-input-mask>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 2">
			<gp-spinner-button
				text="Pesquisar"
				[search]="true"
				[disabled]="!userDocument"
				icon="search"
				marginTop="25px"
				loadingText="Pesquisando"
				[loading]="loading"
				(click)="search()"
			>
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12">
			<gp-grid
				[rows]="participants"
				[columns]="['Nome', 'CPF/CNPJ']"
				[fields]="['name', 'formattedDocument']"
				[showDelete]="false"
				[showEdit]="false"
				[showCustom]="true"
				customText="Vincular"
				customTitle="Vincular"
				customIcon="link"
				(onCustom)="linkParent($event)"
				[showActive]="true"
			></gp-grid>
		</gp-form-col>
	</gp-form-row>
</gp-modal>
