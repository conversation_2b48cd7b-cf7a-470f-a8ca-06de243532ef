import {
    PERMISSION_CAMPAIGNS_PARTICIPANTS_GROUPS_LIST,
    PERMISSION_CAMPAIGNS_PARTICIPANTS_GROUPS_EDIT,
    PERMISSION_CAMPAIGNS_PARTICIPANTS_GROUPS_VIEW
} from '../../../../../../core/auth/access-points';
import { AuthStore } from '../../../../../../core/auth/auth.store';
import { ParticipantsGroupService } from '../participants-groups.service';
import { Component, OnInit, EventEmitter, Output, ViewChild } from '@angular/core';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignStore } from '../../../campaign.store';

@Component({
    selector: 'participants-groups-list',
    templateUrl: 'participants-groups-list.component.html'
})
export class GpParticipantsGroupsListComponent implements OnInit {
    private campaignId: string;
    loading: boolean = false;
    name: string;
    rows: any[] = [];
    params: any = {};

    @Output('onedit') onedit: EventEmitter<any> = new EventEmitter();
    @ViewChild('alert') alert: GpAlertComponent;

    constructor(private _groupsService: ParticipantsGroupService, private campaignStore: CampaignStore, private _as: AuthStore) { }

    ngOnInit() {
        this.campaignId = this.campaignStore.id;
        this.getGroups();
    }

    public getGroups() {
        if (this.campaignId && this.canList()) {
            this.loading = true;
            // if (this.isCrmGroup)
            //     this.params.type = this.handleGroupOrigin();
            this._groupsService.search(this.campaignId, this.name)
                .subscribe(groups => {
                    this.rows = groups;
                    this.loading = false;
                }, err => {
                    this.alert.showError(err, true);
                    this.loading = false;
                });
        }
    }

    // handleGroupOrigin() {
    //     switch (this.type) {
    //       case 'CRM_GROUPS':
    //         return 'MANUALTARGETAUDIENCES';
    //       case 'PARTICIPANTS_GROUPS':
    //         return 'MANUAL';
    //       default:
    //         return '';
    //     }
    //   }

    editGroup($event: any) {
        if ($event) {
            this.onedit.emit($event.id);
        }
    }

    canList() {
        return this._as.hasPermissionTo(PERMISSION_CAMPAIGNS_PARTICIPANTS_GROUPS_LIST);
    }

    canEdit() {
        return this._as.hasPermissionTo(PERMISSION_CAMPAIGNS_PARTICIPANTS_GROUPS_EDIT);
    }

    canView() {
        return this._as.hasPermissionTo(PERMISSION_CAMPAIGNS_PARTICIPANTS_GROUPS_VIEW);
    }
}
