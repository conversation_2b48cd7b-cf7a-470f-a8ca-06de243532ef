<gp-card [first]="true" title="Pesquisa">
	<gp-form-row *ngIf="canList()">
		<gp-form-col cols="12 6 6">
			<div class="form-group">
				<input type="text" placeholder="Descrição do Grupo" class="form-control" name="name" [(ngModel)]="name" />
			</div>
		</gp-form-col>
		<gp-form-col cols="12 6 6">
			<gp-spinner-button text="Pesquisar" buttonClass="bg-primary-dark" icon="search" (click)="getGroups()" [loading]="loading"
			 loadingText="Pesquisando">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row *ngIf="!canList()">
		<gp-form-col cols="12 12 12">
			<gp-alert type="danger" message="Você não tem permissão para acessar esta funcionalidade"></gp-alert>
		</gp-form-col>
	</gp-form-row>
	<gp-alert [overlay]="true" #alert></gp-alert>
</gp-card>
<gp-card [last]="true" title="Registros encontrados" *ngIf="canList()">
	<gp-form-row>
		<gp-form-col cols="12 12 12 12">
			<gp-grid name="grid" [rows]="rows"  [showActive]="true" [showPagination]="false" [showTotalPages]="false"
				[columns]="['Descrição', 'Identificador']" [fields]="['name', 'identifier']"
				[showEdit]="canEdit() || canView()" [showDelete]="false" [loading]="loading" (onEdit)="editGroup($event)">
			</gp-grid>
		</gp-form-col>
	</gp-form-row>
</gp-card>