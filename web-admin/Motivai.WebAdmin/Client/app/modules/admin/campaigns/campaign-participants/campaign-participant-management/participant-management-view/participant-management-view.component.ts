import { Component, OnInit, ViewChild } from '@angular/core';
import { ParticipantManagementListComponent } from '../participant-management-list/participant-management-list.component';
import { ParticipantManagementEditComponent } from '../participant-management-edit/participant-management-edit.component';
import { TabsetComponent } from 'ng2-bootstrap';

@Component({
  selector: 'campaign-participant-management-view',
  templateUrl: './participant-management-view.component.html'
})
export class ParticipantManagementViewComponent implements OnInit {

  @ViewChild('tabs') tabs: TabsetComponent;
  @ViewChild('listComponent') listComponent: ParticipantManagementListComponent;
  @ViewChild('editComponent') editComponent: ParticipantManagementEditComponent;

  constructor() { }

  ngOnInit() {
    this.tabs.tabs[1].disabled = true;
  }


  editParticipant(participant) {
    this.tabs.tabs[1].disabled = false;
    this.tabs.tabs[1].active = true;
    this.editComponent.editParticipant(participant);
  }

  clearEditForm() {
    if (this.editComponent)
      this.editComponent.resetForms();
    this.tabs.tabs[1].disabled = true;
  }


}
