import { Component, ViewChild } from '@angular/core';
import { TabsetComponent } from 'ng2-bootstrap';
import { Observable } from 'rxjs';

import { PERMISSION_CAMPAIGNS_PARTICIPANTS_METADATA_FIELDS_CREATE } from '../../../../../../core/auth/access-points';

import { CampaignStore } from '../../../campaign.store';


import { MetadataFieldsEditorComponent } from '../metadata-fields-editor/metadata-fields-editor.component';
import { MetadataFieldsListComponent } from '../metadata-fields-list/metadata-fields.list.component';
import { AuthStore } from '../../../../../../core/auth/auth.store';


@Component({
    selector: 'metadata-fields-view',
    templateUrl: 'metadata-fields-view.component.html',
})
export class MetadataFieldsViewComponent {
    @ViewChild('tabs') tabs: TabsetComponent;
    @ViewChild('list') list: MetadataFieldsListComponent;
    @ViewChild('edit') edit: MetadataFieldsEditorComponent;

    constructor(private campaignStore: CampaignStore, private authStore: AuthStore) { }

    get campaign$(): Observable<string> {
        return this.campaignStore.asObservable;
    }

    editPage(event) {
        this.edit.findById(event.property);
        this.tabs.tabs[1].active = true;
    }

    canCreateOrUpdate(): boolean {
        return true;
    }

    listFields() {
        this.edit.clearAllForm();
        this.list.loadMetadataFields();
    }

    canCreateMetadataFields() {
        return this.authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_PARTICIPANTS_METADATA_FIELDS_CREATE);
    }
}
