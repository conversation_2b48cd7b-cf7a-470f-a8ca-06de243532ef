<gp-modal title="Evento da Meta" width="800px" #eventModal>
    <gp-alert #alert [overlay]="true"></gp-alert>
    <spinner [overlay]="true" [show]="saving"></spinner>

    <gp-form-row>
        <gp-form-col cols="12 6">
            <gp-simple-input label="Código">
                <input type="text" name="code" class="form-control" [disabled]="hasStarted" [(ngModel)]="event.code" />
            </gp-simple-input>
        </gp-form-col>

        <gp-form-col cols="12 6">
            <gp-simple-input label="Descrição">
                <input type="text" name="description" class="form-control" [(ngModel)]="event.description" />
            </gp-simple-input>
        </gp-form-col>
    </gp-form-row>

    <gp-form-row>
        <gp-form-col cols="12 6">
            <label>Tipo do evento <gp-tooltip message="Tipo do evento.">(?)</gp-tooltip></label>
            <gp-select name="factoryId" [items]="eventTypes" [(ngModel)]="event.eventType">
            </gp-select>
        </gp-form-col>

        <gp-form-col cols="12 6">
            <label>Ativo</label>
            <div>
                <gp-switch name="active" [disabled]="hasStarted" [(ngModel)]="event.active"></gp-switch>
            </div>
        </gp-form-col>
    </gp-form-row>

    <gp-form-row>
        <gp-form-col cols="12 6">
            <label> {{rewardTypeFieldTitle}} <gp-tooltip message="Especificação do tipo de valor a ser aplicado.">(?)</gp-tooltip></label>
            <gp-select name="amountType" [items]="amountTypes" [(ngModel)]="event.amountType">
            </gp-select>
        </gp-form-col>


        <gp-form-col cols="12 6">
            <label>Valor</label>
            <gp-input-mask required name="value" [onlyInteger]="true" [(ngModel)]="event.value"></gp-input-mask>
        </gp-form-col>
    </gp-form-row>

    <gp-form-row>
        <gp-form-col cols="12 6">
            <label>Habilitar quantidade máxima <gp-tooltip message="Limita a quantidade máxima do evento no cálculo do resultado.">(?)</gp-tooltip></label>
            <div>
                <gp-switch name="active" [disabled]="hasStarted" [(ngModel)]="event.enableMaximumAmountEvent"></gp-switch>
            </div>
        </gp-form-col>

        <gp-form-col cols="12 6" *ngIf="event.enableMaximumAmountEvent">
            <label>Quantidade máxima <gp-tooltip message="Quantidade máxima do evento para considerar no cálculo do resultado.">(?)</gp-tooltip></label>
            <gp-input-mask required name="maximumAmountEvent" [onlyInteger]="true" [(ngModel)]="event.maximumAmountEvent"></gp-input-mask>
        </gp-form-col>
    </gp-form-row>

    <gp-form-row>
        <gp-form-col cols="12 12 12 12">
            <gp-spinner-button bootstrapClass="primary" [icon]="icon" pull="right" [text]="textButtonSave"
                (click)="saveEvent()">
            </gp-spinner-button>
        </gp-form-col>
    </gp-form-row>
</gp-modal>