import { Injectable } from '@angular/core';

import { Observable } from 'rxjs';
import { ApiService } from '../../../../../core/api/api.service';
import * as moment from 'moment';

@Injectable()
export class CampaignParticipantsGoalsService {

  constructor(private api: ApiService) { }

  //#region mechanic

  getCampaignMechanics(campaignId: string): Observable<any[]> {
    return this.api.get(`/api/campaigns/${campaignId}/mechanics`);
  }

  //#endregion

  //#region Settings

  findCampaignParticipantsGoalParametrizations(campaignId: string, campaignParticipantGoalId: string): Observable<any> {
    return this.api.get(`/api/campaigns/${campaignId}/participants/goals/parametrizations/${campaignParticipantGoalId}`);
  }

  findCampaignParticipantsGoalsParametrizations(campaignId: string, code?: any): Observable<any[]> {
    return this.api.get(`/api/campaigns/${campaignId}/participants/goals/parametrizations`, { code });
  }

  findCampaignParticipantsGoalsParametrizationsActive(campaignId: string): Observable<any[]> {
    return this.api.get(`/api/campaigns/${campaignId}/participants/goals/parametrizations/active`);
  }

  findCampaignParticipantsGoalsParametrizationsActiveAll(campaignId: string): Observable<any[]> {
    return this.api.get(`/api/campaigns/${campaignId}/participants/goals/parametrizations/active/all`);
  }

  inactiveCampaignParticipantsGoalParametrizations(campaignId: string, campaignParticipantGoalId: string): Observable<any> {
    return this.api.put(`/api/campaigns/${campaignId}/participants/goals/parametrizations/${campaignParticipantGoalId}/inactive`);
  }

  saveCampaignParticipantsGoalParametrizations(campaignId: string, parametrizationId: string, data: any): Observable<any> {
    if (parametrizationId) {
      return this.api.putWithStaticContent(`/api/campaigns/${campaignId}/participants/goals/parametrizations/${parametrizationId}`, data);
    }
    return this.api.post(`/api/campaigns/${campaignId}/participants/goals/parametrizations`, data);
  }

  saveGoalLevels(campaignId: string, parametrizationId: string, data: any): Observable<any> {
    return this.api.put(`/api/campaigns/${campaignId}/participants/goals/parametrizations/${parametrizationId}/levels`, data);
  }

  startGoal(campaignId: string, parametrizationId: string): Observable<any> {
    return this.api.put(`/api/campaigns/${campaignId}/participants/goals/parametrizations/${parametrizationId}/start`);
  }

  //#endregion Settings

  //#region Batches

  findCampaignParticipantsGoalsBatchById(campaignId: string, batchId: string): Observable<any> {
    return this.api.get(`/api/campaigns/${campaignId}/participants/goals/batches/${batchId}`);
  }

  findCampaignParticipantsGoalsBatches(campaignId: string, parameters: any, skip: number, limit: number): Observable<any[]> {
    const params: any = {
      skip,
      limit
    };

    if (parameters.batchNumber)
      params.batchNumber = parameters.batchNumber;

    if (parameters.batchStatus)
      params.batchStatus = parameters.batchStatus;

    return this.api.get(`/api/campaigns/${campaignId}/participants/goals/batches`, params);
  }

  findCampaignParticipantsGoalsByBatchId(campaignId: string, batchId: string): Observable<any> {
    return this.api.get(`/api/campaigns/${campaignId}/participants/goals/${batchId}/batches`);
  }

  saveCampaignParticipantsGoalsBatch(campaignId: string, batchCampaignParticipantsGoals: any): Observable<any> {
    return this.api.post(`/api/campaigns/${campaignId}/participants/goals/batches`, batchCampaignParticipantsGoals);
  }

  exportBatchErrors(campaignId: string, batchId: string): Observable<any> {
    return this.api.get(`/api/campaigns/${campaignId}/participants/goals/batches/${batchId}/export/errors`);
  }
  //#endregion Batches

  //#region meta bateu levou
  findGoalAchievedAwarded(campaignId: string, goalId: string): Observable<any> {
    return this.api.get(
      `/api/campaigns/${campaignId}/participants/goals/${goalId}/awardedtargets`
    );
  }
  //#endregion

  //#region exclusive-range-goals
  findExclusiveLevelGoals(campaignId: string, goalId: string): Observable<any[]> {
    return this.api.get(`/api/campaigns/${campaignId}/participants/goals/${goalId}/exclusives-levels`);
  }

  findEvents(campaignId: string, goalId: string): Observable<any[]> {
    return this.api.get(`/api/campaigns/${campaignId}/participants/goals/${goalId}/events`);
  }
  //#endregion

  //#region range-with-intervals
  findIntervalGoalGoals(campaignId: string, goalId: string): Observable<any[]> {
    return this.api.get(`/api/campaigns/${campaignId}/participants/goals/${goalId}/intervals`);
  }
  //#endregion

  consultParticipantGoals(campaignId: string, filters: any ): Observable<any[]> {
    return this.api.post(`/api/campaigns/${campaignId}/participants/goals/consult/find`, filters);
  }

  exportParticipantGoals(campaingId: string, params: any): Observable<any> {
    const filters: any = {
      ...params,
    };

    if (params.consultReferenceDate) {
      filters.consultReferenceDate = moment(params.consultReferenceDate).format('YYYY-MM-DD');
    }

    return this.api.getFile(`/api/campaigns/${campaingId}/participants/goals/consult/export`, filters);
  }
}
