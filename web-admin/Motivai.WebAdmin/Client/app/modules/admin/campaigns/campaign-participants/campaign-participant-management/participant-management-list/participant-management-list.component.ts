import { Component, OnInit, ViewChild, Output, EventEmitter, OnDestroy } from '@angular/core';
import { TabsetComponent } from 'ng2-bootstrap';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { ActivatedRoute } from '@angular/router';
import { CampaignParticipantManagementService } from '../campaign-participant-management.service';
import { CampaignStore } from '../../../campaign.store';
import { Subscription } from 'rxjs';
import { RxjsHelpers } from '../../../../../../shared/helpers/rxjs-helpers';
import { ParticipantImportManuallyComponent } from '../modals/participant-import-manually-modal/participant-import-manually-modal.component';

@Component({
  selector: 'participant-management-list',
  templateUrl: './participant-management-list.component.html'
})
export class ParticipantManagementListComponent implements On<PERSON>nit, On<PERSON><PERSON>roy {
  @ViewChild('tabs') tabs: TabsetComponent;
  @ViewChild('alert') alert: GpAlertComponent;
  @ViewChild('importManuallyModal') importManuallyModal: ParticipantImportManuallyComponent;
  @Output('onEdit') edit: EventEmitter<any> = new EventEmitter();
  
  _campaignId: string;

  params: any = {};
  loading: boolean = false;
  participants: any = [];

  private campaignStore$: Subscription;

  constructor(private campaignStore: CampaignStore, private campaignParticipantManagementService: CampaignParticipantManagementService) { }

  ngOnInit() {
    this.campaignStore$ = this.campaignStore.asObservable
      .subscribe(_ => this._campaignId = this.campaignStore.id);
  }

  ngOnDestroy() {
    RxjsHelpers.unsubscribe(this.campaignStore$);
  }

  get hasParams() {
    return this.params && this.params.document || this.params.login;
  }

  search() {
    if (!this.hasParams) {
      this.alert.showInfo('Informe um filtro para pesquisar');
      this.loading = false;
      return;
    }

    this.loading = true;
    this.alert.clear();

    this.campaignParticipantManagementService.searchByDocument(this._campaignId, this.params)
      .subscribe(
        participants => {
          this.participants = participants;
          this.loading = false;
        },
        err => {
          this.alert.handleAndShowError(err);
          this.loading = false;
        }
      );
  }

  importManually() {
    this.importManuallyModal.openModal();
  }

  editParticipants($event: any) {
    if ($event) {
      this.edit.emit($event);
    }
  }

  handleError(msg: any) {
    return msg ? (msg.message ? msg.message : msg.toString()) : 'Ocorreu um erro ao processar a requisição';
  }

}
