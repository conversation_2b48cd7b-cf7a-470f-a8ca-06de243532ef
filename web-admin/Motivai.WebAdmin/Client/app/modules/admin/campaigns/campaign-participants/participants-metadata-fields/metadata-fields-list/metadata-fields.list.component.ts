import { OnInit, Component, ViewChild, EventEmitter, Output } from '@angular/core';

import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';

import { PlatformPage, DynamicFieldValueType } from '../metadata-fields-models/UserMetadataModel';
import { MetadataFieldService } from '../metadata-fields.service';
import { CampaignStore } from '../../../campaign.store';

import { Item } from '../../../../../../shared/models/item';

@Component({
    selector: 'metadata-fields-list',
    templateUrl: 'metadata-fields-list.component.html'
})
export class MetadataFieldsListComponent implements OnInit {
    @Output('onSelect') onSelect: EventEmitter<any> = new EventEmitter<any>();
    @Output('onEdit') onEdit: EventEmitter<any> = new EventEmitter();
    @ViewChild('alert') alert: GpAlertComponent;

    loading: boolean = false;
    campaignId: string;
    platformPage: any;

    pages: Item[];
    types: Item[];

    metadatafields: any[];

    constructor(private campaignStore: CampaignStore, private metadataFieldService: MetadataFieldService) { }

    ngOnInit() {
        this.campaignId = this.campaignStore.id;
        this.loadTypes();
        this.loadPages();
        this.loadMetadataFields();
    }

    loadMetadataFields() {
        this.loading = true;

        const platformPage = this.platformPage && !this.platformPage.includes('ALL') ? this.platformPage : undefined;

        this.metadataFieldService.getAll(this.campaignId, platformPage)
            .subscribe(metadatafields => {
                if (metadatafields && metadatafields.length) {
                    this.metadatafields = metadatafields.map((y) => {
                        return {
                            description: y.description,
                            active: y.active,
                            property: y.property
                        };
                    });
                } else
                    this.metadatafields = [];

                this.loading = false;
            },
                err => {
                    this.alert.showError(err, true);
                    this.loading = false;
                });
    }


    loadTypes() {
        this.types = [
            Item.of(DynamicFieldValueType.TEXT, 'Texto')
            , Item.of(DynamicFieldValueType.INTEGER, 'Número Inteiro')
            , Item.of(DynamicFieldValueType.DECIMAL, 'Número Decimal')
            , Item.of(DynamicFieldValueType.CHECK, 'Checkbox')
            , Item.of(DynamicFieldValueType.OPTION, 'Select')
            , Item.of(DynamicFieldValueType.DATE, 'Data')
        ];
    }

    loadPages() {
        this.pages = [
            Item.of('ALL', 'Todas')
            , Item.of(PlatformPage.LOGIN, 'Login')
            , Item.of(PlatformPage.PRE_REGISTRATION, 'Pré-Cadastro')
            , Item.of(PlatformPage.FIRST_ACCESS, 'Primeiro Acesso')
            , Item.of(PlatformPage.PASSWORD_CHANGE, 'Alterar Senha')
            , Item.of(PlatformPage.PASSWORD_RESET, 'Recuperar Senha')
            , Item.of(PlatformPage.RANKING, 'Ranking')
            , Item.of(PlatformPage.SUMMARY, 'Extrato')
            , Item.of(PlatformPage.INVITE_PARTICIPANT, 'Convidar Participante')
        ];

        this.platformPage = 'ALL';
    }

    editPage($event) {
        this.onEdit.emit($event);
    }
}
