import { Component, OnInit, ViewChild } from '@angular/core';
import { TabsetComponent } from 'ng2-bootstrap';
import { CampaignParticipantRankingImportingListComponent } from './campaign-participant-ranking-importing-list/campaign-participant-ranking-importing-list.component';
import { CampaignParticipantRankingImportingEditComponent } from './campaign-participant-ranking-importing-edit/campaign-participant-ranking-importing-edit.component';

@Component({
  selector: 'campaign-participant-ranking-importing',
  templateUrl: './campaign-participant-ranking-importing.component.html',
})
export class CampaignParticipantRankingImportingComponent implements OnInit {
  @ViewChild('tabs') tabs: TabsetComponent;
  @ViewChild('list') list: CampaignParticipantRankingImportingListComponent;
  @ViewChild('edit') edit: CampaignParticipantRankingImportingEditComponent;

  constructor() { }

  ngOnInit() {
  }


  showBatchRankingDetails(event) {
    this.edit.batchId = event.id;
    this.edit.findBatchRankingById();
    this.tabs.tabs[1].active = true;
  }

  reloadBatchesRanking() {
    this.edit.clear();
    this.list.params = { skip: 0, limit: 10};
    this.list.findBatchesRanking();
  }

}
