<div>
    <spinner [overlay]="true" [show]="loadingScreen"></spinner>
    <gp-card [first]="true" title="Cadastro de campos dinâmicos">
        <gp-form-row>
            <gp-form-col cols="12 3">
                <gp-simple-input label="Código do campo recebido" [required]="true">
                    <input type="text" name="key" class="form-control" [disabled]="isEditing" required
                        [(ngModel)]="metadata.key" />
                </gp-simple-input>
            </gp-form-col>
            <gp-form-col cols="12 3">
                <gp-simple-input label="Descrição do campo a ser exibido" [required]="true">
                    <input type="text" name="value" class="form-control" required [(ngModel)]="metadata.value" />
                </gp-simple-input>
            </gp-form-col>
            <gp-form-col cols="12 3">
                <gp-simple-input label="Placeholder">
                    <input type="text" name="placeholder" class="form-control" required
                        [(ngModel)]="metadata.placeholder" />
                </gp-simple-input>
            </gp-form-col>
            <gp-form-col cols="12 3">
                <label style="width: 100%;">Ativo: </label>
                <gp-switch value="true" [(ngModel)]="metadata.active"></gp-switch>
            </gp-form-col>
        </gp-form-row>
    </gp-card>

    <gp-card [first]="true" title="Distribuição">
        <gp-form-row>
            <gp-form-col cols="12 5">
                <label>Página</label>
                <gp-select name="page" [items]="pages" [disabled]="isPageEditing()" [(ngModel)]="metadataPage"
                    [placeholder]="page"></gp-select>
            </gp-form-col>
            <gp-form-col cols="12 2">
                <label style="width: 100%;">Ativo: </label>
                <gp-switch value="true" [(ngModel)]="activeSelectPage"></gp-switch>
            </gp-form-col>
            <gp-form-col cols="12 2">
                <gp-spinner-button type="button" [pink]="true" pull="right" marginTop="26px" marginRight="12px"
                    pull="left" [text]="valuesOfButton.editPage" [disabled]="!dataFromPageIsValid() || loading"
                    (click)="addDataFromPage()" [loading]="loading">
                </gp-spinner-button>
            </gp-form-col>
        </gp-form-row>
        <div style="padding-top: 10px;">
            <gp-form-row>
                <gp-form-col cols="12 5">
                    <label>Tipo</label>
                    <gp-select name="type" [items]="types" [disabled]="isPageEditing()" [(ngModel)]="metadataType"
                        [placeholder]="type"></gp-select>
                </gp-form-col>
                <gp-form-col cols="12 2">
                    <label style="width: 100%;">Somente leitura: </label>
                    <gp-switch [(ngModel)]="metadata.readOnly"></gp-switch>
                </gp-form-col>
                <gp-form-col cols="12 2">
                    <label style="width: 100%;">Obrigatório: </label>
                    <gp-switch [(ngModel)]="metadata.required"></gp-switch>
                </gp-form-col>
            </gp-form-row>
        </div>
        <div *ngIf="isSelectType()">
            <hr />
            <gp-form-row>
                <gp-form-col cols="12 3">
                    <gp-simple-input label="Valor para o item" [required]="true">
                        <input type="text" name="keySelectType" class="form-control" required
                            [(ngModel)]="keySelectType" [disabled]="isEditingValueFromSelectType()" />
                    </gp-simple-input>
                </gp-form-col>
                <gp-form-col cols="12 3">
                    <gp-simple-input label="Descrição para o item" [required]="true">
                        <input type="text" name="valueSelectType" class="form-control" required
                            [(ngModel)]="valueSelectType" />
                    </gp-simple-input>
                </gp-form-col>
                <gp-form-col cols="12 1">
                    <gp-simple-input label="Ordem">
                        <gp-input-mask name="order" mask="00" [(ngModel)]="orderSelectType">
                        </gp-input-mask>
                    </gp-simple-input>
                </gp-form-col>
                <gp-form-col cols="12 2">
                    <label style="width: 100%;">Ativo: </label>
                    <gp-switch value="true" [(ngModel)]="activeSelectType"></gp-switch>
                </gp-form-col>
                <gp-form-col cols="12 1">
                    <gp-spinner-button type="button" [pink]="true" pull="right" marginTop="26px" marginRight="12px"
                        pull="left" [text]="valuesOfButton.editValue" [disabled]="!valuesFromSelectIsValid() || loading"
                        (click)="addValueFromSelectType()" [loading]="loading">
                    </gp-spinner-button>
                </gp-form-col>
            </gp-form-row>

            <gp-form-row>
                <gp-form-col cols="12 12 12">
                    <gp-grid [columns]="[ 'Valor', 'Ordem' ]" [fields]="[ 'value', 'order' ]"
                        [rows]="valuesFromSelectType" [loading]="loading"
                        [showEdit]="canCreateOrUpdateValuesFromSelectType()" [showDelete]="false"
                        [showPagination]="false" [showTotalPages]="false" [showActive]="true" [pageSize]="limit"
                        (onEdit)="editPageFromSelectType($event)" (onPageChanged)="pageChangedFromSelectType($event)">
                    </gp-grid>
                </gp-form-col>
            </gp-form-row>
        </div>
        <hr />
        <gp-form-row>
            <gp-form-col cols="12 12 12">
                <gp-grid [columns]="[ 'Página', 'Campo Obrigatório', 'Somente Leitura' ]"
                    [fields]="[ 'page', 'required', 'readOnly' ]" [rows]="distributedPages" [loading]="loading"
                    [showEdit]="canCreateOrUpdateDistributedPages()" [showDelete]="false" [showPagination]="false"
                    [showTotalPages]="false" [showActive]="true" [pageSize]="limit"
                    (onEdit)="editDistributedPage($event)" (onPageChanged)="pageChangedFromSelectType($event)">
                </gp-grid>
            </gp-form-col>
        </gp-form-row>
    </gp-card>
</div>

<gp-card>
    <div class="row">
        <div class="col-md-12">
            <gp-spinner-button type="button" [pink]="true" pull="right" marginRight="5px" pull="right"
                text="Salvar Campo Dinâmico" [disabled]="!formIsvalid() || loading" (click)="save()"
                [loading]="loading">
            </gp-spinner-button>
            <gp-spinner-button type="button" [clear]="true" text="Limpar" marginRight="5px" pull="right"
                (click)="clearAllForm()">
            </gp-spinner-button>
        </div>
    </div>
    <gp-alert #alert [overlay]="true"></gp-alert>
</gp-card>
