<ng-template tabHeading >Endereços</ng-template>

<gp-card [first]="true">
    <gp-form-col cols="12 12 12">
        <table class="table table-striped table-hover">
            <thead>
                <tr>
                    <th>Descrição do Endereço</th>
                    <th>CEP</th>
                    <th>Principal</th>
                    <th>Ativo</th>
                    <th>Ações</th>
                </tr>
            </thead>

            <tbody>
                <tr *ngFor="let a of addresses">
                    <td>{{a.addressName}}</td>
                    <td>{{a.cep}}</td>
                    <td>{{ a.mainAddress ? 'Sim' : 'Não' }}</td>
                    <td>
                        <div class='label label-success' *ngIf="a.active">Ativo</div>
                        <div class='label label-danger' *ngIf="!a.active">Inativo</div>
                    </td>
                    <td class="text-center">
                        <button type="button" title="Ver" class="btn btn-default btn-sm" tooltip="Ver"
                            (click)="editAddress(a)" *ngIf="!canUpdateAddresses">
                            <i title="Ver" class="fa fa-eye"></i>
                        </button>

                        <button type="button" title="Editar" class="btn btn-default btn-sm" tooltip="Editar"
                            (click)="editAddress(a)" *ngIf="canUpdateAddresses">
                            <i title="Editar" class="fa fa-pencil-square-o grid-edit"></i>
                        </button>

                        <button type="button" title="Excluir" tooltip="Deletar" marginTop="26px"
                            class="btn btn-danger btn-sm grid-remove" (click)="removeAddressSelected(a)"
                            *ngIf="canUpdateAddresses">
                            <i title="Excluir" class="fa fa-trash"></i>
                        </button>
                    </td>
                </tr>
                <tr>
                    <td colspan="5" *ngIf="!addresses || !addresses.length">Nenhum registro encontrado.</td>
                </tr>
            </tbody>
        </table>
    </gp-form-col>
    
    <gp-form-col cols="12 12 12" *ngIf="canUpdateAddresses">
        <button class="btn btn-primary pull-right top-m1" (click)="newAddress()">Novo <i title="Excluir" class="fa fa-plus"></i></button>
    </gp-form-col>
</gp-card>

<div>
    <gp-modal title="Dados do endereço" marginTop="10%" width="860px" #modal>
        <spinner [overlay]="true" [show]="loadingModal"></spinner>
        <form #addressForm="ngForm" (ngSubmit)="saveAddress()" novalidate>
            <gp-alert [overlay]="true" #alert></gp-alert>
            <spinner [overlay]="true" [show]="loading"></spinner>

            <gp-card>
                <gp-form-row>
                    <gp-form-col cols="6 6 6">
                        <gp-simple-input [required]="true" label="Descrição do Endereço"
                            errorMessage="Descrição do Endereço é obrigatório">
                            <input type="text" class="form-control" name="addressName" required
                                [(ngModel)]="address.addressName" />
                        </gp-simple-input>
                    </gp-form-col>
                    <gp-form-col cols="6 6 6">
                        <gp-simple-input [required]="false" label="Endereço Principal">
                            <!--gp-switch [(ngModel)]="address.mainAddress"></gp-switch-->
                            <gp-input-checkbox name="mainAddress" [(ngModel)]="address.mainAddress"></gp-input-checkbox>
                        </gp-simple-input>
                    </gp-form-col>
                </gp-form-row>
            </gp-card>

            <gp-card title="Dados do Endereço">
                <gp-form-row>
                    <gp-form-col cols="12 3 3">
                        <gp-simple-input [required]="true" label="CEP" errorMessage="CEP é obrigatório">
                            <gp-input-mask name="cep" mask="99999-999" [(ngModel)]="address.cep"
                                (onblur)="searchAddressByZipcode()">
                            </gp-input-mask>
                        </gp-simple-input>
                    </gp-form-col>
                    <gp-form-col cols="12 9 9">
                        <p style="margin-top: 32px">{{ renderCepStatus }}</p>
                    </gp-form-col>
                </gp-form-row>
                <gp-form-row>
                    <gp-form-col cols="12 6 6">
                        <gp-simple-input [required]="true" label="Logradouro" errorMessage="Logradouro é obrigatório">
                            <input type="text" class="form-control" [disabled]="addressFound" name="address" required
                                [(ngModel)]="address.street" />
                        </gp-simple-input>
                    </gp-form-col>
                    <gp-form-col cols="12 2 2">
                        <gp-simple-input [required]="true" label="Número" errorMessage="Número é obrigatório">
                            <input type="text" class="form-control" name="number" required
                                [(ngModel)]="address.number" />
                        </gp-simple-input>
                    </gp-form-col>
                    <gp-form-col cols="12 4 4">
                        <gp-simple-input label="Complemento">
                            <input type="text" class="form-control" name="complement"
                                [(ngModel)]="address.complement" />
                        </gp-simple-input>
                    </gp-form-col>
                </gp-form-row>
                <gp-form-row>
                    <gp-form-col cols="12 6">
                        <gp-simple-input [required]="true" label="Bairro" errorMessage="Bairro é obrigatório">
                            <input type="text" class="form-control" [disabled]="addressFound" name="neighborhood"
                                [(ngModel)]="address.neighborhood" />
                        </gp-simple-input>
                    </gp-form-col>
                    <gp-form-col cols="12 6 6">
                        <gp-simple-input [required]="true" label="Cidade" errorMessage="Cidade é obrigatório">
                            <input type="text" class="form-control" [disabled]="addressFound" name="city" required
                                [(ngModel)]="address.city" />
                        </gp-simple-input>
                    </gp-form-col>
                </gp-form-row>
                <gp-form-row>
                    <gp-form-col cols="12 6">
                        <gp-simple-input [required]="true" label="Estado" errorMessage="Estado é obrigatório">
                            <gp-brazil-state-selector name="state" required [disabled]="addressFound"
                                [(ngModel)]="address.state"></gp-brazil-state-selector>
                        </gp-simple-input>
                    </gp-form-col>
                    <gp-form-col cols="12 6">
                        <gp-simple-input label="Ponto de Referência">
                            <input type="text" class="form-control" name="reference" [(ngModel)]="address.reference" />
                        </gp-simple-input>
                    </gp-form-col>
                </gp-form-row>
            </gp-card>

            <gp-card title="Dados do Recebedor">
                <gp-form-row>
                    <gp-form-col cols="12 6 6">
                        <gp-simple-input label="Nome">
                            <input type="text" class="form-control" name="receiverName"
                                [(ngModel)]="address.receiverName" />
                        </gp-simple-input>
                    </gp-form-col>
                    <gp-form-col cols="12 6 6">
                        <gp-simple-input label="CPF">
                            <gp-input-mask name="receiverCpf" mask="000.000.000-00" [(ngModel)]="address.receiverCpf">
                            </gp-input-mask>
                        </gp-simple-input>
                    </gp-form-col>
                </gp-form-row>

                <gp-form-row>
                    <gp-form-col cols="12 6 6">
                        <gp-simple-input label="Telefone">
                            <gp-input-mask name="receiverPhone" mask="(00)0000-0000"
                                [(ngModel)]="address.receiverPhone"></gp-input-mask>
                        </gp-simple-input>
                    </gp-form-col>
                    <gp-form-col cols="12 6 6">
                        <gp-simple-input label="Celular">
                            <gp-input-mask name="receiverMobilePhone" mask="(00)00000-0000"
                                [(ngModel)]="address.receiverMobilePhone"></gp-input-mask>
                        </gp-simple-input>
                    </gp-form-col>
                </gp-form-row>

                <gp-form-row>
                    <gp-form-col cols="12 6 6">
                        <gp-simple-input label="E-mail">
                            <input type="text" class="form-control" name="receiverEmail"
                                [(ngModel)]="address.receiverEmail" />
                        </gp-simple-input>
                    </gp-form-col>
                </gp-form-row>
            </gp-card>

            <gp-card>
                <div class="row">
                    <div class="col-md-12">
                        <div [hidden]="!address.active" *ngIf="canUpdateAddresses">
                            <gp-spinner-button type="submit" text="Salvar Endereço" pull="right" [pink]="true"
                                icon="send" loadingText="Processando" [loading]="sending"
                                [disabled]="addressForm.invalid">
                            </gp-spinner-button>
                        </div>

                        <gp-spinner-button text="Cancelar" bootstrapClass="default" pull="right" marginRight="10px"
                            [disabled]="loading" (click)="hideModal()" [showSpinner]="false">
                        </gp-spinner-button>
                    </div>
                </div>
            </gp-card>
        </form>
    </gp-modal>
</div>
