<gp-card title="Parametrizações do Bônus">
	<gp-form-row>
		<gp-form-col cols="12 4">
			<label>Tipo de processamento
				<gp-tooltip spanClass="text-primary">
					<div style="text-align:justify">
						<ul>
							<li>
								Completo: No final do processamento efetua o crédito do bônus.
							</li>
							<li>
								Acompanhamento: No final do processamento não efetua o crédito, o bônus estará sempre no
								status de bloqueado.
							</li>
						</ul>
					</div>
				</gp-tooltip>
			</label>
			<gp-select name="rewardResultProcessType" [items]="goalRewardResultProcessTypes"
				[(ngModel)]="rewardParametrizations.rewardResultProcessType">
			</gp-select>
		</gp-form-col>

		<gp-form-col cols="12 4">
			<label>Tipo de liberação do bônus
				<gp-tooltip spanClass="text-primary">
					<div style="text-align:justify">
						<ul>
							<li>
								Meta Atual: Na liberação da meta é efetuado o crédito apenas do status da meta atual.
							</li>
							<li>
								Todas Metas Mesmo Período:
								Na liberação da meta, será buscado todos os status de metas que tenham o mesmo período
								da meta em processamento e o processamento do bônus das demais metas for do tipo
								Acompanhamento
								(Podendo o período ser de outra meta).
								Obs.: A meta pode ser cadastrada sem processamento de faixas ou eventos. O bônus será a
								soma de
								todos os bônus dos status das metas encontradas, caso a soma seja maior que zero.
							</li>
						</ul>
					</div>
				</gp-tooltip>
			</label>
			<gp-select name="rewardCreditProcessType" [items]="rewardCreditProcessTypes"
				[(ngModel)]="rewardParametrizations.rewardCreditProcessType">
			</gp-select>
		</gp-form-col>
	</gp-form-row>

	<gp-form-row *ngIf="goalCanGenerateCredit">
		<gp-form-col cols="12 4">
			<label>Habilitar bônus proporcional ao resultado
				<gp-tooltip spanClass="text-primary"
					message="Se habilitado o cáculo do bônus será feito da seguinte forma:
						Resultado divido pela pontuação máxima configurada e multiplicado por 100">
				</gp-tooltip>
			</label>
			<div>
				<gp-switch [disabled]="hasStarted"
					[(ngModel)]="rewardParametrizations.enableRewardProportionalToResult">
				</gp-switch>
			</div>
		</gp-form-col>
	</gp-form-row>

	<gp-form-row *ngIf="rewardParametrizations.enableRewardProportionalToResult && goalCanGenerateCredit">
		<gp-form-col cols="12 6">
			<label>Pontução máxima para o cálculo proporcional</label>
			<gp-input-mask required name="rewardValue" [onlyDecimal]="true" [integers]="6" [decimais]="2"
				placeholder="0,00" [disabled]="hasStarted"
				[(ngModel)]="rewardParametrizations.baseValueForProportionalCalculationReward">
			</gp-input-mask>
		</gp-form-col>

		<gp-form-col cols="12 6">
			<label>Porcentagem mínima de atingimento</label>
			<gp-input-mask required name="rewardValue" [onlyDecimal]="true" [integers]="6" [decimais]="2"
				placeholder="0,00" [disabled]="hasStarted"
				[(ngModel)]="rewardParametrizations.minimumProportionalAmountToCredit">
			</gp-input-mask>
		</gp-form-col>

		<gp-form-col cols="12 6">
			<gp-simple-input label="Tipo da pontuação">
				<select class="form-control" [disabled]="hasStarted"
					[(ngModel)]="rewardParametrizations.rewardType">
					<option value="Currency">Reais (R$)</option>
					<option value="Points">Pontos</option>
				</select>
			</gp-simple-input>
		</gp-form-col>

		<gp-form-col cols="12 6">
			<label>Valor máximo do bônus</label>
			<gp-input-mask required name="rewardValue" [onlyDecimal]="true" [integers]="6" [decimais]="2"
				placeholder="0,00" [disabled]="hasStarted"
				[(ngModel)]="rewardParametrizations.maximumRewardAmount">
			</gp-input-mask>
		</gp-form-col>
	</gp-form-row>
</gp-card>