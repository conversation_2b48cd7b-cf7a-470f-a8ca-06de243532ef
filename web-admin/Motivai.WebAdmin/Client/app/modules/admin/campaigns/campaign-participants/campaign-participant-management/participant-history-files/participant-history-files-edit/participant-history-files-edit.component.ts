import { CampaignStore } from '../../../../campaign.store';
import { GpFileUploadComponent } from '../../../../../../../shared/components/gp-fileupload/gp-fileupload.component';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { Component, OnInit, ViewChild, Input, EventEmitter, Output } from '@angular/core';
import { ParticipantHistoryFilesService } from '../participant-history-files.service';

@Component({
  selector: 'participant-history-files-edit',
  templateUrl: './participant-history-files-edit.component.html'
})
export class ParticipantHistoryFilesEditComponent implements OnInit {
  @ViewChild('alert') alert: GpAlertComponent;
  @ViewChild('gpFile') gpFile: GpFileUploadComponent;
  @Output('view') view: EventEmitter<any> = new EventEmitter();

  @Input()
  set _userId(id: string) {
      if (!id) {
          return;
      }
      this.userId = id;
  }

  loadingScreen: boolean = false;
  uploadPath: string = '';
  campaignId: string = '';
  userId: string = '';


  historyFiles: any = {
    name: '',
    url: ''
  };

  constructor(private campaignStore: CampaignStore, private participantHistoryFilesService: ParticipantHistoryFilesService) { }

  ngOnInit() {
    this.campaignId = this.campaignStore.id;
  }


  saveParticipantHistoryFile() {
    this.loadingScreen = true;
    this.participantHistoryFilesService.save(this.userId, this.campaignId, this.historyFiles).subscribe(
      historyFiles => {
          if (historyFiles) {
            this.alert.showSuccess('Arquivo salvo com sucesso');
            this.updateList();
          }
          this.loadingScreen = false;
      },
      err => {
          this.loadingScreen = false;
          this.alert.showError(err);
      }
    );
  }


  uploadParticipantHistoryFile() {
    if (!this.gpFile || !this.gpFile.hasSelectedFile()) {
      this.alert.showWarning('Selecione um arquivo para upload.');
      return;
    }

    this.loadingScreen = true;
    this.gpFile.path = `api/users/${this.userId}/campaigns/${this.campaignId}/historyfiles/upload`;

    this.gpFile.onComplete = uploadEvent => {
      this.loadingScreen = false;
      let response = JSON.parse(uploadEvent.response);
      if (uploadEvent.success && response.success) {
        this.alert.showSuccess('Arquivo salvo com sucesso.');
        const fileUrl = this.gpFile.extractResultFromResponse(uploadEvent);
        this.historyFiles.url = fileUrl;
        this.saveParticipantHistoryFile();
      } else {
        return this.alert.showError(uploadEvent.errorMessage || response.error || 'Ocorreu um erro ao fazer o upload do arquivo de histórico do participante.');
      }
      this.gpFile.createUploader();
    };

    this.gpFile.uploadFile();
  }

  updateList() {
    this.view.emit({teste: 'chegou aqui'});
  }

}
