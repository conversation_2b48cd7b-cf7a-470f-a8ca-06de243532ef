import { CampaignParticipantsGoalsResultsDetailsPopup } from './campaign-participants-goals-results-details-popup/campaign-participants-goals-results-details-popup.component';
import { Component, OnInit, ViewChild } from "@angular/core";
import { Item } from "../../../../../../shared/models/item";
import { CampaignStore } from "../../../campaign.store";
import { Subscription } from "rxjs";
import { CampaignParticipantsGoalsService } from "../campaign-participants-goals.service";
import { GpAlertComponent } from "../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component";
import { FormatHelper } from '../../../../../../shared/formatters/format-helper';
import { GpGridComponent } from "../../../../../../shared/components/gp-grid/gp-grid.component";
import { GoalLevelProgress } from "./model/goal-level-progress";
import { isNullOrEmpty } from '../../../../../../shared/helpers/comparators';
import { PeriodType } from '../campaign-participants-goals-parametrizations/actions/models/period-type';
import { saveAs } from "file-saver";
@Component({
  selector: 'campaign-participants-goals-results',
  templateUrl: './campaign-participants-goals-results.component.html'
})
export class CampaignParticipantsGoalsResultsComponent implements OnInit {
  @ViewChild('alert') alert: GpAlertComponent;
  @ViewChild('grid') grid: GpGridComponent;
  @ViewChild('goalDetails') goalDetails: CampaignParticipantsGoalsResultsDetailsPopup;

  nestedColumns: Array<string>;
  nestedFields: Array<string>;
  campaignId: string = '';
  private campaignSubs: Subscription;
  get loading() { return this._loading || (this.loadingConsult && this.campaignParticipantsGoals.length <= 0) }
  _loading: boolean;
  loadingConsult: boolean;
  campaignGoals: Item[] = [];
  filters = {
    campaignGoalId: "",
    consultReferenceDate: "",
    periodType: "",
    document: "",
    skip: 0,
    limit: 20
  };
  campaignParticipantsGoals: any[] = new Array<Item>();

  periodTypes: Array<Item> = [
		Item.of(PeriodType.NONE, 'Nenhum'),
		Item.of(PeriodType.WEEKLY, 'Semanal'),
		Item.of(PeriodType.MONTHLY, 'Mensal'),
		Item.of(PeriodType.YEARLY, 'Anual'),
		Item.of(PeriodType.CUSTOM, 'Customizado')
  ];

  constructor(private _campaignStore: CampaignStore, private campaignParticipantsGoalsService: CampaignParticipantsGoalsService) { }

  ngOnInit(): void {
    this.campaignSubs = this._campaignStore.asObservable
      .subscribe(campaignId => { this.campaignId = campaignId;
      this.findCampaignParticipantsGoalsParametrizationsActive();
    });
  }


  findCampaignParticipantsGoalsParametrizationsActive() {
    this._loading = true;
    if (!this.campaignId) {
      return;
    }

    this.campaignParticipantsGoalsService.findCampaignParticipantsGoalsParametrizationsActiveAll(this.campaignId)
      .subscribe(
        response => {
          if (response && response.length > 0) {
            this.campaignGoals = new Array<Item>();
            response.forEach(goal => {
              this.campaignGoals.push(Item.of(goal.id, `${goal.description} (${goal.code})`));
            })
            this._loading = false;
          } else {
            this._loading = false;
            this.alert.showInfo("Ops, nenhuma meta encontrada nessa campanha");
          }
        },
        err => {
          this._loading = false;
          this.alert.showError(err);
        }
      );
  }

  hasGoals() {
    return this.campaignParticipantsGoals.length > 0;
  }

  onClickSearch() {
    this.filters.skip = 0;
    this.filters.limit = 20;
    if (this.grid) {
      this.grid.resetPagination();
    }
    this.search();
  }

  search() {
    this.loadingConsult = true;
    this.campaignParticipantsGoalsService.consultParticipantGoals(this.campaignId, this.filters)
      .subscribe(
        response => {
          if (response && response.length > 0) {
            this.campaignParticipantsGoals = response.filter(goal => this.validateGoal(goal));
            this.campaignParticipantsGoals.forEach(goal => {
              goal.formattedDate = FormatHelper.formatDate(goal.createDate);
              goal.formattedSimulation = (goal.simulation) ? "Sim" : "Não";
              goal.formattedPoints = this.getFormattedPoints(goal);
              goal.name = (isNullOrEmpty(goal.name)) ? this.getCampaignGoalNameById(goal.campaignGoalId) : goal.name;
            })

            this.loadingConsult = false;
          } else {
            this.loadingConsult = false;
            this.alert.showInfo("Ops, nenhuma meta encontrada com os filtros selecionados.");
          }
        },
        err => {
          this.loadingConsult = false;
          this.alert.showError(err);

        }
      );
  }

  validateGoal(goal: any): boolean {
    return goal.campaignGoalFormat != null;
  }

  getFormattedPoints(goal: any): string {
    return (goal.rewardAmount != null && goal.rewardAmount.points) ? goal.rewardAmount.points : 0;
  }

  onPageChanged(page: any) {
    if (page) {
      this.filters.skip = page.skip;
      this.filters.limit = page.limit;
      this.search();
    }
  }

  exportGoals() {
		this._loading = true;
		this.campaignParticipantsGoalsService.exportParticipantGoals(this.campaignId, this.filters)
			.subscribe((response: any) => {
				saveAs(response._body, "metas.xls");
				this._loading = false;
			}, err => {
				this._loading = false;
				this.alert.showError(err);
			});
	}

  openDetailsPopup(goal: any){
    goal.nestedData = Array<GoalLevelProgress>();

    switch(goal.campaignGoalFormat){
      case 'BY_EXCLUSIVE_LEVEL':
        goal.nestedColumns  = [ "Meta", "Level", "Atingiu", "Premio", "ValorMeta", "Resultado Atual" ];
				goal.nestedFields   = [ "goalName", "index", "targetReached", "estimatedRewardPointsAmount", "baseTargetValue", "currentResult" ];
        goal.exclusiveLevelGoal.exclusivesLevelsGoals.forEach(level => {
          goal.nestedData.push(GoalLevelProgress.buildFromLevel(level))
        });
        break;
      case 'BY_INTERVALS':
        goal.nestedColumns  = [ "Meta", "Level", "Atingiu", "Premio", "ValorMeta", "Resultado Atual" ];
				goal.nestedFields   = [ "goalName", "index", "targetReached", "estimatedRewardPointsAmount", "baseTargetValue", "currentResult" ];
        goal.intervalGoal.intervalsGoals.forEach(level => {
          goal.nestedData.push(GoalLevelProgress.buildFromLevel(level))
        });
        break;
      case 'BY_GOAL_LEVELS':
        goal.nestedColumns  = [ "Meta", "Level", "Atingiu", "Premio", "ValorMeta", "Resultado Atual" ];
				goal.nestedFields   = [ "goalName", "index", "targetReached", "estimatedRewardPointsAmount", "baseTargetValue", "currentResult" ];
        goal.goalsLevels.forEach(level => {
          goal.nestedData.push(GoalLevelProgress.buildFromLevel(level))
        });
        break;
      case 'BY_TARGET_AWARDED':
        goal.nestedColumns  = [ "Meta", "Atingiu", "Premio", "ValorMeta", "Resultado Atual" ];
				goal.nestedFields   = [ "goalName", "targetReached", "estimatedRewardPointsAmount", "baseTargetValue", "currentResult" ];
        goal.goalAchievedAwarded.awardedTargetGoals.forEach(level => {
          goal.nestedData.push(GoalLevelProgress.buildFromLevelAwardedTargetGoal(level))
        });
        break;
    }
    goal.nestedData = goal.nestedData.sort((l1, l2) => l1.index - l2.index );

    this.goalDetails.showDetails(goal);
  }
  getCampaignGoalNameById(id: string) : string {
    for (var goal of this.campaignGoals) {
      if(goal.id == id){
        return goal.text;
      }
    }
    return "";
  }

}