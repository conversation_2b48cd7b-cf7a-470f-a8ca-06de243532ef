import { CampaignStore } from '../../../../campaign.store';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { ParticipantHistoryFilesService } from '../participant-history-files.service';
import { Component, OnInit, Output, EventEmitter, ViewChild, Input } from '@angular/core';
import { AuthStore } from '../../../../../../../core/auth/auth.store';

@Component({
  selector: 'participant-history-files-list',
  templateUrl: './participant-history-files-list.component.html',
})
export class ParticipantHistoryFilesListComponent implements OnInit {
  @Output('edit') edit: EventEmitter<any> = new EventEmitter();
  @ViewChild('alert') alert: GpAlertComponent;

  @Input()
  set _userId(id: string) {
      if (!id) {
          return;
      }
      this.userId = id;
  }


  loadingGrid: boolean = false;

  campaignId: string = '';
  userId: string = '';
  historyFiles: any[] = [];

  constructor(private _authStore: AuthStore, private campaignStore: CampaignStore,
    private participantHistoryFilesService: ParticipantHistoryFilesService) { }

  get canUpdateHistoricFiles() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_PARTICIPANTS_MANAGEMENT_UPDATE_HISTORIC_FILES;
  }

  ngOnInit() {
    this.campaignId = this.campaignStore.id;
  }

  findParticipantHistoryFiles() {
    this.loadingGrid = true;
    this.participantHistoryFilesService.getParticipantHistoryFiles(this.userId, this.campaignId).subscribe(
      historyFiles => {
          this.historyFiles = historyFiles;
          this.loadingGrid = false;
      },
      err => {
          this.loadingGrid = false;
          this.alert.showError(err);
      }
    );
  }

  onDelete(event: any) {
    this.loadingGrid = true;
    this.participantHistoryFilesService.unactiveParticipantHistoryFiles(this.userId, this.campaignId, event.id).subscribe(
      historyFiles => {
          if (historyFiles) {
            this.alert.showSuccess('Arquivo inativado com sucesso');
            this.findParticipantHistoryFiles();
          }

          this.loadingGrid = false;
      },
      err => {
          this.loadingGrid = false;
          this.alert.showError(err);
      }
    );
  }

}
