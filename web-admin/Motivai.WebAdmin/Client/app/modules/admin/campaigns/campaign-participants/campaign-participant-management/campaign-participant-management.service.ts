import { Injectable } from '@angular/core';

import { ApiService } from '../../../../../core/api/api.service';
import { Observable } from 'rxjs/Observable';

@Injectable()
export class CampaignParticipantManagementService {
  constructor(private _api: ApiService) { }

  public searchByDocument(campaignId: string, params: any): any {
    return this._api.get(`/api/campaigns/${campaignId}/participants/search`, params);
  }

  public getParticipantDataById(userId: string, campaignId: string): any {
    return this._api.get(`/api/users/${userId}/campaigns/${campaignId}/management/registration`);
  }

  public getParticipantMetadata(userId: string, campaignId: string): Observable<any> {
    return this._api.get(`/api/users/${userId}/campaigns/${campaignId}/metadata`);
  }

  public getParticipantParent(userId: string, campaignId: string): Observable<any> {
    return this._api.get(`/api/users/${userId}/campaigns/${campaignId}/management/parent`);
  }

  public linkParent(userId: string, campaignId: string, parent: any): Observable<any> {
    return this._api.put(`/api/users/${userId}/campaigns/${campaignId}/management/parent`, parent);
  }

  public getAddressesParticipant(userId: string, campaignId: string): any {
    return this._api.get(`/api/users/${userId}/campaigns/${campaignId}/management/addresses`);
  }

  public getAllAddressesParticipant(userId: string, campaignId: string): any {
    return this._api.get(`/api/users/${userId}/campaigns/${campaignId}/management/addresses/all`);
  }

  public updateUserManagemenet(userId: string, campaignId: string, participant: any): any {
    return this._api.put(`/api/users/${userId}/campaigns/${campaignId}/management/registration`, participant);
  }

  public getAccountOperators(userId: string, campaignId: string): Observable<any[]> {
    return this._api.get(`/api/users/${userId}/campaigns/${campaignId}/account/operators`);
  }

  public findAccountOperatorByDocument(userId: string, campaignId: string, document: string): Observable<any> {
    return this._api.get(`/api/users/${userId}/campaigns/${campaignId}/account/operators/search`, { document });
  }

  public createAccountOperator(userId: string, campaignId: string, operator: any): Observable<any> {
    return this._api.post(`/api/users/${userId}/campaigns/${campaignId}/account/operators`, operator);
  }

  public resetAccountOperatorPassword(userId: string, campaignId: string, operatorId: string, loginId: string): Observable<any> {
    return this._api.post(`/api/users/${userId}/campaigns/${campaignId}/account/operators/${operatorId}/logins/${loginId}/password/reset`, {});
  }

  public blockAccountOperator(userId: string, campaignId: string, operatorBlocking: any): Observable<any> {
    return this._api.post(`/api/users/${userId}/campaigns/${campaignId}/account/operators/block`, operatorBlocking);
  }

  public activeAccountOperator(userId: string, campaignId: string, operatorActive: any): Observable<any> {
    console.log("campaignId: ", campaignId)
    return this._api.post(`/api/users/${userId}/campaigns/${campaignId}/account/operators/active`, operatorActive);
  }

  public updateAccountOperatorEmail(userId: string, campaignId: string, operatorId: string, loginId: string, body: any): Observable<any> {
    return this._api.post(`/api/users/${userId}/campaigns/${campaignId}/account/operators/${operatorId}/logins/${loginId}/email`, body);
  }

  public importParticipantManually(campaignId: string, participant: any): Observable<any> {
    return this._api.post(`/api/users/campaigns/${campaignId}/participants/management/import`, participant, 20000);
  }

  public checkPersonAtCredify(campaignId: string, document: string): Observable<any> {
    return this._api.get(`/api/users/document/${document}`, { campaignId });
  }

  public updateOperatorRole(userId: string, campaignId: string, operatorId: string, loginId: string, payload: any): Observable<any> {
    return  this._api.put(`/api/users/${userId}/campaigns/${campaignId}/account/operators/${operatorId}/logins/${loginId}/role`, payload);
  }

	public getAllFields(campaignId: string, platformPage?: string): Observable<Array<any>> {
    if (platformPage && platformPage.length) {
      return this._api.get(`/api/campaigns/${campaignId}/settings/metadata/fields`, { platformPage });
    }
		return this._api.get(`/api/campaigns/${campaignId}/settings/metadata/fields/all`);
  }

  public findUserCampaignsByDocument(document: string, campaignId: string = null): Observable<any> {
    const params: any = {};
    if (campaignId && campaignId.length) {
      params.campaignId = campaignId;
    }
    return this._api.get(`/api/users/document/${document}/campaigns`, params);
  }

  public moveParticipant(userId: string, campaignId: string, userMoveRequest: any): Observable<any> {
    return this._api.post(`/api/users/${userId}/campaigns/${campaignId}/management/move`, userMoveRequest);
  }

  public resetParticipantWallet(campaignId: string, userId: any) {
    return this._api.put(`/api/users/${userId}/campaigns/${campaignId}/management/cards/reset`);
  }

}
