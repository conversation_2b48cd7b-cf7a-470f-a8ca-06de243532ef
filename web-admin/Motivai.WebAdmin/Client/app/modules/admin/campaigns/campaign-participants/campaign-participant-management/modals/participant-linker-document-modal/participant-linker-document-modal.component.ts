import { Component, ViewChild, Input, Output, EventEmitter } from '@angular/core';

import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpModalComponent } from '../../../../../../../shared/components/gp-modal/gp-modal.component';
import { CampaignParticipantManagementService } from '../../campaign-participant-management.service';

@Component({
    selector: 'participant-linker-document-modal',
    templateUrl: './participant-linker-document-modal.component.html'
})
export class ParticipantLinkerDocumentModalComponent {
    @ViewChild('modal') modal: GpModalComponent;
    @ViewChild('alert') alert: GpAlertComponent;
    @Output('close') onclose = new EventEmitter<any>();

    @Input() userId: string;
    @Input() campaignId: string;

    participant: any[] = [];

    customIcon: string = 'plus';
    params: any = {};

    loading: boolean = false;

    constructor(private campaignParticipantManagementService: CampaignParticipantManagementService) { }

    openModal() {
        this.modal.show();
    }

    search() {
        this.loading = true;
        this.participant = [];
        this.campaignParticipantManagementService.findUserCampaignsByDocument(this.params.document)
            .subscribe(
                participant => {
                  this.loading = false;
                  if (participant) {
                    this.participant.push(participant);
                  }
                },
                err => {
                    this.alert.showError(err);
                    this.loading = false;
                }
            );
    }

    moveParticipant(event) {
        this.loading = true;
        this.campaignParticipantManagementService.moveParticipant(this.userId, this.campaignId, { userId: event.userId, userDocument: this.params.document })
            .subscribe(
                response => {
                  this.loading = false;
                    if (response) {
                        this.alert.showSuccess(`Participante vinculado ao CPF/CNPJ ${this.params.document}`);
                    }
                    this.closeModal();
                }, err => {
                    this.loading = false;
                    this.alert.showError(err);
                }
            );
    }

    closeModal() {
        this.modal.hide();
        this.clear();
        this.onclose.emit({});
    }

    clear() {
        this.params = {};
        this.participant = [];
    }
}
