import { Component, EventEmitter, Output, ViewChild, Input } from '@angular/core';
import { v4 as uuid } from 'uuid';

import { GpModalComponent } from '../../../../../../../../shared/components/gp-modal/gp-modal.component';
import { GpAlertComponent } from '../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { isNullOrEmpty } from '../../../../../../../../shared/helpers/comparators';
import { FormatHelper } from '../../../../../../../../shared/formatters/format-helper';
import { Item } from '../../../../../../../../shared/models/item';

@Component({
  selector: 'goal-periods-parametrization',
  templateUrl: './goal-periods-parametrization.component.html'
})
export class GoalPeriodsParametrizationComponent {
  @ViewChild('goalPeriodParams') goalPeriodParamsModal: GpModalComponent;
  @ViewChild('alert') alert: GpAlertComponent;

  @Input() customGoalPeriods: any[] = [];
  @Output('goalPeriods') goalPeriodEmitter: EventEmitter<any[]> = new EventEmitter();

  periodFormats: Array<Item> = [
    Item.of('UNIQUE', 'Período único'),
    Item.of('MULTI_WITH_MONTHLY_RESULT', 'Múltiplos com resultado mensal'),
    Item.of('MULTI_WITHOUT_MONTHLY_RESULT', 'Múltiplos sem resultado mensal')
  ];

  goalPeriod: any = {};
  copiedGoalPeriod: any = {};
  isEditing: boolean = false;
  loading: boolean = false;

  get textButton() {
    if (this.isEditing) {
      return 'Atualizar';
    }
    return 'Adicionar';
  }
  
  newGoalPeriod() {
    this.clear();
    this.showModal();
  }

  editGoalPeriod(event: any) {
    this.isEditing = true;
    this.goalPeriod = event;

    this.copyGoalPeriod();
    this.showModal();
  }

  private copyGoalPeriod(): void {
    this.copiedGoalPeriod = JSON.parse(JSON.stringify(this.goalPeriod));
  }

  private showModal(): void {
    this.goalPeriodParamsModal.show();
  }

  private hideModal(): void {
    this.clear();
    this.goalPeriodParamsModal.hide();
  }

  addPeriod(): void {
    if (!this.validate())
      return;

    const periodKey = this.generatePeriodKey();
    if (isNullOrEmpty(periodKey))
      return this.alert.showWarning('Não possível geral o código do período. Tente novamente');
    
    if (this.existsGoalPeriodWith(periodKey))
      return this.alert.showWarning('Já existe um período dentro do intervalo das datas');

    this.goalPeriod.periodKey = periodKey;

    this.formatDates();
    this.addOrUpdateGoalPeriod();

    this.goalPeriodEmitter.emit(this.customGoalPeriods);
    this.hideModal();
  }

  private formatDates(): void {
    this.goalPeriod.formattedInitialDate = FormatHelper.formatDate(this.goalPeriod.initialDate);
    this.goalPeriod.formattedEndDate = FormatHelper.formatDate(this.goalPeriod.endDate);
  }

  private validate(): boolean {
    if (!this.goalPeriod)
      return false;

    if (isNullOrEmpty(this.goalPeriod.periodFormat)) {
      this.alert.showWarning("Selecione o formato.");
      return false;
    }
    if (isNullOrEmpty(this.goalPeriod.description)) {
      this.alert.showWarning("Informe a descrição.");
      return false;
    }
    if (!this.goalPeriod.initialDate) {
      this.alert.showWarning("Informe a data inicial.");
      return false;
    } 
    if (!this.goalPeriod.endDate) {
      this.alert.showWarning("Informe a data final.");
      return false;
    }

    let initialDate = this.parseDate(this.goalPeriod.initialDate);
    let endDate = this.parseDate(this.goalPeriod.endDate);
    if ((endDate.getFullYear() < initialDate.getFullYear()) || 
        (endDate.getMonth() < initialDate.getMonth() && endDate.getFullYear() < initialDate.getFullYear())) {
      this.alert.showWarning("Data final deve ser maior que a data inicial.");
      return false;
    }
    return true; 
  }

  private generatePeriodKey(): string {
    if (this.goalPeriod.periodFormat === 'UNIQUE')
      return 'ALL';

    let initialDate = this.parseDate(this.goalPeriod.initialDate);
    let endDate = this.parseDate(this.goalPeriod.endDate); 

    const iniYear = initialDate.getFullYear();
    let periodKey = '';
  
    for (var i = iniYear; i <= endDate.getFullYear(); i++) {
      let iniMonth = i !== initialDate.getFullYear() ? 0 : initialDate.getMonth();
      let endMonth = i !== endDate.getFullYear() ? 11 : endDate.getMonth();

      for (var j = iniMonth; j <= endMonth; j++) {
        periodKey = periodKey.concat([i, j + 1].join('-'));

        if (j < endMonth || i < endDate.getFullYear())
          periodKey = periodKey.concat('#');
      }
    }
    return periodKey;
  }

  private parseDate(date: any): Date {
    if (!date)
      return new Date();
    return new Date(date);
  }

  private existsGoalPeriodWith(periodKey: string): boolean {
    const exists = this.findGoalPeriodIndexByPeriodKeyAndPeriodFormat(periodKey) >= 0;

    if (!this.isEditing && exists)
      return true;
    if (this.isEditing && exists) {
      this.setGoalPeriodWithPreviousValues();
      return true;
    }

    return false;
  }

  private findGoalPeriodIndexByPeriodKeyAndPeriodFormat(goalPeriodKey: string): number {
    if (!this.customGoalPeriods)
			return null;

		return this.customGoalPeriods.findIndex(p => p.id !== this.goalPeriod.id && p.periodKey === goalPeriodKey && p.periodFormat === this.goalPeriod.periodFormat);
  }

  private setGoalPeriodWithPreviousValues(): void {
    this.goalPeriod = JSON.parse(JSON.stringify(this.copiedGoalPeriod));
    const goalPeriodIndex = this.findGoalPeriodIndexById(this.goalPeriod.id);
    this.updateGoalPeriod(goalPeriodIndex);
  }

  private addOrUpdateGoalPeriod(): void {
    if (!this.customGoalPeriods) {
      this.customGoalPeriods = new Array<any>();
      return this.addGoalPeriod();
    }
    const goalPeriodIndex = this.findGoalPeriodIndexById(this.goalPeriod.id);
    if (goalPeriodIndex < 0)
      return this.addGoalPeriod();

    this.updateGoalPeriod(goalPeriodIndex);
  }

  private findGoalPeriodIndexById(id: string): number {
    if (!this.customGoalPeriods)
			return -1;

		return this.customGoalPeriods.findIndex(p => p.id === id);
  }

  private addGoalPeriod(): void {
    this.goalPeriod.id = uuid();
    this.customGoalPeriods.push(this.goalPeriod);
  }

  private updateGoalPeriod(goalPeriodIndex: number): void {
    this.customGoalPeriods[goalPeriodIndex] = this.goalPeriod;
  }

  private clear() {
    this.isEditing = false;
    this.goalPeriod = {};
    this.copiedGoalPeriod = {};
  }
}
