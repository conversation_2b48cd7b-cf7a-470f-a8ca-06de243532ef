<gp-modal title="Intervalo da Meta" width="700px" #intervalGoalModal>
    <gp-alert #alert [overlay]="true"></gp-alert>
    <spinner [overlay]="true" [show]="saving"></spinner>

    <gp-form-row>
        <gp-form-col cols="12 6">
            <gp-simple-input label="Nome">
                <input type="text" name="name" class="form-control" [(ngModel)]="intervalGoal.name" />
            </gp-simple-input>
        </gp-form-col>

        <gp-form-col cols="12 6">
            <label>Ativo</label>
            <div>
                <gp-switch name="active" [disabled]="hasStarted" [(ngModel)]="intervalGoal.active"></gp-switch>
            </div>
        </gp-form-col>
    </gp-form-row>

    <gp-form-row>
        <gp-form-col cols="12 6">
            <gp-simple-input label="Código">
                <input type="text" name="code" class="form-control" [disabled]="hasStarted"
                    [(ngModel)]="intervalGoal.code" />
            </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 6">
            <label>Faturamento (Piso)</label>
            <gp-input-mask required name="baseTargetValue" [onlyInteger]="true" [disabled]="hasStarted" [(ngModel)]="intervalGoal.baseTargetValue">
            </gp-input-mask>
        </gp-form-col>
    </gp-form-row>

    <gp-form-row>
        <gp-form-col cols="12 6">
            <label>
                Habilitar cashback proporcional do excedente
                <gp-tooltip spanClass="text-primary">
                    <div style="text-align:justify">
                        Habilita o cálculo do cashback proporcional ao intervalo incompleto.
                    </div>
                </gp-tooltip>
            </label>
            <div>
                <gp-switch name="allowProportionalBonusForUncompletedInterval" [disabled]="hasStarted"
                    [(ngModel)]="intervalGoal.allowProportionalBonusForUncompletedInterval">
                </gp-switch>
            </div>
        </gp-form-col>
        <gp-form-col cols="12 6">
            <label>Intervalo (R$)</label>
            <gp-input-mask required name="interval" [onlyDecimal]="true" [integers]="6" [decimais]="2"
                placeholder="0,00" [disabled]="hasStarted" [(ngModel)]="intervalGoal.interval"></gp-input-mask>
        </gp-form-col>
    </gp-form-row>

    <gp-form-row>
        <gp-form-col cols="12 6">
            <gp-simple-input label="Método da pontuação">
                <gp-select [disabled]="hasStarted" [items]="rewardMethods" [(ngModel)]="intervalGoal.rewardMethod">
                </gp-select>
            </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 6" *ngIf="isCashBackRewardMethod">
            <label>Cashback (%)</label>
            <gp-input-mask required name="cashback" [onlyDecimal]="true" [integers]="6" [decimais]="2"
                placeholder="0,00" [disabled]="hasStarted" [(ngModel)]="intervalGoal.rewardValue">
            </gp-input-mask>
        </gp-form-col>
        <gp-form-col cols="12 6" *ngIf="isPointsRewardMethod">
            <gp-simple-input label="Tipo da pontuação">
                <select class="form-control" [disabled]="hasStarted" [(ngModel)]="intervalGoal.rewardType">
                    <option value="Currency">Reais (R$)</option>
                    <option value="Points">Pontos</option>
                </select>
            </gp-simple-input>
        </gp-form-col>
    </gp-form-row>

    <gp-form-row *ngIf="isPointsRewardMethod">
        <gp-form-col cols="12 6">
            <label>Valor do ganho</label>
            <gp-input-mask required name="points" [onlyDecimal]="true" [integers]="6" [decimais]="2" placeholder="0,00"
                [disabled]="hasStarted" [(ngModel)]="intervalGoal.rewardValue">
            </gp-input-mask>
        </gp-form-col>
    </gp-form-row>

    <gp-form-row>
        <gp-form-col cols="12 12 12 12">
            <gp-spinner-button bootstrapClass="primary" [icon]="icon" pull="right" [text]="textButtonSave"
                (click)="saveGoal()">
            </gp-spinner-button>
        </gp-form-col>
    </gp-form-row>
</gp-modal>
