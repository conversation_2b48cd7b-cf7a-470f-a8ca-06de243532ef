<gp-modal title="Desbloqueio do Participante" width="800px" (onClose)="onClose()" #modal>
	<gp-alert #gpAlert [overlay]="true"></gp-alert>

	<form>
		<gp-form-row>
			<gp-form-col cols="12 12">
				<gp-simple-input label="Motivo do desbloqueio" [required]="true">
					<textarea required class="form-control" rows="5" name="reasonUnblocked" [(ngModel)]="reason">
					</textarea>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-spinner-button type="button" [actionPrimary]="true" pull="right" loadingText="Processando" text="Desbloquear" [loading]="sending"
					(click)="unblockParticipant()">
				</gp-spinner-button>

				<gp-spinner-button type="button" [actionBack]="true" pull="right" marginRight="1em"
					text="Voltar" (click)="closeModal()">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</form>
</gp-modal>
