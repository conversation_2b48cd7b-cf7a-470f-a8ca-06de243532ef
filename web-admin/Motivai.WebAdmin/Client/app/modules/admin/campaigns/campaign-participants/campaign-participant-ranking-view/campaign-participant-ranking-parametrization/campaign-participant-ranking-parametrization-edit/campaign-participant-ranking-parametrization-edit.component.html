<spinner [overlay]="true" [show]="loading"></spinner>
<gp-alert #alert [overlay]="true"></gp-alert>

<gp-card title="Cadastro de Ranking Dinâmico" [first]="true">
    <gp-form-row>
        <gp-form-col cols="12 6">
            <gp-simple-input label="Nome">
                <input type="text" name="name" class="form-control" [(ngModel)]="rankingParametrization.name" />
            </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 6">
            <div>
                <label>Ativo</label>
            </div>
            <gp-switch name="active" [(ngModel)]="rankingParametrization.active"></gp-switch>
        </gp-form-col>
    </gp-form-row>
    <gp-form-row>
        <gp-form-col cols="12 6">
            <gp-simple-input label="Descrição (Exibida no site campanha)" [required]="true">
                <input type="text" name="description" class="form-control" required
                    [(ngModel)]="rankingParametrization.description" />
            </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 6">
            <gp-simple-input label="Código" [required]="true">
                <input type="text" name="code" class="form-control" required [(ngModel)]="rankingParametrization.code" />
            </gp-simple-input>
        </gp-form-col>
    </gp-form-row>
    <gp-form-row>
        <gp-form-col cols="12 6">
            <div>
                <label>Habilitar Notificação</label>
            </div>
            <gp-switch name="enableNotification" [(ngModel)]="rankingParametrization.enableNotification"></gp-switch>
        </gp-form-col>
        <gp-form-col cols="12 6">
            <div>
                <label>Habilitar Site Campanha</label>
            </div>
            <gp-switch name="enableAtCampaignSite" [(ngModel)]="rankingParametrization.enableAtCampaignSite">
            </gp-switch>
        </gp-form-col>
    </gp-form-row>
</gp-card>

<gp-card title="Segmentação">
    <gp-form-row>
        <gp-form-col cols="12 6">
            <label>Habilitar segmentação por grupo</label>
            <div>
                <gp-switch [(ngModel)]="rankingParametrization.enableParticipantsGroups"></gp-switch>
            </div>
        </gp-form-col>
        <gp-campaign-participants-group-selector *ngIf="rankingParametrization.enableParticipantsGroups"
            name="participantsGroups" cols="12 6" [enableEmpty]="false" [campaignId]="campaignId"
            [(ngModel)]="rankingParametrization.participantsGroups">
        </gp-campaign-participants-group-selector>
    </gp-form-row>
</gp-card>

<gp-card title="Parametrizações">
    <gp-form-row>
        <gp-form-col cols="12 6">
            <label>Criar participantes caso não exista</label>
            <div>
                <gp-switch [(ngModel)]="rankingParametrization.createParticipantWhenNotFound"></gp-switch>
            </div>
        </gp-form-col>
        <gp-form-col cols="12 6">
            <label>Não exibir seção da posição atual</label>
            <div>
                <gp-switch [(ngModel)]="rankingParametrization.hideUserCurrentPosition"></gp-switch>
            </div>
        </gp-form-col>
    </gp-form-row>
    <hr>
    <h4>Configuração dos campos a serem exibidos</h4>
    <gp-form-row>
        <gp-form-col cols="6 6">
            <label>Exibir nome</label>
            <div>
                <gp-switch [(ngModel)]="rankingParametrization.columnsParametrizations.showName"></gp-switch>
            </div>
        </gp-form-col>
        <gp-form-col cols="6 6" *ngIf="rankingParametrization.columnsParametrizations.showName">
            <gp-simple-input label="Nome da coluna">
                <input type="text" name="nameHeaderText" class="form-control" [(ngModel)]="rankingParametrization.columnsParametrizations.nameHeaderText" />
            </gp-simple-input>
        </gp-form-col>
    </gp-form-row>
    <gp-form-row>
        <gp-form-col cols="6 6">
            <label>Exibir CPF/CNPJ</label>
            <div>
                <gp-switch [(ngModel)]="rankingParametrization.columnsParametrizations.showDocument"></gp-switch>
            </div>
        </gp-form-col>
        <gp-form-col cols="6 6" *ngIf="rankingParametrization.columnsParametrizations.showDocument">
            <gp-simple-input label="Nome da coluna">
                <input type="text" name="documentHeaderText" class="form-control" [(ngModel)]="rankingParametrization.columnsParametrizations.documentHeaderText" />
            </gp-simple-input>
        </gp-form-col>
    </gp-form-row>
    <gp-form-row>
        <gp-form-col cols="6 6">
            <label>Exibir produção</label>
            <div>
                <gp-switch [(ngModel)]="rankingParametrization.columnsParametrizations.showResultValue"></gp-switch>
            </div>
        </gp-form-col>
        <gp-form-col cols="6 6" *ngIf="rankingParametrization.columnsParametrizations.showResultValue">
            <gp-simple-input label="Nome da coluna">
                <input type="text" name="resultValueHeaderText" class="form-control" [(ngModel)]="rankingParametrization.columnsParametrizations.resultValueHeaderText" />
            </gp-simple-input>
        </gp-form-col>
    </gp-form-row>
</gp-card>

<gp-card title="Conteúdo do Ranking">
    <gp-form-row>
        <gp-form-col cols="12 12">
            <gp-editor id="rankRepresentation" [(ngModel)]="rankingParametrization.rankRepresentation"></gp-editor>
        </gp-form-col>
    </gp-form-row>
</gp-card>

<gp-card>
    <gp-form-row>
        <gp-form-col cols="12" [inputGroup]="false">
            <gp-spinner-button [pink]="true" icon="send" pull="right" text="Salvar" marginLeft="5px"
                [disabled]="loading || !hasRankingParametrization" [loading]="loading"
                (click)="saveDynamicRankingParametrization()"></gp-spinner-button>

            <gp-spinner-button bootstrapClass="default" pull="right" text="Limpar" [loading]="loading"
                loadingText="Processando..." (click)="clear()"></gp-spinner-button>
        </gp-form-col>
    </gp-form-row>
</gp-card>
