import { Component, Input, Output, EventEmitter } from '@angular/core';

import { isNullOrEmpty } from '../../../../../../../../shared/helpers/comparators';
import { Item } from '../../../../../../../../shared/models/item';

@Component({
  selector: 'campaign-participants-goals-reward-parametrizations',
  templateUrl: './campaign-participants-goals-reward-parametrizations.component.html'
})
export class CampaignParticipantsGoalsRewardParametrizationsComponent {
  @Input('hasStarted') hasStarted: boolean = false;
  @Input('params') set _rewardParametrizations(rewardParametrizations: any) {
    if (rewardParametrizations)
      this.rewardParametrizations = rewardParametrizations;
  }

  @Output('rewardParametrizations') paramsEmitter:  EventEmitter<any> = new EventEmitter();
  @Output('validationError') paramsEmitterError:  EventEmitter<any> = new EventEmitter();

  rewardParametrizations: any = {};

  goalRewardResultProcessTypes: Array<Item> = [
    Item.of("COMPLETE", 'Completo'),
    Item.of("VIEW_ONLY", 'Acompanhamento')
  ];

  rewardCreditProcessTypes: Array<Item> = [
    Item.of("CURRENT_GOAL", 'Meta Atual'),
    Item.of("ALL_GOALS_WITH_SAME_PERIOD", 'Todas Metas Mesmo Período')
  ];

  get goalCanGenerateCredit() {
    return this.rewardParametrizations.rewardResultProcessType === 'COMPLETE';
  }

  public validateAndEmitParams(): boolean {
    if (!this.rewardParametrizations) {
      this.paramsEmitterError.emit('Parametrização do bônus inválida');
      return false;
    }
    if (!this.validate())
      return false;

    this.paramsEmitter.emit(this.rewardParametrizations);
    return true;
  }

  private validate(): boolean {
    if (isNullOrEmpty(this.rewardParametrizations.rewardResultProcessType)) {
      this.paramsEmitterError.emit('Informe o tipo de processamento');
      return false;
    }
    if (isNullOrEmpty(this.rewardParametrizations.rewardCreditProcessType)) {
      this.paramsEmitterError.emit('Informe o tipo de liberação do bônus');
      return false;
    }
    if (!this.rewardParametrizations.enableRewardProportionalToResult)
      return true;

    if (isNullOrEmpty(this.rewardParametrizations.rewardType)) {
      this.paramsEmitterError.emit('Informe o tipo da pontuação');
      return false;
    }
    if (!this.rewardParametrizations.maximumRewardAmount ||
      (typeof this.rewardParametrizations.maximumRewardAmount === 'number' && this.rewardParametrizations.maximumRewardAmount <= 0) ||
      (typeof this.rewardParametrizations.maximumRewardAmount === 'string' && isNullOrEmpty(this.rewardParametrizations.maximumRewardAmount))) {
      this.paramsEmitterError.emit('Informe o valor máximo do bônus');
      return false;
    }
    if (!this.rewardParametrizations.baseValueForProportionalCalculationReward ||
      (typeof this.rewardParametrizations.baseValueForProportionalCalculationReward === 'number' && this.rewardParametrizations.baseValueForProportionalCalculationReward <= 0) ||
      (typeof this.rewardParametrizations.baseValueForProportionalCalculationReward === 'string' && isNullOrEmpty(this.rewardParametrizations.baseValueForProportionalCalculationReward))) {
      this.paramsEmitterError.emit('Informe a pontução máxima para o cálculo proporcional');
      return false;
    }
    if (!this.rewardParametrizations.minimumProportionalAmountToCredit ||
      (typeof this.rewardParametrizations.minimumProportionalAmountToCredit === 'number' && this.rewardParametrizations.minimumProportionalAmountToCredit < 0) ||
      (typeof this.rewardParametrizations.minimumProportionalAmountToCredit === 'string' && isNullOrEmpty(this.rewardParametrizations.minimumProportionalAmountToCredit))) {
      this.paramsEmitterError.emit('Informe a porcentagem mínima de atingimento');
      return false;
    }
    return true;
  }

  public clear(): void {
    this.rewardParametrizations = {};
  }
}
