import { Component, OnInit } from '@angular/core';
import { CampaignStore } from '../../campaign.store';

@Component({
  selector: 'campaign-participants-goals',
  templateUrl: './campaign-participants-goals.component.html'
})
export class CampaignParticipantsGoalsComponent implements OnInit {
  campaignId: string = '';

  constructor(private campaignStore: CampaignStore) { }

  ngOnInit(): void {
    this.campaignId = this.campaignStore.id;    
  }

  get showMenublock() {
      return this.campaignStore.isFullCampaignOrUserWithGpBu;
  }

}
