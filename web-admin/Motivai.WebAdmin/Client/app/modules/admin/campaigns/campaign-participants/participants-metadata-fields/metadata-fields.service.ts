import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { ApiService } from '../../../../../core/api/api.service';

import { ResumedMetadataPropertyModel } from './metadata-fields-models/ResumedMetadataPropertyModel';
import { ResumedMetadataPageModel } from './metadata-fields-models/ResumedMetadataPageModel';
import { UserMetadataHeaderDetails } from './metadata-fields-models/UserMetadataModel';

@Injectable()
export class MetadataFieldService {
	constructor(private _api: ApiService) { }

	save(campaignId: string, metadataHeader: string, metadata: UserMetadataHeaderDetails): Observable<boolean> {
		if (!metadataHeader)
			return this._api.post(`/api/campaigns/${campaignId}/settings/metadata`, metadata);

		console.log('CALLL PUT: ', metadataHeader);
		return this._api.put(`/api/campaigns/${campaignId}/settings/metadata/${metadataHeader}`, metadata);
	}

	getAll(campaignId: string, platformPage: string): Observable<ResumedMetadataPropertyModel[]> {
		if (platformPage)
			return this._api.get(`/api/campaigns/${campaignId}/settings/metadata/fields`, { platformPage });

		return this._api.get(`/api/campaigns/${campaignId}/settings/metadata/fields`);
	}

	get(campaignId: string, metadataHeader: string): Observable<ResumedMetadataPageModel> {
		return this._api.get(`/api/campaigns/${campaignId}/settings/metadata/${metadataHeader}`);
	}
}
