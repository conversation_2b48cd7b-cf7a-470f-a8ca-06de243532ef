import { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';

import { GpAlertComponent } from '../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpFileUploadComponent } from '../../../../../../../../shared/components/gp-fileupload/gp-fileupload.component';
import { GpModalComponent } from '../../../../../../../../shared/components/gp-modal/gp-modal.component';
import { FormatHelper } from '../../../../../../../../shared/formatters/format-helper';
import { Item } from '../../../../../../../../shared/models/item';

import { CampaignsParticipantsGoalsActionsService } from '../campaigns-participants-goals-actions.service';
import { CampaignParticipantsGoalsService } from '../../../campaign-participants-goals.service';

@Component({
  selector: 'awarded-target-goal-editing',
  templateUrl: './awarded-target-goal-editing.component.html'
})
export class AwardedTargetGoalEditingComponent {
  @ViewChild('goalTargetAwarded') goalTargetAwardedModal: GpModalComponent;
  @ViewChild('gpFile') gpFile: GpFileUploadComponent;
  @ViewChild('alert') alert: GpAlertComponent;

  @Input() campaignId: string;
  @Input() goalId: string;
  @Input() hasStarted: string;

  @Output('goalAchievedAwarded') paramsEmitter:  EventEmitter<any> = new EventEmitter();
  @Output('validationError') paramsEmitterError:  EventEmitter<any> = new EventEmitter();

  goalAchievedAwarded: any = {
    parametrizations: {},
    awardedTargetGoals: []
  }
  awardedTargetGoals: any[] = [];

  @Input() set _goalAchievedAwarded(goalAchievedAwarded: any) {
    if (goalAchievedAwarded && goalAchievedAwarded.parametrizations) {
      this.goalAchievedAwarded = goalAchievedAwarded;
      this.handleAwardedTargetGoals();
    }
  }

  loading: boolean = false;
  removing: boolean;
  uploading: boolean = false;

  targetAwardedId: string;
  targetAwarded: any = {};
  imageUrl: string;

  constructor(
    private goalsActionsService: CampaignsParticipantsGoalsActionsService,
    private campaignParticipantsGoalsService: CampaignParticipantsGoalsService
  ) {}

  get isEditing(): boolean {
    return this.targetAwarded && this.targetAwarded.id;
  }

  get textButtonSave(): string {
    if (this.isEditing) {
      return 'Atualizar';
    }
    return 'Adicionar';
  }

  get hasImageUrl(): boolean {
    return this.targetAwarded && this.targetAwarded.imageUrl != null;
  }

  get hasSelectedFile(): boolean {
    return this.gpFile && this.gpFile.hasSelectedFile();
  }

  newGoalTargetAwarded(): void {
    this.clear();
    this.targetAwarded.minimumPercentageToReachOfGoalTargetValueToAward = 100;

    this.showModal();
  }

  editGoalTargetAwarded(event: any): void {
    this.targetAwarded = event;
    this.targetAwardedId = event.id;
    this.setImageUrl(this.targetAwarded.imageUrl);
    this.showModal();
  }

  showModal(): void {
    this.goalTargetAwardedModal.show();
  }

  hideModal(): void {
    this.goalTargetAwardedModal.hide();
  }

  addOrUpdate(): void {
    if (!this.validateTargetAwarded())
      return;

    const index = this.findAwardedTargetIndexById();
    if (index < 0) {
      this.addAwardedTargetGoal();
    } else {
      this.updateWardedTargetGoal(index);
    }
    this.updateGoalAchievedAwarded();
  }

  private findAwardedTargetIndexById(): number {
    if (!this.goalAchievedAwarded) {
      this.buildEmpty();
      return -1;
    }
    return this.goalAchievedAwarded.awardedTargetGoals.findIndex(t => t.id === this.targetAwardedId); 
  }

  private addAwardedTargetGoal(): void {
    if (!this.goalAchievedAwarded || !this.goalAchievedAwarded.awardedTargetGoals) 
      this.buildEmpty();
    
    this.goalAchievedAwarded.awardedTargetGoals.push(this.targetAwarded);
  }

  private updateWardedTargetGoal(index: number): void {
    this.goalAchievedAwarded.awardedTargetGoals[index] = this.targetAwarded;
  }

  private buildEmpty(): void {
    if (!this.goalAchievedAwarded) {
      this.goalAchievedAwarded = {
        parametrizations: {},
        awardedTargetGoals: []
      }
      return;
    }
    if (!this.goalAchievedAwarded.awardedTargetGoals) {
      this.goalAchievedAwarded.awardedTargetGoals = [];
    }
  }

  private updateGoalAchievedAwarded(): void {
    if (!this.valideteParametrizations())
      return;
  
    this.loading = true;
    this.goalsActionsService.updateGoalAchievedAwarded(this.campaignId, this.goalId, this.goalAchievedAwarded)
      .subscribe(wasUpdate => {
        this.findGoalAchievedAwarded();
        this.loading = false;

        if (wasUpdate) {
          this.hideModal();
          return this.alert.showSuccess('Alvos salvo com sucesso');
        }
         
        this.alert.showWarning('Erro ao atualizar os alvos. Tente novamente');
      },
      err => {
        this.loading = false;
        this.alert.handleAndShowError(err);
        this.hideModal();
      }
    );
  }

  public validateAndEmitParams(): boolean {
    if (!this.goalAchievedAwarded) {
      this.paramsEmitterError.emit('Parametrização dos alvos inválida');
      return false;
    }
    if (!this.valideteParametrizations())
      return false;

    this.paramsEmitter.emit(this.goalAchievedAwarded);
    return true;
  }

  private valideteParametrizations(): boolean {
    if (!this.goalAchievedAwarded.parametrizations.enableMinimumTargetBonus)
      return true;
    
    if (!this.goalAchievedAwarded.parametrizations.rewardType) {
      this.paramsEmitterError.emit('Selecione o tipo da pontuação');
      return false;
    }
    if (!this.goalAchievedAwarded.parametrizations.minimumTargetAmountToAchieve 
        || this.goalAchievedAwarded.parametrizations.minimumTargetAmountToAchieve <= 0) {
      this.paramsEmitterError.emit('Informe a quantidade mínima de alvos que precisam ser atingidos');
      return false;
    }
    if (this.goalAchievedAwarded.parametrizations.rewardValue <= 0) {
      this.paramsEmitterError.emit('Informe o valor do bônus ao atingir a quantidade mínima de alvos');
      return false;
    }
    return true;
  }

  private validateTargetAwarded() {
    if (!this.targetAwarded.code) {
      this.alert.showWarning('Informe o código');
      return false;
    }
    if (!this.targetAwarded.description) {
      this.alert.showWarning('Informe a descrição');
      return false;
    }
    if (this.targetAwarded.minimumPercentageToReachOfGoalTargetValueToAward < 0) {
      this.alert.showWarning('Informe a porcentagem mínima');
      return false;
    }
    if (!this.targetAwarded.rewardType) {
      this.alert.showWarning('Informe o tipo de pontuação');
      return false;
    }
    return true;
  }

  uploadImage(): void {
    if (!this.hasSelectedFile) {
      return this.alert.showWarning('Selecione uma imagem para upload.');
    }
    this.uploadGoalTargetAwardedImage();
  }

  removeImage(): void {
    this.alert.confirm('Deseja remover a imagem do alvo?').then(result => {
      if (result.value) {
        this.removing = true;
        this.goalsActionsService.removeGoalTargetAwardedImage(this.campaignId, this.goalId, this.targetAwardedId)
          .subscribe(response => {
            this.removing = false;
            if (response) {
              this.resetImagesProperties();
              this.alert.showSuccess('Imagem removida com sucesso');
            }
            this.hideModal();
          },
          err => {
            this.removing = false;
            this.alert.handleAndShowError(err);
          }
        );
      }
    });
  }

  private resetImagesProperties(): void {
    this.imageUrl = null;
    this.targetAwarded.imageUrl = null;
    this.targetAwarded.imageName = null;
    this.targetAwarded.storageImageName = null;

    const index = this.findAwardedTargetIndexById();
    if (index < 0)
      return;

    this.updateWardedTargetGoal(index);
  }

  private uploadGoalTargetAwardedImage() {
    this.uploading = true;

    if (this.gpFile && this.gpFile.hasSelectedFile()) {
      this.gpFile.path = `api/campaigns/${this.campaignId}/participants/goals/${this.goalId}/awardedtargets/${this.targetAwardedId}/images`;
      this.uploading = true;
      this.gpFile.uploadFile();
    }
  }

  onComplete(event: any) {
    this.uploading = false;
    if (!event) {
      this.removeSelectedFileIfNeeded();
      return this.alert.showError('Ocorreu um erro ao fazer o upload da imagem.');
    }

    if (event.success) {
      if (event.response) {
        let apiReturn = JSON.parse(event.response);
        if (apiReturn['success'] && apiReturn['return']) {
          this.uploading = false;
          this.setImageUrl(apiReturn['return']);
          this.showImageSuccessMessage();
        } else {
          this.uploading = false;
          this.showImageErrorMessage(apiReturn['error']);
        }
      }
    } else {
      this.uploading = false;
      this.alert.handleAndShowError(event.errorMessage);
    }
    this.removeSelectedFileIfNeeded();
  }

  private removeSelectedFileIfNeeded(): void {
    if (this.hasSelectedFile)
      this.gpFile.removeAll();

    this.hideModal();
  }

  private showImageSuccessMessage() {
    this.findGoalAchievedAwarded();
    this.alert.showSuccess('Upload da imagem realizado com sucesso.');
  }

  private showImageErrorMessage(message = 'Erro durante o upload da imagem') {
    this.alert.showError(message);
  }

  private setImageUrl(imageUrl: string): void {
    this.imageUrl = null; // force update
    if (imageUrl) {
      this.imageUrl = `${imageUrl}/1680/300`;
    }
  }

  private findGoalAchievedAwarded(): void {
    this.loading = true;
    this.campaignParticipantsGoalsService.findGoalAchievedAwarded(this.campaignId, this.goalId)
      .subscribe(response => {
        if (response) {
          this.goalAchievedAwarded = response;
          this.handleAwardedTargetGoals();
        }
        this.loading = false;
      }, err => {
        this.loading = false;
        this.alert.handleAndShowError(err);
      }
    );
  }

  private handleAwardedTargetGoals(): void {
    if (this.goalAchievedAwarded.awardedTargetGoals && this.goalAchievedAwarded.awardedTargetGoals.length > 0) {
      this.goalAchievedAwarded.awardedTargetGoals.forEach((awardedTarget: any) => {
        awardedTarget.formattedCreateDate = FormatHelper.formatDate(awardedTarget.createDate);
      });
      this.awardedTargetGoals = this.goalAchievedAwarded.awardedTargetGoals;
      this.awardedTargetGoals.sort((a, b) => a.index - b.index);
    }
  }

  private clear(): void {
    this.loading = false;
    this.targetAwarded = {};
    this.targetAwardedId = null;
    this.imageUrl = null;
  }
}
