import { Injectable } from '@angular/core';
import { Observable } from 'rxjs/Observable';

import { ApiService } from '../../../../../core/api/api.service';

@Injectable()
export class CampaignPreRegisterService {
    constructor(private _api: ApiService) {}

    search(campaignId: string, document?: string, integration?: any, skip?: number, limit?: number): Observable<any[]> {
        const params: any = {};
        params.skip = skip || 0;
        params.limit = limit || 50;
        if (document) params.document = document;
        if (integration != null) {
            if (integration.integrated) params.integrated = integration.integrated;
            if (integration.integrationError) params.integrationError = true;
        }

        return this._api.get(`/api/campaigns/${campaignId}/preregister`, params, 20000);
    }

    getPreRegisterMetadata(campaignId: string): Observable<any[]> {
      return this._api.get(`/api/campaigns/${campaignId}/settings/metadata/fields`, { platformPage: 'PRE_REGISTRATION' });
    }

    findById(campaignId: string, preRegisterId: string) {
        return this._api.get(`/api/campaigns/${campaignId}/preregister/${preRegisterId}`, null, 20000);
    }

    approve(campaignId: string, preRegisterId: string) {
        return this._api.put(`/api/campaigns/${campaignId}/preregister/${preRegisterId}/approve`, null, 20000);
    }

    refuse(campaignId: string, preRegisterId: string, reason: string) {
        return this._api.put(`/api/campaigns/${campaignId}/preregister/${preRegisterId}/refuse`, { reason }, 20000);
    }

    integrationRetry(campaignId: string, preRegisterId: string){
        return this._api.put(`/api/campaigns/${campaignId}/preregister/${preRegisterId}/integration/retry`, 20000);
    }
}
