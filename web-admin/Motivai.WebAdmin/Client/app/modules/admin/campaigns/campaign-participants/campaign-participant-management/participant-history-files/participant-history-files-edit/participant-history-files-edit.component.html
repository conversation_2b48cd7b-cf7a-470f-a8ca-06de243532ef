
<gp-card [first]="true">
  <gp-form-row>
    <gp-form-col cols="12 4 4">
        <gp-simple-input label="Nome">
          <input type="text" class="form-control" required [(ngModel)]="historyFiles.name" />
        </gp-simple-input>
    </gp-form-col>

    <gp-form-col cols="12 4 4">
      	<label>Selecione o arquivo</label>
				<gp-fileupload name="historyFile" [path]="uploadPath" #gpFile></gp-fileupload>
    </gp-form-col>

    <gp-form-col cols="12 4 4">
        <gp-spinner-button type="button" [pink]="true" marginTop="26px" text="Novo arquivo"
          pull="right" (click)="uploadParticipantHistoryFile()" [disabled]="loadingScreen">
    </gp-spinner-button>
    </gp-form-col>
  </gp-form-row>
  <spinner [overlay]="true" [show]="loadingScreen"></spinner>
</gp-card>

<gp-alert [overlay]="true" #alert></gp-alert>
