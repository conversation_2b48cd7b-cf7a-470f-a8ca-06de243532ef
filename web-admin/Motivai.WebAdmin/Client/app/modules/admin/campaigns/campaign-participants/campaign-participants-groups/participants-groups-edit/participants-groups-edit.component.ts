import { Component, OnInit, Input, ViewChild } from '@angular/core';
import { <PERSON><PERSON>orm } from '@angular/forms';

import {
  PERMISSION_CAMPAIGNS_PARTICIPANTS_GROUPS_VIEW,
  PERMISSION_CAMPAIGNS_PARTICIPANTS_GROUPS_EDIT,
  PERMISSION_CAMPAIGNS_PARTICIPANTS_GROUPS_CREATE
} from '../../../../../../core/auth/access-points';

import { AuthStore } from '../../../../../../core/auth/auth.store';

import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { FormatHelper } from '../../../../../../shared/formatters/format-helper';
import { ParticipantsGroupService } from '../participants-groups.service';
import { CampaignStore } from '../../../campaign.store';
import { CatalogExtraServicesOrdersService } from '../../../campaign-catalog/campaign-orders/campaign-orders-catalog-extra-services/services/catalog-extra-services-orders.service';
import { PageChangeEvent } from '../../../../../../shared/components/gp-grid/page-change-event';
import { GpGridComponent } from '../../../../../../shared/components/gp-grid/gp-grid.component';

@Component({
  selector: 'participants-groups-edit',
  templateUrl: 'participants-groups-edit.component.html'
})
export class GpParticipantsGroupEditComponent implements OnInit {
  private _groupId: string;
  @Input() set groupId(v: string) {
    if (v) {
      this._groupId = v;
      this.getGroup();
      this.findGroupParticipants();
    }
  }

  @ViewChild('alert') alert: GpAlertComponent;
  @ViewChild('participantsGroupForm') participantsGroupForm: NgForm;
  @ViewChild('grid') gpGrid: GpGridComponent;

  campaignId: string;
  group: any = {};
  participantSearch: any = {};
  participantsFound: any[] = [];
  selectedUserId: string;
  participants: any[] = [];
  skip: number = 0;
  limit: number = 40;
  loadingScreen: boolean = false;
  loadingParticipants: boolean = false;
  loadingDelete: boolean = false;
  loading: boolean = false;

  userDocument: string = '';

  constructor(
    private groupService: ParticipantsGroupService,
    private campaignStore: CampaignStore,
    private as: AuthStore,
    private readonly catalogExtraServiceOrderService: CatalogExtraServicesOrdersService
  ) { }

  ngOnInit() {
    this.campaignId = this.campaignStore.id;
  }

  reset() {
    this.group = {};
    this.participantSearch = {};
    this.participantsFound = [];
    this.participants = [];

    Object.keys(this.participantsGroupForm.controls).forEach(key => {
      this.participantsGroupForm.controls[key].reset();
    });
  }

  pageChanged(event: PageChangeEvent) {
    if(event) {
      this.skip = event.skip;
      this.limit = event.limit;
      this.findGroupParticipants();
    }
  }

  searchParticipant() {
    this.gpGrid.resetPagination();
    this.skip = 0;
    this.loading = true;
    if(this.userDocument && this.userDocument.length > 0) {
      this.catalogExtraServiceOrderService.findParticipantInfoByDocument(this.campaignId, this.userDocument)
      .subscribe(participants => {
        this.loading = false;
        if(participants && participants.length > 0) {
          this.findGroupParticipants(participants[0].userId);
        } else {
          this.participants = [];
        }
      }, error => {
        this.alert.showError("Não foi possível buscar o participante, tente novamente.", error);
        this.loading = false;
      })
    } else {
      this.findGroupParticipants();
      this.loading = false;
    }

  }

  saveGroup() {
    if (this.canCreate() || this.canEdit()) {
      this.loading = true;
      // this.group.origin = this.handleGroupOrigin();

      this.groupService.save(this.campaignId, this.group).subscribe(
        group => {
          this.group = group;
          this.alert.showSuccess('Grupo salvo com sucesso');
          this.loading = false;
        },
        err => {
          this.alert.showError(err, true);
          this.loading = false;
        }
      );
    } else {
      this.alert.showError(
        'Você não tem permissão para realizar esta operação'
      );
    }
  }

  // handleGroupOrigin() {
  //   switch (this.type) {
  //     case 'CRM_GROUPS':
  //       return 'MANUALTARGETAUDIENCES';
  //     case 'PARTICIPANTS_GROUPS':
  //       return 'MANUAL';
  //     default:
  //       return '';
  //   }
  // }

  searchParticipants() {
    if (this.participantSearch.nameOrDocument) {
      this.loading = true;
      this.participantsFound = [];
      this.participantSearch.nameOrDocument = FormatHelper.removeMask(this.participantSearch.nameOrDocument);
      this.groupService
        .searchParticipants(
          this.campaignId,
          this.participantSearch.nameOrDocument
        )
        .subscribe(
          participants => {
            this.participantsFound = participants || [];
            if (participants && participants.length === 1) {
              this.selectedUserId = participants[0].userId;
            }
            this.loading = false;
          },
          err => {
            this.alert.showError(err, true);
            this.loading = false;
          }
        );
    }
  }

  addParticipant() {
    if (this.selectedUserId && this.canEdit()) {
      this.loading = true;
      this.groupService
        .addParticipant(this.campaignId, this.group.id, this.selectedUserId)
        .subscribe(
          response => {
            if (response) {
              this.alert.showSuccess('Participante adicionado ao grupo');
              this.participantSearch = {};
              this.participantsFound = [];
              this.findGroupParticipants();
            } else {
              this.alert.showError(
                'Não foi possível adicionar o participante ao grupo'
              );
            }

            this.loading = false;
          },
          err => {
            this.alert.showError(err, true);
            this.loading = false;
          }
        );
    }
  }

  removeParticipant($event: any) {
    if (this.canEdit()) {
      if ($event) {
        this.loadingDelete = true;
        console.log(this.campaignId);
        console.log(this.group.id);
        console.log($event.userId);
        this.groupService
          .removeParticipant(this.campaignId, this.group.id, $event.userId)
          .subscribe(
            response => {
              if (response) {
                this.alert.showSuccess(
                  'Participante desvinculado com sucesso do grupo'
                );
                this.findGroupParticipants();
              } else {
                this.alert.showError(
                  'Ocorreu um erro ao desvincular o participante do grupo, tente novamente.'
                );
              }

              this.loadingDelete = false;
            },
            err => {
              this.alert.showError(err, true);
              this.loadingDelete = false;
            }
          );
      }
    }
  }

  private getGroup() {
    if (this.campaignId && this._groupId) {
      this.loadingScreen = true;
      this.groupService.getById(this.campaignId, this._groupId).subscribe(
        group => {
          this.group = group || {};
          this.loadingScreen = false;
        },
        err => {
          this.alert.showError(err, true);
          this.loadingScreen = false;
        }
      );
    }
  }

  private findGroupParticipants(userId?: string) {
    if (this.campaignId && this._groupId) {
      this.loadingParticipants = true;
      this.groupService
        .getParticipants(this.campaignId, this._groupId, userId, this.skip, this.limit)
        .subscribe(
          participants => {
            this.participants = participants;
            this.loadingParticipants = false;
          },
          err => {
            this.alert.showError(err, true);
            this.loadingParticipants = false;
          }
        );
    }
  }

  canView() {
    return this.as.hasPermissionTo(
      PERMISSION_CAMPAIGNS_PARTICIPANTS_GROUPS_VIEW
    );
  }

  canEdit() {
    return this.as.hasPermissionTo(
      PERMISSION_CAMPAIGNS_PARTICIPANTS_GROUPS_EDIT
    );
  }

  canCreate() {
    return this.as.hasPermissionTo(
      PERMISSION_CAMPAIGNS_PARTICIPANTS_GROUPS_CREATE
    );
  }
}
