<gp-card [first]="true" title="Pesquisa">
	<gp-alert [overlay]="true" #alert></gp-alert>
	<gp-form-row>
		<gp-form-col cols="12 4 3">
			<gp-simple-input label="CPF/CNPJ">
				<gp-input-mask name="document" mask="00000000000000" [(ngModel)]="params.document"></gp-input-mask>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 3">
			<gp-simple-input label="Login">
				<input type="text" class="form-control" name="login"  [(ngModel)]="params.login"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 3 2" additionalClasses="top-p27">
			<gp-spinner-button [search]="true" text="Pesquisar" icon="search" [loading]="loading" (click)="search()"></gp-spinner-button>
		</gp-form-col>
		<gp-form-col cols="12 4 3 1" additionalClasses="top-p27">
			<gp-spinner-button [pink]="true" text="Cadastrar Participante" icon="plus" [loading]="loading" (click)="importManually()"></gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
</gp-card>
<participant-invite-modal #importManuallyModal></participant-invite-modal>
<gp-card title="Resultados encontrados">
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<gp-grid name="grid" [rows]="participants" [columns]="[ 'CPF/CNPJ', 'Nome' ]" [fields]="[ 'document', 'name' ]"
				[showActive]="true" [showPagination]="false" [showTotalPages]="false"
				[showEdit]="true" [showDelete]="false" [loading]="loading"
				(onEdit)="editParticipants($event)">
			   </gp-grid>
		</gp-form-col>
	</gp-form-row>
</gp-card>
