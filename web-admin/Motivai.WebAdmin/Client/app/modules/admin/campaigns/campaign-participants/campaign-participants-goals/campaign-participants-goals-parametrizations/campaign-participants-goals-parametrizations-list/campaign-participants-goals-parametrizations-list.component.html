<gp-card title="Filtros" [first]="true">
  <gp-form-row>
      <gp-form-col cols="12 4">
          <gp-simple-input label="Código da Meta">
              <input type="text" name="code" class="form-control" [(ngModel)]="params.code" />
          </gp-simple-input>
      </gp-form-col>
      <gp-form-col cols="12 4">
          <gp-spinner-button [search]="true" text="Pesquisar" marginTop="27px" [loading]="loading" loadingText="Processando" (click)="findCampaignParticipantsGoalsParametrizations()"></gp-spinner-button>
      </gp-form-col>
  </gp-form-row>
</gp-card>

<gp-alert #alert [overlay]="true"></gp-alert>
<gp-card>
  <gp-form-row>
      <gp-form-col cols="12">
          <gp-grid [rows]="campaignParticipantsGoalsParametrizations" [columns]="['Nome', 'Código', 'Descrição', 'Iniciada']" [loading]="loading"
              [fields]="['name', 'code', 'description', 'formattedStarted']" [showActive]="true" [showEdit]="true"
              (onEdit)="findCampaignParticipantsGoalParametrizations($event)" [showDelete]="true"
              (onDelete)="inactiveCampaignParticipantsGoalParametrizations($event)"></gp-grid>
      </gp-form-col>
  </gp-form-row>
</gp-card>
