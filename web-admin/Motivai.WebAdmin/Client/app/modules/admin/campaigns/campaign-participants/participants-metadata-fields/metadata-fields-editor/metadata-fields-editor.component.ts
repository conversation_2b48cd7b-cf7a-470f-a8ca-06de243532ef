import { OnInit, Component, ViewChild, Input } from '@angular/core';

import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { Item } from '../../../../../../shared/models/item';

import { DynamicFieldValueType, PlatformPage, UserMetadataHeaderDetails, MetadataPageConfiguration, MetadataAllowedValue } from '../metadata-fields-models/UserMetadataModel';
import { MetadataFieldService } from '../metadata-fields.service';
import { CampaignStore } from '../../../campaign.store';

@Component({
    selector: 'metadata-fields-editor',
    templateUrl: 'metadata-fields-editor.component.html'
})
export class MetadataFieldsEditorComponent implements OnInit {
    @ViewChild('alert') alert: GpAlertComponent;

    metadataHeader: string;
    loadingScreen: boolean = false;

    valuesOfButton: any = {
        editValue: 'Adicionar Item',
        editPage: 'Adicionar <PERSON>ágin<PERSON>'
    };

    campaignId: string;
    metadatafieldId: string;

    metadataType: string;
    metadataPage: string;

    keySelectType: string;
    valueSelectType: string;
    oldValueSelectType: any;
    orderSelectType: string;

    matadataPageOnEditor: any;
    activeSelectType: boolean;
    activeSelectPage: boolean;
    oldValueFromSelectPage: any;
    editingPage: boolean = false;

    valuesFromSelectType: any[];
    distributedPages: any[];

    metadata: any = { active: true };

    isEditing: boolean = false;

    types: Item[];
    pages: Item[];
    loading: boolean = false;

    constructor(private campaignStore: CampaignStore, private metadataFieldService: MetadataFieldService) { }

    get hasItemsForSelectType(): boolean {
      return this.valuesFromSelectType && this.valuesFromSelectType.length > 0;
    }

    ngOnInit() {
        this.campaignId = this.campaignStore.id;
        this.activeSelectPage = true;
        this.activeSelectType = true;
        this.getTypes();
        this.getPages();
    }

    getTypes() {
        this.types = [
            Item.of(DynamicFieldValueType.TEXT, 'Texto')
            , Item.of(DynamicFieldValueType.INTEGER, 'Número Inteiro')
            , Item.of(DynamicFieldValueType.DECIMAL, 'Número Decimal')
            , Item.of(DynamicFieldValueType.CHECK, 'Checkbox')
            , Item.of(DynamicFieldValueType.OPTION, 'Seleção')
            , Item.of(DynamicFieldValueType.DATE, 'Data')
        ];
    }

    getPages() {
        this.pages = [
            Item.of(PlatformPage.LOGIN, 'Login')
            , Item.of(PlatformPage.PRE_REGISTRATION, 'Pré-Cadastro')
            , Item.of(PlatformPage.FIRST_ACCESS, 'Primeiro Acesso')
            , Item.of(PlatformPage.PASSWORD_CHANGE, 'Alterar Senha')
            , Item.of(PlatformPage.PASSWORD_RESET, 'Recuperar Senha')
            , Item.of(PlatformPage.RANKING, 'Ranking')
            , Item.of(PlatformPage.SUMMARY, 'Extrato')
            , Item.of(PlatformPage.INVITE_PARTICIPANT, 'Convidar Participante')            
        ];
    }


    isEditingValueFromSelectType(): boolean {
        return this.oldValueSelectType != null || this.oldValueSelectType != undefined;
    }

    addValueFromSelectType() {
        if (!this.valuesFromSelectIsValid())
            return;

        this.loading = true;

        if (!this.valuesFromSelectType)
            this.valuesFromSelectType = new Array<any>();

        const activeValues = this.valuesFromSelectType.filter(x => x.active === true);

        if (!this.oldValueSelectType && (activeValues && activeValues.length === 50)) {
            this.alert.showError('Máximo de valores ativos para o tipo Seleção atingido.');
        } else {
            const existValue = this.valuesFromSelectType.find(x => x.key && x.key.includes(this.keySelectType));

            if (!this.oldValueSelectType && existValue) {
                this.alert.showError('Já existe este Valor na lista, por favor, preencha outro Valor.');

            } else {
                if (!this.oldValueSelectType) {
                    const data = {
                        key: this.keySelectType,
                        value: this.valueSelectType,
                        order: this.orderSelectType,
                        active: this.activeSelectType
                    };

                    this.valuesFromSelectType.push(data);
                } else {
                    this.valuesFromSelectType.forEach(x => {
                        if (x.key === this.keySelectType) {
                            x.active = this.activeSelectType;
                            x.order = this.orderSelectType;
                            x.value = this.valueSelectType;
                            return;
                        }
                    });
                }

                this.clearValuesFromSelect();
            }
        }

        this.loading = false;
        this.valuesOfButton.editValue = 'Adicionar Item';
    }

    clearValuesFromSelect() {
        this.valueSelectType = '';
        this.keySelectType = '';
        this.orderSelectType = '';
        this.orderSelectType = '';
        this.oldValueSelectType = undefined;
        this.valuesOfButton.editValue = 'Adicionar Item';
    }

    isSelectType() {
        return this.metadataType && this.metadataType.includes(DynamicFieldValueType.OPTION);
    }

    valuesFromSelectIsValid() {
        return this.valueSelectType && this.orderSelectType;
    }

    canCreateOrUpdateValuesFromSelectType() {
        return true;
    }

    canCreateOrUpdateDistributedPages() {
        return true;
    }

    editPageFromSelectType(event) {
        this.oldValueSelectType = event;
        this.activeSelectType = event.active;
        this.valueSelectType = event.value;
        this.keySelectType = event.key;
        this.orderSelectType = event.order;
        this.valuesOfButton.editValue = 'Atualizar Valor';
    }

    editDistributedPage(event) {
        if (!event)
            this.alert.showError('Erro ao editar a página. Contate o Administrador do Sistema.');

        const page = this.pages.find(x => x.text.includes(event.page));

        if (page)
            this.metadataPage = page.id;

        this.matadataPageOnEditor = event;

        this.metadataType = event.metadataType;
        this.activeSelectPage = event.active;

        if (event.readOnly && event.readOnly.includes('Sim'))
            this.metadata.readOnly = true;

        if (event.required && event.required.includes('Sim'))
            this.metadata.required = true;

        if (event.valuesFromSelectType && event.valuesFromSelectType.length)
            this.valuesFromSelectType = event.valuesFromSelectType;

        this.oldValueFromSelectPage = event;
        this.editingPage = true;
        this.valuesOfButton.editPage = 'Atualizar Página';
    }

    isPageEditing() {
        return this.editingPage;
    }

    dataFromPageIsValid() {
        if (!this.metadataPage || !this.metadataType)
            return false;

        if (this.metadataType && this.metadataType.includes(DynamicFieldValueType.OPTION)) {
            if (!this.hasItemsForSelectType)
                return false;
        }
        return true;
    }

    addDataFromPage() {
        if (!this.dataFromPageIsValid())
            return;

        this.loading = true;

        if (!this.distributedPages)
            this.distributedPages = new Array<any>();

        const page = this.pages.find(x => x.id.includes(this.metadataPage));


        if (this.distributedPages.length) {
            let distributedPage: any;

            if (this.matadataPageOnEditor) {
                distributedPage = this.distributedPages.find(x => x.page.includes(this.matadataPageOnEditor.page));

                if (!distributedPage) {
                    this.alert.showError('Essa Página já foi vinculada. Por favor selecione outra.');
                    this.loading = false;
                    return;
                }
            } else if (page) {
                distributedPage = this.distributedPages.find(x => x.page.includes(page.text));

                if (distributedPage) {
                    this.alert.showError('Essa Página já foi vinculada. Por favor selecione outra.');
                    this.loading = false;
                    return;
                }
            }
        }

        if (!this.oldValueFromSelectPage) {
            if (this.hasItemsForSelectType) {
              this.valuesFromSelectType.sort((it1, it2) => it1.order - it2.order);
            }

            const data = {
                page: page ? page.text : undefined,
                active: this.activeSelectPage,
                readOnly: this.metadata.readOnly ? 'Sim' : 'Não',
                required: this.metadata.required ? 'Sim' : 'Não',
                metadataType: this.metadataType,
                valuesFromSelectType: this.valuesFromSelectType
            };

            this.distributedPages.push(data);
        } else if (this.distributedPages && this.distributedPages.length) {
            this.distributedPages.forEach(x => {
                const page = this.pages.find(y => y.id.includes(this.metadataPage));

                if (page && page.text.includes(x.page)) {
                    x.readOnly = this.metadata.readOnly ? 'Sim' : 'Não';
                    x.required = this.metadata.required ? 'Sim' : 'Não';
                    x.active = this.activeSelectPage;
                    return;
                }
            });

            this.editingPage = false;
            this.oldValueFromSelectPage = undefined;
        }

        this.clearDistributedPages();
        this.loading = false;
        this.matadataPageOnEditor = undefined;
    }

    clearDistributedPages() {
        this.metadataPage = '';
        this.metadataType = '';
        this.activeSelectPage = false;
        this.metadata.readOnly = false;
        this.metadata.required = false;
        this.valuesOfButton.editPage = 'Adicionar Página';

        if (this.valuesFromSelectType && this.valuesFromSelectType.length) {
            this.valuesFromSelectType = undefined;
            this.orderSelectType = '';
            this.activeSelectType = false;
        }
    }

    formIsvalid() {
        if (!this.metadata.key)
            return false;

        if (!this.metadata.value)
            return false;

        if (!this.distributedPages || !this.distributedPages.length)
            return false;

        return true;
    }

    save() {
        if (!this.formIsvalid())
            return;

        this.loading = true;
        let allowedValues: MetadataAllowedValue[] = undefined;

        const userMetadataHeaderDetails = {
            active: this.metadata.active,
            property: this.metadata.key,
            description: this.metadata.value,
            placeholder: this.metadata.placeholder,
            origin: 'ADMIN',
            pagesConfigurations: this.distributedPages && this.distributedPages.length ? this.distributedPages.map((x) => {
                const page = this.pages.find(y => y.text.includes(x.page));

                if (x.valuesFromSelectType && x.valuesFromSelectType.length)
                    allowedValues = x.valuesFromSelectType.map((y) => {
                        return {
                            active: y.active,
                            index: y.order,
                            value: y.value,
                            key: y.key
                        } as MetadataAllowedValue;
                    });

                return {
                    active: x.active,
                    page: page ? page.id as PlatformPage : undefined,
                    readOnly: x.readOnly && x.readOnly.includes('Sim') ? true : false,
                    required: x.required && x.required.includes('Sim') ? true : false,
                    type: x.metadataType as DynamicFieldValueType,
                    allowedValues
                } as MetadataPageConfiguration;
            }) : undefined
        } as UserMetadataHeaderDetails;

        userMetadataHeaderDetails.allowedValues = allowedValues;

        this.metadataFieldService.save(this.campaignId, this.metadataHeader, userMetadataHeaderDetails).subscribe(response => {
            if (response) {
                if (!this.metadataHeader)
                    this.alert.showSuccess('Campo dinâmico salvo com sucesso.');
                else
                    this.alert.showSuccess('Campo dinâmico atualizado com sucesso.');
            } else {
                this.alert.showError('Ocorreu um erro ao salvar o campo dinâmico.');
            }

            this.clearAllForm();
            this.loading = false;
        },
            err => {
                this.alert.showError(err, true);
                this.loading = false;
            });
    }

    clearAllForm() {
        this.metadata.key = '';
        this.metadata.value = '';
        this.metadata.placeholder = '';
        this.distributedPages = undefined;
        this.valuesFromSelectType = undefined;
        this.isEditing = false;
        this.matadataPageOnEditor = undefined;
        this.metadataHeader = undefined;
        this.editingPage = undefined;
        this.clearValuesFromSelect();
        this.clearDistributedPages();
    }

    findById(metadataHeader) {
        this.isEditing = true;
        this.loadingScreen = true;
        this.metadataHeader = metadataHeader;
        this.metadataFieldService.get(this.campaignId, metadataHeader)
            .subscribe(metadata => {
                if (metadata) {
                    this.metadata.key = metadata.property;
                    this.metadata.value = metadata.description;
                    this.metadata.placeholder = metadata.placeHolder;

                    if (metadata.pagesConfigurations && metadata.pagesConfigurations.length) {
                        this.distributedPages = metadata.pagesConfigurations.map((x) => {
                            const page = this.pages.find(y => y.id.includes(x.page));

                            return {
                                page: page ? page.text : undefined,
                                active: x.active,
                                readOnly: x.readOnly ? 'Sim' : 'Não',
                                required: x.required ? 'Sim' : 'Não',
                                metadataType: x.type,
                                valuesFromSelectType: x.allowedValues && x.allowedValues.length ? x.allowedValues.map((y) => {
                                    return {
                                        value: y.value,
                                        key: y.key,
                                        order: y.index,
                                        active: y.active
                                    };
                                }) : undefined
                            };
                        });
                    }
                } else
                    this.alert.showError('Erro ao buscar o Campo Dinâmico. Contate o Administrador do Sistema.');

                this.loadingScreen = false;
            },
                err => {
                    this.alert.showError(err, true);
                    this.loadingScreen = false;
                });
    }
}
