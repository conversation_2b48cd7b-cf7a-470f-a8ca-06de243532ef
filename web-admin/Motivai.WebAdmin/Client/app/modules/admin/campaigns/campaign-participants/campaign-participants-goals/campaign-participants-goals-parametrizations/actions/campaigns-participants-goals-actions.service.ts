import { Injectable } from '@angular/core';
import { ApiService } from '../../../../../../../core/api/api.service';
import { Observable } from 'rxjs';

@Injectable()
export class CampaignsParticipantsGoalsActionsService {
  constructor(private api: ApiService) {}

  updateGoalAchievedAwarded(campaignId: string, goalId: string, goalAchievedAwarded: any): Observable<any> {
    return this.api.put(`/api/campaigns/${campaignId}/participants/goals/${goalId}/awardedtargets`, goalAchievedAwarded);
  }

  removeGoalTargetAwardedImage(campaignId: string, goalId: string, targetAwardedId: string): Observable<boolean> {
    return this.api.put(`/api/campaigns/${campaignId}/participants/goals/${goalId}/awardedtargets/${targetAwardedId}/images/remove`);
  }

  saveGoalEvent(campaignId: string, goalId: string, event: any): Observable<any> {
    return this.api.post(`/api/campaigns/${campaignId}/participants/goals/${goalId}/events`, event);
  }

  updateGoalEvent(campaignId: string, goalId: string, event: any): Observable<any> {
    return this.api.put(`/api/campaigns/${campaignId}/participants/goals/${goalId}/events/${event.id}`, event);
  }

  saveExclusiveLevelGoal(campaignId: string, goalId: string, exclusiveLevelGoal: any): Observable<any> {
    return this.api.post(`/api/campaigns/${campaignId}/participants/goals/${goalId}/exclusives-levels`, exclusiveLevelGoal);
  }

  updateExclusiveLevelGoal(campaignId: string, goalId: string, exclusiveLevelGoal: any): Observable<any> {
    return this.api.put(`/api/campaigns/${campaignId}/participants/goals/${goalId}/exclusives-levels/${exclusiveLevelGoal.id}`, exclusiveLevelGoal);
  }

  saveIntervalGoal(campaignId: string, goalId: string, rangeWithInterlvalGoal: any): Observable<any> {
    return this.api.post(`/api/campaigns/${campaignId}/participants/goals/${goalId}/intervals`, rangeWithInterlvalGoal);
  }

  updateIntervalGoal(campaignId: string, goalId: string, rangeWithInterlvalGoal: any): Observable<any> {
    return this.api.put(`/api/campaigns/${campaignId}/participants/goals/${goalId}/intervals/${rangeWithInterlvalGoal.id}`, rangeWithInterlvalGoal);
  }
}
