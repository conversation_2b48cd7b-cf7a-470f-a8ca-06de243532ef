<gp-card *ngIf="showMenublock" [noPaddingTop]="true" [noPaddingBottom]="true">
	<div class="row">
		<accordion [closeOthers]="true">
			<accordion-group panelClass="b0 mb-sm panel-default no-shadow" [isOpen]="true">
				<div accordion-heading>
					<label><em class="fa fa-navicon"></em> Features de Ranking dos Participantes</label>
				</div>
				<div class="row menublock text-center">
					<gp-menublock-item 
						*ngIf="canViewRankingParametrization"
						size="xs" 
						icon="gears" 
						color="text-warning" 
						text="Parâmetrização de rankings" 
						routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'participantes', 'ranking','parametrizacao']">
					</gp-menublock-item>

					<gp-menublock-item 
						size="xs" 
						icon="cloud-upload" 
						color="text-warning" 
						text="Importação" 
						routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'participantes', 'ranking', 'importacao']">
					</gp-menublock-item>
				</div>
			</accordion-group>
		</accordion>
	</div>
</gp-card>
<router-outlet></router-outlet>