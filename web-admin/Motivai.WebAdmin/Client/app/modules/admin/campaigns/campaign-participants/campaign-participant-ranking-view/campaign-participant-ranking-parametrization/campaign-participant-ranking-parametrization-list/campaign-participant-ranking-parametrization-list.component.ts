import { Component, OnInit, ViewChild, Output, EventEmitter } from '@angular/core';
import { CampaignStore } from '../../../../campaign.store';
import { CampaignParticipantRankingService } from '../../campaign-participant-ranking.service';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';

@Component({
    selector: 'campaign-participant-ranking-parametrization-list',
    templateUrl: './campaign-participant-ranking-parametrization-list.component.html'
})
export class CampaignParticipantRankingParametrizationListComponent implements OnInit {
    @ViewChild('alert') alert: GpAlertComponent;
    @Output('onEdit') onEdit: EventEmitter<any> = new EventEmitter<any>();

    rankingsParametrizations: any[] = [];

    params: any = {};

    loading: boolean = false;
    constructor(private _campaignStore: CampaignStore, private _rankingParametrizationService: CampaignParticipantRankingService) { }

    ngOnInit(): void {
        this.findRankingParametrizations();
    }

    findRankingParametrizations() {
        this.loading = true;
        this._rankingParametrizationService.findCampaingParticipantRankingParametrizations(this._campaignStore.id, this.params.code).subscribe(
            response => {
                this.rankingsParametrizations = response || [];
                this.loading = false;
            },
            err => {
                this.loading = false;
                this.alert.showError(err);
            }
        );

    }

    showRankingParametrizationDetails(event) {
        this.onEdit.emit(event);
    }

    inactiveRankingParametrization(event) {
        this.loading = true;
        this._rankingParametrizationService.inactiveRankingParametrization(this._campaignStore.id, event.id).subscribe(
            response => {
                this.findRankingParametrizations();
                this.loading = false;
            },
            err => {
                this.loading = false;
                this.alert.showError(err);
            }
        );
    }
}
