<gp-card [first]="true" title="Resumo">
	<gp-form-row>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Data de cadastro">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.formattedCreationDate"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Status">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.status" />
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4">
			<gp-spinner-button [pink]="true" text="Aprovar" icon="check" [loading]="loadingApprove" marginTop="26px" (click)="approve()" *ngIf="canApprove"></gp-spinner-button>
			<gp-spinner-button bootstrapClass="warning" text="Recusar" icon="close" [loading]="loadingApprove" (click)="refuseModal.show()" marginTop="26px" marginLeft="5px" *ngIf="canRefuse"></gp-spinner-button>
		</gp-form-col>
	</gp-form-row>

	<div *ngIf="preRegister.approvedOn">
		<hr />
		<gp-form-row>
			<gp-form-col cols="12 4 4">
				<gp-simple-input label="Data de aprovação">
					<input type="text" class="form-control" disabled [ngModel]="preRegister.formattedApprovedOn"/>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
	</div>

	<div *ngIf="preRegister.refusedOn">
		<hr />
		<gp-form-row>
			<gp-form-col cols="12 4 4">
				<gp-simple-input label="Data de recusa">
					<input type="text" class="form-control" disabled [ngModel]="preRegister.formattedRefusedOn"/>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<span><strong>Motivo: </strong> {{ preRegister.refuseReason }}</span>
			</gp-form-col>
		</gp-form-row>
	</div>

	<div *ngIf="hasIntegratedData">
		<hr />
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Integrado com parceiro">
					<input type="text" class="form-control" disabled [ngModel]="preRegister.formmatedIntegrated" />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Data de Integração">
					<input type="text" class="form-control" disabled [ngModel]="preRegister.formmatedIntegratedDate"/>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<div *ngIf="hasIntegrationError">
			<gp-form-row>
				<gp-form-col cols="12 6 6">
					<span><strong>Erro na Integração: </strong> {{ preRegister.integrationErrorMessage }}</span>
				</gp-form-col>
				<gp-form-col cols="12 6 6">
					<gp-spinner-button text="Reintegrar com parceiro" [pink]="true" *ngIf="canReintegrate" [loading]="loadingReintegration" (click)="integrationRetry()"></gp-spinner-button>
				</gp-form-col>
			</gp-form-row>
		</div>
	</div>


	<spinner [overlay]="true" [show]="loading"></spinner>
	<gp-alert #alert [overlay]="true" (onclose)="closeAlert($event)"></gp-alert>
</gp-card>

<gp-card title="Dados do participante">
	<gp-form-row>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.document">
			<gp-simple-input label="CPF/CNPJ">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.document | document"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.rg">
			<gp-simple-input label="RG">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.rg"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.companyName">
			<gp-simple-input label="Razão Social">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.companyName"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.name">
			<gp-simple-input label="Nome">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.name"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.stateInscription">
			<gp-simple-input label="Inscrição Estadual">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.stateInscription"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.stateInscriptionUf">
			<gp-simple-input label="Estado Inscrição Estadual">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.stateInscriptionUf"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.formattedBirthDate">
			<gp-simple-input label="Data de nascimento">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.formattedBirthDate"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.maritalStatus">
			<gp-simple-input label="Estado Civil">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.maritalStatus"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.gender">
			<gp-simple-input label="Sexo">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.gender"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.homePhone">
			<gp-simple-input label="Telefone Residencial">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.homePhone"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.businessPhone">
			<gp-simple-input label="Telefone Comercial">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.businessPhone"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.mobilePhone">
			<gp-simple-input label="Telefone Celular">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.mobilePhone"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.mobileOperator">
			<gp-simple-input label="Operadora do Celular">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.mobileOperator"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.personalEmail">
			<gp-simple-input label="E-mail Pessoal">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.personalEmail"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.businessEmail">
			<gp-simple-input label="E-mail Comercial">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.businessEmail"/>
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>

	<gp-form-row  *ngIf="metadata.length > 0">
		<gp-form-col cols="12 4 4" *ngFor="let field of metadata">
			<gp-simple-input [label]="field.description">
				<input type="text" class="form-control" disabled [ngModel]="userMetadataFieldValue(field.property)"/>
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card title="Endereço Residencial" *ngIf="preRegister.homeAddress">
	<gp-form-row>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.homeAddress.zipcode">
			<gp-simple-input label="Cep">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.homeAddress.zipcode"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.homeAddress.street">
			<gp-simple-input label="Logradouro">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.homeAddress.street"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.homeAddress.number">
			<gp-simple-input label="Número">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.homeAddress.number"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.homeAddress.complement">
			<gp-simple-input label="Complemento">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.homeAddress.complement"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.homeAddress.neighborhood">
			<gp-simple-input label="Bairro">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.homeAddress.neighborhood"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.homeAddress.city">
			<gp-simple-input label="Cidade">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.homeAddress.city"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.homeAddress.state">
			<gp-simple-input label="Estado">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.homeAddress.state"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.homeAddress.reference">
			<gp-simple-input label="Ponto de Referência">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.homeAddress.reference"/>
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card title="Endereço Residencial" *ngIf="preRegister.businessAddress">
	<gp-form-row>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.businessAddress.zipcode">
			<gp-simple-input label="Cep">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.businessAddress.zipcode"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.businessAddress.street">
			<gp-simple-input label="Logradouro">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.businessAddress.street"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.businessAddress.number">
			<gp-simple-input label="Número">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.businessAddress.number"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.businessAddress.complement">
			<gp-simple-input label="Complemento">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.businessAddress.complement"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.businessAddress.neighborhood">
			<gp-simple-input label="Bairro">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.businessAddress.neighborhood"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.businessAddress.city">
			<gp-simple-input label="Cidade">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.businessAddress.city"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.businessAddress.state">
			<gp-simple-input label="Estado">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.businessAddress.state"/>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4" *ngIf="preRegister.businessAddress.reference">
			<gp-simple-input label="Ponto de Referência">
				<input type="text" class="form-control" disabled [ngModel]="preRegister.businessAddress.reference"/>
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-modal #refuseModal title="Recusar Participante" width="700px" (onClose)="findPreRegister()">
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<gp-simple-input label="Motivo da recusa">
				<textarea class="form-control" rows="5" name="refuseReason" [(ngModel)]="refuseReason"></textarea>
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<gp-spinner-button [pink]="true" text="Recusar Participante" *ngIf="canRefuse" [disabled]="!refuseReason" [loading]="loadingRefuse" (click)="refuse()"></gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
</gp-modal>
