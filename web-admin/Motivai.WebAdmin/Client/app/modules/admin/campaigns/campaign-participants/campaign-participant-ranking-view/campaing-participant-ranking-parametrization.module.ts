import { NgModule, LOCALE_ID } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CampaignParticipantRankingParametrizationViewComponent } from './campaign-participant-ranking-parametrization/campaign-participant-ranking-parametrization-view.component';
import { CampaignParticipantRankingParametrizationEditComponent } from './campaign-participant-ranking-parametrization/campaign-participant-ranking-parametrization-edit/campaign-participant-ranking-parametrization-edit.component';
import { CampaignParticipantRankingParametrizationListComponent } from './campaign-participant-ranking-parametrization/campaign-participant-ranking-parametrization-list/campaign-participant-ranking-parametrization-list.component';
import { CampaignParticipantRankingService } from './campaign-participant-ranking.service';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../../../../../shared/shared.module';
import { CampaignParticipantRankingViewComponent } from './campaign-participant-ranking-view.component';
import { CampaignParticipantRankingImportingComponent } from './campaign-participant-ranking-importing/campaign-participant-ranking-importing.component';
import { CampaignParticipantRankingImportingListComponent } from './campaign-participant-ranking-importing/campaign-participant-ranking-importing-list/campaign-participant-ranking-importing-list.component';
import { CampaignParticipantRankingImportingEditComponent } from './campaign-participant-ranking-importing/campaign-participant-ranking-importing-edit/campaign-participant-ranking-importing-edit.component';

@NgModule({
    declarations: [
        CampaignParticipantRankingParametrizationViewComponent,
        CampaignParticipantRankingParametrizationEditComponent,
        CampaignParticipantRankingParametrizationListComponent,
        CampaignParticipantRankingViewComponent,
        CampaignParticipantRankingImportingComponent,
        CampaignParticipantRankingImportingListComponent,
        CampaignParticipantRankingImportingEditComponent
    ],
    imports: [ 
        CommonModule,
        SharedModule.forRoot(),
        RouterModule.forChild([
            { 
                path: '', component: CampaignParticipantRankingViewComponent,
                children: [
                    { path: 'parametrizacao', component: CampaignParticipantRankingParametrizationViewComponent },
                    { path: 'importacao', component: CampaignParticipantRankingImportingComponent}
                ]
            }
        ]) ],
    exports: [ RouterModule ],
    providers: [ 
        [{ provide: LOCALE_ID, useValue: 'pt-br' }],
        CampaignParticipantRankingService
    ],
})
export class CampaignParticipantRankingModule {}
