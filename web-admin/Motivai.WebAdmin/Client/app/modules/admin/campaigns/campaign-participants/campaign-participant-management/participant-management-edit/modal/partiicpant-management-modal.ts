import { Component, EventEmitter, Input, Output, ViewChild } from "@angular/core";
import { GpModalComponent } from "../../../../../../../shared/components/gp-modal/gp-modal.component";
import { GpAlertComponent } from "../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component";
import { ParticipantService } from "../../../../../backoffice/participant.service";

@Component({
    selector: 'participant-management-modal',
    templateUrl: './participant-management-modal.html'
})
export class ParticipantMenagementModalComponent {
    @Output('close') closeEmitter: EventEmitter<any> = new EventEmitter<any>();
    @Input() campaignId: string = "";

    @ViewChild('modal') modal: GpModalComponent;
    @ViewChild('gpAlert') gpAlert: GpAlertComponent;

    sending: boolean = false;

    reason: string = "";
    participant: any = {};
    constructor(private readonly _participantService: ParticipantService) {}

    unblockParticipant() {
        this.sending = true;
        this._participantService.unblockParticipant(this.campaignId, this.participant.userId, this.reason)
          .subscribe(response => {
            this.sending = false;
            if (!response) {
              return this.gpAlert.showWarning('Ocorreu um erro ao desbloquear o participante.');
            }
            this.gpAlert.showSuccess('Participante desbloqueado com sucesso.');
            this.closeModal();
          }, err => {
            this.sending = false;
            this.gpAlert.handleAndShowError(err);
          }
        )
      }
    public onClose() {
        this.clear();
        this.closeEmitter.emit({})
    }

    public closeModal() {
        this.modal.hide();
    }

    public showModal(participant: any) {
      this.clear();
      this.participant = participant;
      this.modal.show();
    }

    clear() {
      this.reason = "";
    }
}
