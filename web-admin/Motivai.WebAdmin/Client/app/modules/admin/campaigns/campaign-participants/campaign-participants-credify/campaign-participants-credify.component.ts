import { Component, OnInit, ViewChild, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';

import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { RxjsHelpers } from '../../../../../shared/helpers/rxjs-helpers';

import { CampaignService } from '../../campaign.service';
import { CampaignStore } from '../../campaign.store';

@Component({
	selector: 'campaign-participants-credify',
	templateUrl: './campaign-participants-credify.component.html'
})
export class CampaignParticipantsCredifyComponent implements OnInit, OnDestroy {
	@ViewChild('alert') alert: GpAlertComponent;

	campaignId: string;
	private campaign$: Subscription;

	processesParametrizatons: any = {
		consultPersonData: {
			processesEnabled: {},
			fieldsEnabled: {}
		}
	};
	loading: boolean = false;

	constructor(
		private campaignStore: CampaignStore,
		private campaignService: CampaignService
	) { }

	ngOnInit() {
		this.campaign$ = this.campaignStore.asObservable
		.subscribe(campaignId => {
			if (campaignId) {
				this.campaignId = campaignId;
				this.loadCredifyParametrizations();
			}
		});
	}

	ngOnDestroy() {
		RxjsHelpers.unsubscribe(this.campaign$);
	}

	private loadCredifyParametrizations(): void {
		this.loading = true;
		this.campaignService.getPersonDataConsultParametrizations(this.campaignId)
			.subscribe(response => {
				this.loading = false;
				if (response) {
					this.processesParametrizatons.consultPersonData = response;
				} else {
					this.processesParametrizatons = {
						consultPersonData: {
							processesEnabled: {},
							fieldsEnabled: {}
						}
					}
				}
			},
			err => {
				this.loading = false;
				this.alert.handleAndShowError(err);
			}
		);
	}

	onSave(): void {
		if (!this.validate()) {
			this.alert.showWarning('Selecione ao menos um local para ser habilitado e uma informação a ser exibida');
			return;
		}
		this.loading = true;
		this.campaignService.updatePersonDataConsultParametrizations(this.campaignId, this.processesParametrizatons)
			.subscribe(response => {
				this.loading = false;
				if (response) {
					this.processesParametrizatons.consultPersonData = response;
					this.alert.showSuccess('Parametrizações salvas com sucesso');
				} else {
					this.alert.showError('Ocorreu um erro ao salvar as parametrizações');
					this.loadCredifyParametrizations();
				}
			},
			err => {
				this.loading = false
				this.alert.handleAndShowError(err);
			}
		);
	}

	private validate(): boolean {
		if (!this.processesParametrizatons.consultPersonData.enabled)
			return true;
		if (this.processesParametrizatons.consultPersonData.processesEnabled.preRegister) 
			return true && this.validateFieldsToDisplay();
		if (this.processesParametrizatons.consultPersonData.processesEnabled.participantInvite)
			return true && this.validateFieldsToDisplay();
		if (this.processesParametrizatons.consultPersonData.processesEnabled.pointsDistribute)
			return true && this.validateFieldsToDisplay();
		
		return false;
	}

	private validateFieldsToDisplay(): boolean {
		if (this.processesParametrizatons.consultPersonData.fieldsEnabled.name)
			return true;
		if (this.processesParametrizatons.consultPersonData.fieldsEnabled.birthDate)
			return true;
		if (this.processesParametrizatons.consultPersonData.fieldsEnabled.gender)
			return true;
		if (this.processesParametrizatons.consultPersonData.fieldsEnabled.address)
			return true;

		return false;
	}
}
