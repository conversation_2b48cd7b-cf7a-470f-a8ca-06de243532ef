<gp-card title="Filtros" [first]="true">
    <gp-form-row>
        <gp-form-col cols="12 4">
            <gp-simple-input label="Código do Ranking">
                <input type="text" name="code" class="form-control" [(ngModel)]="params.code" />
            </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 4">
            <gp-spinner-button [search]="true" text="Pesquisar" marginTop="27px" [loading]="loading" loadingText="Processando" (click)="findRankingParametrizations()"></gp-spinner-button>
        </gp-form-col>
    </gp-form-row>
</gp-card>
<gp-alert #alert [overlay]="true"></gp-alert>
<gp-card>
    <gp-form-row>
        <gp-form-col cols="12">
            <gp-grid [rows]="rankingsParametrizations" [columns]="['Name', 'Código Ranking', 'Notificação']" [loading]="loading"
                [fields]="['name', 'code', 'formattedEnableNotification']" [showActive]="true" [showEdit]="true"
                (onEdit)="showRankingParametrizationDetails($event)" [showDelete]="true"
                (onDelete)="inactiveRankingParametrization($event)"></gp-grid>
        </gp-form-col>
    </gp-form-row>
</gp-card>
