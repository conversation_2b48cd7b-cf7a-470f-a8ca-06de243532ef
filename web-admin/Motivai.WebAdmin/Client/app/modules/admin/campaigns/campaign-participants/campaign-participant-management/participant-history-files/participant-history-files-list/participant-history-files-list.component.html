<gp-card>
    <gp-form-col cols="12 12 12">
        <table class="table table-striped table-hover">
            <thead>
                <tr>
                    <th>Nome do arquivo</th>
                    <th>Url</th>
                    <th>Criado por</th>
                    <th>Ativo</th>
                    <th *ngIf="canUpdateHistoricFiles">Ações</th>
                </tr>
            </thead>

            <tbody>
                <tr *ngFor="let a of historyFiles">
                    <td>{{a.name}}</td>
                    <td><a href="{{a.url}}"  target="_blank" >Abrir em uma nova guia</a></td>
                    <td>{{a.userCreation.userAdministratorName}}</td>
                    <td *ngIf="a.active">
                        <div class='label label-success'>Ativo</div>
                    </td>
                    <td *ngIf="!a.active">
                        <div class='label label-danger'>Inativo</div>
                    </td>
                    <div *ngIf="canUpdateHistoricFiles">
                        <td class="text-center" *ngIf="a.active">
                            <button type="button" title="Inativar" class="btn btn-danger btn-sm"
                                (click)="onDelete(a)">
                                <i title="Inativar" class="fa fa-lock grid-edit"></i>
                            </button>
                        </td>
                        <td *ngIf="!a.active">
                            <span>Sem ações</span>
                        </td>
                    </div>
                </tr>
            </tbody>
            <spinner [overlay]="true" [show]="loadingGrid"></spinner>
        </table>
    </gp-form-col>
</gp-card>

<gp-alert [overlay]="true" #alert></gp-alert>
