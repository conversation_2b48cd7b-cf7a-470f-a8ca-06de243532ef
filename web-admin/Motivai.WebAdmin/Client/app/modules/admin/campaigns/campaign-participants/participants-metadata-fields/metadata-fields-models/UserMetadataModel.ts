export interface UserMetadataHeaderDetails {
    active: boolean;
    property: string;
    description: string;
    placeholder: string;
    origin: string;
    originBatchId: string;
    pagesConfigurations: MetadataPageConfiguration[];
    allowedValues: MetadataAllowedValue[];
}

export interface MetadataPageConfiguration {
    active: boolean;
    page: PlatformPage;
    type: DynamicFieldValueType;
    readOnly: boolean;
    required: boolean;
    allowedValues: MetadataAllowedValue[];
}

export enum PlatformPage {
    LOGIN = 'LOGIN',
    FIRST_ACCESS = 'FIRST_ACCESS',
    PASSWORD_RESET = 'PASSWORD_RESET',
    PASSWORD_CHANGE = 'PASSWORD_CHANGE',
    RANKING = 'RANKING',
    SUMMARY = 'SUMMARY',
    PRE_REGISTRATION = 'PRE_REGISTRATION',
    INVITE_PARTICIPANT = 'INVITE_PARTICIPANT'
}

export enum DynamicFieldValueType {
    TEXT = 'TEXT',
    INTEGER = 'INTEGER',
    DECIMAL = 'DECIMAL',
    CHECK = 'CHECK',
    DATE = 'DATE',
    OPTION = 'OPTION'
}

export interface MetadataAllowedValue {
    active: boolean;
    key: string;
    value: string;
    index: number;
}



