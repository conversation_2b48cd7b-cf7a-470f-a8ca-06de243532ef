export interface ResumedMetadataPageModel {
    active: boolean;
    property: string;
    placeHolder: string;
    description: string;
    pagesConfigurations: MetadataPageConfigurationModel[];
}

export interface MetadataPageConfigurationModel {
    active: boolean;
    page: string;
    type: string;
    readOnly: boolean;
    required: boolean;
    allowedValues: MetadataAllowedValueModel[];
}

export interface MetadataAllowedValueModel {
    active: boolean;
    key: string;
    value: string;
    index: number;
}
