import { FormatHelper } from '../../../../../../shared/formatters/format-helper';
import { Component, OnInit, ViewChild, Input, Output, EventEmitter } from '@angular/core';
import { CampaignPreRegisterService } from '../campaign-preregister.service';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { isNullOrUndefined } from 'util';
import { Item } from '../../../../../../shared/models/item';

@Component({
    selector: 'campaign-preregister-list',
    templateUrl: './campaign-preregister-list.component.html'
})
export class CampaignPreRegisterListComponent implements OnInit {
    private _campaignId: string;
    @Input() set campaignId(v: string) {
        if (v) {
            this._campaignId = v;
            this.search();
        }
    }

    parameters: any = {
        integration: {}
    };
    loading: boolean = false;
    preRegisters: any[] = [];
    skip: number = 0;
    limit: number = 20;

    integrationStatus: Array<Item> = [
        Item.of("none", "Todos"),
        Item.of("integrated", 'Integrado'),
        Item.of("notIntegrated", 'Não integrado'),
        Item.of("error", 'Erro durante integração')
    ]

    selectedIntegrationStatus: string;

    @Output('edit') edit: EventEmitter<any> = new EventEmitter();
    @ViewChild('alert') alert: GpAlertComponent;

    constructor(private _preregisterService: CampaignPreRegisterService) { }

    ngOnInit(): void { }

    search() {
        if (this._campaignId) {
            this.loading = true;
            this.alert.clear();
            this._preregisterService.search(this._campaignId, this.parameters.document, this.parameters.integration, this.skip, this.limit).subscribe(
                registers => {
                    if(!isNullOrUndefined(registers)){
                        this.preRegisters = this.treatRegisters(registers);
                    } else {
                        this.alert.showError("Nenhum dado encontrado para o documento informado");
                        this.preRegisters = [];
                    }
                    this.loading = false
                },
                err => {
                    this.alert.showError(this.handleError(err));
                    this.loading = false;
                }
            );
        }
    }

    treatRegisters(registers : any[]) : any[] {
        registers.forEach(el => {
            el.formattedCreateDate = FormatHelper.formatDateWithTime(el.createDate);
        });
        return registers;
    }

    onIntegrationStatusChange() {
        this.parameters.integration = {};

        switch (this.selectedIntegrationStatus) {
            case "integrated" :
                this.parameters.integration.integrated = true;
            break;
            case "notIntegrated" :
                this.parameters.integration.integrated = false;
            break;
            case "error" :
                this.parameters.integration.integrationError = true;
            break;
        }
    }

    onPageChanged($event) {
        if ($event) {
            this.skip = $event.skip;
            this.limit = $event.limit;
            this.search();
        }
    }

    editPreRegister($event) {
        if ($event) {
            this.edit.emit($event);
        }
    }

    handleError(msg: any) {
        return msg ? (msg.message ? msg.message : msg.toString()) : 'Ocorreu um erro ao processar a requisição';
    }
}
