<div>
	<spinner [overlay]="true" [show]="loading"></spinner>
	<gp-alert #alert [overlay]="true"></gp-alert>

	<gp-card [first]="false" title="Parametrizações Credify">
		<gp-form-row>
			<gp-form-col cols="12 6">
				<gp-simple-input label="Habilitar consulta" [required]="false">
					<div>
						<gp-switch name="fieldName" [(ngModel)]="processesParametrizatons.consultPersonData.enabled"></gp-switch>
					</div>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<div *ngIf="processesParametrizatons.consultPersonData.enabled">
			<hr />
			<gp-form-row>
				<gp-form-col cols="12 12 12">
					<gp-simple-input label="Selecione quais páginas terão a consulta habilitada" [required]="false">
						<gp-form-row>
							<gp-input-checkbox grid="12 6 4 3" name="preRegister" text="Pré-Cadastro" 
								[(ngModel)]="processesParametrizatons.consultPersonData.processesEnabled.preRegister">
							</gp-input-checkbox>
							<gp-input-checkbox grid="12 6 4 3" name="participantInvite" text="Convite de Participante" 
								[(ngModel)]="processesParametrizatons.consultPersonData.processesEnabled.participantInvite">
							</gp-input-checkbox>
							<gp-input-checkbox grid="12 6 4 3" name="pointsDistribute" text="Distribuição de Pontos"
								[(ngModel)]="processesParametrizatons.consultPersonData.processesEnabled.pointsDistribute">
							</gp-input-checkbox>
						</gp-form-row>
					</gp-simple-input>
				</gp-form-col>
			</gp-form-row>

			<hr />

			<gp-form-row>
				<gp-form-col cols="12 12 12">
					<gp-simple-input label="Selecione quais informações serão retornadas na consulta" [required]="false">
						<gp-form-row>
							<gp-input-checkbox grid="12 6 4 3" name="name" text="Nome" 
								[(ngModel)]="processesParametrizatons.consultPersonData.fieldsEnabled.name">
							</gp-input-checkbox>
							<gp-input-checkbox grid="12 6 4 3" name="birthDate" text="Data de nascimento" 
								[(ngModel)]="processesParametrizatons.consultPersonData.fieldsEnabled.birthDate">
							</gp-input-checkbox>
							<gp-input-checkbox grid="12 6 4 3" name="gender" text="Gênero"
								[(ngModel)]="processesParametrizatons.consultPersonData.fieldsEnabled.gender">
							</gp-input-checkbox>
							<gp-input-checkbox grid="12 6 4 3" name="address" text="Endereço" 
								[(ngModel)]="processesParametrizatons.consultPersonData.fieldsEnabled.address">
							</gp-input-checkbox>
						</gp-form-row>
					</gp-simple-input>
				</gp-form-col>
			</gp-form-row>
		</div>
	</gp-card>

	<gp-card [last]="true">
		<gp-form-row>
			<gp-form-col cols="12" [inputGroup]="false">
				<gp-spinner-button type="button" [pink]="true" bootstrapClass="primary" text="Salvar" pull="right" icon="send"
					[loading]="loading" loadingText="Aguarde" (click)="onSave()">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</gp-card>
</div>
