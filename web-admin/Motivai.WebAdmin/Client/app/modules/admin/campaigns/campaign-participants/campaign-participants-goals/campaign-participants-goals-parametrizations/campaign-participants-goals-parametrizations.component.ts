import { Component, OnInit, ViewChild } from '@angular/core';
import { CampaignParticipantsGoalsParametrizationsListComponent } from './campaign-participants-goals-parametrizations-list/campaign-participants-goals-parametrizations-list.component';
import { CampaignParticipantsGoalsParametrizationsEditComponent } from './campaign-participants-goals-parametrizations-edit/campaign-participants-goals-parametrizations-edit.component';
import { TabsetComponent } from 'ng2-bootstrap';

@Component({
  selector: 'campaign-participants-goals-parametrizations',
  templateUrl: './campaign-participants-goals-parametrizations.component.html'
})
export class CampaignParticipantsGoalsParametrizationsComponent implements OnInit {
  @ViewChild('tabs') tabs: TabsetComponent;
  @ViewChild('list') list: CampaignParticipantsGoalsParametrizationsListComponent;
  @ViewChild('edit') edit: CampaignParticipantsGoalsParametrizationsEditComponent;
  
  constructor() { }

  ngOnInit() {
  }

  refresh() {
    this.list.findCampaignParticipantsGoalsParametrizations();
    this.edit.clear();
  }

  showCampaignParticipantsGoal($event) {
    this.edit.parametrizationId = $event.id;
    this.edit.findCampaignParticipantsGoal();
    this.tabs.tabs[1].active = true;
  }
}
