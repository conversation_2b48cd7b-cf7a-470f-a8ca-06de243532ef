import { CampaignStore } from '../../../../../campaign.store';
import { CampaignParticipantManagementService } from '../../../campaign-participant-management.service';
import { GpAlertComponent } from '../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpModalComponent } from '../../../../../../../../shared/components/gp-modal/gp-modal.component';
import { Component, OnInit, ViewChild, Input, Output, EventEmitter } from '@angular/core';

@Component({
    selector: 'participant-account-operator-modal',
    templateUrl: './participant-account-operator-modal.component.html'
})
export class ParticipantAccountModalComponent implements OnInit {
    @ViewChild('modal') modal: GpModalComponent;
    @ViewChild('modalActive') modalActive: GpModalComponent;

    @ViewChild('alert') alert: GpAlertComponent;

    @Output('update') update: EventEmitter<any> = new EventEmitter();

    loading: boolean = false;
    operator: any = {};
    userId: string = '';
    campaignId: string = '';
    reason: any = '';
    blockingOperator: any = {};
    activingOperator: any = {};
    campaign$: any = {};

    @Input()
    set _userId(id: string) {
        if (!id) {
            return;
        }
        this.userId = id;
    }

    constructor(private _participantService: CampaignParticipantManagementService, private _campaignStore: CampaignStore) { }

    ngOnInit(): void {
        this.campaign$ = this._campaignStore.asObservable
        .subscribe(
          campaignId => {
            if (campaignId) {
              this.campaignId = campaignId;
            }
          }
        );
    }

    openModal(operator) {
        this.operator = operator;
        this.modal.show();
    }

    openActiveModal(operator) {
        this.operator = operator;
        this.modalActive.show();
    }

    prepareToBlock() {
        this.blockingOperator.accountOperatorId = this.operator.accountOperatorId;
        this.blockingOperator.accountOperatorLoginId = this.operator.accountOperatorLoginId;
        if (this.reason == null) {
            this.alert.showWarning('Preencha o motivo para prosseguir.');
        } else {
            this.blockingOperator.reason = this.reason;
            this.disableOperator();
        }
    }

    prepareToActive() {
        this.activingOperator.accountOperatorId = this.operator.accountOperatorId;
        this.activingOperator.accountOperatorLoginId = this.operator.accountOperatorLoginId;
        if (this.reason == null) {
            this.alert.showWarning('Preencha o motivo para prosseguir.');
        } else {
            this.activingOperator.reason = this.reason;
            this.activeOperator();
        }
    }

    disableOperator() {
        this.loading = true;
        this._participantService.blockAccountOperator(this.userId, this.campaignId, this.blockingOperator).subscribe(
            response => {
                this.alert.showSuccess('Operador bloqueado.');
                this.onUpdate();
                this.loading = false;
            }, err => {
                this.loading = false;
                this.alert.showError(err);
            }
        );
    }

    activeOperator() {
        this.loading = true;
        this._participantService.activeAccountOperator(this.userId, this.campaignId, this.activingOperator)
            .subscribe(
                response => {
                    this.alert.showSuccess('Operador ativado.');
                    this.onUpdate();
                    this.loading = false;
                }, err => {
                    this.loading = false;
                    this.alert.showError(err);
                }
            )
    }

    onUpdate() {
      this.update.emit({});
      this.close();
      this.clear();
    }

    clear() {
        this.reason = '';
        this.blockingOperator = { };
        // this.userId = '';
    }
    close() {
        this.modal.hide();
        this.modalActive.hide();
    }

}
