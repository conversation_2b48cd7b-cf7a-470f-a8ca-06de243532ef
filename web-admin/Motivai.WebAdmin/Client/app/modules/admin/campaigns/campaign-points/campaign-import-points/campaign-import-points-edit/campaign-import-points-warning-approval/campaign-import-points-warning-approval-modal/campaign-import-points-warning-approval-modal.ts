import { FormatHelper } from '../../../../../../../../shared/formatters/format-helper';
import { Subscription } from 'rxjs/Subscription';
import { CampaignStore } from '../../../../../campaign.store';
import { Component, EventEmitter, OnInit, Output, ViewChild } from "@angular/core";
import { GpModalComponent } from "../../../../../../../../shared/components/gp-modal/gp-modal.component";
import { CampaignPointsService } from '../../../../campaign-points.service';
import { GpAlertComponent } from '../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { AuthStore } from '../../../../../../../../core/auth/auth.store';
import { PERMISSION_CAMPAIGNS_IMPORT_POINTS_APPROVE } from '../../../../../../../../core/auth/access-points';

@Component({
    selector: 'campaign-import-points-warning-approval-modal',
    templateUrl: './campaign-import-points-warning-approval-modal.component.html'
})
export class CampaignImportPointsWarningApprovalModalComponent implements OnInit {
    @ViewChild('modal') modal: GpModalComponent;
    @ViewChild('gpAlert') gpAlert: GpAlertComponent;

    @Output() approved: EventEmitter<any> = new EventEmitter();

    private campaign$: Subscription;
    private campaignId: string;
    private loadingApproval: boolean = false;
    private loadingRefusal: boolean = false;

    temporaryPoint: any = {};
    campaignFraudSettings: any = {};

    constructor (private _pointsService: CampaignPointsService, private campaignStore: CampaignStore, private _authStore: AuthStore){}

    ngOnInit(): void {
        this.campaign$ = this.campaignStore.asObservable.subscribe(id => {this.campaignId = id});
    }

    openModal(temporaryPoint: any) {
        this.temporaryPoint = temporaryPoint;
        this.modal.show();
    }

    get warningByParticipant(): boolean {
        return this.temporaryPoint.statusCode == "WARNING_EXCEED_PARTICIPANT_AVERAGE_CREDIT_AMOUNT";
    }

    get warningByCampaign(): boolean {
        return this.temporaryPoint.statusCode == "WARNING_EXCEED_CAMPAIGN_AVERAGE_CREDIT_AMOUNT";
    }

    get canApprove(): boolean {
        return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_IMPORT_POINTS_APPROVE);
    }

    private handleError(err: any) {
        this.gpAlert.showError(err ? (err.message ? err.message : err) : 'Ocorreu um erro durante o processamento, por favor, tente novamente.');
        this.modal.hide();
    }

    private approval(){
        this.loadingApproval = true;
        this._pointsService.approveTemporaryPointAntiFraud(this.temporaryPoint.id)
            .subscribe(
                response => {
                    this.loadingApproval = false;
                    this.gpAlert.showInfo("Ponto aprovado com sucesso!");
                    this.approved.emit();
                },
                err => {
                    this.loadingApproval = false;
                    this.gpAlert.showError('Não foi possível atualizar o ponto temporário. '+err);
                }
            );
    }

    private refuse(){
        this.loadingRefusal = true;
        this._pointsService.refuseTemporaryPointAntiFraud(this.temporaryPoint.id)
        .subscribe(
            response => {
                this.loadingRefusal = false;
                this.gpAlert.showInfo("Ponto reprovado com sucesso!");
                this.approved.emit();
            },
            err => {
                this.loadingRefusal = false;
                this.gpAlert.showError('Não foi possível atualizar o ponto temporário. '+err);
            }
        );
    }

    private close(){
        this.modal.hide();
    }


}
