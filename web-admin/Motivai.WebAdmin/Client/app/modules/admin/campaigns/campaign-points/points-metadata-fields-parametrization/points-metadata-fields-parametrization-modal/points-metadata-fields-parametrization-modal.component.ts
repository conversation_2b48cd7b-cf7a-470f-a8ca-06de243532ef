import { NgForm } from '@angular/forms';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpModalComponent } from '../../../../../../shared/components/gp-modal/gp-modal.component';
import { Component, OnInit, ViewChild, Output, EventEmitter } from '@angular/core';

@Component({
    selector: 'app-points-metadata-fields-parametrization-modal',
    templateUrl: './points-metadata-fields-parametrization-modal.component.html'
})
export class PointsMetadataFieldsParametrizationModalComponent implements OnInit {
    @ViewChild('modal') modal: GpModalComponent;
    @ViewChild('alert') alert: GpAlertComponent;
    @ViewChild('ngForm') ngForm: NgForm;
    @Output() onInsert: EventEmitter<any> = new EventEmitter<any>();

    metadataFieldsParametrization: any = {};

    isEditing: boolean = false;
    loading: boolean = false;
    constructor() { }

    ngOnInit(): void { }

    get selectInputButonText() {
        if (this.isEditing)
          return 'Atualizar';
        return 'Adicionar';
    }

    openModal() {
        this.modal.show();
    }

    closeModal() {
        this.modal.hide();
        this.clear();
        this.isEditing = false;
    }

    createMetadata() {
        this.onInsert.emit({metadataFieldsParametrization: this.metadataFieldsParametrization, isEditing: this.isEditing});
        this.closeModal();
    }

    clear() {
        this.metadataFieldsParametrization = {};
        this.isEditing = false;
        this.ngForm.reset();
    }
}
