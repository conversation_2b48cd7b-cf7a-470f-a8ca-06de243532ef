import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { ApiService } from '../../../../core/api/api.service';

@Injectable()
export class CampaignPointsService {
	constructor(private _api: ApiService) { }

	getDistributionParametrization(campaignId: string): Observable<any> {
		return this._api.get(`/api/campaigns/${campaignId}/points/distribution/parametrization`);
	}

	saveDistributionParametrization(campaignId: string, distributionConfig: any): Observable<any> {
		return this._api.put(`/api/campaigns/${campaignId}/points/distribution/parametrization`, distributionConfig);
	}

	saveMetadataFieldsParametrization(campaignId: string, metadataFieldsParametrization: any): Observable<any> {
        if (!metadataFieldsParametrization.id) {
			return this._api.post(`/api/campaigns/${campaignId}/points/metadata/parametrization`, metadataFieldsParametrization);
		}
		return this._api.put(`/api/campaigns/${campaignId}/points/metadata/parametrization/${metadataFieldsParametrization.id}`, metadataFieldsParametrization);
	}

	loadMetadataFieldsParametrizations(campaignId: string): Observable<any> {
		return this._api.get(`/api/campaigns/${campaignId}/points/metadata/parametrization`);
	}

	loadFraudControlDetailsFromParticipant(userId: string, userParticipantId: string): Observable<any> {
		return this._api.get(`/api/antifraud/${userId}/${userParticipantId}/status`);
	}

	loadCampaignFraudSettings(campaignId: string): Observable<any> {
		return this._api.get(`/api/antifraud/${campaignId}/settings`);
	}

	approveTemporaryPointAntiFraud(temporaryPointId: string){
		return this._api.put(`/api/antifraud/${temporaryPointId}/approve`);
	}

	refuseTemporaryPointAntiFraud(temporaryPointId: string){
		return this._api.put(`/api/antifraud/${temporaryPointId}/refuse`);
	}

}
