import { Component, OnInit, ViewChild } from '@angular/core';
import { PointsMetadataFieldsParametrizationModalComponent } from './points-metadata-fields-parametrization-modal/points-metadata-fields-parametrization-modal.component';
import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignStore } from '../../campaign.store';
import { CampaignPointsService } from '../campaign-points.service';

@Component({
    selector: 'app-points-metadata-fields-parametrization',
    templateUrl: './points-metadata-fields-parametrization.component.html'
})
export class PointsMetadataFieldsParametrizationComponent implements OnInit {
    @ViewChild('metadataFieldsParametrizationModal') metadataFieldsParametrizationModal: PointsMetadataFieldsParametrizationModalComponent;
    @ViewChild('alert') alert: GpAlertComponent;


    metadataFieldsParametrization: any = {
        headersDetails: []
    };

    loading: boolean = false;

    constructor(private _store: CampaignStore, private _service: CampaignPointsService) { }

    ngOnInit(): void {
        this.loadMedataParametrization();
    }

    openMetadataFieldsModal() {
        this.metadataFieldsParametrizationModal.openModal();
    }

    saveMetadataParametrization() {
        this.loading = true;
        this._service.saveMetadataFieldsParametrization(this._store.id, this.metadataFieldsParametrization).subscribe(
            response => {
                this.loading = false;
                this.alert.showSuccess('Parametrização salva.');
                this.loadMedataParametrization();
            },
            err => {
                this.loading = false;
                this.alert.showError(err);
            }
        );
    }

    loadMedataParametrization() {
        this.loading = true;
        this._service.loadMetadataFieldsParametrizations(this._store.id).subscribe(
            response => {
                this.metadataFieldsParametrization = response || {};
                this.loading = false;
            },
            err => {
                this.loading = false;
                this.alert.showError(err);
            }
        );
    }

    linkMetadataFields(event) {
        if (!event) {
            this.alert.showWarning('Preencha os campos corretamente para adicionar.');
            return;
        }
        if (!event.metadataFieldsParametrization.property || !event.metadataFieldsParametrization.property.length) {
            this.alert.showWarning('Preencha o código do campo.');
            return;
        }
        if (!event.metadataFieldsParametrization.description || !event.metadataFieldsParametrization.description.length) {
            this.alert.showWarning('Preencha a descrição do campo.');
            return;
        }

        if (!event.isEditing) {
            if (this.metadataFieldsParametrization.headersDetails && this.metadataFieldsParametrization.headersDetails) {
                if (!this.metadataFieldsParametrization.headersDetails.find(x => x.property == event.metadataFieldsParametrization.property)) {
                    this.metadataFieldsParametrization.headersDetails = this.metadataFieldsParametrization.headersDetails.concat([event.metadataFieldsParametrization]);
                } else {
                    this.alert.showWarning('Não é permitido duplicar os códigos dos campos.');
                }
            } else {
                this.metadataFieldsParametrization.headersDetails = [event.metadataFieldsParametrization];
            }
        }
    }

    editField(row: any) {
        if (!row) return;
        this.metadataFieldsParametrizationModal.metadataFieldsParametrization = row;
        this.openMetadataFieldsModal();
        this.metadataFieldsParametrizationModal.isEditing = true;
    }

    inactiveField(value: any) {
        if (!value) return;

        value.active = false;
    }

}
