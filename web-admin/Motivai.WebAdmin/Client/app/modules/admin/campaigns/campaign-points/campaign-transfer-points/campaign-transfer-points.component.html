<gp-card title="Participante de Origem">
	<gp-form-row>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Tipo de Identificação">
				<select class="form-control" [(ngModel)]="parameters.identificationType">
					<option>Documento</option>
					<option>Login</option>
				</select>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Identificação">
				<input type="text" class="form-control" name="identification" [(ngModel)]="parameters.identification" />
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Mecânica">
				<select class="form-control" [(ngModel)]="parameters.mechanicId">
					<option>Selecione uma mecanica</option>
				</select>
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<gp-spinner-button 
				type="submit" 
				text="Buscar Saldo"
				size="md" [pink]="true" icon="search">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
	<hr />
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<h3><small>Total de Pontos: </small> <h4>0,00</h4></h3>
			<h3><small>Total em Real (R$): </small> <h4>0,00</h4></h3>
		</gp-form-col>
	</gp-form-row>
	<hr />
	<gp-form-row>
		<gp-form-col cols="12 4 4">
			<div class="form-group">
				<label>Valor para transferir (R$)</label>
				<div class="input-group">
					<span class="input-group-addon">R$</span>
					<input type="text" class="form-control" name="currencyAmount" placeholder="50,00" />
				</div>
			</div>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card title="Participante de Destino">
	<gp-form-row>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Tipo de Identificação">
				<select class="form-control" [(ngModel)]="parameters.identificationType">
					<option>Documento</option>
					<option>Login</option>
				</select>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Identificação">
				<input type="text" class="form-control" name="identification" [(ngModel)]="parameters.identification" />
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 3 3">
			<gp-spinner-button 
				type="submit" 
				text="Buscar Participante"
				size="md" [pink]="true" icon="search" marginTop="25px">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Campanha de Destino">
				<select class="form-control" [(ngModel)]="parameters.identificationType">
					<option>Selecione uma Campanha</option>
				</select>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Mecânica de Destino">
				<select class="form-control" [(ngModel)]="parameters.identificationType">
					<option>Selecione uma Mecânica</option>
				</select>
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<p>ATENÇÃO: Os pontos serão calculados de acordo com o fator de conversão da mecânica de destino! Caso nenhuma mecânica seja informada, os pontos serão transferidos fazendo a cópia das mecânicas de origem para a campanha destino!</p>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card>
	<div class="row">
		<div class="col-md-12">
			<gp-spinner-button 
				type="submit" 
				text="Iniciar Transferência"
				size="md" [pink]="true" icon="exchange">
			</gp-spinner-button>
		</div>
	</div>
</gp-card>
