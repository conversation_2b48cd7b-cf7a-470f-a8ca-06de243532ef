import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { PERMISSION_CAMPAIGNS_MECHANICS_ENABLE, PERMISSION_CAMPAIGNS_MECHANICS_LIST, PERMISSION_CAMPAIGNS_MECHANICS_REMOVE, PERMISSION_CAMPAIGNS_MECHANICS_VIEW, PERMISSION_CAMPAIGNS_MECHANICS_EDIT } from '../../../../../../core/auth/access-points';
import { AuthStore } from '../../../../../../core/auth/auth.store';
import { Output, EventEmitter, ViewChild, Component, OnInit, Input } from '@angular/core';
import { CampaignMechanicService } from '../campaign-mechanics.service';

@Component({
    selector: 'campaign-mechanics-list',
    templateUrl: 'campaign-mechanics-list.component.html'
})
export class CampaignMechanicsListComponent implements OnInit {

    private _campaignId: string;
    @Input() set campaignId(v: string) {
        if (v) {
            this._campaignId = v;
            this.search();
        }
    }

    @ViewChild('alert') alert: GpAlertComponent;

    private rows: any[] = [];
    private parameters: any = {};
    private skip: number = 0;
    private limit: number = 20;
    private loading: boolean = false;

    @Output() edit: EventEmitter<any> = new EventEmitter();

    constructor(private mechanicService: CampaignMechanicService, private _as: AuthStore) { }

    ngOnInit() { }

    public search(reset: boolean = false) {
        if (this.canList()) {
            this.loading = true;
            if (reset) {
                this.skip = 0;
                this.limit = 20;
            }
            this.mechanicService.getAll(this._campaignId, this.parameters.description, this.skip, this.limit)
                .subscribe(mechanics => {
                    this.alert.clear();
                    this.rows = mechanics;
                    this.loading = false;
                }, err => {
                    const msg = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao buscar as mecânicas. Entre em contato com o suporte do sistema';
                    this.alert.showError(msg);
                    this.loading = false;
                });
        }
    }

    private editMechanic($event) {
        if ($event) {
            this.edit.emit($event.id);
        }
    }

    private disableMechanic($event) {
        if ($event) {
            this.loading = true;
            this.mechanicService.disableMechanic(this.campaignId, $event.id)
                .subscribe(response => {
                    this.search();
                }, err => {
                    const msg = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao excluir a mecânica. Entre em contato com o suporte do sistema';
                    this.alert.showError(msg);
                    this.loading = false;
                });
        }
    }

    private onPageChanged($event) {
        if ($event) {
            this.skip = $event.skip;
            this.limit = $event.limit;
            this.search();
        }
    }

    private enablePage(): boolean {
        return this._as.hasPermissionTo(PERMISSION_CAMPAIGNS_MECHANICS_ENABLE);
    }

    private canList(): boolean {
        return this._as.hasPermissionTo(PERMISSION_CAMPAIGNS_MECHANICS_LIST);
    }

    private canRemove(): boolean {
        return this._as.hasPermissionTo(PERMISSION_CAMPAIGNS_MECHANICS_REMOVE);
    }

    private canView(): boolean {
        return this._as.hasPermissionTo(PERMISSION_CAMPAIGNS_MECHANICS_VIEW);
    }

    private canEdit(): boolean {
        return this._as.hasPermissionTo(PERMISSION_CAMPAIGNS_MECHANICS_EDIT);
    }
}
