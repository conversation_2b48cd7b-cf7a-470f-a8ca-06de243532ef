<div *ngIf="enablePage()">
	<gp-card [first]="true" title="Pesquisa" *ngIf="canList()">
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<input type="text" class="form-control" name="description" placeholder="Descrição da Mecânica" [(ngModel)]="parameters.description" />
			</gp-form-col>
			<gp-form-col cols="12 4 4">
				<gp-spinner-button 
					text="Pesquisar" 
					buttonClass="bg-primary-dark" 
					icon="search" 
					(click)="search(true)"
					[loading]="loading" 
					loadingText="Pesquisando">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</gp-card>
	<gp-card title="Resultados Encontrados" *ngIf="canList()">
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-grid
					#grid
					name="grid"
					[rows]="rows"
					[columns]="[ 'Descrição da Mecânica', 'Data ou Dias para Expiração', 'Fator de Conversão' ]"
					[fields]="[ 'description', 'expirationDate', 'pointsConversionFactor' ]"
					[showActive]="false"
					[showPagination]="true"
					[showTotalPages]="false"
					[showEdit]="canEdit() || canView()"
					[showDelete]="canRemove()"
					[loading]="loading"
					(onEdit)="editMechanic($event)"
					(onDelete)="disableMechanic($event)"
					(onPageChanged)="onPageChanged($event)">
				</gp-grid>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-alert #alert></gp-alert>
			</gp-form-col>
		</gp-form-row>
	</gp-card>
</div>

<gp-card [first]="true" *ngIf="!enablePage() || !canList()">
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<gp-alert message="Você não tem permissão para acessar esta área" type="danger"></gp-alert>
		</gp-form-col>
	</gp-form-row>
</gp-card>