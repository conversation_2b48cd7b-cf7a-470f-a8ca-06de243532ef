import { Component, OnInit } from '@angular/core';

import { AuthStore } from '../../../../../core/auth/auth.store';
import { LayoutType } from '../../../../../core/auth/layout-type';
import { CampaignStore } from '../../campaign.store';

@Component({
	selector: 'campaign-points-view',
	templateUrl: 'campaign-points-view.component.html'
})
export class CampaignPointsViewComponent implements OnInit {
	campaignId: string;

	constructor(private _authStore: AuthStore, private _campaignStore: CampaignStore) { }
	ngOnInit() {
	}
	get showMenublock() {
		return this._campaignStore.isRewardsOrMarketplace && this._campaignStore.isFullCampaignOrUserWithGpBu;
	}

	canViewImportPoints() {
		return this._authStore.role.PERMISSION_CAMPAIGNS_IMPORT_POINTS_VIEW;
	}

	get canViewPointsEngine() {
		return this._authStore.role.PERMISSION_CAMPAIGNS_POINTS_ENGINE_LIST;
	}

	get canAccessPointsTransfer() {
		return this._authStore.role.PERMISSION_CAMPAIGNS_POINTS_TRANSFER;
	}

	get canViewMechanics() {
		return this._authStore.role.PERMISSION_CAMPAIGNS_MECHANICS_ENABLE;
	}

	get canViewDistribution() {
		return this._authStore.role.PERMISSION_CAMPAIGNS_POINTS_TRANSFER;
	}

	get canAccessManualTransaction() {
		return this._authStore.role.PERMISSION_CAMPAIGNS_MANUAL_TRANSACTION;
	}

	get canViewReportsConciliationsIntegration() {
		return this._authStore.role.PERMISSION_CAMPAIGNS_REPORTS_INTEGRATION_CONCILIATION_VIEW;
	}

	get canViewPointsEnginesMetadataParametrization() {
		return this._authStore.role.PERMISSION_CAMPAIGNS_POINTS_METADATA_PARAMETRIZATION;
	}

	get isApprovalFeature() {
		return this._authStore.loggedUser && this._authStore.loggedUser.layout === LayoutType.APPROVAL_FEATURE;
	}
}
