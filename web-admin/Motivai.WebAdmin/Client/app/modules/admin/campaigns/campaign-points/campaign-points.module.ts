import { CampaignImportPointsWarningApprovalModalComponent } from './campaign-import-points/campaign-import-points-edit/campaign-import-points-warning-approval/campaign-import-points-warning-approval-modal/campaign-import-points-warning-approval-modal';
import { PointsMetadataFieldsParametrizationComponent } from './points-metadata-fields-parametrization/points-metadata-fields-parametrization.component';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule, LOCALE_ID } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';

import { SharedModule } from '../../../../shared/shared.module';
import { CampaignPointsDistributionComponent } from './campaign-points-distribution/campaign-points-distribution.component';
import { CampaignPointsService } from './campaign-points.service';
import { CampaignPointsViewComponent } from './campaign-points-view/campaign-points-view.component';
import { CampaignMechanicsComponent } from './campaign-mechanics/campaign-mechanics.component';
import { CampaignImportPointsComponent } from './campaign-import-points/campaign-import-points.component';
import { CampaignManualTransactionViewComponent } from '../common/campaign-points/campaign-credit-debit-manual/campaign-manual-transaction-view.component';
import { CampaignTransferPointsComponent } from './campaign-transfer-points/campaign-transfer-points.component';
import { CampaignMechanicsListComponent } from './campaign-mechanics/campaign-mechanics-list/campaign-mechanics.list.component';
import { CampaignCommonPointsModule } from '../common/campaign-points/campaign-common-points.module';
import { CampaignCommonBatchesModule } from '../common/campaign-batches/campaign-common-batches.module';
import { CampaignMechanicsEditComponent } from './campaign-mechanics/campaign-mechanics-edit/campaign-mechanics-edit.component';
import { CampaignMechanicService } from './campaign-mechanics/campaign-mechanics.service';
import { CampaignImportPointsEditComponent } from './campaign-import-points/campaign-import-points-edit/campaign-import-points-edit.component';
import { PointsMetadataFieldsParametrizationModalComponent } from './points-metadata-fields-parametrization/points-metadata-fields-parametrization-modal/points-metadata-fields-parametrization-modal.component';
import { CampaignReportsIntegrationConciliationsComponent } from './campaign-reports-integration-conciliations/campaign-reports-integration-conciliations.component';
import { ReportsIntegrationConciliationsRescuesComponent } from './campaign-reports-integration-conciliations/features/reports-integraion-conciliations-rescues/reports-integration-conciliations-rescues.component';
import { ReportsIntegrationConciliationsCreditsComponent } from './campaign-reports-integration-conciliations/features/reports-integration-conciliations-credits/reports-integration-conciliations-credits.component';
import { ReportsIntegrationConciliationsRefundsComponent } from './campaign-reports-integration-conciliations/features/reports-integration-conciliations-refunds/reports-integration-conciliations-refunds.component';
import { ReportsIntegrationConciliationsService } from './campaign-reports-integration-conciliations/services/reports-integration-conciliations.service';
import { CampaignImportPointsWarningApprovalComponent } from './campaign-import-points/campaign-import-points-edit/campaign-import-points-warning-approval/campaign-import-points-warning-approval';

const COMPONENTS = [
	CampaignPointsDistributionComponent,
	PointsMetadataFieldsParametrizationComponent,
	PointsMetadataFieldsParametrizationModalComponent,
	CampaignPointsViewComponent,
	CampaignMechanicsComponent,
	CampaignImportPointsComponent,
	CampaignTransferPointsComponent,
	CampaignMechanicsListComponent,
	CampaignMechanicsEditComponent,
	CampaignImportPointsEditComponent,
	CampaignImportPointsWarningApprovalComponent,
	CampaignImportPointsWarningApprovalModalComponent,
	CampaignReportsIntegrationConciliationsComponent,
	ReportsIntegrationConciliationsCreditsComponent,
	ReportsIntegrationConciliationsRescuesComponent,
	ReportsIntegrationConciliationsRefundsComponent
];

@NgModule({
	imports: [
		CommonModule,
		SharedModule.forRoot(),
		FormsModule,
		RouterModule.forChild([
			{
				path: '', component: CampaignPointsViewComponent,
				children: [
					{ path: 'mecanicas', component: CampaignMechanicsComponent },
					{ path: 'importar-pontos', component: CampaignImportPointsComponent },
					{ path: 'distribuicao', component: CampaignPointsDistributionComponent },
					{ path: 'transferencias', component: CampaignTransferPointsComponent },
					{ path: 'motor-pontos', loadChildren: '../../points-engine/points-engine.module.ts#PointsEngineModule' },
					{ path: 'manual-credito-debito', component: CampaignManualTransactionViewComponent },
					{ path: 'campos-dinamicos', component: PointsMetadataFieldsParametrizationComponent },
					{ path: 'relatorios-conciliacao', component: CampaignReportsIntegrationConciliationsComponent,
						children: [
							{ path: 'retorno-debitos', component: ReportsIntegrationConciliationsRescuesComponent },
							{ path: 'creditos', component: ReportsIntegrationConciliationsCreditsComponent },
							{ path: 'estornos', component: ReportsIntegrationConciliationsRefundsComponent }
						]
				  	}
				]
			}
		]),
		CampaignCommonPointsModule,
		CampaignCommonBatchesModule
	],
	declarations: COMPONENTS,
	providers: [
		[{ provide: LOCALE_ID, useValue: 'pt-br' }],
		CampaignPointsService,
		CampaignMechanicService,
		ReportsIntegrationConciliationsService
	],
	exports: [
		RouterModule
	],
	schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class CampaignPointsModule { }
