<gp-alert #alert [overlay]="true"></gp-alert>
<spinner [show]="loading" [overlay]="true"></spinner>

<gp-card [first]="true" [last]="true" title="Importar Pontos" *ngIf="!_batchImportId && canImport">
	<gp-form-row>
		<gp-form-col cols="12 12 12 12">
			<gp-form-row>
				<gp-form-col cols="12 12 12">
					<gp-simple-input label="Descrição"
						tooltip="Esta informação será usada no envio da notificação de crédito para o participante">
						<input type="text" class="form-control" name="description"
							[(ngModel)]="parameters.description" />
					</gp-simple-input>
				</gp-form-col>
			</gp-form-row>
			<gp-form-row>
				<gp-form-col cols="12 4 4">
					<gp-simple-input label="Tipo de Identificação">
						<select class="form-control" name="identificationType"
							[(ngModel)]="parameters.identificationType">
							<option value="Document">CPF/CNPJ</option>
							<option value="Login">Login</option>
						</select>
					</gp-simple-input>
				</gp-form-col>
				<gp-form-col cols="12 4 4">
					<gp-simple-input label="Tipo da Moeda">
						<select class="form-control" name="coinType" [(ngModel)]="parameters.coinType">
							<option value="Currency">Real (R$)</option>
							<option value="Points">Pontos</option>
						</select>
					</gp-simple-input>
				</gp-form-col>
				<gp-form-col cols="12 4 4">
					<gp-simple-input label="Mecânica">
						<select class="form-control" name="mechanicId" [(ngModel)]="parameters.mechanicId">
							<option value="default">Mecânica Padrão</option>
							<option *ngFor="let m of mechanics" [value]="m.id">{{ m.description }}</option>
						</select>
					</gp-simple-input>
				</gp-form-col>
			</gp-form-row>
			<gp-form-row>
				<gp-form-col cols="12 4 4">
					<gp-simple-input label="A primeira linha é um cabeçalho?">
						<select class="form-control" name="headerOnFirstLine"
							[(ngModel)]="parameters.headerOnFirstLine">
							<option value="1">Sim - colunas serão identificadas automaticamente</option>
							<option value="0">Não - colunas precisam estar na ordem correta</option>
						</select>
					</gp-simple-input>
				</gp-form-col>
			</gp-form-row>
			<gp-form-row>
				<gp-form-col cols="12 4 4">
					<label>Contabilizar no Extrato:</label> <br>
					<gp-form-row>
						<gp-form-col cols="12 6 6">
							<label>Ignorar Pontos Bloqueados</label>
							<div>
								<gp-switch [(ngModel)]="parameters.skipBlockedTransactionsOnSummary"></gp-switch>
							</div>
						</gp-form-col>
						<gp-form-col cols="12 6 6">
							<label>Ignorar Pontos Cancelados</label>
							<div>
								<gp-switch [(ngModel)]="parameters.skipCanceledTransactionsOnSummary"></gp-switch>
							</div>
						</gp-form-col>
					</gp-form-row>
				</gp-form-col>
			</gp-form-row>
			<gp-form-row>
				<gp-form-col cols="12 12 12">
					<gp-fileupload
						name="files"
						[multiple]="true"
						[csv]="true"
						[path]="uploadPath"
						[data]="parameters"
						[disabled]="!parameters.identificationType || !parameters.coinType || !parameters.mechanicId"
						(oncomplete)="onItemComplete($event)" #gpFile>
					</gp-fileupload>
				</gp-form-col>
			</gp-form-row>
			<hr />
			<gp-form-row>
				<gp-form-col cols="12 12 12">
					<p>Instruções de uso:</p>
					<p>1. O formato do arquivo deve ser <u>[.csv]</u>, com as colunas separadas por [;], <u>com ou sem
							cabeçalho</u> de acordo com o campo <strong>A primeira linha é um cabeçalho</strong>.</p>
					<p>2. A identificação deve ser o <u>[documento] ou [Login]</u>:</p>
					<ul>
						<li>Caso a identificação seja <u>[documento]</u>, informar: 11 digitos para CPF; 14 digitos para
							CNPJ, sem formatação.</li>
						<li><strong>Se atente a formatar a coluna de documento como <u>texto</u>, para manter os zeros a
								esquerda.</strong></li>
					</ul>
					<p>3. O valor pode ser em <u>[Pontos] ou [R$].</u></p>
					<ul>
						<li>Caso a moeda seja <u>[R$]</u>, será utilizada a taxa de conversão da mecânica.</li>
						<li>Casas decimais: o valor precisa ser formatado como texto e com ponto <u>[.]</u> no lugar da
							virgula <u>[,]</u>.</li>
						<li>Exemplo: um crédito de <u>1.152,50</u> pontos precisar estar salvo como <u>1152.50</u>.</li>
					</ul>
				</gp-form-col>
				<gp-form-col cols="12 4 4">
					<p>4. Caso o arquivo seja importado sem cabeçalho, as colunas devem seguir a seguinte ordem:</p>
					<ul>
						<li>
							<strong>A: </strong>descrição do ponto
						</li>
						<li>
							<strong>B: </strong>identificação do participante
						</li>
						<li>
							<strong>C: </strong>valor
						</li>
					</ul>
				</gp-form-col>
				<gp-form-col cols="12 8 8">
					<img src="/assets/img/imports/importacao_ponto.png" style="width: 500px;"
						alt="Exemplo de arquivo de pontos" /> <br /><br />
					<a href="/assets/files/modelo_importacao_pontos.csv">Clique aqui para baixar um modelo de
						planilha</a>
				</gp-form-col>
				<gp-form-col cols="12 12 12">
					<p>5. Caso a primeira linha do arquivo seja um cabeçalho, automaticamente identificaremos as colunas
						através de sua respectiva descrição. Abaixo seguem as palavras reservadas:</p>
				</gp-form-col>
				<gp-form-col cols="12 4 4">
					<ul>
						<li>
							<strong>descrição do ponto: </strong>descricao
						</li>
						<li>
							<strong>identificação do participante: </strong>documento, login, usuario
						</li>
						<li>
							<strong>valor: </strong>valor, total, pontos
						</li>
						<li>
							<strong>identificação única na campanha: </strong>identificacao
						</li>
						<li>
							<strong>código da transação (permite atualização do status): </strong>codigo, id, transacao
						</li>
						<li>
							<strong>status do ponto: </strong>status
							<br>Valores permitidos:
							<ul>
								<li><strong>Liberado:</strong> liberado, ativo, l, a</li>
								<li><strong>Bloqueado:</strong> bloqueado, b</li>
								<li><strong>Cancelado:</strong> cancelado, c</li>
								<li><strong>Inválido:</strong> invalido, i</li>
							</ul>
						</li>
					</ul>
				</gp-form-col>
				<gp-form-col cols="12 8 8">
					<img src="/assets/img/imports/importacao_ponto_dinamica.png" style="width: 650px;"
						alt="Exemplo de arquivo de pontos com cabeçalho" /> <br /><br />
					<a href="/assets/files/modelo_importacao_pontos_dinamica.csv">Clique aqui para baixar um modelo de
						planilha</a>
				</gp-form-col>
				<gp-form-col cols="12 12 12">
					<p>Importante: as colunas não identificadas pelo sistema também serão salvas e apresentadas para o
						participante nos detalhes do ponto no extrato.</p>
				</gp-form-col>
			</gp-form-row>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<div *ngIf="_batchImportId && canView">
	<gp-card [first]="true" title="Detalhes da Importação">
		<gp-form-row>
			<gp-form-col cols="12 12">
				<label>Descrição do lote</label>
				<input type="text" class="form-control" name="description" disabled [value]="batchImport.description" />
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<label>Código do lote</label>
				<input type="text" name="batchNumber" class="form-control" disabled [value]="batchImport.batchNumber" />
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<label>Status</label>
				<input type="text" name="status" class="form-control" disabled [value]="translateStatus(batchImport.status)" />
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6">
				<label>Arquivo original</label>
				<input type="text" class="form-control" name="description" disabled [value]="batchImport.originalFileName" />
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<label>Data do lote</label>
				<input type="text" name="batchNumber" class="form-control" disabled
					[value]="batchImport.createDate | datetimezone" />
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<label>Tipo da identificação</label>
				<input type="text" name="identificationType" class="form-control" disabled
					[value]="batchImport.identificationType" />
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<label>Tipo da moeda</label>
				<input type="text" name="coinType" class="form-control" disabled
					[value]="batchImport.coinType" />
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<label>Mecânica</label>
				<input type="text" name="coinType" class="form-control" disabled
					[value]="mechanic.description" />
			</gp-form-col>

			<gp-form-col cols="12 6 6" *ngIf="campaignStore.isMakeYourCampaign">
				<div class="form-group">
					<label>Custo do lote <span style="color: red"
						tooltip="Custo calculado de acordo com os registros que apresentaram sucesso nas validações temporárias">(?)</span></label>
					<input type="text" name="coinType" class="form-control" disabled
						[value]="batchImport.batchCost | coin" />
				</div>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<hr/>
	<h4>Resumo da Importação</h4>

	<gp-card [last]="true" [noPaddingTop]="true" [noPaddingBottom]="true">
		<gp-form-row>
			<div class="grid-container grid-container--fit">
				<div class="grid-element">
					<div class="box-card">
						<p class="text-center">
							<i class="fa fa-list-ol fa-3x text-danger" aria-hidden="true"></i>
						</p>
						<p class="text-center text-bold">
							<span *ngIf="showApprovalLink">Quantidade de registros para aprovar</span>
							<span *ngIf="showExportPoints">Quantidade de registros aprovados</span>
						</p>
						<p class="text-center text-bold" *ngIf="!loading">
							<span *ngIf="showApprovalLink">{{ successImportedCount }}</span>
							<span *ngIf="showExportPoints">{{ successCompletedCount }}</span>
						</p>
					</div>
				</div>

				<div class="grid-element">
					<div class="box-card">
						<p class="text-center">
							<i class="fa fa-usd fa-3x text-danger" aria-hidden="true"></i>
						</p>
						<p class="text-center text-bold">
							Valor total do lote <span style="color: red" tooltip="Custo calculado de acordo com os registros que apresentaram sucesso nas validações temporárias">(?)</span>
						</p>
						<p class="text-center text-bold" *ngIf="!loading"> {{ batchCostWithCoinType }} </p>
					</div>
				</div>

				<div class="grid-element">
					<div class="box-card">
						<p class="text-center">
							<i class="fa fa-balance-scale fa-3x text-danger" aria-hidden="true"></i>
						</p>
						<p class="text-center text-bold">
							Ticket médio por registro aprovado <span style="color: red" tooltip="Custo médio por registro no arquivo que será aprovado e creditado">(?)</span>
						</p>
						<p class="text-center text-bold" *ngIf="!loading"> {{ mediumTicket }} </p>
					</div>
				</div>
			</div>
		</gp-form-row>
	</gp-card>

	<hr/>
	<h4>Processamento temporário</h4>

	<gp-form-row>
		<gp-form-col cols="12 6 6">
			<gp-card>
				<gp-form-row>
					<gp-form-col cols="12 12 12">
						<h3>Sucesso: <span class="text-success">{{ successImportedCount }}</span></h3>
						<hr />
						<h4 *ngIf="showProcessing" style="color: #CCC">PROCESSANDO...</h4>
						<h4 *ngIf="showApprovalLink" style="color: #CCC">APROVAÇÃO PENDENTE</h4>
						<h4 *ngIf="!showApprovalLink && !showProcessing" style="color: #CCC">-</h4>
					</gp-form-col>
				</gp-form-row>
			</gp-card>
		</gp-form-col>
		<gp-form-col cols="12 6 6">
			<gp-card>
				<gp-form-row>
					<gp-form-col cols="12 12 12">
						<h3>Erros: <span class="text-danger">{{ errorsImportedCount }}</span></h3>
						<hr />
						<h4 *ngIf="errorsImportedCount === 0 && canApprove" style="color: #CCC">-</h4>
						<a *ngIf="errorsImportedCount > 0 && canApprove" style="text-decoration: none;" (click)="downloadErrorsCsv()">
							<h4 class="text-danger">DETALHES</h4>
						</a>
					</gp-form-col>
				</gp-form-row>
			</gp-card>
		</gp-form-col>
	</gp-form-row>

	<hr />
	<h4>Resultado da aprovação</h4>

	<gp-form-row>
		<gp-form-col cols="12 6 6">
			<gp-card>
				<gp-form-row>
					<gp-form-col cols="12 12 12">
						<h3>Sucesso: <span class="text-success">{{ successCompletedCount }}</span></h3>
						<hr />
						<a *ngIf="showExportPoints" style="text-decoration: none;" (click)="downloadSuccessCsv()">
							<h4 class="text-success">{{ !loadingExport ? 'EXPORTAR PONTOS' : 'PROCESSANDO...' }}</h4>
						</a>
						<h4 *ngIf="showProcessing" style="color: #CCC">PROCESSANDO...</h4>
						<h4 *ngIf="!showProcessing && !showExportPoints" style="color: #CCC">-</h4>
					</gp-form-col>
				</gp-form-row>
			</gp-card>
		</gp-form-col>
		<gp-form-col cols="12 6 6">
			<gp-card>
				<gp-form-col cols="12 12 12">
					<h3>Erros: <span class="text-danger">{{ errorCompletedCount }}</span></h3>
					<hr />
					<h4 *ngIf="showProcessing" style="color: #CCC">PROCESSANDO...</h4>
					<h4 *ngIf="errorCompletedCount == 0 && !showProcessing" style="color: #CCC">-</h4>
					<a *ngIf="errorCompletedCount > 0 && !showProcessing" style="text-decoration: none;" (click)="downloadApprovalErrorsCsv()">
						<h4 class="text-danger">DETALHES</h4>
					</a>
				</gp-form-col>
			</gp-card>
		</gp-form-col>
	</gp-form-row>

	<gp-card title="Registros" *ngIf="canViewTemporary || canRemoveTemporary">
		<gp-form-row>
			<gp-form-col>
				<gp-simple-input label="Identificador">
					<input type="text" class="form-control" name="participantIdentification" [(ngModel)]="filters.participantIdentification" />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col [inputGroup]="false" additionalClasses="top-p27">
				<gp-spinner-button bootstrapClass="primary" text="Pesquisar" pull="right" icon="search"
					[loading]="loadingGrid" loadingText="Aguarde" (click)="applyFilters()">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-grid name="grid" [rows]="rows"
					[columns]="['Participante', 'Identificação', 'Total Pontos', 'Total Distribuível', 'Disponível para Uso', 'Cód. Transacional', 'Status do Ponto']"
					[fields]="['participantName', 'participantIdentification', 'pointsAmount', 'distributablePointsAmount', 'availablePointsAmount', 'clientTransactionCode', 'formattedStatus']"
					[showActive]="true" activeHeaderText="Processado" activeField="processed" activeText="Sim"
					inactiveText="Não" [showPagination]="true" [showTotalPages]="false" [showEdit]="false"
					[showDelete]="false" [loading]="loadingGrid" [pageSize]="limit"
					(onDelete)="deleteTemporaryPoints($event)" (onPageChanged)="onPageChanged($event)">
				</gp-grid>

			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card [last]="true" *ngIf="showActions">
		<gp-form-row>
			<gp-form-col cols="12" [inputGroup]="false">
				<gp-spinner-button type="submit" [pink]="true" text="Aprovar Lote" pull="right" icon="check" marginLeft="1em"
					[loading]="loadingApproval" loadingText="Processando" (click)="approval()" *ngIf="showApprovalLink">
				</gp-spinner-button>

				<gp-spinner-button type="button" bootstrapClass="danger" icon="close"
					text="Reprovar Lote" pull="right" [loading]="loadingRefuse" (click)="refuseBatch()" *ngIf="showRefuseButton">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-modal #withoutBalanceModal title="Identificamos um problema :(" width="800px" marginLeft="20%">
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<p><strong>Atenção:</strong> Você não possui saldo para completar esta operação. <br />
					Mas não se preocupe, sua importação ficará disponível para ser aprovada assim que novos créditos
					forem adquiridos. <br />
					<strong>Confira abaixo o resumo financeiro:</strong>
				</p>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<span>Seu saldo atual: <h4>R$ {{ withoutBalanceError.currentBalance | coin }}</h4></span> <br />
				<span>Valor total da importação: <h4>R$ {{ withoutBalanceError.amountNeeded | coin }}</h4></span>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-spinner-button [pink]="true" icon="send" [text]="withoutBalanceError.purchaseButtonText"
					[loading]="loadingRedirect" (click)="navigateToCreditsPurchase()">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</gp-modal>

</div>
