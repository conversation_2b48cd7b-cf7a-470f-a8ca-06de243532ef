import { Router } from '@angular/router';
import { Gp<PERSON>lertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import {
    PERMISSION_CAMPAIGNS_IMPORT_POINTS_IMPORT,
    PERMISSION_CAMPAIGNS_IMPORT_POINTS_VIEW,
    PERMISSION_CAMPAIGNS_IMPORT_POINTS_APPROVE,
    PERMISSION_CAMPAIGNS_IMPORT_POINTS_VIEWTEMPORARY,
    PERMISSION_CAMPAIGNS_IMPORT_POINTS_REMOVETEMPORARY
} from '../../../../../../core/auth/access-points';
import { Component, OnInit, Input, ViewChild } from '@angular/core';

import { BatchImport } from '../../../campaign-import';
import { CampaignImportService } from '../../../campaign-import.service';
import { AuthStore } from '../../../../../../core/auth/auth.store';
import { CampaignMechanicService } from '../../campaign-mechanics/campaign-mechanics.service';
import { CampaignStore } from '../../../campaign.store';
import { CoinPipe } from '../../../../../../shared/pipes/currency.pipe';
import { GpModalComponent } from '../../../../../../shared/components/gp-modal/gp-modal.component';
import { BatchStatus } from '../../../batch-status';

@Component({
    selector: 'campaign-import-points-edit',
    templateUrl: 'campaign-import-points-edit.component.html'
})
export class CampaignImportPointsEditComponent implements OnInit {
    @ViewChild('alert') alert: GpAlertComponent;
    @ViewChild('withoutBalanceModal') withoutBalanceModal: GpModalComponent;

    private _campaignId: string;
    @Input() set campaignId(v: string) {
        if (v) {
            this._campaignId = v;
            this.getManualMechanics();
        }
    }

    public _batchImportId: string;
    @Input() set batchImportId(v: string) {
        this._batchImportId = v;
        this.getBatchImport();
        this.loadTemporaryPoints();
    }


    // Page configuration
    private coinPipe: CoinPipe = new CoinPipe();
    public batchImport: BatchImport = new BatchImport();
    public parameters: any = {};
    public mechanics: any[] = [];
    public mechanic: any = {};
    public loading: boolean = false;
    public loadingApproval: boolean = false;
    public loadingExport: boolean = false;
    public loadingGrid: boolean = false;
    public loadingRefuse: boolean = false;
    public loadingRedirect: boolean = false;
    public errorsImportedCount: number = 0;
    public successImportedCount: number = 0;
    public successCompletedCount: number = 0;
    public errorCompletedCount: number = 0;
    public withoutBalanceError: any = {};

    // Grid configuration
    public rows: any[] = [];
    filters: any = {};
    public skip: number = 0;
    public limit: number = 100;

    // Upload file configuration
    private countItemsQueue: number = 0;
    private countItemsProcessed: number = 0;

    constructor(private _mechanicService: CampaignMechanicService, private _campaignImportService: CampaignImportService,
        private _authStore: AuthStore, public campaignStore: CampaignStore, private router: Router) { }

    get showActions() {
        return this.showApprovalLink || this.showRefuseButton;
    }

    get uploadPath(): string {
        return `api/campaigns/${this._campaignId}/batchimport/points/upload`;
    }

    get showRefuseButton(): boolean {
        return this.batchImport.status === BatchStatus.WAITING_APPROVAL;
    }

    get showApprovalLink(): boolean {
        return this.batchImport.status === BatchStatus.WAITING_APPROVAL && this.canApprove;
    }

    get showExportPoints(): boolean {
        return this.batchImport.status === BatchStatus.COMPLETED && this.canApprove;
    }

    get showProcessing() {
        return this.batchImport.status === BatchStatus.RUNNING || this.batchImport.status === BatchStatus.RUNNING_APPROVAL;
    }

    get enableDeleteButton() {
        return this.showApprovalLink && this.canRemoveTemporary;
    }

    get canImport() {
        return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_IMPORT_POINTS_IMPORT);
    }

    get canView() {
        return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_IMPORT_POINTS_VIEW);
    }

    get canApprove() {
        return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_IMPORT_POINTS_APPROVE);
    }

    get canViewTemporary() {
        return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_IMPORT_POINTS_VIEWTEMPORARY);
    }

    get canRemoveTemporary() {
        return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_IMPORT_POINTS_REMOVETEMPORARY);
    }

    get mediumTicket() {
        if (this.batchImport.batchCost != 0) {
            // ticket medio aprovado
            if (this.showExportPoints && this.successCompletedCount > 0) {
                return (this.batchImport.batchCost / this.successCompletedCount).toFixed(2);
            }
            // ticket medio para aprovação
            if (this.showApprovalLink && this.successImportedCount > 0) {
                return (this.batchImport.batchCost / this.successImportedCount).toFixed(2);
            }
        }
        return '0';
    }

    get batchCostWithCoinType() {
        if (this.batchImport.coinType == 'Points') {
            return `${this.coinPipe.transform(this.batchImport.batchCost)} pontos`;
        }
        return `R$ ${this.coinPipe.transform(this.batchImport.batchCost)}`;
    }

    ngOnInit() { }

    // On upload complete
    onItemComplete($event: any) {
        if ($event) {
            this.countItemsProcessed += Number(1);
            if (this.countItemsProcessed === this.countItemsQueue) {
                this.alert.showSuccess('Arquivos enviados para a fila de processamento.');
            }
        }
    }

    // Get batchimport detail information
    private getBatchImport() {
        if (this._batchImportId) {
            this.loading = true;
            this._campaignImportService.getBatchById(this._campaignId, this._batchImportId)
                .subscribe(batch => {
                    this.batchImport = batch;
                    this.batchImport.coinType = this.handleCoinType(batch.coinType);
                    this.batchImport.identificationType = this.handleIdentificationType(batch.identificationType);
                    this.countSuccess();
                    this.countError();
                    this.countSuccessCompleted();
                    this.countErrorCompleted();

                    this.getMechanicDetail(batch.mechanicId);

                    this.loading = false;
                }, err => {
                    this.alert.showError(err, true);
                    this.loading = false;
                });
        }
    }

    // Get manual mechanics
    private getManualMechanics() {
        if (this._campaignId) {
            this._mechanicService.getAll(this._campaignId)
                .subscribe(mechanics => {
                    mechanics.sort((a, b) => {
                        if (a.description < b.description)
                            return -1;
                        if (a.description > b.description)
                            return 1;
                        return 0;
                    });

                    this.mechanics = mechanics;
                }, err => {
                    console.log(err);
                });
        }
    }

    private getMechanicDetail(mechanicId: string): any {
        if (mechanicId) {
            this._mechanicService.getMechanicName(this._campaignId, mechanicId)
                .subscribe(mechanic => {
                    this.mechanic = { description: mechanic };
                }, err => {
                    console.log(err);
                });
        }
    }

    applyFilters() {
        this.skip = 0;
        this.loadTemporaryPoints();
    }

    onPageChanged($event: any) {
        if ($event) {
            this.skip = $event.skip;
            this.limit = $event.limit;
            this.loadTemporaryPoints();
        }
    }

    private loadTemporaryPoints() {
        if (this._batchImportId) {
            this.loadingGrid = true;
            this._campaignImportService.getTemporaryPointsByBatchId(this._campaignId, this._batchImportId, undefined, true,
                    this.filters.participantIdentification, this.skip, this.limit)
                .subscribe(temporaries => {
                    this.rows = temporaries;
                    if (this.rows) {
                        this.rows.forEach(r => r.formattedStatus = this.handleStatus(r.status));
                    }
                    this.loadingGrid = false;
                }, err => {
                    console.log(err);
                    this.loadingGrid = false;
                });
        }
    }

    private handleStatus(status: string) {
        if (!status) {
          return 'Indefinido';
        }

        switch (status) {
          case 'ACTIVE':
            return 'Disponível';
          case 'BLOCKED':
            return 'Bloqueado';
          case 'CANCELED':
            return 'Cancelado';
          case 'INVALID':
            return 'Inválido';
          case 'NONE':
          default:
            return 'Indefinido';
        }
      }

    private countSuccess() {
        this.successImportedCount = this.batchImport.totalSuccessImported || 0;
    }

    private countSuccessCompleted() {
        this.successCompletedCount = this.batchImport.totalSuccessCompleted || 0;
    }

    navigateToCreditsPurchase() {
        if (this.withoutBalanceError) {
            this.loadingRedirect = true;
            this.router.navigate(['/compra-de-credito'], { queryParams: { neededAmount: this.withoutBalanceError.balanceNeeded } });
            this.loadingRedirect = false;
        }
    }

    private countError() {
        this.errorsImportedCount = this.batchImport.totalErrorImported || 0;
    }

    private countErrorCompleted() {
        this.errorCompletedCount = this.batchImport.totalErrorCompleted || 0;
    }

    // Approve batch import
    approval() {
        if (!this._batchImportId || this.loadingApproval) {
            return;
        }

        this.alert.confirm(`Atenção: Ao prosseguir com esta ação, o lote será aprovado e os pontos serão creditados aos participantes.<br/><br/>Deseja realmente <strong>APROVAR</strong> o lote com total de <strong>${this.batchCostWithCoinType}</strong>?`)
            .then(result => {
                if (result && result.value) {
                    this.loadingApproval = true;
                    this._campaignImportService.approveTemporaryPoints(this._campaignId, this._batchImportId)
                        .subscribe(response => {
                            if (!response) this.alert.showError('Ocorreu um problema ao processar a requisição, tente novamente!');
                            if (!response.reserved) {
                                this.withoutBalanceError = response;
                                this.withoutBalanceError.balanceNeeded = Number(response.amountNeeded) - Number(response.currentBalance);
                                this.withoutBalanceError.purchaseButtonText = `Clique aqui para comprar R$ ${this.coinPipe.transform(Number(response.amountNeeded) - Number(response.currentBalance))}`;
                                this.loadingApproval = false;
                                return this.withoutBalanceModal.show();
                            }
                            if (!response.statusChanged || !response.queued) {
                                this.loadingApproval = false;
                                return this.alert.showError(response.error);
                            }

                            this.getBatchImport();
                            this.loadTemporaryPoints();
                            this.countError();
                            this.countSuccess();
                            this.countSuccessCompleted();
                            this.countErrorCompleted();

                            this.loadingApproval = false;
                        }, err => {
                            this.alert.showError(err, true);
                            this.loadingApproval = false;
                        });
                }
            });
    }

    public refuseBatch() {
        if (!this._batchImportId) {
            return;
        }
        this.alert.confirm('Atenção: Ao prosseguir com esta ação, o lote será reprovado e os pontos não poderão ser creditados aos participantes.<br/><br/>Deseja realmente <strong>REPROVAR</strong> o lote?')
            .then(result => {
                if (result && result.value) {
                    this.loadingRefuse = true;
                    this._campaignImportService.refuseBatch(this._campaignId, this._batchImportId).subscribe(
                        response => {
                            if (response) {
                                this.alert.showSuccess('Lote reprovado com sucesso');
                                this.getBatchImport();
                            } else {
                                this.alert.showError('Ocorreu um erro ao reprovar o lote, tente novamente!');
                            }

                            this.loadingRefuse = false;
                        },
                        err => {
                            this.alert.showError(err, true);
                            this.loadingRefuse = false;
                        }
                    );
                }
            });
    }

    downloadSuccessCsv() {
        window.open(this.batchImport.successFileUrl, '_blank');
    }

    downloadErrorsCsv() {
        window.open(this.batchImport.errorFileUrl, '_blank');
    }

    downloadApprovalErrorsCsv() {
        window.open(this.batchImport.approvalErrorFileUrl, '_blank');
    }

    private deleteTemporaryPoints($event) {
        if ($event.id) {
            this.loadingGrid = true;
            this._campaignImportService.removeTemporaryPoint(this._campaignId, this._batchImportId, $event.id)
                .subscribe(response => {
                    this.loadTemporaryPoints();
                }, err => {
                    console.log(err);
                    this.loadingGrid = false;
                });
        }
    }

    // Helper to translate batch status
    translateStatus(status: string): string {
        switch (status) {
            case BatchStatus.WAITING:
                return 'Aguardando';
            case BatchStatus.RUNNING:
                return 'Processando';
            case BatchStatus.WAITING_APPROVAL:
                return 'Aguardando Aprovação';
            case BatchStatus.RUNNING_APPROVAL:
                return 'Processando Aprovação';
            case BatchStatus.COMPLETED:
                return 'Finalizado';
            case BatchStatus.REFUSED:
                return 'Reprovado';
            default:
                return 'Status indefinido';
        }
    }

    // Helper to translate coin type
    private handleCoinType(coin: string): string {
        switch (coin) {
            case 'Currency':
                return 'Real (R$)';
            case 'Points':
                return 'Pontos';
            default:
                return 'Não identificado';
        }
    }

    // Helper to translate identification type
    private handleIdentificationType(identification: string): string {
        switch (identification) {
            case 'Document':
                return 'Documento';
            case 'Login':
                return 'Login';
            default:
                return 'Não identificado';
        }
    }
}
