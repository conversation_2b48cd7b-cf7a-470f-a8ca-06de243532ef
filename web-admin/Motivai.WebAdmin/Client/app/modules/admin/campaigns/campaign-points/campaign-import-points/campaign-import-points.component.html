<user-balance-resume *ngIf="cs.isMakeYourCampaign"></user-balance-resume>
<tabset #tabs class="bg-white p0 tab-no-border" [justified]="true" *ngIf="!approvalLayout">
	<tab (select)="refreshGrid()">
		<ng-template tabHeading>Lotes</ng-template>
		<campaign-import-batchlist 
			#listComponent
			[campaignId]="campaignId" 
			processType="Points" 
			(edit)="editBatchImport($event)" #batchList>
		</campaign-import-batchlist>
	</tab>
	<tab *ngIf="canImportOrViewOrApprove()">
		<ng-template tabHeading>Importação</ng-template>
		<campaign-import-points-edit [campaignId]="campaignId" [batchImportId]="batchImportId"></campaign-import-points-edit>
	</tab>
</tabset>
<campaign-import-points-edit *ngIf="approvalLayout" [campaignId]="campaignId" [batchImportId]="batchImportId"></campaign-import-points-edit>
