<gp-card *ngIf="showMenublock && !isApprovalFeature"[noPaddingTop]="true" [noPaddingBottom]="true">
	<div class="row">
		<accordion [closeOthers]="true">
			<accordion-group panelClass="b0 mb-sm panel-default no-shadow" [isOpen]="true">
				<div accordion-heading>
					<label>
						<em class="fa fa-navicon"></em> Features de Pontos</label>
				</div>
				<div class="row menublock text-center">
					<gp-menublock-item size="xs" icon="gear" color="text-warning" text="Mecânicas" routerLinkActive="active"
						routerLink="mecanicas" *ngIf="canViewMechanics">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="code" color="text-warning" text="Campos Dinâmicos" routerLinkActive="active"
						routerLink="campos-dinamicos" *ngIf="canViewPointsEnginesMetadataParametrization" >
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="cloud-upload" color="text-warning" text="Importação" routerLinkActive="active"
						routerLink="importar-pontos" *ngIf="canViewImportPoints">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="gears" color="text-warning" text="Motor de Pontos" routerLinkActive="active"
						routerLink="motor-pontos" *ngIf="canViewPointsEngine">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="share-alt" color="text-warning" text="Distribuição" routerLinkActive="active"
						routerLink="distribuicao" *ngIf="canViewDistribution">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="exchange" color="text-warning" text="Transferências" routerLinkActive="active"
						routerLink="transferencias" *ngIf="canAccessPointsTransfer">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="retweet" color="text-warning" text="Crédito/Débito Manual" routerLinkActive="active"
						routerLink="manual-credito-debito" *ngIf="canAccessManualTransaction">
					</gp-menublock-item>
				</div>
			</accordion-group>
		</accordion>
	</div>
</gp-card>

<router-outlet></router-outlet>
