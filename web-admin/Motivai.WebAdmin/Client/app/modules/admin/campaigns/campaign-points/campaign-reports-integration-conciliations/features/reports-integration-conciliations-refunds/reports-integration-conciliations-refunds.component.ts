import { DecimalPipe } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { AuthStore } from '../../../../../../../core/auth/auth.store';
import { CampaignStore } from '../../../../campaign.store';
import { ReportsIntegrationConciliationsService } from '../../services/reports-integration-conciliations.service';
import { FormatHelper } from '../../../../../../../shared/formatters/format-helper';

@Component({
  selector: 'reports-integration-conciliations-refunds',
  templateUrl: './reports-integration-conciliations-refunds.component.html'
})
export class ReportsIntegrationConciliationsRefundsComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;

  tomorrow: Date = new Date(); // Bloquear DateTimePicker
  decimalPipe: DecimalPipe = new DecimalPipe('pt-BR');

  filtering: boolean = false;
  exporting: boolean = false;
  campaignId: string;
  params: any = {};

  totalRefunds: number = 0;
  limit: number = 20;
  refunds: any = [];

  constructor(
    private authStore: AuthStore,
    private campaignStore: CampaignStore,
    private conciliationsService: ReportsIntegrationConciliationsService
  ) {}

  ngOnInit() {
    this.tomorrow.setDate(new Date().getDate() + 1);
  }

  get canExport() {
    return this.authStore.role.PERMISSION_CAMPAIGNS_REPORTS_INTEGRATION_CONCILIATION_REFUNDS_EXPORT;
  }

  verifyAndFilter() {
    if (!this.params.from || !this.params.to) {
      return this.gpAlert.showWarning('Selecione o período de consulta.');
    }

    this.countTotalRefunds();
  }

  applyFilter() {
    if (!this.params.from || !this.params.to) {
      return this.gpAlert.showWarning('Selecione o período de consulta.');
    }
    
    this.filtering = true;
    this.conciliationsService
      .findRefunds(this.campaignStore.id, this.params)
      .subscribe(
        refunds => {
          if (refunds) {
            refunds.forEach(rescue => {
              rescue.formattedCreatedAt = FormatHelper.formatDate(
                rescue.createdAt
              );
              rescue.formattedPointsAmount = this.decimalPipe.transform(
                rescue.pointsAmount
              );
            });
          }
          this.refunds = refunds;
          this.filtering = false;
        },
        err => {
          this.filtering = false;
          this.gpAlert.showError(err);
        }
      );
  }

  countTotalRefunds() {
    this.filtering = true;
    this.conciliationsService
      .countTotalRefunds(this.campaignStore.id, this.params)
      .subscribe(
        totalRefunds => {
          if (totalRefunds && totalRefunds > 0) {
            this.totalRefunds = totalRefunds;
            this.applyFilter();
          } else {
            this.refunds = [];
            this.totalRefunds = 0;
          }
          this.filtering = false;
        },
        err => {
          this.filtering = false;
          this.gpAlert.showError(err);
        }
      );
  }

  exporter() {
    if (!this.refunds || this.refunds.length <= 0) {
      return;
    }

    if (!this.params.from || !this.params.to) {
      this.gpAlert.showWarning('Selecione o período de consulta.');
      return;
    }

    this.exporting = true;
    this.conciliationsService
      .exportRefundsCsv(this.campaignStore.id, this.params)
      .subscribe(
        response => {
          this.exporting = false;
          window.open(response, '_blank');
        },
        err => {
          this.exporting = false;
          this.gpAlert.showError(err);
        }
      );
  }

  changePage(event: any) {
    if (event) {
      this.params.skip = event.skip;
      this.params.limit = event.limit;
      this.applyFilter();
    }
  }
}
