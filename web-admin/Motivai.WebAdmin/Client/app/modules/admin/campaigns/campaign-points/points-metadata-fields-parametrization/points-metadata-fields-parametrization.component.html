<gp-card title="Parametrização">
    <gp-form-row>
        <gp-form-col cols="12 4">
            <label>Padrão de visualização dos campos dinâmicos:</label>
        </gp-form-col>
    </gp-form-row>
    <gp-form-row>
        <gp-form-col cols="12 2">
            <gp-input-radio name="displayOrientation" radioValue="HORIZONTAL" label="Horizontal"
                [(ngModel)]="metadataFieldsParametrization.displayOrientation"></gp-input-radio>
        </gp-form-col>
        <gp-form-col cols="12 2">
            <gp-input-radio name="displayOrientation" radioValue="VERTICAL" marginTop="26px" label="Vertical"
                [(ngModel)]="metadataFieldsParametrization.displayOrientation"></gp-input-radio>
        </gp-form-col>
    </gp-form-row>
</gp-card>
<spinner [show]="loading" [overlay]="true"></spinner>
<gp-alert #alert [overlay]="true"></gp-alert>
<gp-card>
    <!-- <gp-form-row>
        <gp-form-col cols="12">
            <gp-spinner-button type="button" [pink]="true" icon="plus" text="Cadastrar campo" pull="left"
                (click)="openMetadataFieldsModal()"></gp-spinner-button>
        </gp-form-col>
    </gp-form-row> -->
    <gp-form-row>
        <gp-form-col cols="12">
            <gp-grid id="parametrizationList" [rows]="metadataFieldsParametrization.headersDetails" [columns]="['Descrição', 'Valor','Posição']"
                [fields]="['description', 'property', 'position']" [showActive]="true" (onEdit)="editField($event)" (onDelete)="inactiveField($event)"></gp-grid>
        </gp-form-col>
    </gp-form-row>
</gp-card>
<gp-card>
    <gp-form-row>
        <gp-form-col cols="12" [inputGroup]="false">
            <gp-spinner-button type="button" [pink]="true" icon="send" text="Salvar" pull="right"
                (click)="saveMetadataParametrization()"></gp-spinner-button>
        </gp-form-col>
    </gp-form-row>
</gp-card>
<app-points-metadata-fields-parametrization-modal (onInsert)="linkMetadataFields($event)"
    #metadataFieldsParametrizationModal></app-points-metadata-fields-parametrization-modal>