<!-- <spinner [show]="loading" [overlay]="true"></spinner> -->
<gp-alert #gpAlert [overlay]="true"></gp-alert>

<gp-card title="Filtros">
  <gp-form-row>
    <gp-form-col cols="12 6">
      <gp-simple-input label="Nome do arquivo">
        <input type="text" class="form-control" name="fileName" [(ngModel)]="params.fileName" />
      </gp-simple-input>
    </gp-form-col>
    <gp-form-col cols="12 6">
      <gp-simple-input label="CNPJ">
        <gp-input-mask name="cnpj" mask="00000000000000" [(ngModel)]="params.cnpj"></gp-input-mask>
      </gp-simple-input>
    </gp-form-col>
  </gp-form-row>

  <gp-form-row>
    <gp-datepicker cols="12 6 6" [disableSince]="tomorrow" [required]="false" label="De" name="from" [(ngModel)]="params.from">
    </gp-datepicker>
    <gp-datepicker cols="12 6 6" [disableSince]="tomorrow" [required]="false" label="Até" name="to" [(ngModel)]="params.to">
    </gp-datepicker>
  </gp-form-row>

  <gp-form-row>
    <gp-form-col cols="12" [inputGroup]="false">
      <gp-spinner-button pull="left" text="Pesquisar" [loading]="filtering" loadingText="Aguarde"
        bootstrapClass="primary" icon="search" (click)="applyFilter()" marginTop="24px">
      </gp-spinner-button>

      <gp-spinner-button pull="right" text="Exportar" [loading]="exporting" loadingText="Aguarde..." [pink]="true"
        icon="file-excel-o" (click)="exporter()" marginTop="24px" *ngIf="canExport">
      </gp-spinner-button>
    </gp-form-col>
  </gp-form-row>
</gp-card>

<gp-card title="Registros Encontrados">
  <gp-form-col cols="12 12 12">
    <gp-grid name="creditsGrid"
      [rows]="credits"
      [columns]="[
        'Nome do arquivo',
        'Status do lote',
        'Qtd de linhas',
        'Data do arquivo',
        'Número da Transação',
        'CNPJ',
        'Nome',
        'Valor R$',
        'Creditado'
      ]"
      [fields]="[
        'fileName',
        'status',
        'numberOfLines',
        'formattedCreatedAt',
        'clientTransactionId',
        'participantDocument',
        'participantName',
        'formattedCurrencyAmount',
        'formattedCredited'
      ]"
      (onPageChanged)="changePage($event)"
      [totalRecords]="totalCredits"
      [showTotalPages]="false" 
      [showPagination]="true"
      [pageSize]="limit" 
      [showActive]="false"
      [showDelete]="false"
      [showEdit]="false"
      [loading]="filtering">
    </gp-grid>
  </gp-form-col>
</gp-card>