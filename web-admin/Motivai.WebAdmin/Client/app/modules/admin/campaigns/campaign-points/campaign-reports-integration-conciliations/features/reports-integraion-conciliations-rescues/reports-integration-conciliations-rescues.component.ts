import { DecimalPipe } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { ReportsIntegrationConciliationsService } from '../../services/reports-integration-conciliations.service';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { AuthStore } from '../../../../../../../core/auth/auth.store';
import { CampaignStore } from '../../../../campaign.store';
import { FormatHelper } from '../../../../../../../shared/formatters/format-helper';

@Component({
  selector: 'reports-integration-conciliations-rescues',
  templateUrl: './reports-integration-conciliations-rescues.component.html'
})
export class ReportsIntegrationConciliationsRescuesComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;

  tomorrow: Date = new Date(); // Bloquear DateTimePicker
  decimalPipe: DecimalPipe = new DecimalPipe('pt-BR');

  filtering: boolean = false;
  exporting: boolean = false;
  campaignId: string;
  params: any = {};

  totalRescues: number = 0;
  limit: number = 20;
  rescues: any = [];

  constructor(
    private authStore: AuthStore,
    private campaignStore: CampaignStore,
    private conciliationsService: ReportsIntegrationConciliationsService
  ) {}

  ngOnInit() {
    this.tomorrow.setDate(new Date().getDate() + 1);
  }

  get canExport() {
    return this.authStore.role
      .PERMISSION_CAMPAIGNS_REPORTS_INTEGRATION_CONCILIATION_RESCUES_EXPORT;
  }

  verifyAndFilter() {
    if (!this.params.from || !this.params.to) {
      return this.gpAlert.showWarning('Selecione o período de consulta.');
    }

    this.countTotalRescues();
  }

  applyFilter() {
    if (!this.params.from || !this.params.to) {
      return this.gpAlert.showWarning('Selecione o período de consulta.');
    }
    
    this.filtering = true;
    this.conciliationsService
      .findRescues(this.campaignStore.id, this.params)
      .subscribe(
        rescues => {
          if (rescues) {
            rescues.forEach(rescue => {
              rescue.formattedDebitDate = FormatHelper.formatDate(
                rescue.debitDate
              );
              // rescue.formattedCnpj = FormatHelper.formatDocument(rescue.cnpj);
              rescue.formattedTotalAmount = this.decimalPipe.transform(
                rescue.totalAmount
              );
            });
          }
          this.rescues = rescues;
          this.filtering = false;
        },
        err => {
          this.filtering = false;
          this.gpAlert.showError(err);
        }
      );
  }

  countTotalRescues() {
    this.filtering = true;
    this.conciliationsService
      .countTotalRescues(this.campaignStore.id, this.params)
      .subscribe(
        totalRescues => {
          if (totalRescues && totalRescues > 0) {
            this.totalRescues = totalRescues;
            this.applyFilter();
          } else {
            this.rescues = [];
            this.totalRescues = 0;
          }
          this.filtering = false;
        },
        err => {
          this.filtering = false;
          this.gpAlert.showError(err);
        }
      );
  }

  exporter() {
    if (!this.rescues || this.rescues.length <= 0) {
      return;
    }

    if (!this.params.from || !this.params.to) {
      this.gpAlert.showWarning('Selecione o período de consulta.');
      return;
    }

    this.exporting = true;
    this.conciliationsService
      .exportRescuesCsv(this.campaignStore.id, this.params)
      .subscribe(
        response => {
          this.exporting = false;
          window.open(response, '_blank');
        },
        err => {
          this.exporting = false;
          this.gpAlert.showError(err);
        }
      );
  }

  changePage(event: any) {
    if (event) {
      this.params.skip = event.skip;
      this.params.limit = event.limit;
      this.applyFilter();
    }
  }
}
