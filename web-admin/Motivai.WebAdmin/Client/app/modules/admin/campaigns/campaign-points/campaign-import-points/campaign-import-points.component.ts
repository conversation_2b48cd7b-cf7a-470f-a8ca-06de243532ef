import { GpCampaignImportBatchlistComponent } from '../../common/campaign-batches/campaign-import-batchlist.component';
import { TabsetComponent } from 'ng2-bootstrap/tabs';
import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { Subscription } from 'rxjs/Subscription';
import { RxjsHelpers } from '../../../../../shared/helpers/rxjs-helpers';
import { CampaignService } from '../../campaign.service';
import { AuthStore } from '../../../../../core/auth/auth.store';
import { PERMISSION_CAMPAIGNS_IMPORT_POINTS_IMPORT, PERMISSION_CAMPAIGNS_IMPORT_POINTS_VIEW, PERMISSION_CAMPAIGNS_IMPORT_POINTS_APPROVE } from '../../../../../core/auth/access-points';
import { CampaignStore } from '../../campaign.store';

@Component({
    selector: 'campaign-import-points',
    templateUrl: 'campaign-import-points.component.html'
})

export class CampaignImportPointsComponent implements OnInit {
    @ViewChild('tabs') tabs: TabsetComponent;
    @ViewChild('listComponent') listComponent: GpCampaignImportBatchlistComponent;
    
    campaignId: string;
    approvalLayout: boolean;
    private batchImportId: string;

	private _campaign$: Subscription;

    constructor(private _campaignService: CampaignService, private route: ActivatedRoute, private _as: AuthStore, public cs: CampaignStore, private authStore: AuthStore) { }

    ngOnInit() {
        if (this.authStore.loggedUser.hasApprovalFeature) {
            this.batchImportId = this.authStore.loggedUser.approvalFeature.contextId;
            this.approvalLayout = true;
        }
		this._campaign$ = this.cs.asObservable
        .subscribe(id => this.campaignId = id);
        
    }

	ngOnDestroy() {
		RxjsHelpers.unsubscribe(this._campaign$);
	}


    private editBatchImport($event) {
        if ($event) {
            this.batchImportId = $event.id;
            this.tabs.tabs[1].active = true;
        }
    }

    private refreshGrid() {
        this.batchImportId = '';
        this.listComponent.getBatches();
    }

    get canImport() {
        return this._as.hasPermissionTo(PERMISSION_CAMPAIGNS_IMPORT_POINTS_IMPORT);
    }

    get canView() {
        return this._as.hasPermissionTo(PERMISSION_CAMPAIGNS_IMPORT_POINTS_VIEW);
    }

    get canApprove() {
        return this._as.hasPermissionTo(PERMISSION_CAMPAIGNS_IMPORT_POINTS_APPROVE);
    }

    private canImportOrViewOrApprove(): boolean {
        return this.canImport || this.canView || this.canApprove;
    }
}
