import { CampaignStore } from '../../campaign.store';
import { PERMISSION_CAMPAIGNS_MECHANICS_VIEW, PERMISSION_CAMPAIGNS_MECHANICS_CREATE, PERMISSION_CAMPAIGNS_MECHANICS_EDIT } from '../../../../../core/auth/access-points';
import { CampaignMechanicsEditComponent } from './campaign-mechanics-edit/campaign-mechanics-edit.component';
import { CampaignMechanicsListComponent } from './campaign-mechanics-list/campaign-mechanics.list.component';
import { TabsetComponent } from 'ng2-bootstrap/tabs';
import { ViewChild } from '@angular/core';
import { AuthStore } from '../../../../../core/auth/auth.store';
import { ActivatedRoute } from '@angular/router';
import { Component, OnInit } from '@angular/core';
import { RxjsHelpers } from '../../../../../shared/helpers/rxjs-helpers';
import { Subscription } from 'rxjs/Subscription';

@Component({
    selector: 'campaign-mechanics',
    templateUrl: 'campaign-mechanics.component.html'
})
export class CampaignMechanicsComponent implements OnInit {
    @ViewChild('tabs') tabs: TabsetComponent;
    @ViewChild('listComponent') listComponent: CampaignMechanicsListComponent;
    @ViewChild('editComponent') editComponent: CampaignMechanicsEditComponent;

    private campaignId: string;
    private mechanicId: string;

    private _campaign$: Subscription;

    constructor(private route: ActivatedRoute, private _as: AuthStore, private cs: CampaignStore) { }

    ngOnInit() {
		this._campaign$ = this.cs.asObservable
        .subscribe(id => this.campaignId = id);

    }

	ngOnDestroy() {
		RxjsHelpers.unsubscribe(this._campaign$);
	}

    private mechanicEdit($event) {
        if ($event) {
            this.mechanicId = $event;
            this.tabs.tabs[1].active = true;
        }
    }

    private refreshGrid() {
        this.mechanicId = '';
        this.editComponent.reset();
        this.listComponent.search();
    }

    private canViewOrCreateOrEdit() {
        return this._as.hasPermissionTo(PERMISSION_CAMPAIGNS_MECHANICS_VIEW) ||
            this._as.hasPermissionTo(PERMISSION_CAMPAIGNS_MECHANICS_CREATE) ||
            this._as.hasPermissionTo(PERMISSION_CAMPAIGNS_MECHANICS_EDIT)
    }
}
