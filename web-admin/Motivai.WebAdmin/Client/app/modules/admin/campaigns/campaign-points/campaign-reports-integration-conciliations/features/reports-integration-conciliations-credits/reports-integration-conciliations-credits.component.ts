import { Component, OnInit, ViewChild } from '@angular/core';
import { DecimalPipe } from '@angular/common';
import { ReportsIntegrationConciliationsService } from '../../services/reports-integration-conciliations.service';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { AuthStore } from '../../../../../../../core/auth/auth.store';
import { CampaignStore } from '../../../../campaign.store';
import { FormatHelper } from '../../../../../../../shared/formatters/format-helper';

@Component({
  selector: 'reports-integration-conciliations-credits',
  templateUrl: './reports-integration-conciliations-credits.component.html'
})
export class ReportsIntegrationConciliationsCreditsComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;

  tomorrow: Date = new Date(); // Bloquear DateTimePicker
  decimalPipe: DecimalPipe = new DecimalPipe('pt-BR');

  filtering: boolean = false;
  exporting: boolean = false;
  campaignId: string;
  params: any = {};

  totalCredits: number = 0;
  limit: number = 20;
  credits: any = [];

  constructor(
    private authStore: AuthStore,
    private campaignStore: CampaignStore,
    private conciliationsService: ReportsIntegrationConciliationsService
  ) {}

  ngOnInit() {
    this.tomorrow.setDate(new Date().getDate() + 1);
  }

  get canExport() {
    return this.authStore.role
      .PERMISSION_CAMPAIGNS_REPORTS_INTEGRATION_CONCILIATION_CREDITS_EXPORT;
  }

  verifyAndFilter() {
    if (!this.params.from || !this.params.to) {
      return this.gpAlert.showWarning('Selecione o período de consulta.');
    }

    this.countTotalCredits();
  }

  countTotalCredits() {
    if (!this.params.from || !this.params.to) {
      return this.gpAlert.showWarning('Selecione o período de consulta.');
    }
    
    this.filtering = true;
    this.conciliationsService
      .countTotalRescues(this.campaignStore.id, this.params)
      .subscribe(
        totalCredits => {
          if (totalCredits && totalCredits > 0) {
            this.totalCredits = totalCredits;
            this.applyFilter();
          } else {
            this.credits = [];
            this.totalCredits = 0;
          }
          this.filtering = false;
        },
        err => {
          this.filtering = false;
          this.gpAlert.showError(err);
        }
      );
  }

  applyFilter() {
    this.filtering = true;
    this.conciliationsService
      .findCredits(this.campaignStore.id, this.params)
      .subscribe(
        credits => {
          if (credits) {
            credits.forEach(credit => {
              credit.formattedCreatedAt = FormatHelper.formatDate(
                credit.createdAt
              );
              // credit.formattedParticpantDocument = FormatHelper.formatDocument(credit.participantDocunent);
              credit.formattedCurrencyAmount = this.decimalPipe.transform(
                credit.currencyAmount
              );
              credit.formattedCredited =
                credit.credited == true ? 'Sim' : 'Não';
            });
          }
          this.credits = credits;
          this.filtering = false;
        },
        err => {
          this.filtering = false;
          this.gpAlert.showError(err);
        }
      );
  }

  exporter() {
    if (!this.params.from || !this.params.to) {
      this.gpAlert.showWarning('Selecione o período de consulta.');
      return;
    }

    this.exporting = true;
    this.conciliationsService
      .exportCreditsCsv(this.campaignStore.id, this.params)
      .subscribe(
        response => {
          this.exporting = false;
          window.open(response, '_blank');
        },
        err => {
          this.exporting = false;
          this.gpAlert.showError(err);
        }
      );
  }

  changePage(event: any) {
    if (event) {
      this.params.skip = event.skip;
      this.params.limit = event.limit;
      this.applyFilter();
    }
  }
}
