import { Observable } from 'rxjs/Observable';
import { Injectable } from '@angular/core';
import { ApiService } from '../../../../../core/api/api.service';

@Injectable()
export class CampaignMechanicService {
    constructor(private _api: ApiService) {}

    getMechanicById(campaignId: string, mechanicId: string): Observable<any> {
        return this._api.get(`/api/campaigns/${campaignId}/mechanics/${mechanicId}`);
    }

    getMechanicName(campaignId: string, mechanicId: string): Observable<string> {
        return this._api.get(`/api/campaigns/${campaignId}/mechanics/${mechanicId}/name`, 20000);
    }

    getAll(campaignId: string, description?: string, skip?: number, limit?: number): Observable<any[]> {
        let params: any = {};
        if (description) params.description = description;
        if (skip != null) params.skip = skip;
        if (limit != null) params.limit = limit;

        return this._api.get(`/api/campaigns/${campaignId}/mechanics`, params);
    }

    save(campaignId: string, mechanic: any): Observable<any> {
        if (mechanic.id)
            return this._api.put(`/api/campaigns/${campaignId}/mechanics/${mechanic.id}`, mechanic);
        else
            return this._api.post(`/api/campaigns/${campaignId}/mechanics`, mechanic);
    }

    disableMechanic(campaignId: string, mechanicId): Observable<boolean> {
        return this._api.delete(`/api/campaigns/${campaignId}/mechanics/${mechanicId}`);
    }
}
