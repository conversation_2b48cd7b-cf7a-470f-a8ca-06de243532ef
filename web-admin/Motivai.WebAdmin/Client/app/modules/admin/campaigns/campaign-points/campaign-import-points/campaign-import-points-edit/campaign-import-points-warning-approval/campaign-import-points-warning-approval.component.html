<campaign-import-points-warning-approval-modal #approvalModal (approved)="pointApproved($event)"></campaign-import-points-warning-approval-modal>
<hr/>
<gp-card>
    <gp-form-row>
        <gp-form-col cols="12 12 12">
            <h3 class="text-warning">Importações com alerta do sistema anti-fraude  <span class="fa fa-exclamation-triangle"></span></h3>
                <gp-grid name="grid" [rows]="rows"
					[columns]="[
                        'Participante', 
                        'Identificação', 
                        'Total Pontos',
                        'Alerta',
                        'Excedente'
                    ]"
					[fields]="[
                        'participantName', 
                        'participantIdentification', 
                        'pointsAmount', 
                        'formattedWarning',
                        'antiFraudAnalysis.detail.formattedObtainedOverdraft'

                    ]"
                    [showActive]="true" activeField="antiFraudApproved" activeText="Aprovado" inactiveText="Nao Aprovado"
					[showPagination]="false" [showTotalPages]="false" 
                    [showEdit]="false" [showDelete]="false" [showTooltip]="true"
                    [showCustom]="true" customIcon="search-plus" customTooltip="Detalhes"
                    [loading]="loadingGrid" [pageSize]="limit" (onPageChanged)="onPageChanged($event)" (onCustom)="onDetails($event)"
                    >
				</gp-grid>
        </gp-form-col>
    </gp-form-row>
    <gp-form-row>
        <gp-form-col cols="12">
                <gp-spinner-button type="button" icon="cloud-download" bootstrapClass="info"
                    text="Download" pull="right" (click)="download()">
                </gp-spinner-button>
        </gp-form-col>
    </gp-form-row>
</gp-card>