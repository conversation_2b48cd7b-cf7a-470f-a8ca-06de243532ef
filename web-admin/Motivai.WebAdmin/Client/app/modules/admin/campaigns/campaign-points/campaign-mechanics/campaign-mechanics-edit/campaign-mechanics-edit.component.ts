import { AuthStore } from '../../../../../../core/auth/auth.store';
import { NgForm } from '@angular/forms';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignMechanicService } from '../campaign-mechanics.service';
import { CampaignService } from '../../../campaign.service';
import { Component, OnInit, Input, ViewChild } from '@angular/core';
import * as moment from 'moment';

@Component({
    selector: 'campaign-mechanics-edit',
    templateUrl: 'campaign-mechanics-edit.component.html'
})
export class CampaignMechanicsEditComponent implements OnInit {

    _campaignId: string;
    @Input() set campaignId(v: string) {
        if (v) {
            this._campaignId = v;
            this.getRankings();
            this.getCampaignGeneralInfo();
        }
    }

    private _mechanicId: string;
    @Input() set mechanicId(v: string) {
        if (v) {
            this._mechanicId = v;
            this.getMechanicDetail();
        }
    }

    @ViewChild('alert') alert: GpAlertComponent;
    @ViewChild('mechanicForm') mechanicForm: NgForm;

    mechanic: any = {
        distributableConfig: {},
        unlockParametrization: {}
    };
    campaign: any = {};
    rankings: any[] = [];
    loading: boolean = false;
    loadingCampaign: boolean = false;
    loadingMechanic: boolean = false;
    loadingRankings: boolean = false;

    product: any = {};
    allowedSkus: Array<any> = [];

    constructor(private _campaignService: CampaignService,
        private _mechanicService: CampaignMechanicService,
        private _as: AuthStore) { }

    ngOnInit() { }

    get canCreateOrEdit() {
      return this.canCreate || this.canEdit;
    }

    get canView(): boolean {
        return this._as.role.PERMISSION_CAMPAIGNS_MECHANICS_VIEW;
    }

    get canEdit(): boolean {
        return this._as.role.PERMISSION_CAMPAIGNS_MECHANICS_EDIT;
    }

    get canCreate(): boolean {
        return this._as.role.PERMISSION_CAMPAIGNS_MECHANICS_CREATE;
    }

    get usablePercent() {
        return 100 - (this.mechanic.distributableConfig.percentageDistributable || 0);
    }

    get isEditing() {
      return this._mechanicId && this._mechanicId.length && this.mechanic;
    }

    public reset() {
        this.mechanic = {
            distributableConfig: {},
            unlockParametrization: {}
        };
        this.allowedSkus = [];

        Object.keys(this.mechanicForm.controls).forEach(key => {
            this.mechanicForm.controls[key].reset();
        });

        this.getCampaignGeneralInfo();
    }

    private getCampaignGeneralInfo() {
        if (this._campaignId) {
            this.loadingCampaign = true;
            this._campaignService.getGeneralInfo(this._campaignId)
                .subscribe(info => {
                    this.campaign = info;
                    this.loadingCampaign = false;
                }, err => {
                    const msg = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao salvar os dados da campanha. Entre em contato com o suporte do sistema!';
                    this.alert.showError(msg);
                    this.loadingCampaign = false;
                });
        }
    }

    private getMechanicDetail() {
        if (this._mechanicId) {
            this.loadingMechanic = true;
            this._mechanicService.getMechanicById(this._campaignId, this._mechanicId)
                .subscribe(mechanic => {
                    this.handleMechanic(mechanic);
                    this.loadingMechanic = false;
                }, err => {
                    const msg = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao buscar a mecânica. Entre em contato com o suporte do sistema!';
                    this.alert.showError(msg);
                    this.loadingMechanic = false;
                });
        }
    }

    private handleMechanic(mechanic: any) {
        if (Number(mechanic.pointsConversionFactor) !== Number(this.campaign.pointsConversionFactor)) {
            mechanic.changeConversionFactor = true;
        } else {
            mechanic.changeConversionFactor = false;
        }
        if (!mechanic.distributableConfig) {
            mechanic.distributableConfig = {};
        }
        if (mechanic.redemptionRules && mechanic.redemptionRules.allowedSkus && mechanic.redemptionRules.allowedSkus.length) {
            this.allowedSkus = mechanic.redemptionRules.allowedSkus;
        } else {
            this.allowedSkus = [];
        }
        if (!mechanic.unlockParametrization) {
            mechanic.unlockParametrization = {};
        }
        this.mechanic = mechanic || {
            distributableConfig: {}
        };
    }

    mechanicSubmit() {
        this.loading = true;
        this.alert.clear();
        this.mechanic.campaignId = this.campaignId;

        if (!this.mechanic.changeConversionFactor)
            this.mechanic.pointsConversionFactor = this.campaign.pointsConversionFactor;

        if (!this.mechanic.changeExpirationPoints) {
            if (this.campaign.pointsExpirationType == 'FixedDate') {
                this.mechanic.expirationDate = moment(this.campaign.expirationDate, 'DD-MM-YYYY').toDate();
            } else if (this.campaign.pointsExpirationType == 'Period') {
                this.mechanic.expirationPeriod = this.campaign.expirationPeriod;
            }
            this.mechanic.pointsExpirationType = this.campaign.pointsExpirationType;
        }

        if (this.allowedSkus && this.allowedSkus.length) {
          this.mechanic.redemptionRules = {
            allowedSkus: this.allowedSkus
          };
        }

        this._mechanicService.save(this._campaignId, this.mechanic)
            .subscribe(mechanic => {
                this.mechanic.id = mechanic.id;
                this.alert.showSuccess('Dados da mecânica salvos com sucesso');
                this.loading = false;
            }, err => {
                const msg = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao salvar a mecânica. Entre em contato com o suporte do sistema!';
                this.alert.showError(msg);
                this.loading = false;
            });
    }

    private getRankings() {
        if (this._campaignId) {
            this.loadingRankings = true;
            this._campaignService.getRankings(this._campaignId)
                .subscribe(rankings => {
                    this.rankings = rankings;
                    this.loadingRankings = false;
                }, err => {
                    const msg = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao buscar os rankings';
                    this.alert.showError(msg);
                    this.loadingRankings = false;
                });
        }

    }
    changeConvertionFactor() {
        if (!this.mechanic.changeConversionFactor)
            this.mechanic.pointsConversionFactor = '';
    }

    changeExpirationDate() {
        if (!this.mechanic.changeExpirationDate)
            this.mechanic.expirationDate = '';
    }

    onProductSearchError(err) {
      this.alert.handleAndShowError(err);
    }

    addProduct() {
      if (!this.product || !this.product.skuId) {
        this.alert.showError('Selecione o produto para adicionar.');
        return;
      }
      this.product.active = true;
      this.allowedSkus.push(this.product);
      this.product = {};
    }

    removeProduct(product) {
      if (!product || !product.skuId) return;
      product.active = false;
    }
}
