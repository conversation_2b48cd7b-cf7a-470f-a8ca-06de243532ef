
<!-- <gp-modal #modal width="500px"> -->
<gp-modal #modal titleCustomClass="text-warning" title="Detalhes">
    <gp-alert #gpAlert [overlay]="true"></gp-alert>
    <form #ngForm="ngForm" novalidate>
        <gp-form-row>
            <gp-form-col cols="12 6 6">
                <div>
                    <p><span class="text-bold ">Participante: </span> {{ temporaryPoint.participantName }}</p>
                </div>
            </gp-form-col>
            <gp-form-col cols="12 6 6">
                <div>
                    <p><span class="text-bold ">Identificação: </span> {{ temporaryPoint.participantIdentification }}</p>
                </div>
            </gp-form-col>
        </gp-form-row>
        <gp-form-row>
            <gp-form-col cols="12 6 6">
                <p><span class="text-bold ">Valor: </span> {{ temporaryPoint.formattedAmount }}</p>
            </gp-form-col>
            <gp-form-col cols="12 6 6">
                <p><span class="text-bold ">Data: </span> {{ temporaryPoint.formattedDate }}</p>
            </gp-form-col>
        </gp-form-row>
        <gp-form-row>
            <gp-form-col cols="12">
                <p><span class="text-bold ">Descrição: </span> {{ temporaryPoint.formattedWarning }}</p>
            </gp-form-col>
        </gp-form-row>

        <hr/>

        <gp-form-row *ngIf="warningByParticipant">
            <gp-form-col cols="12">
                <gp-form-row>
                    <gp-form-col cols="12">
                        <p><span class="text-bold ">Média participante durante análise: </span> {{ temporaryPoint.antiFraudAnalysis.detail.formattedAverage }}</p>
                    </gp-form-col>
                </gp-form-row>
                <gp-form-row>
                    <gp-form-col cols="12">
                        <p><span class="text-bold ">Desvio aceitável durante análise: </span> {{ temporaryPoint.antiFraudAnalysis.detail.formattedOverdraft }}</p>
                    </gp-form-col>
                </gp-form-row>
                <gp-form-row>
                    <gp-form-col cols="12">
                        <p><span class="text-bold ">Desvio obtido para a importação: </span> {{ temporaryPoint.antiFraudAnalysis.detail.formattedObtainedOverdraft }}</p>
                    </gp-form-col>
                </gp-form-row>
            </gp-form-col>
        </gp-form-row>

        <gp-form-row *ngIf="warningByCampaign">
            <gp-form-col cols="12">
                <gp-form-row>
                    <gp-form-col cols="12">
                        <p><span class="text-bold ">Média da campanha durante análise: </span> {{ temporaryPoint.antiFraudAnalysis.detail.formattedAverage }}</p>
                    </gp-form-col>
                </gp-form-row>
                <gp-form-row>
                    <gp-form-col cols="12">
                        <p><span class="text-bold ">Desvio aceitável na campanha durante análise: </span> {{ temporaryPoint.antiFraudAnalysis.detail.formattedOverdraft }}</p>
                    </gp-form-col>
                </gp-form-row>
                <gp-form-row>
                    <gp-form-col cols="12">
                        <p><span class="text-bold ">Desvio obtido para a importação: </span> {{ temporaryPoint.antiFraudAnalysis.detail.formattedObtainedOverdraft }}</p>
                    </gp-form-col>
                </gp-form-row>
            </gp-form-col>
        </gp-form-row>

        <gp-form-row>
            <gp-form-col cols="12">
                <gp-spinner-button type="button" [pink]="true" text="Aprovar" pull="right" icon="thumbs-o-up"
					[loading]="loadingApproval" loadingText="Processando" (click)="approval()" *ngIf="canApprove">
				</gp-spinner-button>
                <gp-spinner-button type="button" bootstrapClass="danger" marginRight="5px" text="Reprovar" pull="right" icon="thumbs-o-down"
					[loading]="loadingRefusal" loadingText="Processando" (click)="refuse()" *ngIf="canApprove">
				</gp-spinner-button>
                <gp-spinner-button type="button" icon="arrow-left" bootstrapClass="default" marginRight="5px"
                    text="Voltar" pull="right" (click)="close()">
                </gp-spinner-button>
            </gp-form-col>
        </gp-form-row>
    </form>


</gp-modal>