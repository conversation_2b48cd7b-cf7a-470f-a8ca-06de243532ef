import { Component, OnInit } from '@angular/core';
import { AuthStore } from '../../../../../core/auth/auth.store';
import { CampaignStore } from '../../campaign.store';

@Component({
  selector: 'campaign-reports-integration-conciliations',
  templateUrl: './campaign-reports-integration-conciliations.component.html'
})
export class CampaignReportsIntegrationConciliationsComponent
  implements OnInit {
  campaignId: string = '';

  constructor(
    private authStore: AuthStore,
    private campaignStore: CampaignStore
  ) {}

  ngOnInit() {
    this.campaignId = this.campaignStore.id;
  }


  get canView() {
    return this.canViewCredits || this.canViewRefunds || this.canViewRescues;
  }
  
  get canViewRescues() {
    return this.authStore.role.PERMISSION_CAMPAIGNS_REPORTS_INTEGRATION_CONCILIATION_RESCUES_VIEW;
  }

  get canViewCredits() {
    return this.authStore.role.PERMISSION_CAMPAIGNS_REPORTS_INTEGRATION_CONCILIATION_CREDITS_VIEW;
  }

  get canViewRefunds() {
    return this.authStore.role.PERMISSION_CAMPAIGNS_REPORTS_INTEGRATION_CONCILIATION_REFUNDS_VIEW;
  }
}
