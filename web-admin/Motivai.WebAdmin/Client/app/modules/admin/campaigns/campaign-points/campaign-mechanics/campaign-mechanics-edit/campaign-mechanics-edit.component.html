<form #mechanicForm="ngForm" (ngSubmit)="mechanicSubmit()" novalidate *ngIf="canView">
<gp-card [first]="true" title="Dados da Mecânica">
	<gp-alert [overlay]="true" #alert></gp-alert>
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<gp-simple-input label="Descrição da Mecânica" [required]="true" errorMessage="Descrição da mecânica é obrigatório">
				<input type="text" class="form-control" required name="description" [(ngModel)]="mechanic.description" />
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 6 6">
			<gp-simple-input label="Ranking do Participante">
				<select class="form-control" name="ranking" [(ngModel)]="mechanic.rankingId" placeholder="Selecione">
					<option *ngFor="let r of rankings" [value]="r.id">{{ r.name }}</option>
				</select>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 6 6">
			<gp-simple-input label="Prioridade para Débitos">
				<gp-input-mask
					id="boost"
					name="boost"
					[onlyInteger]="true"
					[(ngModel)]="mechanic.boost">
				</gp-input-mask>
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 6 6">
			<label>Habilitar apenas valores inteiros <span class="text-danger" tooltip="Caso seja marcado como 'Sim', será permitido apenas a importação de pontos usando números inteiros">(?)</span></label>
			<div>
				<label class="switch">
					<input type="checkbox" checked="checked" name="allowOnlyInteger" [(ngModel)]="mechanic.allowOnlyInteger" /><span></span>
				</label>
			</div>
		</gp-form-col>
	</gp-form-row>
	<spinner [overlay]="true" [show]="loadingCampaign || loadingMechanic"></spinner>
</gp-card>

<gp-card title="Data de Expiração">
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<p>ATENÇÃO: Caso nenhuma data de expiração de pontos seja informada, será utilizado o padrão da campanha</p>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<div *ngIf="campaign.pointsExpirationType == 'FixedDate'">
			<gp-form-col cols="12 4 4">
				<strong>Tipo de expiração</strong>
				<p>Data fixa</p>
			</gp-form-col>
			<gp-form-col cols="12 4 4">
				<gp-simple-input label="Data de expiração da campanha">
					<input type="text" class="form-control" name="campaignExpirationDate" disabled
						[ngModel]="(campaign.expirationDate | date:'dd/MM/yyyy')" />
				</gp-simple-input>
			</gp-form-col>
		</div>

		<div *ngIf="campaign.pointsExpirationType == 'Period'">
				<gp-form-col cols="12 4 4">
						<strong>Tipo de expiração</strong>
						<p>Prazo por dias</p>
				</gp-form-col>
				<gp-form-col cols="12 4 4">
					<gp-simple-input label="Dias até a expiração">
						<input type="text" class="form-control" name="campaignExpirationDate" disabled [(ngModel)]="campaign.expirationPeriod" />
					</gp-simple-input>
				</gp-form-col>
			</div>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 4 4">
			<label>Alterar data de expiração dos pontos</label>
			<div>
				<label class="switch">
					<input type="checkbox" checked="checked" name="changeExpirationPoints" [(ngModel)]="mechanic.changeExpirationPoints" /><span></span>
				</label>
			</div>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row *ngIf="mechanic.changeExpirationPoints">
		<gp-form-col cols="12 4 4">
			<label>Tipo de prazo</label>
			<select class="form-control input-sm" name="pointsExpirationType" [(ngModel)]="mechanic.pointsExpirationType">
				<option value="FixedDate">Data Fixa</option>
				<option value="Period">Prazo</option>
			</select>
		</gp-form-col>

		<div *ngIf="mechanic.pointsExpirationType == 'FixedDate'">
			<gp-datepicker cols="12 4 4 4" label="Data de expiração da mecânica" name="expirationDate" [(ngModel)]="mechanic.expirationDate"></gp-datepicker>
		</div>

		<gp-form-col cols="12 4 4" *ngIf="mechanic.pointsExpirationType == 'Period'">
			<gp-simple-input label="Dias até a expiração">
				<input type="text" class="form-control"  placeholder="365" name="expirationPeriod" [(ngModel)]="mechanic.expirationPeriod" />
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card title="Parametrização de Desbloqueio">
	<gp-form-row>
		<gp-form-col cols="12 12 6">
			<label>Creditar pontos bloqueado</label>
			<div>
				<gp-switch name="creditPointsAsBlocked" [(ngModel)]="mechanic.unlockParametrization.creditPointsAsBlocked"></gp-switch>
			</div>
		</gp-form-col>

		<gp-form-col cols="12 12 6">
			<gp-simple-input label="Período para desbloqueio automático (dias)">
				<input type="text" class="form-control"  placeholder="365" name="automaticUnlockPeriodInDays." 
					[(ngModel)]="mechanic.unlockParametrization.automaticUnlockPeriodInDays" />
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card title="Fator de Conversão">
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<p>ATENÇÃO: Caso nenhum fator de conversão de pontos seja informado, será utilizado o padrão da campanha</p>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Fator de conversão da campanha">
				<input type="text" class="form-control" name="campaignConversionFactor" disabled [(ngModel)]="campaign.pointsConversionFactor" />
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 6 6">
			<label>Alterar fator de conversão</label>
			<div>
				<label class="switch">
					<input type="checkbox" checked="checked" name="changeConversionFactor" (change)="changeConvertionFactor()" [(ngModel)]="mechanic.changeConversionFactor" /><span></span>
				</label>
			</div>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Fator de conversão da mecânica">
				<gp-input-mask
					id="conversionFactor"
					name="conversionFactor"
					[onlyDecimal]="true"
					[disabled]="!mechanic.changeConversionFactor"
					[decimais]="5"
					[(ngModel)]="mechanic.pointsConversionFactor">
				</gp-input-mask>
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card title="Distribuição de Pontos">
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<p>Permite a distribuição dos pontos creditados nesta mecânica para outros participantes da campanha</p>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 3 3">
			<label>Habilitar distribuição de pontos</label>
			<div>
				<label class="switch">
					<input type="checkbox" checked="checked" name="distributable" [(ngModel)]="mechanic.distributable" /><span></span>
				</label>
			</div>
		</gp-form-col>
		<gp-form-col cols="12 6 6">
			<label>Permitir distribuição do percentual disponível para uso <span style="color: red" tooltip="Permite a distribuição do próprio saldo, além da parte distribuível">(?)</span></label>
			<div>
				<label class="switch">
					<input type="checkbox" name="canDistributeAvailableBalance" [(ngModel)]="mechanic.distributableConfig.canDistributeAvailableBalance" /><span></span>
				</label>
			</div>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 3 3">
			<gp-simple-input label="Percentual distribuível" tooltip="Percentual que poderá/deverá ser distribuído a outros participantes">
				<gp-input-mask
					name="percentageDistributable"
					[onlyDecimal]="true"
					[disabled]="!mechanic.distributable"
					[decimais]="2"
					[(ngModel)]="mechanic.distributableConfig.percentageDistributable">
				</gp-input-mask>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 3 3">
			<gp-simple-input label="Percentual disponível para uso" tooltip="Percentual que poderá ser utilizado pelo participante para compras no catálogo">
				<gp-input-mask
					name="usablePercent"
					[onlyDecimal]="true"
					[disabled]="true"
					[decimais]="2"
					[(ngModel)]="usablePercent">
				</gp-input-mask>
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card title="Regras de Resgate">
	<gp-form-row>
		<gp-form-col cols="12 6">
			<label>Habilitar regras de resgates</label>
			<div>
				<gp-switch name="enableRedemptionRules" [(ngModel)]="mechanic.enableRedemptionRules"></gp-switch>
			</div>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 12">
			<gp-simple-input label="Produto" [required]="false">
				<campaign-partner-sku-selector name="product" [campaignId]="_campaignId" [(ngModel)]="product" (error)="onProductSearchError($event)"></campaign-partner-sku-selector>
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row *ngIf="!isEditing">
		<gp-form-col cols="12" [inputGroup]="false">
			<gp-spinner-button type="button" [pink]="true" text="Adicionar" pull="right" icon="plus" (click)="addProduct()">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 12" [inputGroup]="true">
			<gp-grid name="allowedSkus" [rows]="allowedSkus" [showEdit]="false" [showDelete]="true" [showActive]="false"
				[columns]="['Parceiro', 'Produto', 'Código SKU']"
				[fields]="['partnerName', 'skuName', 'skuCode']"
				(onDelete)="removeProduct($event)">
			</gp-grid>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card title="Regras de processamento">
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<p>Aciona regras intermediárias antes de processar e creditar o ponto</p>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 3 3">
			<label>Habilitar regras de processamento</label>
			<div>
				<label class="switch">
					<input type="checkbox" checked="checked" name="enableTriggerRules" [(ngModel)]="mechanic.enableTriggerRules" /><span></span>
				</label>
			</div>
		</gp-form-col>
		<gp-form-col cols="12 3 3">
			<gp-simple-input label="Identificação da regra">
				<input type="text" class="form-control" name="triggerRuleIdentification" [(ngModel)]="mechanic.triggerRuleIdentification" />
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 3 3">
			<label>Habilitar regras de pós-processamento</label>
			<div>
				<label class="switch">
					<input type="checkbox" checked="checked" name="enablePostProcessingTriggerRules" [(ngModel)]="mechanic.enablePostProcessingTriggerRules" /><span></span>
				</label>
			</div>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card>
	<div class="row">
		<div class="col-md-12">
			<gp-spinner-button
				type="submit"
				text="Salvar Mecânica"
				size="md" pull="right" [pink]="true" icon="send"
				loadingText="Processando" [loading]="loading" [disabled]="!mechanicForm.valid"
				*ngIf="canCreateOrEdit">
			</gp-spinner-button>

			<gp-spinner-button text="Novo" bootstrapClass="default" icon="plus" size="md" pull="right" marginRight="10px"
				[disabled]="loading" (click)="reset()" [showSpinner]="false" *ngIf="canCreate">
			</gp-spinner-button>
		</div>
	</div>
</gp-card>
</form>
