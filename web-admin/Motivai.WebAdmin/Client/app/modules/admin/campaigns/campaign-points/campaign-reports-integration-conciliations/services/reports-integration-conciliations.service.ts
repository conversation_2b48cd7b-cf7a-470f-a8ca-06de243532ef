import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from '../../../../../../core/api/api.service';
import moment = require('moment');

@Injectable()
export class ReportsIntegrationConciliationsService {
  constructor(private api: ApiService) {}

  //#region Rescues

  countTotalRescues(campaignId: string, params: any): Observable<any> {
    return this.api.get(
      `/api/reports/campaigns/${campaignId}/conciliations/rescues/total`,
      this.extractParameters(params)
    );
  }

  findRescues(campaignId: string, parameters: any): Observable<any[]> {
    return this.api.get(
      `/api/reports/campaigns/${campaignId}/conciliations/rescues`,
      this.extractParameters(parameters)
    );
  }

  exportRescuesCsv(campaignId: string, parameters: any): Observable<any> {
    return this.api.get(
      `/api/reports/campaigns/${campaignId}/conciliations/rescues/export/csv`,
      this.extractParameters(parameters)
    );
  }

  //#endregion

  //#region Credits

  countTotalCredits(campaignId: string, params: any): Observable<any> {
    return this.api.get(
      `/api/reports/campaigns/${campaignId}/conciliations/credits/total`,
      this.extractParameters(params)
    );
  }

  findCredits(campaignId: string, parameters: any): Observable<any[]> {
    return this.api.get(
      `/api/reports/campaigns/${campaignId}/conciliations/credits`,
      this.extractParameters(parameters)
    );
  }

  exportCreditsCsv(campaignId: string, parameters: any): Observable<any> {
    return this.api.get(
      `/api/reports/campaigns/${campaignId}/conciliations/credits/export/csv`,
      this.extractParameters(parameters)
    );
  }

  //#endregion

  //#region Refunds

  countTotalRefunds(campaignId: string, params: any): Observable<any> {
    return this.api.get(
      `/api/reports/campaigns/${campaignId}/conciliations/refunds/total`,
      this.extractParameters(params)
    );
  }

  findRefunds(campaignId: string, parameters: any): Observable<any[]> {
    return this.api.get(
      `/api/reports/campaigns/${campaignId}/conciliations/refunds`,
      this.extractParameters(parameters)
    );
  }

  exportRefundsCsv(campaignId: string, parameters: any): Observable<any> {
    return this.api.get(
      `/api/reports/campaigns/${campaignId}/conciliations/refunds/export/csv`,
      this.extractParameters(parameters)
    );
  }

  //#endregion

  private extractParameters(parameters: any): any {
    const params: any = {};

    if (parameters.fileName) {
      params.fileName = parameters.fileName;
    }

    if (parameters.cnpj) {
      params.cnpj = parameters.cnpj;
    }

    if (parameters.from) {
      params.from = moment(parameters.from).format('YYYY-MM-DD');
    }

    if (parameters.to) {
      params.to = moment(parameters.to).format('YYYY-MM-DD');
    }

    if (parameters.skip) {
      params.skip = parameters.skip;
    }

    if (parameters.limit) {
      params.limit = parameters.limit;
    }

    return params;
  }
}
