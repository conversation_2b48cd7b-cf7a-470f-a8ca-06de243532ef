<form>
	<gp-card title="Configuração da Distribuição de Pontos">
		<spinner [overlay]="true" [show]="loading"></spinner>
		<gp-form-row>
			<gp-form-col>
				<gp-simple-input name="distributionModel" label="Modelo de Distribuição" [required]="true">
					<select name="distributionModel" class="form-control" [(ngModel)]="config.distributionModel" required>
						<option value="CnpjToAll">CNPJ > Todos</option>
						<option value="CnpjToCnpj">CNPJ > CNPJ</option>
						<option value="CnpjToCpf">CNPJ > CPF</option>
						<option value="CpfToCpf">CPF > CPF</option>
						<option value="AllToAll">Todos > Todos</option>
					</select>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col>
				<label>Ativo</label>
				<div>
					<gp-switch name="active" [(ngModel)]="config.active"></gp-switch>
				</div>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 6">
				<label>Desativar cadastro de novos participantes</label>
				<div>
					<gp-switch name="disableCreateNewParticipant" [(ngModel)]="config.disableCreateNewParticipant">
					</gp-switch>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 6">
				<label>Habilitar distribuição de pontos apenas para os participantes filhos</label>
				<div>
					<gp-switch name="allowDistributeToOnlyParticipantInParentHierarchy"
						[(ngModel)]="config.allowDistributeToOnlyParticipantInParentHierarchy">
					</gp-switch>
				</div>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col>
				<label for="groups">Grupo de Participante</label>
				<gp-select name="groups" placeholder="Selecione o grupo de participantes que poderão distribuir" [multiple]="true"
					[items]="participantsGroups" [(ngModel)]="selectedGroups">
				</gp-select>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card [last]="true">
		<div class="row">
			<div class="col-md-12">
				<gp-spinner-button type="submit" text="Salvar" pull="right" size="md" [pink]="true" icon="send"
					[loading]="sending" loadingText="Processando" (click)="save()">
				</gp-spinner-button>
			</div>
		</div>
		<div class="row">
			<div class="col-md-12">
				<gp-alert #gpAlert></gp-alert>
			</div>
		</div>
	</gp-card>
</form>
