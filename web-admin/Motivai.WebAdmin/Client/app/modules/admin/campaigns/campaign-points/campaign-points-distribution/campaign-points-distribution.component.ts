import { Component, OnInit, ViewChild } from '@angular/core';

import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignStore } from '../../campaign.store';
import { CampaignPointsService } from '../campaign-points.service';
import { ParticipantsGroupService } from '../../campaign-participants/campaign-participants-groups/participants-groups.service';
import { Item } from '../../../../../shared/models/item';

@Component({
  selector: 'campaign-points-distribution',
  templateUrl: 'campaign-points-distribution.component.html'
})
export class CampaignPointsDistributionComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;

  config: any = {};
  participantsGroups: Array<Item> = [];
  selectedGroups: Array<Item> = [];

  loading: boolean = false;
  sending: boolean = false;

  constructor(private _campaignStore: CampaignStore, private _pointsService: CampaignPointsService,
    private groupsService: ParticipantsGroupService) {}

  ngOnInit() {
    this.loadParametrization();
    this.loadGroups();
  }

  private handleError(err: any) {
    this.gpAlert.showError(err ? (err.message ? err.message : err) : 'Ocorreu um erro durante o processamento, por favor, tente novamente.');
    this.loading = false;
    this.sending = false;
  }

  loadParametrization() {
    if (!this._campaignStore.id)
      return;
    this.loading = true;
    this._pointsService.getDistributionParametrization(this._campaignStore.id)
      .subscribe(
        config => {
          this.loading = false;
          if (config) {
            this.config = config;
            if (this.config.groups && this.config.groups.length) {
              // this.selectedGroups = this.participantsGroups.filter(pg => this.config.groups.findIndex(pg) >= 0);
              this.selectedGroups = this.config.groups;
            }
          } else {
            this.config = {};
          }
        },
        err => this.handleError(err)
      );
  }

  private loadGroups() {
    if (!this._campaignStore.id)
      return;
    this.groupsService.search(this._campaignStore.id)
      .subscribe(
        groups => {
          if (groups) {
            this.participantsGroups = groups.map(g => Item.of(g.id, g.name));
          }
        },
        err => this.handleError(err)
      );
  }

  save() {
    this.gpAlert.clear();
    this.sending = true;
    if (this.selectedGroups && this.selectedGroups.length) {
      this.config.groups = this.selectedGroups.map(g => g.id);
    } else {
      this.config.groups = [];
    }
    this._pointsService.saveDistributionParametrization(this._campaignStore.id, this.config)
      .subscribe(
        result => {
          this.sending = false;
          if (result) {
            this.gpAlert.showSuccess('Configuração salva com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível salvar as configurações, por favor, tente novamente.');
          }
        },
        err => this.handleError(err)
      );
  }
}
