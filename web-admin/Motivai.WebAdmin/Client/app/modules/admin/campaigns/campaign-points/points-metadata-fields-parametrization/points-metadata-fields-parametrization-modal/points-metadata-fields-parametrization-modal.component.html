<gp-modal #modal width="1000px">
    <form #ngForm="ngForm" novalidate>
        <gp-form-row>
            <gp-form-col cols="12 3">
                <gp-simple-input label="Código do campo recebido" [required]="true">
                    <input type="text" name="property" class="form-control" [disabled]="isEditing" required [ngModelOptions]="{standalone: true}"
                        [(ngModel)]="metadataFieldsParametrization.property" />
                </gp-simple-input>
            </gp-form-col>
            <gp-form-col cols="12 3">
                <gp-simple-input label="Descrição do campo a ser exibido" [required]="true">
                    <input type="text" name="description" class="form-control" required [ngModelOptions]="{standalone: true}"
                        [(ngModel)]="metadataFieldsParametrization.description" />
                </gp-simple-input>
            </gp-form-col>
            <gp-form-col cols="12 2">
                <gp-simple-input label="Posição" [required]="true">
                    <gp-input-mask name="position" [onlyInteger]="true" [ngModelOptions]="{standalone: true}"
                        [(ngModel)]="metadataFieldsParametrization.position">
                    </gp-input-mask>
                </gp-simple-input>
            </gp-form-col>
            <gp-form-col cols="12 1">
                <label style="width: 100%;">Ativo: </label>
                <gp-switch value="true" [(ngModel)]="metadataFieldsParametrization.active" [ngModelOptions]="{standalone: true}"></gp-switch>
            </gp-form-col>
        </gp-form-row>
        <hr>
        <gp-form-row>
            <gp-form-col cols="12" [inputGroup]="false">
                <gp-spinner-button type="button" [pink]="true" icon="plus" marginLeft="5px"
                    [text]="selectInputButonText" pull="right" (click)="createMetadata()"></gp-spinner-button>
                <!-- <gp-spinner-button type="button" [clear]="true" text="Limpar" pull="right" (click)="clear()">
                </gp-spinner-button> -->
            </gp-form-col>
        </gp-form-row>
    </form>
</gp-modal>