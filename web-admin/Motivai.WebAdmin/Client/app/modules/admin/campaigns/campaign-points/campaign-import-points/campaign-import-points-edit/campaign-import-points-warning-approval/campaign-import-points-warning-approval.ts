import { Subscription } from 'rxjs';
import { OnInit } from '@angular/core';
import { Component, Input, ViewChild } from '@angular/core';
import { CampaignImportService } from '../../../../campaign-import.service';
import { CampaignStore } from '../../../../campaign.store';
import { CampaignImportPointsWarningApprovalModalComponent } from './campaign-import-points-warning-approval-modal/campaign-import-points-warning-approval-modal';
import { FormatHelper } from '../../../../../../../shared/formatters/format-helper';

@Component({
    selector: 'campaign-import-points-warning-approval',
    templateUrl: 'campaign-import-points-warning-approval.component.html'
})
export class CampaignImportPointsWarningApprovalComponent implements OnInit {
    @ViewChild('approvalModal') approvalModal: CampaignImportPointsWarningApprovalModalComponent;

    campaignId: string = "";
    _batchImportId: string = "";
    @Input() set batchImportId(v: string) {
        if (v) {
            this._batchImportId = v;
            this.loadWarningTemporaryPoints();
        }
    }
    private campaign$: Subscription;

    loadingGrid: boolean = false;
    rows: any[] = [];
    filters: any = {};
    skip: number = 0;
    limit: number = 0;

    constructor(private _campaignImportService: CampaignImportService, private campaignStore: CampaignStore) { }

    ngOnInit(): void {
        this.campaign$ = this.campaignStore.asObservable.subscribe(id => { this.campaignId = id });
    }

    loadWarningTemporaryPoints() {
        this.loadingGrid = true;
        this._campaignImportService.getWarningTemporaryPointsByBatchId(this.campaignStore.id, this._batchImportId, this.skip, this.limit)
            .subscribe(
                temporaries => {
                    this.rows = temporaries;
                    if (this.rows) {
                        this.rows.forEach(r => {
                            r.formattedWarning = this.handleWarning(r.statusCode);
                            r.formattedAmount = FormatHelper.formatCurrency(r.currencyAmount);
                            r.formattedDate = FormatHelper.formatDateWithTimezone(r.createDate);
                            r.antiFraudAnalysis.detail.formattedAverage = FormatHelper.formatCurrency(r.antiFraudAnalysis.detail.average);
                            r.antiFraudAnalysis.detail.formattedOverdraft = FormatHelper.formatPercentage(r.antiFraudAnalysis.detail.overdraft);
                            r.antiFraudAnalysis.detail.formattedObtainedOverdraft = FormatHelper.formatPercentage(r.antiFraudAnalysis.detail.obtainedOverdraft);
                        });
                    }
                    this.loadingGrid = false;
                },
                err => {
                    console.log(err);
                    this.loadingGrid = false;
                });
    }

    private handleWarning(statusCode: string) {
        if (!statusCode) {
            return 'Indefinido';
        }

        switch (statusCode) {
            case 'WARNING_EXCEED_PARTICIPANT_AVERAGE_CREDIT_AMOUNT':
                return 'Valor importado excede o critério de análise nas importações anteriores do participante';
            case 'WARNING_EXCEED_CAMPAIGN_AVERAGE_CREDIT_AMOUNT':
                return 'Valor importado excede o critério de análise padrão da campanha';
            case 'NONE':
            default:
                return 'Indefinido';
        }
    }

    private onDetails($event: any) {
        this.approvalModal.openModal($event);
    }

    private pointApproved($event: any) {
        this.loadWarningTemporaryPoints();
    }

    private download() {
        this._campaignImportService.getWarningTemporaryPointsByBatchIdCsv(this.campaignStore.id, this._batchImportId).subscribe(
            url => {
                window.open(url, '_blank');
            },
            err => {
                console.log(err);
            }
        )
    }


}