import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { ApiService } from '../../../../core/api/api.service';

@Injectable()
export class CampaignDiscountService {
    constructor(private _api: ApiService) {}

    save(campaignId: string, discount: any): Observable<any> {
        if (discount.id) {
            return this._api.put(`/api/campaigns/${campaignId}/discounts/${discount.id}`, discount, 20000);
        }
        return this._api.post(`/api/campaigns/${campaignId}/discounts`, discount, 20000);
    }

    find(campaignId: string, description: string, type: string, modality: string, skip?: number, limit?: number): Observable<any[]> {
        const params: any = {};
        params.campaignId = campaignId;
        if (description) params.description = description;
        if (type) params.type = type;
        if (modality) params.modality = modality;
        if (skip != null) params.skip = skip;
        if (limit != null) params.limit = limit;

        return this._api.get(`/api/campaigns/${campaignId}/discounts`, params, 20000);
    }

    findById(campaignId: string, discountId: string): Observable<any> {
        return this._api.get(`/api/campaigns/${campaignId}/discounts/${discountId}`, null, 20000);
    }

    linkPartner(campaignId: string, discountId: string, partnerId: string): Observable<any> {
        return this._api.put(`/api/campaigns/${campaignId}/discounts/${discountId}/filters/partners/link`, { partnerId }, 20000);
    }

    unlinkPartner(campaignId: string, discountId: string, partnerId: string): Observable<any> {
        return this._api.put(`/api/campaigns/${campaignId}/discounts/${discountId}/filters/partners/unlink`, { partnerId }, 20000);
    }

    getLinkedPartners(campaignId: string, discountId: string): Observable<any[]> {
        return this._api.get(`/api/campaigns/${campaignId}/discounts/${discountId}/filters/partners`, null, 20000);
    }

    linkCategoriesClassification(campaignId: string, discountId: string, classification: any): Observable<any> {
        return this._api.put(`/api/campaigns/${campaignId}/discounts/${discountId}/filters/categories/link`, classification, 20000);
    }

    unlinkCategoriesClassification(campaignId: string, discountId: string, classification: any): Observable<any> {
        return this._api.put(`/api/campaigns/${campaignId}/discounts/${discountId}/filters/categories/unlink`, classification, 20000);
    }

    getLinkedCategoriesClassification(campaignId: string, discountId: string): Observable<any[]> {
        return this._api.get(`/api/campaigns/${campaignId}/discounts/${discountId}/filters/categories`, null, 20000);
    }

    linkSkus(campaignId: string, discountId: string, filter: any): Observable<any> {
        return this._api.put(`/api/campaigns/${campaignId}/discounts/${discountId}/filters/skus/link`, filter, 20000);
    }

    unlinkSkus(campaignId: string, discountId: string, filter: any): Observable<any> {
        return this._api.put(`/api/campaigns/${campaignId}/discounts/${discountId}/filters/skus/unlink`, filter, 20000);
    }

    getLinkedSkus(campaignId: string, discountId: string): Observable<any[]> {
        return this._api.get(`/api/campaigns/${campaignId}/discounts/${discountId}/filters/skus`, null, 20000);
    }
}
