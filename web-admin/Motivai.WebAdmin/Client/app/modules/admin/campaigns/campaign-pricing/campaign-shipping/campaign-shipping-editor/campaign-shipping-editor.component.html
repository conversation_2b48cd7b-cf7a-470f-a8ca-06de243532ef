<gp-form-validate #formShipping [formGroup]="gpForm" [validationMessages]="messages" (onSubmit)="onFormSubmit($event)">
	<gp-card [first]="true" title="Configuração do Frete" *ngIf="canViewShipping">
		<div class="row">
			<gp-input-validate cols="12 6 6" formControlName="description" label="Descrição"></gp-input-validate>
			<div grid="12 6" [group]="true">
				<label>Ativo:</label>
				<div>
					<gp-switch formControlName="active"></gp-switch>
				</div>
			</div>
		</div>
		<div class="row">
			<gp-select-validate cols="12 12 8" label="Tipo do frete:" placeholder="Selecione" [required]="true" formControlName="type">
				<option value="Fixed">Fixo</option>
				<option value="Embedded">Embutido no Produto</option>
				<!--<option value="Calculated">Cálculo via Integração/Tabela</option>-->
			</gp-select-validate>
		</div>
		<div class="row" *ngIf="isFixed">
			<gp-select-validate cols="12 6 4" label="Tipo do valor:" placeholder="Selecione" [required]="true"
				formControlName="fixedCostType">
				<option value="Currency">Valor</option>
				<option value="Percentage">Percentual</option>
			</gp-select-validate>
			<gp-mask-validate cols="12 6 4" [label]="labelFixedCostValue" [onlyDecimal]="true"
				formControlName="fixedCostValue"></gp-mask-validate>
		</div>
		<div class="row" *ngIf="isCalculated">
			<div grid="12 6" [group]="true">
				<label>Utilizar integração com o Correios:</label>
				<div>
					<gp-switch formControlName="correiosIntegration"></gp-switch>
				</div>
			</div>
		</div>
		<div class="row">
			<gp-mask-validate cols="12 6 6" formControlName="sourceCep" label="CEP de origem do cálculo"
				mask="00000-000" [guide]="false" *ngIf="isCalculated && isCorreios"></gp-mask-validate>
		</div>
		<spinner [overlay]="true" [show]="loading"></spinner>
	</gp-card>

	<gp-card title="Acréscimos" *ngIf="canViewShipping">
		<div class="row">
			<div grid="12 6 3 2" [group]="true">
				<label>Adicionar valor:</label>
				<div>
					<gp-switch formControlName="applyFeeValue"></gp-switch>
				</div>
			</div>
			<gp-select-validate cols="12 6 4" label="Tipo do acréscimo:" placeholder="Selecione" [required]="valueRequiredFee"
				formControlName="feeValueType">
				<option value="Currency">Valor</option>
				<option value="Percentage">Percentual</option>
			</gp-select-validate>
		</div>
		<div class="row">
			<gp-mask-validate cols="12 6" [label]="labelFeeValue" [onlyDecimal]="true"
				[required]="valueRequiredFee" formControlName="feeValue"></gp-mask-validate>
		</div>
	</gp-card>
	
	<gp-card title="Acréscimos por item" *ngIf="canViewShipping">
		<div class="row">
			<div grid="12 6 3 2" [group]="true">
				<label>Adicionar valor:</label>
				<div>
					<gp-switch formControlName="applyAdditionalValue"></gp-switch>
				</div>
			</div>
			<gp-select-validate cols="12 6 4" label="Tipo do acréscimo:" placeholder="Selecione" [required]="valueRequiredFeeByItem"
				formControlName="additionalType">
				<option value="Currency">Valor</option>
				<option value="Percentage">Percentual</option>
			</gp-select-validate>
		</div>
		<div class="row">
			<gp-mask-validate cols="12 6" [label]="labelAdditionalValue" [onlyDecimal]="true"
				[required]="valueRequiredFeeByItem" formControlName="additionalValue"></gp-mask-validate>
		</div>
	</gp-card>
</gp-form-validate>

<gp-card title="Filtro de Aplicação" *ngIf="isEdition && canViewShipping">
	<accordion [closeOthers]="true">
		<accordion-group panelClass="b0 mb-sm panel-default">
			<div accordion-heading>
				<label>Por Parceiro</label>
			</div>
			<div class="row">
				<div grid="12 6 6" [group]="true">
					<label>Parceiro:</label>
					<ng-select [allowClear]="true" [items]="campaignPartners" (data)="selectPartner($event)" placeholder="Selecione um parceiro">
					</ng-select>
				</div>
				<div grid="12 6 6" class="top-p2 pull-right">
					<gp-spinner-button type="button" [pink]="true" icon="plus" text="Adicionar" loadingText="Adicionando" pull="right" [loading]="sendingFilter"
						[disabled]="!filters.partnerId" (click)="addPartnerToFilter()">
					</gp-spinner-button>
				</div>
			</div>
			<div class="row">
				<div grid="12">
					<gp-grid name="partnersGrid" [rows]="shipping.partnersFilters" [columns]="['Parceiro']" [fields]="['partnerName']" [loading]="false"
						[showActive]="false" [showEdit]="true" [showDelete]="true" emptyMessage="Nenhum parceiro no filtro."
						(onDelete)="removePartnerFromFilter($event)">
					</gp-grid>
				</div>
			</div>
		</accordion-group>

		<accordion-group panelClass="b0 mb-sm panel-default">
			<div accordion-heading>
				<label>Por Classificação (Departamento, Categoria e/ou Subcategoria)</label>
			</div>
			<div class="row">
				<div grid="12 3 3" [group]="true">
					<gp-simple-input label="Departamento">
						<gp-categories-select id="department" name="department" [active]="true" [level]="1" required [(ngModel)]="filters.departmentId">
						</gp-categories-select>
					</gp-simple-input>
				</div>
				<div grid="12 3 3" [group]="true">
					<gp-simple-input label="Categoria">
						<gp-categories-select id="category" name="category" [active]="true" [level]="2" emptyMessage="Sem categorias"
							[parentId]="filters.departmentId" [disabled]="!filters.departmentId" [(ngModel)]="filters.categoryId">
						</gp-categories-select>
					</gp-simple-input>
				</div>
				<div grid="12 3 3" [group]="true">
					<gp-simple-input label="Subcategoria">
						<gp-categories-select id="category" name="subcategory" [active]="true" [level]="3" emptyMessage="Sem subcategorias"
							[parentId]="filters.categoryId" [disabled]="!filters.categoryId" [(ngModel)]="filters.subcategoryId">
						</gp-categories-select>
					</gp-simple-input>
				</div>
				<div grid="12 3 3" class="top-p2 pull-right">
					<gp-spinner-button type="button" [pink]="true" icon="plus" text="Adicionar" loadingText="Adicionando" pull="right"
						[loading]="sendingFilter" [disabled]="!filters.departmentId" (click)="addDepartmentToFilter()">
					</gp-spinner-button>
				</div>
			</div>
			<div class="row">
				<div grid="12">
					<gp-grid name="departmentsGrid" [rows]="shipping.categoriesFilters"
						[columns]="['Departamento', 'Categoria', 'Subcategoria']" [fields]="['departmentName', 'categoryName', 'subcategoryName']"
						[loading]="false" [showActive]="false" [showEdit]="false" [showDelete]="true" emptyMessage="Nenhum departamento no filtro."
						(onDelete)="removeDepartmentFromFilter($event)">
					</gp-grid>
				</div>
			</div>
		</accordion-group>

		<accordion-group panelClass="b0 mb-sm panel-default">
			<div accordion-heading>
				<label>Por SKU</label>
			</div>
			<div class="row">
				<div grid="12 7" [group]="true">
					<label>Parceiro:</label>
					<ng-select [allowClear]="true" [items]="campaignPartners" (data)="selectPartner($event)" placeholder="Selecione um parceiro">
					</ng-select>
				</div>
				<div grid="12 5" [group]="true">
					<gp-simple-input label="Código SKU">
						<input type="text" class="form-control" name="skuCode" [(ngModel)]="filters.skuCode" (blur)="searchSku()" />
					</gp-simple-input>
				</div>
				<div grid="12 10" [group]="true">
					<input type="text" class="form-control" [value]="product?.name" disabled />
				</div>
				<div grid="12 2" class="pull-right">
					<gp-spinner-button type="button" [pink]="true" icon="plus" text="Adicionar" loadingText="Adicionando" pull="right"
						[loading]="sendingFilter" [disabled]="isSkuInvalid" (click)="addProductToFilter()">
					</gp-spinner-button>
				</div>
			</div>
			<div class="row">
				<div grid="12">
					<gp-grid name="productsGrid" [rows]="shipping.skusFilters"
						[columns]="['Parceiro', 'Código SKU', 'Produto']" [fields]="['partnerName', 'skuCode', 'productName']"
						[loading]="false" [showActive]="false" [showEdit]="false" [showDelete]="true" emptyMessage="Nenhum produto no filtro."
						(onDelete)="removeProductFromFilter($event)">
					</gp-grid>
				</div>
			</div>
		</accordion-group>
	</accordion>
</gp-card>


<gp-card [last]="true">
	<div class="row col-sm-12" *ngIf="canViewShipping">
		<gp-spinner-button text="Novo" bootstrapClass="default" icon="plus" (click)="newShipping()"
			[disabled]="disableButtons" *ngIf="canCreateShipping"></gp-spinner-button>

		<gp-spinner-button type="submit" text="Salvar" [pink]="true" icon="send" [disabled]="disableButtons" [loading]="sending"
			loadingText="Salvando" (click)="formShipping.submit()" *ngIf="canSaveShipping"></gp-spinner-button>
	</div>
	<div class="row">
		<div class="col-sm-12">
			<gp-alert #gpAlert></gp-alert>
		</div>
	</div>
</gp-card>
