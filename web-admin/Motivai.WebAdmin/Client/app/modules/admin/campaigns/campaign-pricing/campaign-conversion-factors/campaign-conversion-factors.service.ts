import { ApiService } from '../../../../../core/api/api.service';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs/Observable';

@Injectable()
export class CampaignFactorsService {

    constructor(private _api: ApiService) {}

    createConversionFactor(campaignId: string, conversionFactor: any): Observable<any> {
        if (conversionFactor.id) {
            return this._api.put(`/api/campaigns/${campaignId}/conversion/factors/${conversionFactor.id}`, conversionFactor);
        }
        return this._api.post(`/api/campaigns/${campaignId}/conversion/factors`, conversionFactor);
    }

    getConversionFactors(campaignId: string): Observable<any[]> {
        return this._api.get(`/api/campaigns/${campaignId}/conversion/factors`);
    }

    getConversionFactorById(campaignId: string, factorId: string): Observable<any[]> {
        return this._api.get(`/api/campaigns/${campaignId}/conversion/factors/${factorId}`);
    }
}
