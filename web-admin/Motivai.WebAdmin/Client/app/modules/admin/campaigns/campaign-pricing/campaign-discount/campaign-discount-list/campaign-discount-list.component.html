<gp-card [first]="true" title="Pesquisa">
	<gp-alert #alert [overlay]="true"></gp-alert>
	<gp-form-row>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Descrição da promoção">
				<input type="text" name="name" class="form-control" [(ngModel)]="filters.description"  />
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Tipo do desconto">
				<select name="discountType" class="form-control" [(ngModel)]="filters.type">
					<option value="">Todos</option>
					<option value="VALUE">Valor</option>
					<option value="PERCENTAGE">Percentual</option>
				</select>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Modalidade do desconto">
				<select name="modalityType" class="form-control" [(ngModel)]="filters.modality">
					<option value="">Todos</option>
					<option value="CATALOG">Aplicação direta no catálogo</option>
					<option value="COUPON">Cupom de desconto</option>
				</select>
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 6 6">
			<gp-spinner-button 
				text="Pesquisar" 
				buttonClass="bg-primary-dark" 
				icon="search" 
				(click)="findDiscounts(true)"
				[loading]="loading" 
				loadingText="Pesquisando">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card title="Resultados encontrados">
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<gp-grid
				name="grid"
				[rows]="discounts"
				[columns]="[ 'Descrição da Promoção', 'Início', 'Término', 'Tipo', 'Modalidade' ]"
				[fields]="[ 'description', 'formatedStartDate', 'formatedEndDate', 'discountTypeDescription', 'modalityTypeDescription' ]"
				[showActive]="true"
				[showPagination]="true"
				[showTotalPages]="false"
				[showEdit]="true"
				[showDelete]="false"
				[loading]="loading"
				[pageSize]="limit"
				(onEdit)="editDiscount($event)"
				(onPageChanged)="onPageChanged($event)">
			</gp-grid>
		</gp-form-col>
	</gp-form-row>
</gp-card>