import { Component, OnInit, ViewChild, Output, EventEmitter } from '@angular/core';
import { CampaignStore } from '../../../campaign.store';
import { CampaignFactorsService } from '../campaign-conversion-factors.service';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { BusinessUnitService } from '../../../../../security/business-unit/business-unit.service';

@Component({
    selector: 'campaign-conversion-factors-list',
    templateUrl: './campaign-conversion-factors-list.component.html'
})
export class CampaignConversionFactorsListComponent implements OnInit {
    @ViewChild('alert') alert: GpAlertComponent;
    @Output('onEdit') onEdit: EventEmitter<any> = new EventEmitter();

    conversionFactors: Array<any> = [];
    campaignId: string = '';
    loading: boolean = false;

    constructor(private _campaignStore: CampaignStore, private _factorsService: CampaignFactorsService, private buService: BusinessUnitService) { }

    ngOnInit(): void {
        this.campaignId = this._campaignStore.id;
        this.getConversionFactors();
    }

    getConversionFactors() {
        this.loading = true;
        this._factorsService.getConversionFactors(this.campaignId).subscribe(
            conversionFactors => {
                if (conversionFactors != null) {
                    conversionFactors.forEach(rule => {
                        if (rule.type == 'SKU') {
                            if (rule.sku.skuCode && rule.sku.skuName && rule.sku.partnerName) {
                                rule.skuDescription = `${rule.sku.skuName} (${rule.sku.skuCode})`;
                                rule.partnerName = rule.sku.partnerName;
                            }
                        } else {
                            if (!rule.partnerId) {
                                rule.partnerName = 'Todos';
                            }
                            if (!rule.department) {
                                rule.department = { name: 'Todos' };
                            }
                            if (!rule.category) {
                                rule.category = { name: 'Todos' };
                            }
                            if (!rule.subcategory) {
                                rule.subcategory = { name: 'Todos' };
                            }
                        }
                    });
                    this.conversionFactors = conversionFactors;
                }
                this.loading = false;
            },
            err => {
                this.loading = false;
                this.alert.showError(err);
            }
        );

    }

    editFactor(event) {
        this.onEdit.emit(event.id);
    }
}
