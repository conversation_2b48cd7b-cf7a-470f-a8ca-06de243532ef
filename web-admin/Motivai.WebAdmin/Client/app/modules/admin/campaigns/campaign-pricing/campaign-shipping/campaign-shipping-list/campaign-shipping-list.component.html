<tabset class="bg-white p0 tab-no-border" [justified]="true" #tabset>
    <tab>
        <ng-template tabHeading><em class="fa fa-list-ul fa-fw"></em>Listagem</ng-template>
        <gp-card [first]="true" [last]="true" title="Fretes cadastrados">
            <gp-form-row>
                <gp-form-col cols="12 12 6 3">
                    <gp-spinner-button text="Carregar" [search]="true" (click)="loadShippings()"
                        [loading]="loading" loadingText="Carregando"></gp-spinner-button>
                </gp-form-col>
            </gp-form-row>
            <gp-form-row>
                <gp-form-col cols="12 12 12">
                    <gp-grid name="shippingGrid" [rows]="shippings"
                        [columns]="['Descrição', 'Tipo']" [fields]="['description', 'typeDesc']"
                        [loading]="loading" [showActive]="true" [showPagination]="false"
                        [showEdit]="canViewShipping" [showDelete]="false"
                        (onEdit)="editShipping($event)">
                    </gp-grid>
                </gp-form-col>
            </gp-form-row>
            <div class="row">
                <div class="col-md-12">
                    <gp-alert #roleAlert></gp-alert>
                </div>
            </div>
        </gp-card>
	</tab>
	<tab>
		<ng-template tabHeading><em class="fa fa-list-ul fa-fw"></em>Cadastro</ng-template>
        <campaign-shipping-editor #shippingEditor (shippingUpdate)="loadShippings()"></campaign-shipping-editor>
	</tab>
</tabset>