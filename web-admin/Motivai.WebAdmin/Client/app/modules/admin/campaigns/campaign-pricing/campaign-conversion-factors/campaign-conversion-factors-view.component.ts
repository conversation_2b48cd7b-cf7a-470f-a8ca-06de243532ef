import { Component, OnInit, ViewChild } from '@angular/core';
import { TabsetComponent } from 'ng2-bootstrap';
import { CampaignConversionFactorsListComponent } from './campaign-conversion-factors-list/campaign-conversion-factors-list.component';
import { CampaignConversionFactorsEditComponent } from './campaign-conversion-factors-edit/campaign-conversion-factors-edit.component';

@Component({
    selector: 'campaign-conversion-factors-view',
    templateUrl: './campaign-conversion-factors-view.component.html'
})
export class CampaignConversionFactorsViewComponent implements OnInit {
    @ViewChild('tabs') tabs: TabsetComponent;
    @ViewChild('factorsList') factorsList: CampaignConversionFactorsListComponent;
    @ViewChild('factorsEdit') factorsEdit: CampaignConversionFactorsEditComponent;
    
    factorId: string = '';

    constructor() { }

    ngOnInit(): void { }

    refreshGrid(event) {
        this.factorsEdit.clear();
        this.factorsList.getConversionFactors();
    }

    edit(event) {
        this.factorsEdit.factorId = event;
        this.factorsEdit.getConversionFactorById();
        this.tabs.tabs[1].disabled = false;
        this.tabs.tabs[1].active = true;
    }
}
