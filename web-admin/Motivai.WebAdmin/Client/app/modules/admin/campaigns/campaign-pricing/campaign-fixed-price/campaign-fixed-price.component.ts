import { Component, OnInit, ViewChild } from '@angular/core';

import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignStore } from '../../campaign.store';
import { CampaignService } from '../../campaign.service';
import { CompanyService } from '../../../companies/company/company.service';

@Component({
  selector: 'campaign-fixed-price',
  templateUrl: './campaign-fixed-price.component.html'
})
export class CampaignFixedPriceComponent implements OnInit {
  @ViewChild('alert') alert: GpAlertComponent;

  partners: any[] = [];
  fixedProducts: any[] = [];
  product: any = {
    name: ''
  };

  campaignId: string = '';

  sending: boolean = false;
  loadingGrid: boolean = false;

  constructor(
    private _campaignService: CampaignService,
    private _store: CampaignStore,
    private _companyService: CompanyService
  ) {}

  get disableButton() {
    return !this.product.productId || !this.product.skuId;
  }

  ngOnInit(): void {
    this.campaignId = this._store.id;
    this.getPartners();
    this.getProductsFixedPrices();
  }

  getProductsFixedPrices() {
    this.loadingGrid = true;
    this._campaignService.getProductsFixedPrices(this.campaignId)
        .subscribe(
        fixedProducts => {
            this.fixedProducts = fixedProducts || [];
            this.loadingGrid = false;
        },
        err => {
            this.alert.handleAndShowError(err);
            this.loadingGrid = false;
        }
    );
  }

  getPartners() {
    this.sending = true;
    this._campaignService
      .getLinkedPartnersDataByCampaign(this.campaignId)
      .subscribe(
        partners => {
          this.partners = partners.map(x => ({
            id: x.partnerId,
            text: x.partnerName
          }));
          this.sending = false;
        },
        err => {
          this.sending = false;
          this.alert.showError(err);
        }
      );
  }

  searchSku() {
    this.product.name = '';

    const partnerId = this.product.partnerId;
    const skuCode = this.product.skuCode;
    if (!partnerId || !skuCode) {
      return;
    }

    this.product.name = 'Pesquisando...';
    this._companyService.findPartnerProductBySkuCode(partnerId, skuCode)
      .subscribe(
        product => {
          if (product) {
            this.product.productId = product.id;
            this.product.skuId = product.skuId;
            this.product.partnerName = product.partnerName;
            this.product.name = product.name;
          } else {
            this.alert.showWarning('Produto não encontrado pelo SKU informado.');
            this.product.productId = null;
            this.product.skuId = null;
            this.product.partnerName = '';
            this.product.name = '';
          }
        },
        err => {
          this.alert.handleAndShowError(err);
        }
      );
  }

  prepareToSave() {
    this.sending = true;

    const existingProduct = this.fixedProducts.find(p => p.partnerId == this.product.partnerId && p.productId == this.product.productId);

    if (existingProduct) {
      this.alert.showWarning('Preço fixo do produto ja cadastrado');
      this.clear();
      this.sending = false;
      return;
    }
    this.save();
  }

  save() {
    this._campaignService
      .saveProductsFixedPrices(this.campaignId, this.product)
      .subscribe(
        fixedProducts => {
            this.sending = false;
            this.clear();
            this.getProductsFixedPrices();
        },
        err => {
            this.alert.showError(err);
            this.sending = false;
        }
      );
  }

  deleteFixedPrice($event) {
    let id = $event.id;
    this.loadingGrid = true;
    this._campaignService
      .deleteProductFixedPriceById(this.campaignId, id)
      .subscribe(
        fixedProducPrice => {
            this.loadingGrid = false;
            this.getProductsFixedPrices();
        },
        err => {
            this.loadingGrid = false;
            this.alert.showError(err);
        }
      );
  }

  clear() {
    this.product.productId = null;
    this.product.skuId = null;
    this.product.skuCode = '';
    this.product.partnerName = '';
    this.product.name = '';
    this.product.fixedSalePrice = '';
  }

  onPartnerChange($event) {
    this.product.partnerId = $event.id;
  }
}
