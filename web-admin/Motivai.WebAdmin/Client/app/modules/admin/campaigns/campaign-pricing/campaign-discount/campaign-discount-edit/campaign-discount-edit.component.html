<form #discountForm="ngForm" (ngSubmit)="saveDiscount()" novalidate>
	<gp-alert #alert [overlay]="true"></gp-alert>
	<spinner [show]="loadingScreen" [overlay]="true"></spinner>
	<gp-card [first]="true" title="Dados da Promoção">
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-simple-input label="Descrição da Promoção" [required]="true" errorMessage="Descrição da promoção é obrigatório">
					<input type="text" class="form-control" name="name" required [(ngModel)]="discount.description" />
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Tipo do Desconto" [required]="true" errorMessage="Tipo do desconto é obrigatório">
					<select name="discountType" class="form-control" [(ngModel)]="discount.discountType" required>
						<option value="">Selecione</option>
						<option value="VALUE">Valor</option>
						<option value="PERCENTAGE">Percentual</option>
					</select>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Modalidade do Desconto" [required]="true" errorMessage="Modalidade do desconto é obrigatório">
					<select name="modalityType" class="form-control" [(ngModel)]="discount.modalityType" required>
						<option value="">Selecione</option>
						<option value="CATALOG">Aplicação direta no catálogo</option>
						<option value="COUPON">Cupom de desconto</option>
					</select>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-datepicker cols="12 6 6" label="Data de início" name="startDate" [(ngModel)]="discount.startDate"></gp-datepicker>
			<gp-datepicker cols="12 6 6" label="Data de término" name="endDate" [(ngModel)]="discount.endDate"></gp-datepicker>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Valor do Desconto" [required]="true" errorMessage="Valor de desconto é obrigatório">
					<gp-input-mask name="discountValue" required [onlyDecimal]="true" [(ngModel)]="discount.discountValue"></gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<label>Ativo</label>
                <div>
                    <gp-switch name="active" [(ngModel)]="discount.active"></gp-switch>
                </div>
			</gp-form-col>
		</gp-form-row>
		<div *ngIf="discount.modalityType === 'COUPON'">
			<hr />
			<gp-form-row>
				<gp-form-col cols="12 6 6">
					<gp-simple-input label="Código do cupom de desconto">
						<input type="text" class="form-control" name="couponCode" [(ngModel)]="discount.discountCoupon.couponCode" />
					</gp-simple-input>
				</gp-form-col>
				<gp-form-col cols="12 6 6">
					<label>Permitir que o cupom seja usado mais de uma vez</label>
						<div>
							<gp-switch name="active" [(ngModel)]="discount.discountCoupon.allowUsingMultipleTimes"></gp-switch>
						</div>
				</gp-form-col>
			</gp-form-row>
		</div>
		<div class="row">
			<div class="col col-md-12">
				<gp-spinner-button type="submit" [pink]="true" icon="send" text="Salvar desconto" [loading]="loading" pull="right"></gp-spinner-button>

				<gp-spinner-button type="button" [actionSecondary]="true" text="Novo" (click)="clear()" pull="right" marginRight="5px"></gp-spinner-button>
			</div>
		</div>
	</gp-card>
	<gp-card *ngIf="discount.id">
		<accordion [closeOthers]="true">
			<accordion-group panelClass="b0 mb-sm panel-default">
				<div accordion-heading>
					<label>Desconto por Parceiro</label>
				</div>
				<gp-form-row>
					<gp-form-col cols="12 6 6">
						<gp-simple-input label="Parceiro">
							<ng-select name="selectedPartner" [allowClear]="false" [items]="partners" placeholder="Parceiro" [(ngModel)]="filters.selectedPartner"></ng-select>
						</gp-simple-input>
					</gp-form-col>
					<gp-form-col cols="12 6 6">
						<gp-spinner-button type="button" [pink]="true" icon="plus" text="Adicionar"
							[disabled]="!filters.selectedPartner" (click)="linkPartner()" marginTop="24px" [loading]="loading">
						</gp-spinner-button>
					</gp-form-col>
				</gp-form-row>
				<gp-form-row>
					<gp-form-col cols="12 12 12">
						<gp-grid name="partnersGrid" [rows]="filteredPartners" [loading]="loadingLinkedPartners"
							[columns]="['Parceiro']" [fields]="['name']"
							[showActive]="false" [showPagination]="false"
							[showEdit]="false" [showDelete]="true" emptyMessage="Nenhum parceiro vinculado nesta promoção"
							(onDelete)="unlinkPartner($event)">
						</gp-grid>
					</gp-form-col>
				</gp-form-row>
			</accordion-group>

			<accordion-group panelClass="b0 mb-sm panel-default">
				<div accordion-heading>
					<label>Desconto por Departamentos</label>
				</div>
				<gp-form-row>
					<gp-form-col cols="12 3 3">
						<gp-simple-input label="Departamento">
							<gp-categories-select id="department" name="department" [active]="true" [level]="1" required
								[(ngModel)]="filters.departmentId">
							</gp-categories-select>
						</gp-simple-input>
					</gp-form-col>
					<gp-form-col cols="12 3 3">
						<gp-simple-input label="Categoria">
							<gp-categories-select id="category" name="category" [active]="true" [level]="2" emptyMessage="Sem categorias"
								[parentId]="filters.departmentId" [disabled]="!filters.departmentId" [includeEmptyOption]="true" [(ngModel)]="filters.categoryId">
							</gp-categories-select>
						</gp-simple-input>
					</gp-form-col>
					<gp-form-col cols="12 3 3">
						<gp-simple-input label="Subcategoria">
							<gp-categories-select id="category" name="subcategory" [active]="true" [level]="3" emptyMessage="Sem subcategorias"
								[parentId]="filters.categoryId" [disabled]="!filters.categoryId" [includeEmptyOption]="true" [(ngModel)]="filters.subcategoryId">
							</gp-categories-select>
						</gp-simple-input>
					</gp-form-col>
					<gp-form-col cols="12 3 3">
						<gp-spinner-button
							type="button"
							[pink]="true"
							icon="plus"
							text="Adicionar"
							loadingText="Processando"
							pull="left"
							[loading]="loading"
							[disabled]="!filters.departmentId"
							(click)="linkCategoryClassification()"
							marginTop="24px">
						</gp-spinner-button>
					</gp-form-col>
				</gp-form-row>
				<gp-form-row>
					<gp-form-col cols="12 12 12">
						<gp-grid name="departmentsGrid" [rows]="filteredCategories" [loading]="loadingLinkedCategories"
							[columns]="['Departamento', 'Categoria', 'Subcategoria']" [fields]="['departmentName', 'categoryName', 'subcategoryName']"
							[loading]="false" [showActive]="false" [showPagination]="false"
							[showEdit]="false" [showDelete]="true" emptyMessage="Nenhum departamento vinculado nesta promoção"
							(onDelete)="unlinkCategoryClassification($event)">
						</gp-grid>
					</gp-form-col>
				</gp-form-row>
			</accordion-group>

			<accordion-group panelClass="b0 mb-sm panel-default">
				<div accordion-heading>
					<label>Desconto por SKU</label>
				</div>
				<gp-form-row>
					<gp-form-col cols="12 5 5">
						<gp-simple-input label="Parceiro">
							<ng-select name="partner" [allowClear]="false" [items]="partners" placeholder="Parceiro" [(ngModel)]="filters.sku.selectedPartner"></ng-select>
						</gp-simple-input>
					</gp-form-col>
					<gp-form-col cols="12 5 5">
						<gp-simple-input label="Código SKU">
							<input type="text" class="form-control" name="skucode" (blur)="getProductDetail()" [(ngModel)]="filters.sku.skuCode" />
						</gp-simple-input>
					</gp-form-col>
				</gp-form-row>
				<gp-form-row>
					<gp-form-col cols="12 10 10">
						<input type="text" disabled class="form-control" [value]="product.name || ''" />
					</gp-form-col>
					<gp-form-col cols="12 2 2">
						<gp-spinner-button
							type="button"
							[pink]="true"
							icon="plus"
							text="Adicionar"
							loadingText="Processando"
							[loading]="loading"
							[disabled]="!product.id"
							(click)="linkProduct()">
						</gp-spinner-button>
					</gp-form-col>
				</gp-form-row>
				<gp-form-row>
					<gp-form-col cols="12 12 12">
						<gp-grid name="skusGrid" [rows]="filteredProducts"
							[columns]="['Parceiro', 'Código SKU', 'Produto']" [fields]="['partnerName', 'skuCode', 'productName']"
							[showActive]="false" [showPagination]="false" [loading]="loadingLinkedSkus"
							[showEdit]="false" [showDelete]="true" emptyMessage="Nenhum produto vinculado nesta promoção"
							(onDelete)="unlinkProduct($event)">
						</gp-grid>
					</gp-form-col>
				</gp-form-row>
			</accordion-group>
		</accordion>
	</gp-card>
</form>
