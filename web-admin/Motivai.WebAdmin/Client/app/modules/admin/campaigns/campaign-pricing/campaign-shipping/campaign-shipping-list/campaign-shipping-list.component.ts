import { TabsetComponent } from 'ng2-bootstrap/tabs';
import { OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Component } from '@angular/core';

import { CampaignService } from '../../../campaign.service';
import { CampaignShippingEditorComponent } from '../campaign-shipping-editor/campaign-shipping-editor.component';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { AuthStore } from '../../../../../../core/auth/auth.store';
import { PERMISSION_CAMPAIGNS_SHIPPING_VIEW } from '../../../../../../core/auth/access-points';

@Component({
  selector: 'campaign-shipping-list',
  templateUrl: 'campaign-shipping-list.component.html'
})
export class CampaignShippingListComponent implements OnInit {
  @ViewChild('shippingEditor') shippingEditor: CampaignShippingEditorComponent;
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @ViewChild('tabset') tabs: TabsetComponent;
  campaignId: string = '';
  shippings: Array<any> = [];
  loading: boolean = false;

  constructor(private _authStore: AuthStore, private route: ActivatedRoute, private router: Router,
    private _campaignService: CampaignService) {}

  ngOnInit(): void {
    this.loading = true;
    if (this.route.parent != null && this.route.parent.parent != null) {
      this.route.parent.parent.params.subscribe((params: any) => {
        this.campaignId = params['id'];
        this.loadShippings();
      });
    } else {
      this.router.navigate(['/campanha']);
    }
  }

  get canViewShipping() {
    return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_SHIPPING_VIEW);
  }

  public handleError(err) {
    let errorMessage = (err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao efetuar a operação.');
    this.gpAlert.showError(errorMessage);
    this.loading = false;
  }

  loadShippings() {
    if (this.campaignId) {
      this.loading = true;
      this._campaignService.getShippings(this.campaignId)
        .subscribe(
          shippings => {
            this.shippings = shippings;
            if (this.shippings) {
              this.shippings.forEach(s => {
                if (s.type == 'Fixed')
                  s.typeDesc = 'Fixo';
                else if (s.type == 'Embedded')
                  s.typeDesc = 'Embutido';
                else
                  s.type = 'Calculado';
              })
            }
            this.loading = false;
          },
          err => this.handleError(err)
        )
    }
  }

  editShipping(shipping: any) {
    if (shipping) {
      this.shippingEditor.prepareEdit(shipping.id);
      this.tabs.tabs[1].active = true;
    }
  }
}
