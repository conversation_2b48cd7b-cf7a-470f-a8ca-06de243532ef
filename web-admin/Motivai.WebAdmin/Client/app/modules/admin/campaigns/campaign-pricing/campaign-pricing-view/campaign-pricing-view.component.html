<gp-card [noPaddingTop]="true" [noPaddingBottom]="true">
	<div class="row">
		<accordion [closeOthers]="true">
			<accordion-group panelClass="b0 mb-sm panel-default no-shadow" [isOpen]="true">
				<div accordion-heading>
					<label><em class="fa fa-navicon"></em> Features do Catálogo</label>
				</div>
				<div class="row menublock text-center">
					<gp-menublock-item size="xs" icon="usd" color="text-danger" text="Fatores de Conversão" routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'pricing', 'fatores']" *ngIf="canViewConversionFactors" ></gp-menublock-item>

					<gp-menublock-item size="xs" icon="truck" color="text-danger" text="Fretes" routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'pricing', 'fretes']" *ngIf="canViewShipping"></gp-menublock-item>

					<gp-menublock-item size="xs" icon="money" color="text-danger" text="Preço Fixo" routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'pricing', 'preco-fixo']" *ngIf="canViewFixedPrice"></gp-menublock-item>

					<gp-menublock-item size="xs" icon="percent" color="text-danger" text="Promoções e Descontos" routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'pricing', 'promocoes-e-descontos']" *ngIf="canAccessDiscounts"></gp-menublock-item>
				</div>
			</accordion-group>
		</accordion>
	</div>
</gp-card>
<router-outlet></router-outlet>
