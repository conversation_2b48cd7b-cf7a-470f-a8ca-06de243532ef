import { Component, OnInit, ViewChild } from '@angular/core';
import { TabsetComponent, TabDirective } from 'ng2-bootstrap';

import { GpCampaignDiscountListComponent } from './campaign-discount-list/campaign-discount-list.component';
import { GpCampaignDiscountEditComponent } from './campaign-discount-edit/campaign-discount-edit.component';

@Component({
    selector: 'campaign-discount',
    templateUrl: 'campaign-discount.component.html'
})
export class GpCampaignDiscountComponent implements OnInit {
    discountId: string = '';

    @ViewChild('tabs') tabs: TabsetComponent;
    @ViewChild('listComponent') listComponent: GpCampaignDiscountListComponent;
    @ViewChild('editComponent') editComponent: GpCampaignDiscountEditComponent;

    constructor() { }

    ngOnInit() { }

    checkSelectEvent(ev?: any) {
        if (!(ev instanceof TabDirective)) return false;
        return true;
    }

    editDiscount($event) {
        this.discountId = $event;
        this.tabs.tabs[1].active = true;
    }

    refreshGrid(ev?: any) {
        if (this.checkSelectEvent(ev)) {
            this.discountId = '';
            this.editComponent.clear();
            this.listComponent.findDiscounts();
        }
    }
}
