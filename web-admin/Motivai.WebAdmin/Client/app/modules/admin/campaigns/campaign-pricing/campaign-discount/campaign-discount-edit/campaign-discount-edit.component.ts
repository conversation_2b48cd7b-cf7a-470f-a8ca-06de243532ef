import { Component, OnInit, ViewChild, Input } from '@angular/core';
import { NgForm } from '@angular/forms';

import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignService } from '../../../campaign.service';
import { CampaignStore } from '../../../campaign.store';
import { CampaignDiscountService } from '../../../campaign-discount/campaign-discount.service';
import { CompanyService } from '../../../../companies/company/company.service';

@Component({
    selector: 'campaign-discount-edit',
    templateUrl: 'campaign-discount-edit.component.html'
})
export class GpCampaignDiscountEditComponent implements OnInit {
    private _discountId: string = '';
    @Input() set discountId(v: string) {
        if (v) {
            this._discountId = v;
            this.findDiscountById();
            this.getLinkedPartners();
            this.getLinkedCategories();
            this.getLinkedProducts();
        }
    }

    campaignId: string = '';
    loading: boolean = false;
    loadingLinkedPartners: boolean = false;
    loadingLinkedCategories: boolean = false;
    loadingLinkedSkus: boolean = false;
    loadingScreen: boolean = false;
    discount: any = {
        discountCoupon: {}
    };
    filters: any = {
        sku: {}
    };
    product: any = {};
    partners: any[] = [];
    filteredPartners: { partnerId: string, name: string }[] = [];
    filteredCategories: {
        departmentId: string,
        categoryId: string,
        subcategoryId: string,
        departmentName: string,
        categoryName: string,
        subcategoryName: string
    }[] = [];
    filteredProducts: {
        productId: string,
        partnerId: string,
        skuId: string,
        partnerName: string,
        productName: string,
        skuCode: string
    }[] = [];

    @ViewChild('alert') alert: GpAlertComponent;
    @ViewChild('discountForm') discountForm: NgForm;

    constructor(private campaignService: CampaignService, private campaignStore: CampaignStore, private discountService: CampaignDiscountService,
        private companyService: CompanyService) { }

    ngOnInit() {
        this.campaignId = this.campaignStore.id;
        this.getCampaignPartners();
    }

    clear() {
        this._discountId = '';
        this.loading = false;
        this.loadingScreen = false;
        this.discount = { discountCoupon: {} };
        this.filters = {
            sku: {}
        };
        this.filteredPartners = [];
        Object.keys(this.discountForm.controls).forEach(key => {
            this.discountForm.controls[key].reset();
        });
    }

    findDiscountById() {
        if (this._discountId) {
            this.loadingScreen = true;
            this.discountService.findById(this.campaignId, this._discountId).subscribe(
                discount => {
                    if (!discount)
                        this.alert.showError('Ocorreu um erro ao encontrar o desconto, tente novamente');

                    this.discount = discount || { discountCoupon: {} };
                    this.loadingScreen = false;
                },
                err => {
                    this.alert.showError(err, true);
                    this.loadingScreen = false;
                }
            );
        }
    }

    getCampaignPartners() {
        this.loading = true;
        this.alert.clear();
        this.campaignService.getLinkedPartnersDataByCampaign(this.campaignId).subscribe(
            partners => {
                this.partners = partners.map(x => ({ id: x.partnerId, text: x.partnerName }));
                this.loading = false;
            },
            err => {
                this.loading = false;
                this.alert.showError(err, true);
            }
        );
    }

    linkPartner() {
        if (this.filters.selectedPartner && this.discount.id) {
            this.loading = true;
            const partner = this.filters.selectedPartner[0];
            this.discountService.linkPartner(this.campaignId, this.discount.id, partner.id).subscribe(
                response => {
                    this.getLinkedPartners();
                    this.loading = false;
                },
                err => {
                    this.alert.showError(err, true);
                    this.loading = false;
                }
            );
        }
    }

    unlinkPartner($event) {
        if ($event) {
            this.loadingLinkedPartners = true;
            const partnerId = $event.partnerId;
            this.discountService.unlinkPartner(this.campaignId, this._discountId, partnerId).subscribe(
                response => {
                    this.loadingLinkedPartners = false;
                    this.getLinkedPartners();
                },
                err => {
                    this.alert.showError(err, true);
                    this.loadingLinkedPartners = false;
                }
            );
        }
    }

    getLinkedPartners() {
        if (this._discountId) {
            this.loadingLinkedPartners = true;
            this.discountService.getLinkedPartners(this.campaignId, this._discountId).subscribe(
                response => {
                    this.filteredPartners = response || [];
                    this.loadingLinkedPartners = false;
                },
                err => {
                    this.alert.showError(err, true);
                    this.loadingLinkedPartners = false;
                }
            );
        }
    }

    linkCategoryClassification() {
        if (this.filters.departmentId && this._discountId) {
            this.loading = true;
            const classification = {
                departmentId: this.filters.departmentId,
                categoryId: this.filters.categoryId || '',
                subcategoryId: this.filters.subcategoryId || ''
            };
            this.discountService.linkCategoriesClassification(this.campaignId, this._discountId, classification).subscribe(
                response => {
                    this.getLinkedCategories();
                    this.loading = false;
                },
                err => {
                    this.alert.showError(err, true);
                    this.loading = false;
                }
            );
        }
    }

    unlinkCategoryClassification($event) {
        if ($event && this._discountId) {
            this.loadingLinkedCategories = true;
            this.discountService.unlinkCategoriesClassification(this.campaignId, this._discountId, $event).subscribe(
                response => {
                    this.getLinkedCategories();
                    this.loadingLinkedCategories = false;
                },
                err => {
                    this.alert.showError(err, true);
                    this.loadingLinkedCategories = false;
                }
            );
        }
    }

    getLinkedCategories() {
        if (this._discountId) {
            this.loadingLinkedCategories = true;
            this.discountService.getLinkedCategoriesClassification(this.campaignId, this._discountId).subscribe(
                response => {
                    this.filteredCategories = response || [];
                    this.loadingLinkedCategories = false;
                },
                err => {
                    this.alert.showError(err, true);
                    this.loadingLinkedCategories = false;
                }
            );
        }
    }

    getProductDetail() {
        if (this.filters.sku.selectedPartner && this.filters.sku.skuCode) {
            this.loading = true;
            this.product.name = 'Pesquisando...';
            const partnerId = this.filters.sku.selectedPartner[0].id;
            const skuCode = this.filters.sku.skuCode;
            this.companyService.findPartnerProductBySkuCode(partnerId, skuCode).subscribe(
                product => {
                    if (!product) {
                        this.alert.showWarning('Produto não encontrado pelo SKU informado');
                        this.filters.sku.product = {};
                        this.product.name = '';
                        this.loading = false;
                    }
                    this.product = product;
                    this.loading = false;
                },
                err => {
                    this.alert.showError(err);
                    this.loading = false;
                }
            );
        }
    }

    linkProduct() {
        if (this.product.id && this._discountId) {
            this.loading = true;
            this.product.productId = this.product.id;
            this.discountService.linkSkus(this.campaignId, this._discountId, this.product).subscribe(
                response => {
                    this.getLinkedProducts();
                    this.product = {};
                    this.filters.sku.skuCode = '';
                    this.loading = false;
                },
                err => {
                    this.alert.showError(err, true);
                    this.loading = false;
                }
            );
        }
    }

    unlinkProduct($event) {
        if (this._discountId && $event) {
            this.loadingLinkedSkus = true;
            this.discountService.unlinkSkus(this.campaignId, this._discountId, $event).subscribe(
                response => {
                    this.getLinkedProducts();
                    this.loading = false;
                },
                err => {
                    this.alert.showError(err, true);
                    this.loading = false;
                }
            );
        }
    }

    getLinkedProducts() {
        if (this._discountId) {
            this.loadingLinkedSkus = true;
            this.discountService.getLinkedSkus(this.campaignId, this._discountId).subscribe(
                products => {
                    this.filteredProducts = products || [];
                    this.loadingLinkedSkus = false;
                },
                err => {
                    this.alert.showError(err, true);
                    this.loadingLinkedSkus = false;
                }
            );
        }
    }

    saveDiscount() {
        if (this.campaignId && this.discount) {
            this.loading = true;
            this.discountService.save(this.campaignId, this.discount).subscribe(
                response => {
                    if (!response) {
                        this.alert.showError('Ocorreu um erro ao salvar o desconto, tente novamente');
                        this.loading = false;
                    }

                    if (response.id) this.discount = response;
                    this.alert.showSuccess('Promoção salva com sucesso');
                    this.loading = false;
                },
                err => {
                    this.alert.showError(err, true);
                    this.loading = false;
                }
            );
        }
    }
}
