import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

import { AuthStore } from '../../../../../core/auth/auth.store';

@Component({
    selector: 'campaign-pricing-view',
    templateUrl: 'campaign-pricing-view.component.html'
})
export class CampaignPricingViewComponent implements OnInit {
    campaignId: string;

    constructor(private _authStore: AuthStore, private route: ActivatedRoute, private router: Router) { }

    ngOnInit() {
        if (this.route.parent != null) {
            this.route.parent.params.subscribe((params: any) => {
                this.campaignId = params['id'];
                if (!this.campaignId) {
                    this.router.navigate(['/campanha']);
                }
            });
        } else {
            this.router.navigate(['/campanha']);
        }
    }
    
    get canViewShipping() {
        return this._authStore.role.PERMISSION_CAMPAIGNS_SHIPPING_LIST;
    }

    get canViewFixedPrice() {
        return this._authStore.role.CAMPAIGNS_FIXED_PRODUCTS_PRICES;
    }

    get canAccessDiscounts() {
      return this._authStore.role.PERMISSION_CAMPAIGNS_CAMPAIGNS_DISCOUNTS;
    }
    
    get canViewConversionFactors() {
        return this._authStore.role.CAMPAIGNS_CONVERSION_FACTORS_VIEW;
    }
}
