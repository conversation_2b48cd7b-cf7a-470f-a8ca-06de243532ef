<gp-alert #alert [overlay]="true"></gp-alert>

<gp-card [first]="true">
    <spinner [show]="loading" [overlay]="true"></spinner>
    <div class="row">
        <accordion [closeOthers]="true">
            <accordion-group panelClass="b0 mb-sm panel-default no-shadow" [isOpen]="true" (click)="clearSkuAccordion()">
                <div accordion-heading>
                    <label><em class="fa fa-navicon"></em> Por Parceiros</label>
                </div>
                <gp-form-row>
                    <gp-form-col cols="12 6">
                        <gp-simple-input label="Parceiro" errorMessage="Parceiro é obrigatório">
                            <gp-partners-select name="partner" [(ngModel)]="conversionFactor.partnerId"
                                 (onSelected)="selectedPartner($event)"></gp-partners-select>
                        </gp-simple-input>
                    </gp-form-col>
                </gp-form-row>
                <gp-form-row>
                    <gp-form-col cols="12 6 4">
                        <gp-simple-input label="Departamento">
                            <gp-categories-select id="department" name="department" [active]="true" [level]="1"
                                (select)="department($event)" [(ngModel)]="conversionFactor.department.id">
                            </gp-categories-select>
                        </gp-simple-input>
                    </gp-form-col>
                    <gp-form-col cols="12 6 4">
                        <gp-simple-input label="Categoria">
                            <gp-categories-select id="category" name="category" [active]="true" [level]="2"
                                emptyMessage="Sem categorias" [parentId]="conversionFactor.department.id" [disabled]="!conversionFactor.department.id"
                                (select)="category($event)" [(ngModel)]="conversionFactor.category.id">
                            </gp-categories-select>
                        </gp-simple-input>
                    </gp-form-col>
                    <gp-form-col cols="12 6 4">
                        <gp-simple-input label="Subcategoria">
                            <gp-categories-select id="category" name="category" [active]="true" [level]="3"
                                emptyMessage="Sem subcategorias" [parentId]="conversionFactor.category.id" [disabled]="!conversionFactor.category.id"
                                (select)="subCategory($event)" [(ngModel)]="conversionFactor.subcategory.id">
                            </gp-categories-select>
                        </gp-simple-input>
                    </gp-form-col>
                </gp-form-row>
            </accordion-group>

            <accordion-group panelClass="b0 mb-sm panel-default no-shadow" [isOpen]="true" (click)="clearPartnerAccordion()">
                <div accordion-heading>
                    <label><em class="fa fa-navicon"></em> Por Sku</label>
                </div>
                <campaign-partner-sku-selector name="sku" [campaignId]="campaignId" [(ngModel)]="conversionFactor.sku">
                </campaign-partner-sku-selector>
            </accordion-group>
        </accordion>
    </div>
</gp-card>

<gp-card>
    <gp-form-row>
        <gp-form-col cols="12 4" [inputGroup]="false">
            <gp-simple-input label="Fator de Conversão de Pontos" [required]="true" errorMessage="Fator é obrigatório">
                <gp-input-mask [onlyDecimal]="true" required [integers]="2" [decimais]="5" [(ngModel)]="conversionFactor.pointsConversionFactor"></gp-input-mask>
            </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 4" [inputGroup]="false">
            <gp-simple-input [required]="true" errorMessage="Taxa é obrigatória" label="Taxa do Parceiro">
                <div class="input-group">
                    <span class="input-group-addon">%</span>
                    <gp-input-mask name="fee" [onlyDecimal]="true" [decimais]="3" [integers]="3" [(ngModel)]="conversionFactor.partnerFee" required></gp-input-mask>
                </div>
            </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 4">
            <label>Ativo</label>
            <div>
                <gp-switch [(ngModel)]="conversionFactor.active"></gp-switch>
            </div>
        </gp-form-col>
    </gp-form-row>
</gp-card>

<gp-card>
    <gp-form-row>
        <gp-form-col cols="12" [inputGroup]="false">
            <gp-spinner-button type="button" [pink]="true" icon="send" text="Salvar" loadingText="Processando"
                pull="right" [loading]="loading" (click)="saveConversionFactor()" marginTop="24px" marginLeft="5px">
            </gp-spinner-button>
            <gp-spinner-button type="button" bootstrapClass="default" text="Novo" loadingText="Processando" pull="right"
                [loading]="loading" (click)="clear()" marginTop="24px">
            </gp-spinner-button>
        </gp-form-col>
    </gp-form-row>
</gp-card>
