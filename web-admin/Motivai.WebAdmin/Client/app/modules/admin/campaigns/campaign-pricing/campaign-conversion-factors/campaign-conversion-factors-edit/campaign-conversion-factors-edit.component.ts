import { Component, OnInit, ViewChild } from '@angular/core';
import { CampaignStore } from '../../../campaign.store';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignFactorsService } from '../campaign-conversion-factors.service';

@Component({
    selector: 'campaign-conversion-factors-edit',
    templateUrl: './campaign-conversion-factors-edit.component.html'
})
export class CampaignConversionFactorsEditComponent implements OnInit {
    @ViewChild('alert') alert: GpAlertComponent;

    campaignId: string = '';
    departments: Array<any> = [];
    categories: Array<any> = [];
    subCategories: Array<any> = [];

    factorId: string = '';
    conversionFactor: any = {
        type: 'SKU',
        sku: {},
        department: {},
        category: {},
        subcategory: {}
    };
    loading: boolean = false;

    constructor(private _campaignStore: CampaignStore, private _factorsService: CampaignFactorsService) { }

    ngOnInit(): void {
        this.campaignId = this._campaignStore.id;
    }

    saveConversionFactor() {
        this.loading = true;
        this._factorsService.createConversionFactor(this.campaignId, this.conversionFactor).subscribe(
            factor => {
                this.alert.showSuccess('Fator salvo com sucesso.');
                this.factorId = factor;
                this.getConversionFactorById();
                this.loading = false;
            },
            err => {
                this.loading = false;
                this.alert.showError(err);
            }
        );
    }

    getConversionFactorById() {
        this.loading = true;
        this._factorsService.getConversionFactorById(this.campaignId, this.factorId).subscribe(
            factor => {
                this.conversionFactor = factor;
                if (this.conversionFactor) {
                    if (!this.conversionFactor.department)
                        this.conversionFactor.department = {};
                    if (!this.conversionFactor.category)
                        this.conversionFactor.category = {};
                    if (!this.conversionFactor.subcategory)
                        this.conversionFactor.subcategory = {};
                }
                this.loading = false;
            },
            err => {
                this.loading = false;
                this.alert.showError(err);
            }
        );
    }

    selectedPartner(event) {
        if (event) {
            this.conversionFactor.partnerName = event.name;
        } else {
            this.conversionFactor.partnerName = null;
        }
    }

    department(event) {
        if (event) {
            this.conversionFactor.department.name = event.name;
        } else {
            this.conversionFactor.department.name = null;
        }
    }

    category(event) {
        if (event) {
            this.conversionFactor.category.name = event.name;
        } else {
            this.conversionFactor.category.name = null;
        }
    }

    subCategory(event) {
        if (event) {
            this.conversionFactor.subcategory.name = event.name;
        } else {
            this.conversionFactor.subcategory.name = null;
        }
    }

    clearSkuAccordion() {
        this.conversionFactor.type = 'Classification';
        this.conversionFactor.sku = {};
    }
    
    clearPartnerAccordion() {
        this.conversionFactor.type = 'SKU';
        this.conversionFactor.partnerName = '';
        this.conversionFactor.partnerId = '';
        this.conversionFactor.category = {};
        this.conversionFactor.subcategory = {};
        this.conversionFactor.department = {};
    }

    clear() {
        this.factorId = null;
        this.conversionFactor.id = null;
        this.conversionFactor.sku = {};
        this.conversionFactor.department = { id: null };
        this.conversionFactor.category = { id: null };
        this.conversionFactor.subcategory = { id: null };
        this.conversionFactor.pointsConversionFactor = 0;
        this.conversionFactor.partnerFee = 0;
    }
}
