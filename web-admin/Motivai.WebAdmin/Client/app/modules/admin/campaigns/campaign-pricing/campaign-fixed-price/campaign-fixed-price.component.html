<gp-alert [overlay]="true" #alert></gp-alert>
<gp-card title="Fixar preço dos Produtos">
    <gp-form-row>
        <gp-form-col cols="12 3 3">
            <div>
                <label>Parceiro</label>
            </div>
            <ng-select [allowClear]="false" [items]="partners" placeholder="Parceiro" (data)="onPartnerChange($event)">
            </ng-select>
        </gp-form-col>
        <gp-form-col cols="12 3 3">
            <gp-simple-input label="SKU" [required]="true" errorMessage="SKU é obrigatório.">
                <input type="text" class="form-control" name="skuCode" [(ngModel)]="product.skuCode" (blur)="searchSku()" />
            </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 3 3">
            <gp-simple-input label="Preço Fixo em Pontos" [required]="true" errorMessage="Preço fixo é obrigatório.">
                <gp-input-mask id="fixedSalePrice" name="fixedSalePrice" [onlyDecimal]="true" [(ngModel)]="product.fixedSalePrice">
                </gp-input-mask>
            </gp-simple-input>
        </gp-form-col>

        <gp-form-col cols="12 3 3">
            <gp-spinner-button type="submit" [pink]="true" text="Salvar" loadingText="Salvando..." [loading]="sending"
                marginTop="26px" icon="plus" (click)="prepareToSave()" pull="rigth" [disabled]="disableButton"></gp-spinner-button>
        </gp-form-col>
    </gp-form-row>
    <gp-form-row>
        <gp-form-col cols="12 12 12">
            <input type="text" class="form-control" [value]="product.name" disabled />
        </gp-form-col>
    </gp-form-row>
</gp-card>

<gp-card title="Preços Fixados">
    <gp-form-row>
        <gp-form-col cols="12">
            <gp-grid [rows]="fixedProducts" [columns]="['Parceiro', 'SKU','Nome do Produto', 'Preço Fixo em Pontos']"
                [fields]="['partnerName', 'sku','productName', 'fixedSalePrice']" [loading]="loadingGrid"
                [showDelete]="true" [showEdit]="false" (onDelete)="deleteFixedPrice($event)">
            </gp-grid>
        </gp-form-col>
    </gp-form-row>
</gp-card>
