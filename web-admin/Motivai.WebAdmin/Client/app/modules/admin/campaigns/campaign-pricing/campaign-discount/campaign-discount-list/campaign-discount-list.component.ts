import { Component, OnInit, ViewChild, Output, EventEmitter } from '@angular/core';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignStore } from '../../../campaign.store';
import { CampaignDiscountService } from '../../../campaign-discount/campaign-discount.service';

@Component({
    selector: 'campaign-discount-list',
    templateUrl: 'campaign-discount-list.component.html'
})
export class GpCampaignDiscountListComponent implements OnInit {
    campaignId: string = '';
    discounts: any[] = [];
    loading: boolean = false;
    filters: any = {};
    skip: number = 0;
    limit: number = 100;

    @ViewChild('alert') alert: GpAlertComponent;
    @Output('onEdit') onEdit: EventEmitter<any> = new EventEmitter();


    constructor(private campaignStore: CampaignStore, private discountService: CampaignDiscountService) { }

    ngOnInit() {
        this.campaignId = this.campaignStore.id;
        this.findDiscounts();
    }

    findDiscounts(resetPagination: boolean = false) {
        this.loading = true;
        if (resetPagination) {
            this.skip = 0;
            this.limit = 100;
        }
        this.discountService.find(this.campaignId, this.filters.description, this.filters.type, this.filters.modality, this.skip, this.limit).subscribe(
            discounts => {
                this.discounts = discounts || [];
                this.loading = false;
            },
            err => {
                this.alert.showError(err, true);
                this.loading = false;
            }
        );
    }

    editDiscount($event) {
        if ($event.id)
            this.onEdit.emit($event.id);
    }

    onPageChanged($event) {
        if ($event) {
            this.skip = $event.skip;
            this.limit = $event.limit;
            this.findDiscounts();
        }
    }
}
