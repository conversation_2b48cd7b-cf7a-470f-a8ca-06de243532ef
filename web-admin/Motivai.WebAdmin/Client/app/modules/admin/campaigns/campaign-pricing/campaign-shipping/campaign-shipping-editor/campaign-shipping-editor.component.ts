import { TabsetComponent } from 'ng2-bootstrap/tabs';
import { Route, ActivatedRoute } from '@angular/router';
import { OnInit } from '@angular/core';
import { Component, EventEmitter, Output, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';

import { CompanyService } from '../../../../companies/company/company.service';
import { GpFileUploadComponent } from '../../../../../../shared/components/gp-fileupload/gp-fileupload.component';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignService } from '../../../campaign.service';
import { Item } from '../../../../../../shared/models/item';
import { AuthStore } from '../../../../../../core/auth/auth.store';
import { PERMISSION_CAMPAIGNS_SHIPPING_INS, PERMISISON_CAMPAIGNS_SHIPPING_EDIT, PERMISSION_CAMPAIGNS_SHIPPING_VIEW } from '../../../../../../core/auth/access-points';

@Component({
  selector: 'campaign-shipping-editor',
  templateUrl: 'campaign-shipping-editor.component.html'
})
export class CampaignShippingEditorComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @Output() shippingUpdate: EventEmitter<boolean> = new EventEmitter<boolean>();

  gpForm: FormGroup;
  messages = {
    description: {
      required: 'Nome é obrigatório'
    },
    type: {
      required: 'Tipo é obrigatório'
    },
    fixedCostType: {
      required: 'Tipo do valor é obrigatório'
    },
    fixedCostValue: {
      required: 'Valor é obrigatório'
    },
    correiosIntegration: {
      required: 'Integração com o Correios é obrigatório'
    },
    sourceCep: {
      required: 'CEP de origem é obrigatório'
    },
    additionalType: {
      required: 'Tipo é obrigatório'
    },
    additionalValue: {
      required: 'Valor é obrigatório'
    },
    feeValueType: {
      required: 'Tipo é obrigatório'
    },
    feeValue: {
      required: 'Valor é obrigatório'
    }
  };

  feeValueTypePercentage: boolean = false;
  campaignPartners: Array<any> = [];
  selectedType: string = 'Fixed';
  fixedCostTypePercentage: boolean = false;
  isCorreios: boolean = false;
  additionalTypePercentage: boolean = false;
  valueRequiredFee: boolean = false;
  valueRequiredFeeByItem: boolean = false;
  campaignId: string;
  shippingId: string;
  shipping: any = {};

  filters: any = {
    partnerId: null,
    skuCode: null,
    departmentId: null,
    categoryId: null,
    subcategoryId: null,
  };
  product: any = null;

  loading: boolean = false;
  sending: boolean = false;
  sendingFilter: boolean = false;

  constructor(private _authStore: AuthStore, private route: ActivatedRoute, fb: FormBuilder,
    private _campaignService: CampaignService, private _companyService: CompanyService) {
    this.buildForm(fb);
  }

  ngOnInit(): void {
    this.loading = true;
    if (this.route.parent != null && this.route.parent.parent != null) {
      this.route.parent.parent.params.subscribe((params: any) => {
        this.campaignId = params['id'];
        this.loadCombos();
      });
    }
  }

  private buildForm(fb: FormBuilder) {
    let typeCtrl = new FormControl(this.selectedType, Validators.required);
    let fixedCostTypeCtrl = new FormControl('Currency');
    fixedCostTypeCtrl.valueChanges.subscribe(val => this.fixedCostTypePercentage = val === 'Percentage');
    let fixedCostValueCtrl = new FormControl(null, Validators.required);
    typeCtrl.valueChanges.subscribe(val => {
      this.selectedType = val;
      if (this.isFixed) {
        fixedCostValueCtrl.enable();
      } else {
        fixedCostValueCtrl.disable();
      }
    });
    // Correios
    let correiosCtrl = new FormControl(false, Validators.required);
    let sourceCepCtrl = new FormControl(null, Validators.required);
    sourceCepCtrl.disable();
    correiosCtrl.valueChanges.subscribe(val => {
      this.isCorreios = !!val;
      if (this.isCorreios) {
        sourceCepCtrl.enable();
      } else {
        sourceCepCtrl.disable();
      }
    });


    // Acrescimos sobre o frete
    let applyFeeValueCtrl = new FormControl(false);
    let feeValueCtrl = new FormControl(null);
    applyFeeValueCtrl.valueChanges.subscribe(val => {
      this.valueRequiredFee = !!val;
      if (this.valueRequiredFee) {
        feeValueCtrl.enable();
      } else {
        feeValueCtrl.disable();
      }
    });
    let feeValueTypeCtrl = new FormControl('Currency');
    feeValueTypeCtrl.valueChanges.subscribe(val => this.additionalTypePercentage = val === 'Percentage');

    // Acrescimos por item
    let applyAdditionalValueCtrl = new FormControl(false);
    let additionalValueCtrl = new FormControl(null);
    applyAdditionalValueCtrl.valueChanges.subscribe(val => {
      this.valueRequiredFeeByItem = !!val;
      if (this.valueRequiredFeeByItem) {
        additionalValueCtrl.enable();
      } else {
        additionalValueCtrl.disable();
      }
    });
    let additionalTypeCtrl = new FormControl('Currency');
    additionalTypeCtrl.valueChanges.subscribe(val => this.additionalTypePercentage = val === 'Percentage');

    this.gpForm = fb.group({
      description: ['', Validators.required],
      type: typeCtrl,
      fixedCostType: fixedCostTypeCtrl,
      fixedCostValue: fixedCostValueCtrl,
      correiosIntegration: correiosCtrl,
      sourceCep: sourceCepCtrl,
      applyAdditionalValue: applyAdditionalValueCtrl,
      additionalType: additionalTypeCtrl,
      additionalValue: additionalValueCtrl,
      applyFeeValue: applyFeeValueCtrl,
      feeValueType: feeValueTypeCtrl,
      feeValue: feeValueCtrl,
      active: [true]
    });
  }

  get canViewShipping() {
    if (this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_SHIPPING_VIEW)) {
      return true;
    }
    this.gpAlert.showError('Você não tem permissão para acessar este módulo, por favor, contate o administrador.');
    return false;
  }

  get canCreateShipping() {
    return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_SHIPPING_INS);
  }

  get canSaveShipping() {
    if (this.shippingId)
      return this._authStore.hasPermissionTo(PERMISISON_CAMPAIGNS_SHIPPING_EDIT);
    return this.canCreateShipping;
  }

  get disableButtons() {
    return this.loading || this.sending;
  }

  get isFixed() {
    return this.selectedType === 'Fixed' || this.selectedType === 'Embedded';
  }

  get isCalculated() {
    return this.selectedType === 'Calculated';
  }

  get isFee() {
    return this.selectedType === 'ShippingCostFee';
  }

  get labelFixedCostValue() {
    return `Valor do frete (${this.fixedCostTypePercentage ? '%' : 'R$'}):`;
  }

  get labelFeeValue() {
    return `Valor do frete (${this.feeValueTypePercentage ? '%' : 'R$'}):`;
  }

  get labelAdditionalValue() {
    return `Valor de acréscimo (${this.additionalTypePercentage ? '%' : 'R$'})`;
  }

  private loadShipping(shippingId: string) {
    this.gpAlert.clear();
    if (shippingId) {
      this.loading = true;
      this._campaignService.getShipping(this.campaignId, shippingId)
        .subscribe(
          shipping => {
            if (shipping) {
              this.shippingId = shippingId;
              this.shipping = shipping;
              this.gpForm.patchValue(shipping);
              if (!this.shipping.skusFilters) {
                this.shipping.skusFilters = [];
              }
            } else {
              this.gpAlert.showWarning('Frete da campanha não encontrado.');
            }
            this.loading = false;
          },
          err => this.handleError(err)
        );
    }
  }

  private loadCombos() {
    this.loading = true;
    this._campaignService.getLinkedPartnersDataByCampaign(this.campaignId)
      .subscribe(
        linkedPartners => {
          if (linkedPartners) {
            this.campaignPartners = linkedPartners.map(part => Item.of(part.partnerId, part.partnerName));
          } else {
            this.campaignPartners = [];
          }
          this.loading = false;
        },
        err => this.handleError(err)
      );
  }

  private handleError(err) {
    let errorMessage = (err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao efetuar a operação.');
    this.gpAlert.showError(errorMessage);
    this.loading = false;
    this.sending = false;
    this.sendingFilter = false;
  }

  newShipping() {
    this.gpAlert.clear();
    this.shippingId = '';
    this.shipping = {};
    this.gpForm.reset();
    this.clearFilters();
  }

  private clearFilters() {
    this.product = {};
    this.filters = {};
  }

  public prepareEdit(shippingId: string) {
    if (shippingId) {
      this.loadShipping(shippingId);
    }
  }

  get isEdition() {
    return this.campaignId && this.shippingId && this.shipping;
  }

  get isSkuInvalid() {
    return !(this.product && this.product.skuId);
  }

  selectPartner(item: { id: string, text: string }) {
    if (item && item.id) {
      this.filters.partnerId = item.id;
    }
  }

  searchSku() {
    this.gpAlert.clear();
    this.product = { name: '' };
    let partnerId = this.filters.partnerId;
    let skuCode = this.filters.skuCode;

    if (!partnerId || !skuCode) {
      return;
    }
    this.product = { name: 'Pesquisando...' };
    this._companyService.findPartnerProductBySkuCode(partnerId, skuCode)
      .subscribe(
        product => {
          if (product) {
            this.product = product;
          } else {
            this.gpAlert.showWarning('Produto não encontrado pelo SKU informado.');
            this.product = null;
          }
        },
        err => this.handleError(err)
      );
  }

  addPartnerToFilter() {
    this.gpAlert.clear();
    if (!this.filters.partnerId) {
      this.gpAlert.showWarning('Selecione um parceiro para adicionar ao filtro.');
      return;
    }
    this.sendingFilter = true;
    this._campaignService.addPartnerToShippingFilters(this.campaignId, this.shippingId, this.filters.partnerId)
      .subscribe(
        result => {
          if (result) {
            this.clearFilters();
            this.loadShipping(this.shippingId);
            this.gpAlert.showSuccess('Parceiro adicionado no filtro com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível adicionar parceiro no filtro');
          }
          this.sendingFilter = false;
        },
        err => this.handleError(err)
      );
  }

  removePartnerFromFilter(event: any) {
    this.gpAlert.clear();
    if (!confirm(`Deseja remover o parceiro '${event.partnerName}' do filtro?`)) {
      return;
    }
    this._campaignService.removePartnerFromShippingFilters(this.campaignId, this.shippingId, event.partnerId)
      .subscribe(
        result => {
          if (result) {
            this.clearFilters();
            this.shipping.partnersFilters = this.shipping.partnersFilters.filter(p => p.partnerId != event.partnerId);
            this.gpAlert.showSuccess('Parceiro removido com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível remover o parceiro do filtro');
          }
          this.sendingFilter = false;
        },
        err => this.handleError(err)
      );
  }

  addDepartmentToFilter() {
    this.gpAlert.clear();
    if (!this.filters.departmentId) {
      this.gpAlert.showWarning('Selecione pelo menos um departamento para adicionar ao filtro.');
      return;
    }
    this.sendingFilter = true;
    this._campaignService.addClassificationToShippingFilters(this.campaignId, this.shippingId, {
      departmentId: this.filters.departmentId,
      categoryId: this.filters.categoryId,
      subcategoryId: this.filters.subcategoryId
    })
      .subscribe(
        result => {
          if (result) {
            this.clearFilters();
            this.loadShipping(this.shippingId);
            this.gpAlert.showSuccess('Parceiro adicionado no filtro com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível adicionar parceiro no filtro');
          }
          this.sendingFilter = false;
        },
        err => this.handleError(err)
      );
  }

  removeDepartmentFromFilter(event: any) {
    this.gpAlert.clear();
    if (!confirm(`Deseja remover a classificação do filtro?`)) {
      return;
    }
    this._campaignService.removeClassificationFromShippingFilters(this.campaignId, this.shippingId, event.id)
      .subscribe(
        result => {
          if (result) {
            this.clearFilters();
            this.shipping.categoriesFilters = this.shipping.categoriesFilters.filter(c => c.id != event.id);
            this.gpAlert.showSuccess('Classificação removida com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível remover a classificação do filtro');
          }
          this.sendingFilter = false;
        },
        err => this.handleError(err)
      );
  }

  addProductToFilter() {
    this.gpAlert.clear();
    if (this.isSkuInvalid) {
      this.gpAlert.showWarning('Preencha os campos para continuar.');
      return;
    }
    if (this.shipping.skusFilters) {
      if (this.shipping.skusFilters.findIndex(s => s.partnerId == this.product.partnerId && s.skuId == this.product.skuId) >= 0) {
        this.gpAlert.showWarning('SKU deste parceiro já está na lista.');
        this.product = undefined;
        this.filters.partnerId = null;
        this.filters.skuCode = null;
        return;
      }
    }
    this.sendingFilter = true;
    this._campaignService.addPartnerSkuToShippingFilters(this.campaignId, this.shippingId, this.filters.partnerId, this.product.skuId)
      .subscribe(
        result => {
          if (result) {
            this.clearFilters();
            this.loadShipping(this.shippingId);
            this.gpAlert.showSuccess('Filtro adicionado com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível adicionar o filtro');
          }
          this.sendingFilter = false;
        },
        err => this.handleError(err)
      );
  }

  removeProductFromFilter(event: any) {
    this.gpAlert.clear();
    if (!confirm(`Deseja remover o SKU '${event.productName}' do parceiro '${event.partnerName}' do filtro?`)) {
      return;
    }
    this._campaignService.removePartnerSkuFromShippingFilters(this.campaignId, this.shippingId, event.id)
      .subscribe(
        result => {
          if (result) {
            this.clearFilters();
            this.shipping.skusFilters = this.shipping.skusFilters.filter(s => s.id != event.id);
            this.gpAlert.showSuccess('SKU removido com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível remover o SKU do filtro');
          }
          this.sendingFilter = false;
        },
        err => this.handleError(err)
      );
  }

  onFormSubmit(event: any) {
    if (!event.valid) {
      this.gpAlert.showWarning('Preencha os campos corretamente para continuar.');
      return;
    }
    this.sending = true;
    this.gpAlert.clear();
    let shipping = event.model;
    if (this.shippingId) {
      shipping.id = this.shippingId;
    }
    this._campaignService.saveShipping(this.campaignId, shipping)
      .subscribe(
        result => {
          if (result) {
            if (!this.shippingId)
              this.shippingId = result;
            this.gpAlert.showSuccess('Frete da campanha salvo com sucesso.');
            this.shippingUpdate.emit(true);
          } else {
            this.gpAlert.showWarning('Não foi possível salvar o frete na campanha, por favor, tente novamente.');
          }
          this.sending = false;
        },
        err => this.handleError(err)
      );
  }
}
