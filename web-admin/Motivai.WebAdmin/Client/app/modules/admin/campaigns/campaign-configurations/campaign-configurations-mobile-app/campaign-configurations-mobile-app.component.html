<gp-form-validate (onSubmit)="onSubmit($event)">
  <spinner [overlay]="true" [show]="loading"></spinner>
  <tabset #tabs class="bg-white p0 tab-no-border" [justified]="true">
    <tab>
      <ng-template tabHeading><em class="fa fa-check"></em> Páginas</ng-template>

      <gp-card [first]="true">
        <div>
          <div class="row">
            <div class="col-sm-12">
              <h4>Habilitar Páginas</h4>
            </div>
          </div>
          <div class="row">
            <gp-input-checkbox grid="12 6 4 3" text="Dados Pessoais"
              [(ngModel)]="mobileApplicationSettings?.pages.enablePersonalData"></gp-input-checkbox>
            <!--gp-input-checkbox grid="12 6 4 3" text="Mecânica" formControlName="enableMechanic"></gp-input-checkbox-->
            <gp-input-checkbox grid="12 6 4 3" text="Cartões"
              [(ngModel)]="mobileApplicationSettings?.pages.enableCards"></gp-input-checkbox>
            <gp-input-checkbox grid="12 6 4 3" text="Endereços"
              [(ngModel)]="mobileApplicationSettings?.pages.enableAddresses"></gp-input-checkbox>
            <gp-input-checkbox grid="12 6 4 3" text="Pedidos"
              [(ngModel)]="mobileApplicationSettings?.pages.enableOrders"></gp-input-checkbox>
            <gp-input-checkbox grid="12 6 4 3" text="Extrato"
              [(ngModel)]="mobileApplicationSettings?.pages.enableExtract"></gp-input-checkbox>
            <gp-input-checkbox grid="12 6 4 3" text="Configurações > Alteração de Senha"
              [(ngModel)]="mobileApplicationSettings?.pages.enableUpdatePassword"></gp-input-checkbox>
            <gp-input-checkbox grid="12 6 4 3" text="Configurações -> Gerenciar Dispositivos"
              [(ngModel)]="mobileApplicationSettings?.pages.enableManagementDevices"></gp-input-checkbox>
          </div>
        </div>

        <div>
          <gp-spinner-button type="button" [pink]="true" icon="send" text="Salvar" loadingText="Processando"
            pull="right" [loading]="loading" (click)="saveConfigurationsMobile()" marginTop="24px">
          </gp-spinner-button>
        </div>

      </gp-card>
    </tab>


    <tab>
      <ng-template tabHeading><em class="fa fa-sliders"></em> Features</ng-template>
      <gp-card [first]="true">
        <div>
          <div class="row">
            <div class="col-sm-12">
              <h4>Habilitar Features</h4>
            </div>
          </div>
          <div class="row">
            <div grid="12 6" class="trim">
              <div grid="12 10 8">
                <label>Habilitar pagamentos:</label>
              </div>
              <div grid="12 2 4">
                <gp-switch [(ngModel)]="mobileApplicationSettings?.features.enablePayments"></gp-switch>
              </div>
            </div>
            <div grid="12 6" class="trim">
              <div grid="12 10 8">
                <label>Habilitar pague contas:</label>
              </div>
              <div grid="12 2 4">
                <gp-switch [(ngModel)]="mobileApplicationSettings?.features.enablePayAccounts"></gp-switch>
              </div>
            </div>
          </div>

          <div class="row">
            <div grid="12 6" class="trim">
              <div grid="12 10 8">
                <label>Habilitar recarga de celular:</label>
              </div>
              <div grid="12 2 4">
                <gp-switch [(ngModel)]="mobileApplicationSettings?.features.enableRechargeMobile"></gp-switch>
              </div>
            </div>
            <div grid="12 6" class="trim">
              <div grid="12 10 8">
                <label>Habilitar transferência:</label>
              </div>
              <div grid="12 2 4">
                <gp-switch [(ngModel)]="mobileApplicationSettings?.features.enableTransfers"></gp-switch>
              </div>
            </div>
          </div>
        </div>

        <div>
          <gp-spinner-button type="button" [pink]="true" icon="send" text="Salvar" loadingText="Processando"
            pull="right" [loading]="loading" (click)="saveConfigurationsMobile()" marginTop="24px">
          </gp-spinner-button>
        </div>
      </gp-card>
    </tab>
    <gp-alert #gpAlert [overlay]="true"></gp-alert>
  </tabset>
</gp-form-validate>
