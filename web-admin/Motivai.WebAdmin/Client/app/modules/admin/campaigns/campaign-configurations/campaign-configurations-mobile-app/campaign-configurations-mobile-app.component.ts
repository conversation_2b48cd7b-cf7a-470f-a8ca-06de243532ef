import { ActivatedRoute, Router } from '@angular/router';
import { Component, OnInit, ViewChild } from '@angular/core';
import { CampaignConfigurationsService } from '../campaign-configurations.service';
import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';

@Component({
  selector: 'campaign-configurations-mobile-app',
  templateUrl: './campaign-configurations-mobile-app.component.html'
})
export class CampaignConfigurationsMobileAppComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  campaignId: string;
  loadingScreen: boolean = false;
  loading: boolean = false;

  mobileApplicationSettings: {};

  constructor(private route: ActivatedRoute, private router: Router, private campaignConfigurationsService: CampaignConfigurationsService) {
  }

  ngOnInit() {
    if (this.route.parent.parent != null) {
      this.route.parent.parent.params.subscribe((params: any) => {
        this.campaignId = params['id'];
        if (!this.campaignId) {
          this.router.navigate(['/campanha']);
        } else this.findConfigurationsMobile();

      });
    }
  }


  findConfigurationsMobile() {
    this.loadingScreen = true;

    this.campaignConfigurationsService.findConfigurationsMobile(this.campaignId)
      .subscribe(
        _mobileApplicationSettings => {
          this.mobileApplicationSettings = _mobileApplicationSettings || { pages: {}, features: {} };
        },
        (err: any) => {
          this.gpAlert.handleError(err);
          this.loadingScreen = false;
        }
      );
    this.loadingScreen = false;
  }

  saveConfigurationsMobile() {
    this.loading = true;

    this.campaignConfigurationsService.saveConfigurationsMobile(this.campaignId, this.mobileApplicationSettings)
      .subscribe(
        _mobileApplicationSettings => {
          if (_mobileApplicationSettings) {
            this.gpAlert.showSuccess('Configurações do aplicativo salvas com sucesso');
          } else {
            this.gpAlert.showError('Não foi possível salvar as configurações do aplicativo. Tente novamente');
          }
          this.loading = false;
        },
        (err: any) => {
          this.gpAlert.showError(this.gpAlert.handleError(err));
          this.loading = false;
        }
      );

    this.loading = false;
  }

}

