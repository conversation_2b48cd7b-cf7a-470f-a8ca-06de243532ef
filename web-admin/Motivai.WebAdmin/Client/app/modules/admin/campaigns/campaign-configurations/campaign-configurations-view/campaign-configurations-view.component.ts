import { Component, OnInit } from '@angular/core';
import { AuthStore } from '../../../../../core/auth/auth.store';
import { ActivatedRoute, Router } from '@angular/router';
import { CampaignStore } from '../../campaign.store';
import { CampaignConfigurationsService } from '../campaign-configurations.service';

@Component({
  selector: 'campaign-configurations-view',
  templateUrl: './campaign-configurations-view.component.html'
})
export class CampaignConfigurationsViewComponent implements OnInit {
  campaignId: string;
  
  constructor(private _authStore: AuthStore, private route: ActivatedRoute, private router: Router , private _campaignStore: CampaignStore) { }

  ngOnInit() {
    if (this.route.parent != null) {
      this.route.parent.params.subscribe((params: any) => {
        this.campaignId = params['id'];
        if (!this.campaignId) {
          this.router.navigate(['/campanha']);
        }
      });
    } else {
      this.router.navigate(['/campanha']);
    }
  }

}
