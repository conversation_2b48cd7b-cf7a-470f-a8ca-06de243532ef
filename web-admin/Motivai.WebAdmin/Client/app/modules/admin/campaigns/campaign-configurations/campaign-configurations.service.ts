import { Injectable } from '@angular/core';
import { ApiService } from '../../../../core/api/api.service';

@Injectable()
export class CampaignConfigurationsService {

  constructor(private api: ApiService) { }

  saveConfigurationsMobile(campaignId: string, campaignConfigurationsMobile: any): any {
    return this.api.put(`/api/campaigns/${campaignId}/mobile/settings`, campaignConfigurationsMobile, 20000);

  }

  findConfigurationsMobile(campaignId: string): any {
    return this.api.get(`/api/campaigns/${campaignId}/mobile/settings`);
  }

}
