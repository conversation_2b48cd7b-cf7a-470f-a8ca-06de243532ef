<div *ngIf="!isRegistrationDataFromRootCampaign">
  <gp-card title="Configurações de Integração" [first]="true">
    <gp-form-row>
      <gp-form-col cols="12 6 6">
        <gp-simple-input label="Integrar com">
          <select class="form-control" name="integrator" [(ngModel)]="integrationSettings.integrator"
            [ngModelOptions]="{standalone: true}">
            <option value="None">Desabilitada</option>
            <option value="Fzcom">Fzcom</option>
            <option value="Gopague">Go Pague</option>
          </select>
        </gp-simple-input>
      </gp-form-col>
      <gp-form-col cols="12 6 6">
        <gp-simple-input label="Código da Campanha">
          <input type="text" class="form-control" name="clientCampaignId"
            [(ngModel)]="integrationSettings.clientCampaignId" [ngModelOptions]="{standalone: true}" />
        </gp-simple-input>
      </gp-form-col>
    </gp-form-row>
  </gp-card>

  <gp-card title="Parametrizações disponíveis para esse tipo de integração" *ngIf="hasTemplateIntegrationParameters()">
    <gp-form-row>
      <gp-form-col cols="12 12 12">
        <ng-container *ngFor="let item of templateIntegrationParameters">
        <ul>
          <li ><strong>Parametro: </strong> {{ item.parametro }} </li>
          <ul>
            <li> <strong>Exemplo: </strong> {{ item.exemplo }} </li>
            <li> <strong>Descrição: </strong> {{ item.descricao }} </li>
          </ul>
        </ul>
      </ng-container>
      </gp-form-col>
    </gp-form-row>
  </gp-card>

  <gp-card title="Configurações para Go Pague" *ngIf="useGopagueIntegrator">
    <gp-form-row>
      <gp-form-col cols="12 6 6">
        <gp-simple-input label="Token">
          <input type="text" class="form-control" name="token" [(ngModel)]="integrationSettings.token"
            [ngModelOptions]="{standalone: true}" />
        </gp-simple-input>
      </gp-form-col>
      <gp-form-col cols="12 6 6">
        <gp-simple-input label="Accept">
          <input type="text" class="form-control" name="accept" [(ngModel)]="integrationSettings.accept"
            [ngModelOptions]="{standalone: true}" />
        </gp-simple-input>
      </gp-form-col>
    </gp-form-row>
  </gp-card>

  <gp-card title="Configurações para FZcom" *ngIf="useFzcomIntegrator">
    <gp-form-row>
      <gp-form-col cols="12 6 6">
        <gp-simple-input label="Login">
          <input type="text" class="form-control" name="login" [(ngModel)]="integrationSettings.fzcomSettings.login"
            [ngModelOptions]="{standalone: true}" />
        </gp-simple-input>
      </gp-form-col>
      <gp-form-col cols="12 6 6">
        <gp-simple-input label="Senha">
          <input type="text" class="form-control" name="password"
            [(ngModel)]="integrationSettings.fzcomSettings.password" [ngModelOptions]="{standalone: true}" />
        </gp-simple-input>
      </gp-form-col>
    </gp-form-row>
  </gp-card>

  <gp-card title="Cadastro de parâmetros da integração" *ngIf="!useNoneIntegrator && !isDefaultIntegration">
    <form>
      <gp-form-row>
        <gp-form-col cols="12 3 3">
          <gp-simple-input label="Parâmetro">
            <input type="text" class="form-control" name="name" [(ngModel)]="externalIntegrationParameters.name" />
          </gp-simple-input>
        </gp-form-col>

        <gp-form-col cols="12 3 3">
          <gp-simple-input label="Descrição">
            <input type="text" class="form-control" name="placeholder"
              [(ngModel)]="externalIntegrationParameters.description" />
          </gp-simple-input>
        </gp-form-col>

        <gp-form-col cols="12 3 3">
          <gp-simple-input label="Valor">
            <input type="text" class="form-control" name="value" [(ngModel)]="externalIntegrationParameters.value" />
          </gp-simple-input>
        </gp-form-col>

        <gp-form-col cols="12 3 3" additionalClasses="top-p2">
          <gp-spinner-button type="button" pull="right" buttonClass="bg-primary-dark" text="Adicionar" icon="plus"
            (click)="addParamsExternalIntegration(externalIntegrationParameters)">
          </gp-spinner-button>
        </gp-form-col>
      </gp-form-row>
    </form>

    <div *ngIf="hasExternalIntegrationSettings">
      <hr>

      <h4>Parâmetros Cadastrados</h4>
      <gp-form-row *ngFor="let settings of integrationSettings.externalIntegrationSettings">
        <gp-form-col cols="12 3 3">
          <gp-simple-input label="Parâmetro">
            <input type="text" class="form-control" name="name" [(ngModel)]="settings.name"
              [ngModelOptions]="{standalone: true}" />
          </gp-simple-input>
        </gp-form-col>

        <gp-form-col cols="12 3 3">
          <gp-simple-input label="Descrição">
            <input type="text" class="form-control" name="description" [(ngModel)]="settings.description"
              [ngModelOptions]="{standalone: true}" />
          </gp-simple-input>
        </gp-form-col>

        <gp-form-col cols="12 3 3">
          <gp-simple-input label="Valor">
            <input type="text" class="form-control" name="value" [(ngModel)]="settings.value"
              [ngModelOptions]="{standalone: true}" />
          </gp-simple-input>
        </gp-form-col>

        <gp-form-col cols="12 3 3" additionalClasses="top-p2">
          <gp-spinner-button type="button" pull="right" buttonClass="bg-danger" text="Remover" icon="remove"
            (click)="removeParamsExternalIntegration(settings.name)">
          </gp-spinner-button>
        </gp-form-col>
      </gp-form-row>
    </div>
  </gp-card>

  <gp-card title="O que será integrado" *ngIf="useAnyIntegrator">
    <gp-form-row>
      <gp-input-checkbox grid="12 4 2" text="Login" name="enableLogin" [(ngModel)]="integrationSettings.enableLogin"
        [ngModelOptions]="{standalone: true}">
      </gp-input-checkbox>
      <gp-input-checkbox grid="12 4 2" text="Atualização Cadastral" name="enableChangeRegistrationData"
      [(ngModel)]="integrationSettings.enableChangeRegistrationData" [ngModelOptions]="{standalone: true}">
    </gp-input-checkbox>
      <gp-input-checkbox grid="12 4 2" text="Alteração de Senha" name="enableChangePassword"
        [(ngModel)]="integrationSettings.enableChangePassword" [ngModelOptions]="{standalone: true}">
      </gp-input-checkbox>
      <gp-input-checkbox grid="12 4 2" text="Recuperação de Senha" name="enableRecoveryPassword"
        [(ngModel)]="integrationSettings.enableRecoveryPassword" [ngModelOptions]="{standalone: true}">
      </gp-input-checkbox>

      <gp-input-checkbox grid="12 4 2" text="Primeiro Acesso" name="enableFirstAccess"
        [(ngModel)]="integrationSettings.enableFirstAccess" [ngModelOptions]="{standalone: true}">
      </gp-input-checkbox>
      <gp-input-checkbox grid="12 4 2" text="Dados de Cadastro" name="enableRegistrationData"
        [(ngModel)]="integrationSettings.enableRegistrationData" [ngModelOptions]="{standalone: true}">
      </gp-input-checkbox>
    </gp-form-row>
    <gp-form-row>
      <gp-input-checkbox grid="12 4 2" text="Saldo" name="enableBalance" [(ngModel)]="integrationSettings.enableBalance"
        [ngModelOptions]="{standalone: true}">
      </gp-input-checkbox>
      <gp-input-checkbox grid="12 4 2" text="Extrato do Catálogo" name="enableSummary"
        [(ngModel)]="integrationSettings.enableSummaryAtWebCatalog" [ngModelOptions]="{standalone: true}">
      </gp-input-checkbox>
      <gp-input-checkbox grid="12 4 2" text="Extrato do Site Campanha" name="enableSummaryAtCampaignsSite"
        [(ngModel)]="integrationSettings.enableSummaryAtCampaignsSite" [ngModelOptions]="{standalone: true}">
      </gp-input-checkbox>
      <gp-input-checkbox grid="12 4 2" text="Expiração de pontos" name="enableExpiringPoints"
        [(ngModel)]="integrationSettings.enableExpiringPoints" [ngModelOptions]="{standalone: true}">
      </gp-input-checkbox>
      <gp-input-checkbox grid="12 4 2" text="Crédito de Pontos" name="enablePointsCredit"
        [(ngModel)]="integrationSettings.enablePointsCredit" [ngModelOptions]="{standalone: true}">
      </gp-input-checkbox>
      <gp-input-checkbox grid="12 4 2" text="Ranking" name="enableRanking"
        [(ngModel)]="integrationSettings.enableRanking" [ngModelOptions]="{standalone: true}">
      </gp-input-checkbox>
      <gp-input-checkbox grid="12 4 2" text="Estorno" name="enableRefund" [(ngModel)]="integrationSettings.enableRefund"
        [ngModelOptions]="{standalone: true}">
      </gp-input-checkbox>
      <gp-input-checkbox grid="12 4 2" text="Pré-Cadastro" name="enablePreRegistration"
      [(ngModel)]="integrationSettings.enablePreRegistration" [ngModelOptions]="{standalone: true}">
      </gp-input-checkbox>
      <gp-input-checkbox grid="12 4 2" text="Convidar Participante" name="enableInviteParticipant"
      [(ngModel)]="integrationSettings.enableInviteParticipant" [ngModelOptions]="{standalone: true}">
      </gp-input-checkbox>
    </gp-form-row>

    <hr />

    <gp-form-row>
      <gp-form-col cols="12 12 12">
        <gp-simple-input label="URL de autenticação do catálogo">
          <input type="text" class="form-control" placeholder="URL de autenticação para catálogo do cliente"
            name="clientCatalogUrl" [(ngModel)]="integrationSettings.clientCatalogUrl"
            [ngModelOptions]="{standalone: true}" />
        </gp-simple-input>
      </gp-form-col>
    </gp-form-row>
  </gp-card>

  <app-campaign-integration-oauth2 [oAuth2Settings]="integrationSettings.oAuth2Settings"
    *ngIf="isAuthenticationWithOAuth2">
  </app-campaign-integration-oauth2>
</div>

<div *ngIf="isRegistrationDataFromRootCampaign">
  <gp-card title="Configurações de Integração" [first]="true">
    <h4 style="text-align: center;">As configurações de integração foram herdadas da campanha raiz</h4>
  </gp-card>
</div>
