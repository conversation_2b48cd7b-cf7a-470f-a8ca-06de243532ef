import { Component, forwardRef, Input } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';


import { CampaignFactory } from '../../campaign.factory';

@Component({
    selector: 'campaign-preregister-params',
    templateUrl: './campaign-preregister-params.component.html',
    providers: [
        { provide: NG_VALUE_ACCESSOR, useExisting: forwardRef(() => CampaignPreRegisterParamComponent), multi: true }
    ]
})
export class CampaignPreRegisterParamComponent implements ControlValueAccessor {
    @Input() registrationDataFromRootCampaign: boolean = false;

    value: any = CampaignFactory.createFirstPreRegisterSettings();

    writeValue(obj: any): void {
        if (obj) {
            this.value = obj;
        } else {
            this.value = CampaignFactory.createFirstPreRegisterSettings();
        }

        if (!this.value.parentParticipant) {
            this.value.parentParticipant = {
                searchField: {}
            }
        }
        if (!this.value.viewParametrizations) {
            this.value.viewParametrizations = {
              enablePreRegistrationFinishMessage: false,
              preRegistrationFinishMessage: ''
            }
        }
    }

    get isRegistrationDataFromRootCampaign() {
        return this.registrationDataFromRootCampaign;
    }

    onChange = () => {};
    onTouched = () => {};

    registerOnChange(fn: any): void {
        this.onChange = fn;
    }

    registerOnTouched(fn: any): void {
        this.onTouched = fn;
    }
}

