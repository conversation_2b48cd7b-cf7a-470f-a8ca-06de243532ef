<gp-alert [overlay]="true" #gpAlert></gp-alert>

<gp-form-validate [formGroup]="campaignForm" [formErrors]="formErrors" [validationMessages]="messages"
	(onSubmit)="onSubmit($event)">
	<tabset #tabs class="bg-white p0 tab-no-border" [justified]="true" *ngIf="canViewBasicCampaign">
		<tab>
			<ng-template tabHeading><em class="fa fa-list-alt"></em> Dados da Campanha</ng-template>
			<gp-card title="Configuração da Campanha" [first]="true">
				<div class="row">
					<div grid="12 6 6" [group]=true *ngIf="!canEditCampaignConfiguration">
						<label>Tipo de Camapanha</label>
						<input type="text" class="form-control" [readonly]="true"
							[value]="handleTypeCampaign(campaign.type)" />
					</div>
					<gp-select-validate cols="12 6 6" label="Tipo da Campanha" formControlName="type"
						*ngIf="canEditCampaignConfiguration">
						<option value="">Selecione</option>
						<option value="Rewards">Incentivo & Fidelidade</option>
						<option value="B2B">B2B</option>
						<option value="Marketplace">Marketplace</option>
					</gp-select-validate>
					<div grid="12 6 6" [group]=true *ngIf="!canEditCampaignConfiguration">
						<label>Tipo da Moeda</label>
						<input type="text" class="form-control" [readonly]="true" [value]="campaign.coinType" />
					</div>
					<gp-select-validate cols="12 6 6" label="Tipo da Moeda" formControlName="coinType"
						*ngIf="canEditCampaignConfiguration">
						<option value="">Selecione</option>
						<option value="Pontos">Pontos</option>
						<option value="Reais">Reais</option>
					</gp-select-validate>

				</div>
				<div class="row">
					<div grid="12 3 3" [group]=true *ngIf="!canEditCampaignConfiguration">
						<label>Modalidade da Campanha</label>
						<input type="text" class="form-control" [readonly]="true"
							[value]="handleModalityDescription(campaign.modality)" />
					</div>
					<gp-select-validate cols="12 3 3" label="Modalidade da Campanha" formControlName="modality"
						*ngIf="canEditCampaignConfiguration">
						<option value="">Selecione</option>
						<option value="OnlyCatalog">Catálogo</option>
						<option value="OnlySite">Site Campanha</option>
						<option value="CatalogWithSite">Catálogo + Site Campanha</option>
					</gp-select-validate>
					<div grid="12 3 3" [group]=true *ngIf="!canEditCampaignConfiguration">
						<label>Licença da Campanha</label>
						<input type="text" class="form-control" [readonly]="true"
							[value]="handleLicenseDescription(campaign.license)" />
					</div>
					<gp-select-validate cols="12 3 3" label="Licença da Campanha" formControlName="license"
						*ngIf="canEditCampaignConfiguration">
						<option value="">Selecione</option>
						<option value="MAKE_YOUR_CAMPAIGN">Monte sua Campanha</option>
						<option value="FULL">Campanha Completa</option>
					</gp-select-validate>
					<div grid="12 3 3" [group]="true" style="margin-right: 15px">
						<div class="form-group">
							<label>Ativo</label>
							<div>
								<gp-switch formControlName="active"></gp-switch>
							</div>
						</div>
					</div>
				</div>
			</gp-card>

			<gp-card title="Dados da Campanha">
				<div class="row">
					<div grid="12 12 12" [group]="true" *ngIf="isEdition">
						<label>Cliente</label>
						<input type="text" class="form-control" [readonly]="true" [value]="campaign.clientName" />
					</div>
					<gp-select-validate searchable="true" cols="12 12 12" label="Cliente" formControlName="clientId"
						[items]="clients" *ngIf="!isEdition"></gp-select-validate>
				</div>
				<div class="row" *ngIf="canAssignBu">
					<div class="form-group" grid="12 12 12">
						<label>Unidade de Negócio</label>
						<gp-bu-select formControlName="buId"></gp-bu-select>
					</div>
				</div>
				<div class="row">
					<gp-input-validate cols="12 12 12" label="Nome da Campanha" formControlName="name">
					</gp-input-validate>
				</div>
				<div class="row">
					<gp-datepicker cols="12 6 6 6" label="Data de Início" formControlName="startDate">
					</gp-datepicker>
					<gp-datepicker cols="12 6 6 6" label="Data de Término" formControlName="endDate">
					</gp-datepicker>
				</div>
				<div class="row" *ngIf="isCampaignRewards">
					<gp-input-validate cols="12 12 6 6" label="Prefixo da Moeda" formControlName="coinPrefix"
						[required]="false">
					</gp-input-validate>
					<gp-input-validate cols="12 12 6 6" label="Sufixo da Moeda" formControlName="coinName"
						[required]="false">
					</gp-input-validate>
				</div>
				<div class="row">
					<gp-mask-validate cols="12 6 6 6" label="Fator de conversão para os pontos (1 ponto = R$ ?)"
						formControlName="pointsConversionFactor" [onlyDecimal]="true" [decimais]="5"></gp-mask-validate>
					<gp-mask-validate cols="12 6 6 6" label="Taxa de Administração para os Parceiros (10% = 0.1)"
						formControlName="productConversionFactor" [onlyDecimal]="true" [decimais]="5">
					</gp-mask-validate>
				</div>
			</gp-card>
			<spinner [overlay]="true" [show]="loading"></spinner>

			<gp-card title="Mecânica de pontos">
				<div class="row">
					<div grid="12 6 6">
						<label>Tipo de prazo</label>
						<select class="form-control input-sm" name="pointsExpirationType"
							formControlName="pointsExpirationType">
							<option value="FixedDate">Data Fixa</option>
							<option value="Period">Prazo</option>
						</select>
					</div>

					<div grid="12 6 6" *ngIf="useExpirationFixedDateSettings">
						<gp-datepicker cols="12 12 12" label="Data de expiração dos pontos"
							formControlName="pointsExpirationDate">
						</gp-datepicker>
					</div>

					<div grid="12 6 6" *ngIf="useExpirationPeriodSettings">
						<gp-input-validate cols="12 12 12" label="Dias para expiração dos pontos"
							formControlName="pointsExpirationPeriod" placeholder="365" [required]="false">
						</gp-input-validate>
					</div>
				</div>
			</gp-card>

			<div *ngIf="!loading">
				<gp-card title="Dados do Catálogo" *ngIf="hasCatalog">
					<div class="row">
						<gp-input-validate cols="12 12 6 6" label="URL do Catálogo" formControlName="url"
							placeholder="http://www.minha-campanha.com.br" [required]="false" [readonly]="!isRootBu">
						</gp-input-validate>
						<gp-input-validate cols="12 12 6 6" label="Código Google Analytics para o Catálogo"
							formControlName="googleAnalyticsCode" placeholder="UA-XXXXX-Y" [required]="false">
						</gp-input-validate>
					</div>
				</gp-card>

				<gp-card title="Dados do Site Campanha" *ngIf="hasCampaignSite" formGroupName="campaignSite">
					<div class="row">
						<gp-input-validate cols="12 6 6" label="URL do Site Campanha" groupName="campaignSite"
							formControlName="url" [required]="false" placeholder="http://www.meu-site-campanha.com.br"
							[readonly]="!isRootBu">
						</gp-input-validate>
						<gp-input-validate cols="12 6 6" label="Código Google Analytics para o Site Campanha"
							formControlName="googleAnalyticsCode" placeholder="UA-XXXXX-Y" [required]="false">
						</gp-input-validate>
					</div>
				</gp-card>

				<gp-card title="Dados de Atendimento">
					<gp-form-row>
						<gp-input-validate label="Telefone" [required]="false" formControlName="attendancePhoneNumber">
						</gp-input-validate>
						<gp-input-validate label="Horário de Funcionamento" [required]="false"
							formControlName="attendancePeriod"></gp-input-validate>
					</gp-form-row>
					<gp-form-row>
						<gp-input-validate label="Celular" [required]="false" formControlName="attendanceMobileNumber">
						</gp-input-validate>
						<gp-input-validate label="E-mail" [required]="false" formControlName="attendanceEmail">
						</gp-input-validate>
					</gp-form-row>
				</gp-card>

				<div class="row" *ngIf="canCreateOrEditCampaign">
					<div class="col-xs-12 col-sm-6" [ngClass]="{'col-sm-12':!hasCampaignSite}" *ngIf="hasCatalog">
						<gp-card title="Arquivo de Layout do Catálogo">
							<div class="row">
								<div class="col-xs-12 col-md-12">
									<label>Selecione o arquivo ZIP com a customização</label>
								</div>
							</div>
							<div class="row" style="height:70px;">
								<div class="col-xs-12 col-md-6">
									<gp-fileupload name="layout" label="Selecione o ZIP" [zip]="true"
										(oncomplete)="onCompleteCatalog($event)" #gpCatalogLayout></gp-fileupload>
								</div>
								<div class="col-xs-12 col-md-6">
									<gp-spinner-button type="button" text="Remover layout do catálogo" [pink]="true"
										icon="trash" [disabled]="disableButtons" [loading]="removing"
										loadingText="Removendo" (click)="removeCurrentCatalogLayout()"
										*ngIf="hasCustomLayout"></gp-spinner-button>
								</div>
							</div>
						</gp-card>
					</div>

					<div class="col-xs-12 col-sm-6" [ngClass]="{'col-sm-12':!hasCatalog}" *ngIf="hasCampaignSite">
						<gp-card title="Arquivo de Layout do Site Campanha">
							<div class="row">
								<div class="col-xs-12 col-md-12">
									<label>Selecione o arquivo ZIP com a customização</label>
								</div>
							</div>
							<div class="row" style="height:70px;">
								<div class="col-xs-12 col-md-6">
									<gp-fileupload name="siteLayout" label="Selecione o ZIP" [zip]="true"
										(oncomplete)="onCompleteSite($event)" #gpSiteLayout>
									</gp-fileupload>
								</div>
								<div class="col-xs-12 col-md-6">
									<gp-spinner-button type="button" text="Remover layout do site" [pink]="true"
										icon="trash" [disabled]="disableButtons" [loading]="removing"
										loadingText="Removendo" (click)="removeCurrentSiteLayout()"
										*ngIf="hasCustomSiteLayout">
									</gp-spinner-button>
								</div>
							</div>
						</gp-card>
					</div>
				</div>
			</div>
		</tab>

		<tab *ngIf="canViewFullCampaign">
			<ng-template tabHeading><em class="fa fa-sliders"></em> Parâmetros</ng-template>

			<gp-card [first]="true">
				<accordion [closeOthers]="true" *ngIf="!loading">
					<accordion-group panelClass="b0 mb-sm panel-default" [isOpen]="true">
						<div accordion-heading>
							<label>Configurações de Processo</label>
						</div>
						<div class="param-panel" formGroupName="parametrizations">
							<gp-card title="Parametrizações da Campanha">
								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="8" class="input-param-text">
											<label>Layout do site campanha*
												<gp-tooltip spanClass="text-primary">
													<div style="text-align:justify">
														Define o modelo de layout do site campanha (ignorado quando
														modalidade for somente catálogo)
														<ul class="tooltip-list">
															<li>Site padrão: Site de conteúdo padrão.</li>
															<li>Clube de Benefícios: Site com padrão de clube de
																benefícios.</li>
															<li>Social: Site com padrão de rede social.</li>
															<li>Site com Painel: Site de conteúdo padrão com painel com
																resumo da conta.</li>
														</ul>
													</div>
												</gp-tooltip>:
											</label>
										</div>
										<div grid="4">
											<select class="form-control input-sm" formControlName="campaignSiteLayout"
												required>
												<option value="Default">Site Padrão</option>
												<option value="BenefitsClub">Clube de Benefícios</option>
												<option value="Social">Social</option>
												<option value="DefaultPanel">Site com Painel</option>
											</select>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="8" class="input-param-text">
											<label>Habilitar menu customizado por grupo:</label>
										</div>
										<div grid="4">
											<gp-switch formControlName="enableCustomMenuByGroup"></gp-switch>
										</div>
									</div>
								</div>

								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="8" class="input-param-text">
											<label>Habilitar campos dinâmicos:</label>
										</div>
										<div grid="4">
											<gp-switch formControlName="enableMetadataFields"></gp-switch>
										</div>
									</div>
								</div>

								<hr class="small" />
								<h5>Expiração de Pontos</h5>

								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="8" class="input-param-text">
											<label>Habilitar expiração de pontos automática:</label>
										</div>
										<div grid="4">
											<gp-switch formControlName="enableAutomaticPointsExpiration"></gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="8" class="input-param-text">
											<label>Habilitar notificação de expiração de campanhas</label>
										</div>
										<div grid="4">
											<gp-switch formControlName="enablePointsExpiringNotification"></gp-switch>
										</div>
									</div>
								</div>

								<hr class="small" />
								<h5>Convite de Participantes</h5>

								<gp-form-row>
									<div grid="12 6" class="trim">
										<div grid="8" class="input-param-text">
											<label>Tipo de Pessoa que pode convidar participantes*:</label>
										</div>
										<div grid="4">
											<select class="form-control input-sm"
												formControlName="personTypeAllowedToInviteNewParticipant"
												[attr.disabled]="isRegistrationDataFromRootCampaign === true ? true : null"
												required>
												<option value="BOTH">Ambos</option>
												<option value="Fisica">Física</option>
												<option value="Juridica">Jurídica</option>
											</select>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="12 10 8" class="input-param-text">
											<label>Exibir os participantes convidados:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch [disabled]="isRegistrationDataFromRootCampaign"
												formControlName="showInvitedParticipantsByParent"></gp-switch>
										</div>
									</div>
								</gp-form-row>

								<gp-form-row>
									<div grid="12 6" class="trim">
										<div grid="8" class="input-param-text">
											<label>Permitir bloquear um participante convidado:</label>
										</div>
										<div grid="4">
											<gp-switch [disabled]="isRegistrationDataFromRootCampaign"
												formControlName="enableParentBlockInvitedParticipant"></gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="8" class="input-param-text">
											<label>Permitir consultar os lançamentos do participante convidado:</label>
										</div>
										<div grid="4">
											<gp-switch [disabled]="isRegistrationDataFromRootCampaign"
												formControlName="allowViewInvitedParticipantsDynamicFormFilled">
											</gp-switch>
										</div>
									</div>

								</gp-form-row>
							</gp-card>

							<gp-card>
								<h5>Marketing Geral da Plataforma</h5>
								<gp-form-row>
									<div grid="12 6" class="trim">
										<div grid="8" class="input-param-text">
											<label>Não exibir os mediaboxes gerais da plataforma:</label>
										</div>
										<div grid="4">
											<gp-switch [disabled]="isRegistrationDataFromRootCampaign"
												formControlName="skipPlatformMediaboxes">
											</gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="8" class="input-param-text">
											<label>Não exibir as lojas especiais gerais da plataforma:</label>
										</div>
										<div grid="4">
											<gp-switch [disabled]="isRegistrationDataFromRootCampaign"
												formControlName="skipPlatformSpecialShops">
											</gp-switch>
										</div>
									</div>
								</gp-form-row>
							</gp-card>

							<gp-card title="Parametrizações das Importações">
								<h5>Importação de Participantes</h5>

								<gp-form-row>
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Gerar código do participante automáticamente:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch [disabled]="isRegistrationDataFromRootCampaign"
												formControlName="enableGenerateClientUserId">
											</gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitar histórico na importação de participantes:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="enableParticipantsHistoricAtImporting">
											</gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Rodar público alvo nos novos participantes:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="executeTargetAudienceForNewParticipant">
											</gp-switch>
										</div>
									</div>
								</gp-form-row>

								<hr class="small" />
								<h5>Importação de Pontos</h5>

								<gp-form-row>
									<div grid="12 6" class="trim">
										<div grid="8" class="input-param-text">
											<label>Unicidade do código da transação na importação de pontos
												<gp-tooltip spanClass="text-primary">
													<div style="text-align:justify">
														Define a forma de validação do código da transação
														(identificador do ponto) na importação:
														<ul class="tooltip-list">
															<li>Por campanha: o código da transação precisa ser único
																por campanha.</li>
															<li>Por participante: o código da transação precisa ser
																único por participante.</li>
														</ul>
													</div>
												</gp-tooltip>:
											</label>
										</div>
										<div grid="4">
											<select class="form-control input-sm"
												formControlName="pointsImportTransactionCodeUniquenessRule" required>
												<option value="CAMPAIGN">Por Campanha</option>
												<option value="PARTICIPANT">Por Participante</option>
											</select>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Permitir cadastrar o participante durante a importação caso campanha
												esteja em coalizão:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="allowCreateParticipantOnPointsImporting">
											</gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Pular aprovação da importação de pontos:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="skipApprovalOnPointsImporting"></gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="8" class="input-param-text">
											<label>Momento da importação de pedidos para notificação:</label>
										</div>
										<div grid="4">
											<select class="form-control input-sm" formControlName="importMomentToNotify"
												required>
												<option value="Disabled">Desabilitado</option>
												<option value="Import">Importação</option>
												<option value="Approve">Aprovação</option>
											</select>
											<span *ngIf="formErrors['importMomentToNotify']"
												class="text-danger">{{formErrors['importMomentToNotify']}}</span>
										</div>
									</div>
								</gp-form-row>
								<hr />
								<gp-form-row *ngIf="canViewCampaignFeaturesApprovers">
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Solicitar aprovação do administrador:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="requireSpecificUserAdministratorApproval">
											</gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="12 10 4">
											<label>Selecione o Administrador:</label>
										</div>
										<div grid="12 6">
											<user-administrator-selector *ngIf="canAddCampaignFeaturesApprovers"
												name="userAdministratorId"
												[disable]="!requireSpecificUserAdministratorApproval"
												(select)="setUserAdministrator($event)"></user-administrator-selector>
										</div>
										<div grid="12 2">
											<gp-spinner-button [pink]="true" *ngIf="canAddCampaignFeaturesApprovers"
												[disable]="!requireSpecificUserAdministratorApproval" icon="plus"
												(click)="addCampaignFeaturesApprover()"></gp-spinner-button>
										</div>
									</div>
								</gp-form-row>
								<gp-form-row *ngIf="canViewCampaignFeaturesApprovers">
									<gp-form-col cols="12">
										<gp-grid name="campaignFeaturesApprovers" [rows]="campaignFeaturesApprovers"
											[columns]="['Nome']" [fields]="['name']" [loading]="loading"
											[showActive]="false" [showPagination]="false" [showEdit]="false"
											[showDelete]="canRemoveCampaignFeaturesApprovers"
											emptyMessage="Nenhum administrador adicionada."
											(onDelete)="removeCampaignFeaturesApprover($event)"></gp-grid>
									</gp-form-col>
								</gp-form-row>
							</gp-card>

							<gp-card title="Parametrizações de Login">
								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="8" class="input-param-text">
											<label>Forma de login*
												<gp-tooltip spanClass="text-primary">
													<div style="text-align:justify">
														Define a forma do participante se autenticar no catálogo e no
														site campanha.
														<ul class="tooltip-list">
															<li>Login e Senha: O participante digita seu login e senha.
															</li>
															<li>Login e Senha Integrado: O participante digita seu login
																e senha que será validado via integração.</li>
															<li>Login e Senha ou Login e Senha Integrado: Permite
																selecionar
																se usuará login e senha ou SSO OAuth.</li>
															<li>Single Sign-On: O participante digita seu login e então
																é redirecionado para uma página de autenticação que
																depende da integração.</li>
															<li>Login e Senha ou SSO OAuth: Permite selecionar
																se usará login e senha ou SSO OAuth.</li>
															<li>SAML: O participante informa seus dados de autenticação
																que é validado pelo provedor de autenticação do cliente.
																O provedor de autenticação é configurado via integração.
															</li>
														</ul>
													</div>
												</gp-tooltip>:
											</label>
										</div>
										<div grid="4">
											<select class="form-control input-sm" formControlName="loginType"
												[attr.disabled]="isRegistrationDataFromRootCampaign === true ? true : null"
												required>
												<option value="Password">Login e Senha</option>
												<option value="SSO">Single Sign-On</option>
												<option value="SAML">Saml</option>
												<option value="IntegratedPassword">Login e Senha Integrado</option>
												<option value="PrioritizeInternalBeforeIntegratedPassword">Login e Senha
													Interno antes Integrado</option>
											</select>
											<span *ngIf="formErrors['loginType']"
												class="text-danger">{{formErrors['loginType']}}</span>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitar login via site:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch [disabled]="isRegistrationDataFromRootCampaign"
												formControlName="enableLoginSite"
												title="Define se a campanha permitirá os participantes efetuar login através da página de login">
											</gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitar login via catálogo:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch [disabled]="isRegistrationDataFromRootCampaign"
												formControlName="enableLoginCatalog"
												title="Define se a campanha permitirá os participantes efetuar login através da página de login no Catálogo">
											</gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Permite login de operadores:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch [disabled]="isRegistrationDataFromRootCampaign"
												formControlName="allowAccountOperators"
												title="Parâmetro para definir se a campanha permite login de operadores">
											</gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Redirecionar página automaticamente
												<gp-tooltip spanClass="text-primary">
													<div style="text-align:justify">
														A página de login será redirecionada automaticamente ao provedor
														de autenticação cadastrado na integração.
													</div>
												</gp-tooltip>:
											</label>
										</div>
										<div grid="4">
											<gp-switch [disabled]="isRegistrationDataFromRootCampaign"
												formControlName="redirectAutomaticallyWhenLoginWithSso">
											</gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Permite notificação do Login:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="allowLoginNotification"
												title="Parâmetro para definir se a campanha permite notificação de login">
											</gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitar campos dinâmicos na recuperação de senha:</label>
										</div>
										<div grid="4">
											<gp-switch [disabled]="isRegistrationDataFromRootCampaign"
												formControlName="enableMetadataFieldsAtParticipantPasswordRecovery">
											</gp-switch>
										</div>
									</div>
								</div>
								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitar mensagem de erro customizada no login:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="enableLoginCustomErrorMessage"></gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="12 12">
											<label>Mensagem de erro customizada para o login:</label>
											<input type="text" class="form-control"
												formControlName="loginCustomErrorMessage" style="min-width:115px;" />
										</div>
									</div>
								</div>
							</gp-card>

							<gp-card title="Parametrizações de Pré-Cadastro e Primeiro Acesso">
								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Obrigatório aceitar regulamento:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="acceptRegulationRequired"></gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitar multiplos regulamentos:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="enableCampaignWithMultiRegulations"></gp-switch>
										</div>
									</div>
								</div>

								<hr class="small" />
								<h5>Pré-Cadastro</h5>

								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitar pré-cadastro:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch [disabled]="isRegistrationDataFromRootCampaign"
												formControlName="enableParticipantSelfRegistration"></gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Pular aprovação de pré-cadastro:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch [disabled]="isRegistrationDataFromRootCampaign"
												formControlName="skipParticipantSelfRegistrationApproval">
											</gp-switch>
										</div>
									</div>
								</div>

								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="8">
											<label>Limitar período de pré-cadastro</label>
										</div>
										<div grid="4">
											<gp-switch [disabled]="isRegistrationDataFromRootCampaign"
												formControlName="limitParticipantSelfRegistrationPeriod">
											</gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="4">
											<label>Período limite de pré-cadastro</label>
										</div>
										<gp-datepicker cols="12 4" [small]="true"
											name="participantSelfRegistrationStartDate"
											formControlName="participantSelfRegistrationStartDate">
										</gp-datepicker>
										<gp-datepicker cols="12 4" [small]="true"
											name="participantSelfRegistrationEndDate"
											formControlName="participantSelfRegistrationEndDate">
										</gp-datepicker>
									</div>
								</div>

								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="8">
											<label>Permitir informar o participante pai:</label>
										</div>
										<div grid="4">
											<gp-switch [disabled]="isRegistrationDataFromRootCampaign"
												formControlName="allowInformParentParticipantOnPreRegistration">
											</gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="8" class="input-param-text">
											<label>Campo de pesquisa do participante pai:</label>
										</div>
										<div grid="4">
											<select class="form-control input-sm"
												formControlName="parentParticipantSearchFieldOnPreRegistration"
												required>
												<option value="CLIENT_USER_ID">Código</option>
												<option value="DOCUMENT">CPF/CNPJ</option>
											</select>
										</div>
									</div>
								</div>

								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="8">
											<label>Obrigar a existência do participante pai:</label>
										</div>
										<div grid="4">
											<gp-switch [disabled]="isRegistrationDataFromRootCampaign"
												formControlName="requiredExistingParentParticipantOnPreRegistration">
											</gp-switch>
										</div>
									</div>
								</div>

								<hr class="small" />
								<h5>Primeiro Acesso</h5>

								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitar primeiro acesso:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="enableFirstAccess"></gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Pular atualização cadastral:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch [disabled]="isRegistrationDataFromRootCampaign"
												formControlName="skipFirstAccessRegistrationData"></gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitar edição do campo de CPF/CNPJ no primeiro acesso:</label>
										</div>
										<div grid="4">
											<gp-switch [disabled]="isRegistrationDataFromRootCampaign"
												formControlName="enableDocumentFieldAtFirstAccess"></gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>
												Habilitar migração de participante no primeiro acesso
												<gp-tooltip
													message="Permite a migração do participante para outro usuário caso o CPF/CNPJ preenchido no primeiro acesso já exista em outro usuário.">
												</gp-tooltip>
												:
											</label>
										</div>
										<div grid="4">
											<gp-switch [disabled]="isRegistrationDataFromRootCampaign"
												formControlName="enableParticipantMigrationAtFirstAccess"></gp-switch>
										</div>
									</div>
								</div>

								<gp-form-row>
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitar captura de cartão:</label>
										</div>
										<div grid="4">
											<gp-switch formControlName="enableCardRegisterAtFirstAccess"></gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="8" class="input-param-text">
											<label>Período de revisão do cadastro:</label>
										</div>
										<div grid="4">
											<select class="form-control input-sm"
												formControlName="registrationReviewPeriodicity" required>
												<option value="Never">Nunca</option>
												<option value="Daily">Diariamente</option>
												<option value="Weekly">Semanal</option>
												<option value="Biweekly">Quinzenal</option>
												<option value="Monthly">Mensal</option>
												<option value="Bimonthly">Bimestral</option>
												<option value="Quarter">Trimestral</option>
												<option value="Biannual">Semestral</option>
											</select>
										</div>
									</div>
								</gp-form-row>
							</gp-card>

							<gp-card title="Parametrizações das Metas">
								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="12 10 8" class="input-param-text">
											<label>Habilitar metas dos participantes:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="enableCampaignParticipantsGoals"></gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Configurar metas para os novos participantes:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="configureCampaignGoalsForNewParticipant">
											</gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitar agrupamento de metas:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="enableCampaignParticipantsGoalsGroupLayout"
												(onchange)="useEnableCampaignParticipantsGoalsGroupLayout($event)">
											</gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim" *ngIf="isEnableCampaignParticipantsGoalsGroupLayout">
										<div grid="12 10 8" class="input-param-text">
											<label>Limite de visualização do histórico de desempenho:</label>
										</div>
										<div grid="12 2 4">
											<gp-input-mask [thousandsSeparator]="false" [onlyInteger]="true"
												[integers]="3"
												[disabled]="!isEnableCampaignParticipantsGoalsGroupLayout" size="sm"
												formControlName="limitVisualizationPreviousPerformanceGoals">
											</gp-input-mask>
										</div>
									</div>
								</div>
							</gp-card>

							<gp-card title="Parametrizações do Ranking">
								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="8" class="input-param-text">
											<label>Tipo do Ranking:</label>
										</div>
										<div grid="4">
											<select class="form-control input-sm" formControlName="campaignRankingType"
												required>
												<option value="CREDIT">Crédito</option>
												<option value="DYNAMIC">Dinâmico</option>
											</select>
											<span *ngIf="formErrors['campaignRankingType']"
												class="text-danger">{{formErrors['campaignRankingType']}}</span>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="8" class="input-param-text">
											<label>Layout da Página de Ranking:</label>
										</div>
										<div grid="4">
											<select class="form-control input-sm"
												formControlName="campaignRankingPageLayout" required>
												<option value="CARDS">Cards</option>
												<option value="TABLES">Tabelas</option>
												<option value="CARDS_TABLES">Listagem Cards e Detalhes Tabela</option>
											</select>
											<span *ngIf="formErrors['campaignRankingPageLayout']"
												class="text-danger">{{formErrors['campaignRankingPageLayout']}}</span>
										</div>
									</div>
								</div>

								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="12 10 8" class="input-param-text">
											<label>Habilitar visualização da posição fixa do ranking:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="enableCampaignRankingFixedPosition"
												(onchange)="useEnableCampaignRankingFixedPosition($event)"></gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim" *ngIf="isEnableCampaignRankingFixedPosition">
										<div grid="12 10 8" class="input-param-text">
											<label>Número da posição fixa visualizada*:</label>
										</div>
										<div grid="12 2 4">
											<gp-input-mask [thousandsSeparator]="false" [onlyInteger]="true"
												[integers]="12" [disabled]="!isEnableCampaignRankingFixedPosition"
												size="sm" formControlName="campaignRankingFixedPosition">
											</gp-input-mask>
										</div>
									</div>
								</div>
							</gp-card>

							<gp-card title="Parametrizações de Serviços">
								<div class="row" *ngIf="hasCatalog">
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitar compra de pontos:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="allowBuyPoints"></gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitar recarga de celular:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="enableMobileRecharge"></gp-switch>
										</div>
									</div>
								</div>

								<div class="row">
									<div grid="12 6" class="trim" *ngIf="hasCatalog">
										<div grid="12 10 8">
											<label>Habilitado transferência:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch [disabled]="true" formControlName="enableBankTransfer"></gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitado transferência P2P
												<gp-tooltip
													message="Se está habilitado a transferência entre participantes (peer-to-peer).">
													(?)</gp-tooltip>
												:
											</label>
										</div>
										<div grid="12 2 4">
											<gp-switch [disabled]="true" formControlName="enableP2pTransfer">
											</gp-switch>
										</div>
									</div>
								</div>

								<hr class="small" />
								<h5>Cartões Pré-pagos</h5>

								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitado cartão com saque:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch [disabled]="true" formControlName="enableWithdrawableCard">
											</gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="12 8">
											<label>Permitir cartão pré-pago com saque apenas para pessoa: </label>
										</div>
										<div grid="12 4">
											<select class="form-control
												input-sm" name="allowDrawablePrepaidCardOnlyForPersonType"
												 formControlName="allowDrawablePrepaidCardOnlyForPersonType">
												<option selected value="BOTH">Ambos</option>
												<option value="Fisica">Física</option>
												<option value="Juridica">Jurídica</option>
											</select>
										</div>
									</div>
								</div>

								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitado cartão sem saque:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch [disabled]="true" formControlName="enableNoDrawableCard">
											</gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="12 8">
											<label>Permitir cartão pré-pago sem saque apenas para pessoa: </label>
										</div>
										<div grid="12 4">
											<select class="form-control input-sm"
												name="allowNoDrawablePrepaidCardOnlyForPersonType"
												formControlName="allowNoDrawablePrepaidCardOnlyForPersonType">
												<option selected value="BOTH">Ambos</option>
												<option value="Fisica">Física</option>
												<option value="Juridica">Jurídica</option>
											</select>
										</div>
									</div>
								</div>

								<hr class="small" />
								<h5>Pague Contas</h5>

								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitar pague contas:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="enableBillPayment"></gp-switch>
										</div>
									</div>
								</div>

								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Permitir agendamento do pague contas:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="allowBillPaymentSchedulement"
												(onchange)="allowBillPaymentSchedulementChange($event)"></gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="8">
											<label>Horário agendamento <a
													tooltip="Horário para considerar como dia útil na contagem do dia para o agendamento.">(?)</a>:</label>
										</div>
										<div grid="2">
											<gp-input-mask [onlyInteger]="true" [integers]="2"
												[disabled]="!allowBillPaymentSchedulement" size="sm"
												formControlName="billPaymentMinimumHour"
												title="Horário de início do agendamento">
											</gp-input-mask>
											<span *ngIf="formErrors['parametrizations.billPaymentMinimumHour']"
												class="text-danger">{{formErrors['parametrizations.billPaymentMinimumHour']}}</span>
										</div>
										<div grid="2">
											<gp-input-mask [onlyInteger]="true" [integers]="2"
												[disabled]="!allowBillPaymentSchedulement" size="sm"
												formControlName="billPaymentMaximumHour"
												title="Horário limite para agendamento">
											</gp-input-mask>
											<span *ngIf="formErrors['parametrizations.billPaymentMaximumHour']"
												class="text-danger">{{formErrors['parametrizations.billPaymentMaximumHour']}}</span>
										</div>
									</div>
								</div>

								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="8">
											<label>Habilitar prazo antes do vencimento (dias mínimo):</label>
										</div>
										<div grid="4">
											<gp-switch formControlName="useBillPaymentMinimumDueDays"
												(onchange)="useBillPaymentMinimumDueDaysChange($event)">
											</gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="8" class="input-param-text">
											<label>Prazo mínimo de vencimento (dias)<a
													tooltip="Quantidade mínima de dias antes do vencimento para permitir agendamento.">(?)</a>:</label>
										</div>
										<div grid="4">
											<gp-input-mask [onlyInteger]="true" [integers]="3"
												[disabled]="!useBillPaymentMinimumDueDays" size="sm"
												formControlName="billPaymentMinimumDueDays">
											</gp-input-mask>
											<span *ngIf="formErrors['parametrizations.minimumShippingTime']"
												class="text-danger">{{formErrors['parametrizations.minimumShippingTime']}}</span>
										</div>
									</div>
								</div>

								<div class="row">
									<gp-input-checkbox additionalClass="top-p1" grid="12 4 4 3"
										text="Exibir no pagamento de contas" formControlName="showBillPaymentMessage">
									</gp-input-checkbox>
									<div grid="12 8 8 9" [group]="true">
										<label>Mensagem para exibição no pagamento de contas</label>
										<input type="text" class="form-control" formControlName="billPaymentMessage" />
									</div>
								</div>
							</gp-card>

							<gp-card title="Parametrizações do Minha Conta">
								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilita a obrigatoriedade do CPF quando CNPJ:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="enableAccountRepresentative"
												title="Parâmetro para definir se a campanha obriga o cadastro de um Cpf quando Cnpj">
											</gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Obrigatório preencher o Complemento e a Referência do
												endereço:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="requiredShippingAddressDetails"></gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Permite cadastrar ou alterar endereços de entrega:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="allowChangeShippingAddress"
												title="Define se os participantes poderão cadastrar novos endereços ou alterar os endereços">
											</gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Permite alterar os endereços cadastrados via integração:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="allowChangeIntegrationShippingAddress"
												title="Define se os participantes poderão alterar os endereços que foram cadastrados via integração">
											</gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="8" class="input-param-text">
											<label>Tipo do extrato:</label>
										</div>
										<div grid="4" class="input-param-text">
											<select class="form-control input-sm" formControlName="extractType"
												required>
												<option value="S">Simples</option>
												<option value="D">Detalhado</option>
											</select>
											<span *ngIf="formErrors['extractType']"
												class="text-danger">{{formErrors['extractType']}}</span>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitar edição do perfil dos operadores:</label>
										</div>
										<div grid="4">
											<gp-switch [disabled]="isRegistrationDataFromRootCampaign"
												formControlName="allowControlOperatorsRoles"></gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitar newsletters:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="hasNewsletters"></gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitar exibição de saldo bloqueado no site campanha:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="enableBlockedBalanceAtCampaignSite"
												title="Parâmetro para definir se o site campanha também irá exibir o saldo bloqueado no cabeçalho">
											</gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilita saldo dos formulários preenchidos pendentes de
												aprovação:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="enableDynamicFormsPendingBalance"
												title="Parâmetro para definir se a campanha informa o saldo disponivel dos formulários preenchidos pendentes de aprovação">
											</gp-switch>
										</div>
									</div>
								</div>
							</gp-card>

							<gp-card title="Parametrizações de Pedidos">
								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Origem do CPF/CNPJ para faturamento:</label>
										</div>
										<div grid="12 2 4">
											<select class="form-control input-sm" name="originTaxIdForInvoicing"
												 formControlName="originTaxIdForInvoicing">
												<option value="AccountDocument" selected>CPF/CNPJ do participante
												</option>
												<option value="ShippingReceiverDocument">CPF do destinatário da entrega
												</option>
											</select>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Regionalização de pedidos:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="enableOrdersRegionalization"></gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Enviar e-mail de notificação de compra p/ parceiro:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="enableSendNotificationPartner"
												title="Enviar e-mail de notificação de pedido com os itens do parceiro">
											</gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Aceitar apenas pedidos integrados c/ o cliente:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="requireSuccessOnClientOrderIntegration"
												title="Finalizar processamento apenas se o pedido foi integrado com sucesso no cliente">
											</gp-switch>
										</div>
									</div>
								</div>

								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="8">
											<label>Limitar período de resgate</label>
										</div>
										<div grid="4">
											<gp-switch formControlName="limitRedemptionPeriod">
											</gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="4">
											<label>Período de limite do resgate de pontos</label>
										</div>
										<gp-datepicker cols="12 4" [small]="true" name="redemptionPeriodStart"
											formControlName="redemptionPeriodStart">
										</gp-datepicker>
										<gp-datepicker cols="12 4" [small]="true" name="redemptionPeriodEnd"
											formControlName="redemptionPeriodEnd">
										</gp-datepicker>
									</div>
								</div>

								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="8">
											<label>
												Permitir resgates em horários restritos
												<gp-tooltip message="Horários restritos: Segunda à Sexta das 20h às 8h, Sábado e Domingo o dia todo.">(?)</gp-tooltip>
											</label>
										</div>
										<div grid="4">
											<gp-switch formControlName="allowPlaceOrderDuringRestrictedPeriod" [disabled]="!canEditSecurityParametrization">
											</gp-switch>
										</div>
									</div>
								</div>

								<hr class="small" />
								<h5>Débito e Estorno</h5>

								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="8" class="input-param-text">
											<label>Prioridade de débito das mecânicas*
												<gp-tooltip spanClass="text-primary">
													<div style="text-align:justify">
														Define qual a prioridade utilizada para débitar os pontos das
														mecânicas.
														<ul class="tooltip-list">
															<li>Por Boost prioriza as mecânicas com maior valor no Boost
																e por data de expiração.</li>
															<li>Por Menor Ranking prioriza as mecânicas que estejam nas
																posições mais baixa da árvore de Ranking.</li>
															<li>Por Maior Ranking prioriza as mecânicas que estejam nas
																posições mais altas da árvore de Rakning.</li>
														</ul>
													</div>
												</gp-tooltip>:
											</label>
										</div>
										<div grid="4">
											<select class="form-control input-sm" formControlName="mechanicPriority"
												required>
												<option value="Boost">Boost e Data de Expiração</option>
												<option value="LowestRanking">Menor Ranking</option>
												<option value="HighestRanking">Maior Ranking</option>
											</select>
											<span *ngIf="formErrors['mechanicPriority']"
												class="text-danger">{{formErrors['mechanicPriority']}}</span>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitar regras de resgates na mecânica:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="enableMechanicRedemptionRules"></gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim" *ngIf="isRootBu">
										<div grid="12 10 8">
											<label>Permite resgatar sem saldo:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="allowRedeemWithoutBalance"></gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim" *ngIf="hasCatalog">
										<div grid="12 10 8">
											<label>Permite estorno de pontos:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="allowReversalPoints"></gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitar seleção de formas de pagamentos:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="enablePaymentMethodsSelection"></gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim" *ngIf="hasCatalog">
										<div grid="12 10 8">
											<label>Permite apenas o estorno do pedido completo:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="allowOnlyPartnerOrderRefund"></gp-switch>
										</div>
									</div>
								</div>

								<hr class="small" />
								<h5>Carrinho</h5>

								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="8">
											<label>Limitar quantidade de itens no carrinho:</label>
										</div>
										<div grid="4">
											<gp-switch formControlName="limitCartItems"
												(onchange)="limitCartItemsChange($event)">
											</gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="8" class="input-param-text">
											<label>Quantidade máxima de itens no carrinho:</label>
										</div>
										<div grid="4">
											<gp-input-mask [onlyInteger]="true" [integers]="3"
												[disabled]="!limitCartItems" size="sm"
												formControlName="maximumNumberCartItems">
											</gp-input-mask>
											<span *ngIf="formErrors['parametrizations.maximumNumberCartItems']"
												class="text-danger">{{formErrors['parametrizations.maximumNumberCartItems']}}</span>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="8">
											<label>Habilitar valor mínimo de resgate:</label>
										</div>
										<div grid="4">
											<gp-switch formControlName="requireMinimumOrderTotalAmount"
												(onchange)="requireMinimumOrderTotalAmount = $event">
											</gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="8" class="input-param-text">
											<label>Valor mínimo de resgate (pontos):</label>
										</div>
										<div grid="4">
											<gp-input-mask [onlyDecimal]="true"
												[disabled]="!requireMinimumOrderTotalAmount" size="sm"
												formControlName="minimumOrderTotalAmount">
											</gp-input-mask>
											<span *ngIf="formErrors['parametrizations.minimumOrderTotalAmount']"
												class="text-danger">{{formErrors['parametrizations.minimumOrderTotalAmount']}}</span>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Permitir cupom de desconto:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="enableDiscountCoupon"></gp-switch>
										</div>
									</div>

									<!-- <div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitar atualização cadastral no resgate:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="enableUpdateRegistrationDataAtShoppingCart"></gp-switch>
										</div>
									</div> -->
								</div>

								<hr class="small" />
								<h5>Frete</h5>

								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Utilizar endereço principal como padrão de entrega:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="useDefaultShippingAddress"></gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Permitir selecionar endereço de entrega:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="enableSelectShippingAddress"></gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitar cálculo de frete:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="enableShippingCalculation"></gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitar frete customizado:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="enableEmbeddedShippingCost"></gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="8">
											<label>Utiliza prazo de entrega personalizado (mínimo):</label>
										</div>
										<div grid="4">
											<gp-switch formControlName="useMinimumShippingTime"
												(onchange)="useMinimumShippingTimeChange($event)">
											</gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="8" class="input-param-text">
											<label>Prazo mínimo de entrega (dias):</label>
										</div>
										<div grid="4">
											<gp-input-mask [onlyInteger]="true" [integers]="3"
												[disabled]="!useMinimumShippingTime" size="sm"
												formControlName="minimumShippingTime"
												title="Quando o prazo de entrega do parceiro ou do Correios for menor que o valor atribuído no campo, o catálogo utilizará o prazo atribuído.">
											</gp-input-mask>
											<span *ngIf="formErrors['parametrizations.minimumShippingTime']"
												class="text-danger">{{formErrors['parametrizations.minimumShippingTime']}}</span>
										</div>
									</div>
								</div>
							</gp-card>

							<gp-card title="Parametrizações de Produtos">
								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitar marketplace por EAN:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="enableMatchingByEan"></gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Permitir estoque:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="enableCampaignStock"></gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Regionalização de produtos:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="enableProductsRegionalization"></gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitar compra de pontos na listagem de produtos <a
													tooltip="Habilita na listagem de produto a informação de quanto falta para o participante comprar o produto (saldo em pontos + R$)">(?)</a>:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="showProductsListingWithPointsPurchaseCost">
											</gp-switch>
										</div>
									</div>
									<div grid="12 12" class="trim">
										<div grid="4">
											<label>Rótulo para os produtos com preço dinâmico <a
													tooltip="O texto do rótulo é exibido no lugar do preço">(?)</a>:</label>
										</div>
										<div grid="2">
											<input type="text" class="form-control input-sm"
												name="dynamicPriceDescription" maxlength="25"
												formControlName="dynamicPriceDescription">
										</div>
									</div>
								</div>
							</gp-card>

							<gp-card title="Parametrizações de Integrações">
								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Habilitar login via integração:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch [disabled]="isRegistrationDataFromRootCampaign"
												formControlName="enableLoginIntegration"
												title="Define se a campanha permitirá os participantes efetuar login através da página de login">
											</gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Token de integração:</label>
										</div>
										<div grid="12 2 4">
											<input type="text" class="form-control input-sm" [readonly]="true"
												formControlName="token" style="min-width:115px;" />
										</div>
									</div>
								</div>

								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Cadastrar o participante via integração de login:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch [disabled]="isRegistrationDataFromRootCampaign"
												formControlName="createParticipantOnIntegration"></gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Aceitar participante que não existe na integração de login:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="acceptNonIntegratedParticipant"></gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Utilizar mecânica de pontos do cliente (integração):</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="useClientMechanicPoints"></gp-switch>
										</div>
									</div>

									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Enviar pedido para o cliente (integração):</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="enableClientOrderIntegration"
												title="Enviar pedido para o cliente da campanha utilizando a integração">
											</gp-switch>
										</div>
									</div>
								</div>
							</gp-card>

							<gp-card title="Parametrizações de B2B" *ngIf="isCampaignB2b">
								<div class="row">
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Permitir selecionar o fabricante do produto:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="enableSelectProductFactory"
												title="Define se o participante poderá selecionar o fabricante do produto antes de adicionar no carrinho">
											</gp-switch>
										</div>
									</div>
									<div grid="12 6" class="trim">
										<div grid="12 10 8">
											<label>Enviar e-mail de notificação de compra p/ fabricante:</label>
										</div>
										<div grid="12 2 4">
											<gp-switch formControlName="enableSendNotificationFactory"
												title="Enviar e-mail de notificação de pedido com os itens do fabricante">
											</gp-switch>
										</div>
									</div>
								</div>
							</gp-card>

							<gp-card title="Parametrizações de Notificações" [last]="true">
								<div class="row">
									<div grid="4">
										<label>Nome usado nos e-mails transacionais:</label>
									</div>
									<div grid="8">
										<input type="text" class="form-control input-sm"
											formControlName="nameForTransactionalEmails" style="min-width:115px;" />
									</div>
								</div>
								<div class="row top-p05">
									<div grid="4">
										<label>Reply-to dos e-mails transacionais:</label>
									</div>
									<div grid="8">
										<input type="text" class="form-control input-sm"
											formControlName="emailForTransactionalEmails" style="min-width:115px;" />
									</div>
								</div>
								<div class="row top-p05">
									<div grid="4">
										<label>Nome usado nos SMS transacionais:</label>
									</div>
									<div grid="8">
										<input type="text" class="form-control input-sm"
											formControlName="fromForTransactionalSms" style="min-width:115px;" />
									</div>
								</div>
							</gp-card>
						</div>
					</accordion-group>

					<accordion-group panelClass="b0 mb-sm panel-default" *ngIf="hasCatalog">
						<div accordion-heading>
							<label>Configurações do Catálogo</label>
						</div>
						<div formGroupName="pageSettings">
							<gp-card [last]="true">
								<div class="row">
									<div class="col-sm-12">
										<h4>Habilitar Páginas</h4>
									</div>
								</div>
								<div class="row">
									<gp-input-checkbox grid="12 6 4 3" text="Fale Conosco"
										formControlName="enableContact">
									</gp-input-checkbox>
									<!--gp-input-checkbox grid="12 6 4 3" text="Mecânica" formControlName="enableMechanic"></gp-input-checkbox-->
									<gp-input-checkbox grid="12 6 4 3" text="Regulamento"
										formControlName="enableRegulation">
									</gp-input-checkbox>
									<gp-input-checkbox grid="12 6 4 3" text="Política de Privacidade"
										formControlName="enablePolicy"></gp-input-checkbox>
									<gp-input-checkbox grid="12 6 4 3" text="Política de Entrega e Troca"
										formControlName="enableShippingPolicy"></gp-input-checkbox>
									<gp-input-checkbox grid="12 6 4 3" text="Minha Conta"
										formControlName="enableMyAccount">
									</gp-input-checkbox>
									<gp-input-checkbox grid="12 6 4 3" text="Cadastro"
										formControlName="enableParticipantData">
									</gp-input-checkbox>
									<gp-input-checkbox [disabled]="isRegistrationDataFromRootCampaign" grid="12 6 4 3"
										text="Alterar Senha" formControlName="enablePassword"></gp-input-checkbox>
									<gp-input-checkbox grid="12 6 4 3" text="Pedidos" formControlName="enableOrders">
									</gp-input-checkbox>
									<gp-input-checkbox grid="12 6 4 3" text="Endereços"
										formControlName="enableAddresses">
									</gp-input-checkbox>
									<gp-input-checkbox grid="12 6 4 3" text="Pontos Bloqueados e a Expirar"
										formControlName="enablePointsToExpireAndBlocked"></gp-input-checkbox>
									<gp-input-checkbox grid="12 6 4 3" text="Extrato" formControlName="enableExtract">
									</gp-input-checkbox>
									<gp-input-checkbox grid="12 6 4 3" text="Dúvidas Frequentes"
										formControlName="enableFaq">
									</gp-input-checkbox>
								</div>

								<hr />
								<div class="row">
									<div grid="12 6" [group]="true">
										<label>Página principal após o login</label>
										<select class="form-control" formControlName="pageAfterLogin" required>
											<option value="HomePage">Home Page</option>
											<option value="MyAccount">Minha Conta</option>
											<option value="BillPayments">Pague Contas</option>
										</select>
									</div>
									<div grid="12 6" [group]="true">
										<label>Ocultar menu do catálogo</label>
										<div>
											<gp-switch formControlName="hideMenu"></gp-switch>
										</div>
									</div>
								</div>
								<gp-form-row>
									<div grid="12 6" [group]="true">
										<label>Destacar loja especial na home do catálogo: </label>
										<div>
											<gp-switch formControlName="showSpecialShopAsFeaturedInHomePage">
											</gp-switch>
										</div>
									</div>
								</gp-form-row>

								<hr />
								<div class="row">
									<div class="col-sm-12">
										<h4>Mensagem do Carrinho e da Confirmação do Pedido</h4>
									</div>
								</div>
								<div class="row">
									<gp-input-checkbox additionalClass="top-p1" grid="12 4 4 3"
										text="Exibir no carrinho" formControlName="showCartMessage"></gp-input-checkbox>
									<div grid="12 8 8 9" [group]="true">
										<label>Mensagem para exibição no carrinho</label>
										<input type="text" class="form-control" formControlName="cartMessage" />
									</div>
								</div>
								<div class="row">
									<gp-input-checkbox additionalClass="top-p1" grid="12 6 4 3"
										text="Exibir na confirmação do pedido" formControlName="showOrderMessage">
									</gp-input-checkbox>
									<div grid="12 8 8 9" [group]="true">
										<label>Mensagem para exibição na confirmação do pedido</label>
										<input type="text" class="form-control" formControlName="orderMessage" />
									</div>
								</div>

								<gp-form-row>
									<div grid="12 6" [group]="true">
										<label>Exibir mensagem customizada ao finalizar pedido </label>
										<div>
											<gp-switch formControlName="showCustomMessageAfterOrderConfirmation">
											</gp-switch>
										</div>
									</div>
								</gp-form-row>
							</gp-card>
						</div>
					</accordion-group>

					<accordion-group panelClass="b0 mb-sm panel-default" *ngIf="hasCampaignSite">
						<div accordion-heading>
							<label>Configurações do Site Campanha</label>
						</div>
						<gp-card>
							<div class="row">
								<div class="col-sm-12">
									<h4>Habilitar Páginas do Minha Conta</h4>
								</div>
							</div>
							<div class="row">
								<gp-input-checkbox grid="12 6 4 3" text="Minha Conta"
									[(ngModel)]="campaignSiteParams.enableMyAccount"
									[ngModelOptions]="{standalone:true}">
								</gp-input-checkbox>
								<gp-input-checkbox grid="12 6 4 3" text="Cadastro"
									[(ngModel)]="campaignSiteParams.enableParticipantData"
									[ngModelOptions]="{standalone:true}">
								</gp-input-checkbox>
								<gp-input-checkbox grid="12 6 4 3" text="Endereços"
									[(ngModel)]="campaignSiteParams.enableAddresses"
									[ngModelOptions]="{standalone:true}">
								</gp-input-checkbox>
								<gp-input-checkbox grid="12 6 4 3" text="Histórico Transacional"
									[(ngModel)]="campaignSiteParams.enableHistoryFiles"
									[ngModelOptions]="{standalone:true}">
								</gp-input-checkbox>
								<gp-input-checkbox [disabled]="isRegistrationDataFromRootCampaign" grid="12 6 4 3"
									text="Alterar Senha" [(ngModel)]="campaignSiteParams.enablePassword"
									[ngModelOptions]="{standalone:true}"></gp-input-checkbox>
								<gp-input-checkbox grid="12 6 4 3" text="Pagamentos Agendados"
									[(ngModel)]="campaignSiteParams.enableScheduledPayments"
									[ngModelOptions]="{standalone:true}"></gp-input-checkbox>
							</div>
						</gp-card>

						<gp-card title="Mensagens nas Páginas">
							<gp-form-row>
								<gp-input-checkbox additionalClass="top-p1" grid="12 4 4 3" text="Exibir no Extrato"
									[(ngModel)]="campaignSiteParams.enableExtractMessage"
									[ngModelOptions]="{standalone:true}">
								</gp-input-checkbox>
								<div grid="12 8 8 9" [group]="true">
									<label>Mensagem para exibição no extrato</label>
									<input type="text" class="form-control"
										[(ngModel)]="campaignSiteParams.extractMessage"
										[ngModelOptions]="{standalone:true}" />
								</div>
							</gp-form-row>

							<gp-form-row>
								<gp-input-checkbox additionalClass="top-p1" grid="12 4 4 3" text="Exibir no Login"
									[(ngModel)]="campaignSiteParams.enableLoginHeaderMessage"
									[ngModelOptions]="{standalone:true}">
								</gp-input-checkbox>
								<div grid="12 8 8 9" [group]="true">
									<label>Mensagem para exibição no header de login</label>
									<input type="text" class="form-control"
										[(ngModel)]="campaignSiteParams.loginHeaderMessage"
										[ngModelOptions]="{standalone:true}" />
								</div>
							</gp-form-row>

							<gp-form-row>
								<gp-input-checkbox additionalClass="top-p1" grid="12 4 4 3" text="Exibir no Catálogo"
									[(ngModel)]="campaignSiteParams.enableCatalogMessage"
									[ngModelOptions]="{standalone:true}">
								</gp-input-checkbox>
								<div grid="12 8 8 9" [group]="true">
									<label>Mensagem para exibição no Catálogo</label>
									<input type="text" class="form-control"
										[(ngModel)]="campaignSiteParams.catalogMessage"
										[ngModelOptions]="{standalone:true}" />
								</div>
							</gp-form-row>
						</gp-card>

						<gp-card title="Configurações das Páginas">
							<gp-form-row>
								<gp-form-col cols="12 6">
									<label>Layout da página de login</label>
									<select class="form-control input-sm" name="loginPageLayout"
										[(ngModel)]="campaignSiteParams.loginPageLayout"
										[ngModelOptions]="{standalone:true}">
										<option value="Header">Formulário de login no header</option>
										<option value="Body">Formulário de login na página</option>
									</select>
								</gp-form-col>

								<gp-form-col cols="12 6">
									<label>Página principal após o login</label>
									<select class="form-control input-sm" name="pageAfterLogin"
										[(ngModel)]="campaignSiteParams.pageAfterLogin"
										[ngModelOptions]="{standalone:true}">
										<option value="HOME">Home Page</option>
										<option value="MY_ACCOUNT">Minha Conta</option>
										<option value="EXTRACT">Extrato</option>
										<option value="MECHANIC">Mecânica</option>
										<option value="HOW_PARTICIPATE">Como Participar</option>
										<option value="RANKINGS">Rankings</option>
										<option value="GOALS">Metas</option>
										<option value="DYNAMIC_FORMS">Formulários Dinâmicos</option>
										<option value="DYNAMIC_PAGES">Páginas Dinâmicas</option>
										<option value="DYNAMIC_SECTIONS">Seções Dinâmicas</option>
										<option value="PARTICIPANTS_INVITATION">Convite de Participantes</option>
										<option value="CAMPAIGNS_GROUP_ACCESS">Acesso às Campanhas em Coalizão</option>
										<option value="PREPAID_CARDS">Cartões Pré-Pagos</option>
									</select>
								</gp-form-col>
							</gp-form-row>

							<h4>Visualizações de Postagens</h4>
							<gp-form-row>
								<gp-form-col cols="12 6 3">
									<label>Origem</label>
									<select class="form-control input-sm" name="homePagePostsOrigin"
										[(ngModel)]="campaignSiteParams.homePagePostsOrigin"
										[ngModelOptions]="{standalone:true}">
										<option value="ALL" selected>Todos</option>
										<option value="ADMIN">Administrativo</option>
										<option value="PARTICIPANTS">Participante</option>
									</select>
								</gp-form-col>
								<gp-form-col cols="12 6 3">
									<label>Habilitar Clusterização</label>
									<div>
										<gp-switch [(ngModel)]="campaignSiteParams.enablePostClusterization"
											[ngModelOptions]="{standalone:true}"></gp-switch>
									</div>
								</gp-form-col>
							</gp-form-row>

							<h4>Layout das Páginas</h4>
							<gp-form-row>
								<gp-form-col cols="12 6 6 3">
									<label>Página de Avaliações e Treinamentos</label>
									<select class="form-control input-sm" name="campaignEvaluationsPageLayout"
										[ngModelOptions]="{standalone:true}"
										[(ngModel)]="campaignSiteParams.campaignEvaluationsPageLayout">
										<option value="CARDS">Cards</option>
										<option value="SIDEBAR">Menu Lateral</option>
									</select>
								</gp-form-col>

								<gp-form-col cols="12 6 6 3">
									<label>Página de Formulários Dinâmicos</label>
									<select class="form-control input-sm" name="campaignDynamicFormsPageLayout"
										[ngModelOptions]="{standalone:true}"
										[(ngModel)]="campaignSiteParams.campaignDynamicFormsPageLayout">
										<option value="CARDS">Cards</option>
										<option value="SIDEBAR">Menu Lateral</option>
									</select>
								</gp-form-col>
							</gp-form-row>

							<h4>Páginas Customizadas</h4>
							<gp-form-row>
								<gp-form-col cols="12 6 6 3">
									<label>Habilitar páginas customizadas<gp-tooltip
											message="Hábilita a customização das páginas do site da campanha.">
											(?)</gp-tooltip></label>
									<div>
										<gp-switch name="enableCampaignSitePagesCustomization"
											[(ngModel)]="campaignSiteParams.enableCampaignSitePagesCustomization"
											[ngModelOptions]="{standalone:true}">
										</gp-switch>
									</div>
								</gp-form-col>
							</gp-form-row>
						</gp-card>

						<gp-card [last]="true" title="Configurações dos Menus">
							<p>O menu comporta até 9 itens em uma linha de menu. Caso sejam habilitados mais do
								que 9 itens será exibido um menu hamburguer com os itens excedentes.</p>

							<gp-form-row>
								<div grid="12 6 6 4" [group]="true">
									<label>Mostrar menu apenas para usuário logado</label>
									<div>
										<gp-switch [(ngModel)]="campaignSiteParams.showMenuOnlyWhenLogged"
											[ngModelOptions]="{standalone:true}">
										</gp-switch>
									</div>
								</div>
								<div grid="12 6 6 3" [group]="true">
									<label>Ocultar menu do topo</label>
									<div>
										<gp-switch [(ngModel)]="campaignSiteParams.hideTopMenu"
											[ngModelOptions]="{standalone:true}">
										</gp-switch>
									</div>
								</div>
								<div grid="12 6 6 3" [group]="true">
									<label>Ocultar menu da lateral</label>
									<div>
										<gp-switch [(ngModel)]="campaignSiteParams.hideSidebarMenu"
											[ngModelOptions]="{standalone:true}">
										</gp-switch>
									</div>
								</div>
							</gp-form-row>

							<hr />

							<gp-form-row>
								<gp-input-checkbox additionalClass="top-p1" grid="12 4 4 3"
									[ngModelOptions]="{standalone:true}" text="Exibir páginas dinâmicas no menu"
									[(ngModel)]="campaignSiteParams.showDynamicWebPagesOnMenu">
								</gp-input-checkbox>

								<gp-input-checkbox additionalClass="top-p1" grid="12 4 4 3"
									[ngModelOptions]="{standalone:true}" text="Exibir formulários dinâmicos no menu"
									[(ngModel)]="campaignSiteParams.showDynamicFormsOnMenu">
								</gp-input-checkbox>
								<gp-input-checkbox additionalClass="top-p1" grid="12 4 4 3"
									[ngModelOptions]="{standalone:true}" text="Exibir metas no menu"
									[(ngModel)]="campaignSiteParams.showGoalsOnMenu">
								</gp-input-checkbox>
							</gp-form-row>

							<hr />

							<div>
								<campaign-site-page-param name="campaignMenuSitelogin" label="Login"
									[disableGpSwitch]="isRegistrationDataFromRootCampaign"
									[readOnly]="isRegistrationDataFromRootCampaign"
									[(ngModel)]="campaignSiteParams.login" [ngModelOptions]="{standalone:true}"
									[showVisibleMenu]="false" [showRequireAuth]="false">
								</campaign-site-page-param>
								<campaign-site-page-param name="campaignMenuSiteHome" label="Home"
									[(ngModel)]="campaignSiteParams.home" [ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>

								<campaign-site-page-param name="campaignMenuSiteHowToParticipate"
									label="Como Participar" [(ngModel)]="campaignSiteParams.howToParticipate"
									[ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>
								<campaign-site-page-param name="campaignMenuSiteRegulament" label="Regulamento"
									[(ngModel)]="campaignSiteParams.regulament" [ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>
								<campaign-site-page-param name="campaignMenuSiteMechanic" label="Mecânica"
									[(ngModel)]="campaignSiteParams.mechanic" [ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>

								<campaign-site-page-param name="campaignMenuSiteFaq" label="FAQ"
									[(ngModel)]="campaignSiteParams.faq" [ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>
								<campaign-site-page-param name="campaignMenuSiteContact" label="Fale Conosco"
									[(ngModel)]="campaignSiteParams.contact" [ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>

								<campaign-site-page-param name="campaignMenuSiteExtract" label="Extrato de Pontos"
									[showRequireAuth]="false" [requireAuth]="true"
									[(ngModel)]="campaignSiteParams.extract" [ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>

								<campaign-site-page-param name="campaignMenuSiteCatalog" label="Catálogo"
									[showRequireAuth]="false" [requireAuth]="true"
									[(ngModel)]="campaignSiteParams.catalog" [ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>

								<campaign-site-page-param name="campaignMenuSitePointsDistribution"
									label="Distribuição de Pontos" [showVisibleMenu]="false" [visibleMenu]="true"
									[showRequireAuth]="false" [requireAuth]="true"
									[(ngModel)]="campaignSiteParams.pointsDistribution"
									[ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>

								<campaign-site-page-param name="campaignMenuSiteRanking" label="Ranking"
									[(ngModel)]="campaignSiteParams.ranking" [ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>

								<campaign-site-page-param name="campaignMenuSiteGallery" label="Galeria de Fotos"
									[(ngModel)]="campaignSiteParams.gallery" [ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>

								<campaign-site-page-param name="campaignMenuSiteVideoGallery" label="Galeria de Videos"
									[(ngModel)]="campaignSiteParams.videoGallery" [ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>

								<campaign-site-page-param name="campaignMenuSitePosts" label="Postagens"
									[(ngModel)]="campaignSiteParams.posts" [ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>

								<campaign-site-page-param name="campaignMenuSiteSurveys" label="Enquetes"
									[(ngModel)]="campaignSiteParams.surveys" [ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>

								<campaign-site-page-param name="campaignMenuSiteDynamicPages" label="Páginas Dinâmicas"
									[(ngModel)]="campaignSiteParams.dynamicPages" [ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>

								<campaign-site-page-param name="campaignMenuSiteDynamicPages" label="Seções Dinâmicas"
									[(ngModel)]="campaignSiteParams.dynamicSections"
									[ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>

								<campaign-site-page-param name="campaignMenuSitePromotionsSearch"
									label="Pesquisa de Promoções" [(ngModel)]="campaignSiteParams.promotionsSearch"
									[ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>

								<campaign-site-page-param name="campaignMenuSiteBillsPayments" label="Pague Contas"
									[showRequireAuth]="false" [requireAuth]="true"
									[(ngModel)]="campaignSiteParams.billsPayment" [ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>

								<campaign-site-page-param name="campaignMenuSiteAccountOperators"
									label="Operadores de Contas" [showRequireAuth]="false" [requireAuth]="true"
									[(ngModel)]="campaignSiteParams.accountOperators"
									[ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>

								<campaign-site-page-param name="campaignMenuSiteInviteParticipant"
									label="Convidar Participante" [showRequireAuth]="false" [requireAuth]="true"
									[(ngModel)]="campaignSiteParams.inviteParticipant"
									[ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>

								<campaign-site-page-param name="campaignMenuSiteDynamicCampaignParticipantsGoals"
									label="Metas do Participante" [showRequireAuth]="false" [requireAuth]="true"
									[(ngModel)]="campaignSiteParams.campaignParticipantsGoals"
									[ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>

								<campaign-site-page-param name="campaignMenuSiteDynamicWebForms"
									label="Formulários Dinâmicos" [showRequireAuth]="false" [requireAuth]="true"
									[(ngModel)]="campaignSiteParams.dynamicWebForms"
									[ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>

								<campaign-site-page-param name="campaignMenuSiteEvaluations"
									label="Avaliações e Treinamentos" [showRequireAuth]="false" [requireAuth]="true"
									[(ngModel)]="campaignSiteParams.evaluations" [ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>

								<campaign-site-page-param name="campaignMenuSiteP2pTransfer"
									label="Transferência de Pontos" [showRequireAuth]="false" [requireAuth]="true"
									[(ngModel)]="campaignSiteParams.p2pTransfer" [ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>

								<campaign-site-page-param name="campaignMenuSitePointsEnginesRegisters"
									label="Motor de Pontos" [showRequireAuth]="false" [requireAuth]="true"
									[(ngModel)]="campaignSiteParams.pointsEngineRegisters"
									[ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>

								<campaign-site-page-param name="campaignMenuSiteDynamicWebForms" label="Sorteio"
									[showRequireAuth]="false" [requireAuth]="true"
									[(ngModel)]="campaignSiteParams.raffles" [ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>

								<campaign-site-page-param name="eLearning" label="Capacitação" [showRequireAuth]="false"
									[requireAuth]="true" [(ngModel)]="campaignSiteParams.eLearning"
									[ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>

								<campaign-site-page-param name="campaignMenuSiteCampaignsGroupAccess"
									label="Acesso às Campanhas em Coalizão" [showRequireAuth]="false"
									[requireAuth]="true" [(ngModel)]="campaignSiteParams.campaignsGroupAccess"
									[ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>

								<campaign-site-page-param name="campaignMenuSitePrepaidCard" label="Cartões Pré-Pagos"
									[showRequireAuth]="false" [requireAuth]="true"
									[(ngModel)]="campaignSiteParams.prepaidCards" [ngModelOptions]="{standalone:true}">
								</campaign-site-page-param>
							</div>
						</gp-card>
					</accordion-group>
				</accordion>
			</gp-card>
		</tab>

		<tab *ngIf="canViewFullCampaign">
			<ng-template tabHeading><em class="fa fa-address-book-o"></em> Pré-cadastro</ng-template>
			<campaign-preregister-params [(ngModel)]="preRegisterSettings"
				[registrationDataFromRootCampaign]="isRegistrationDataFromRootCampaign"
				[ngModelOptions]="{standalone: true}">
			</campaign-preregister-params>
		</tab>

		<tab *ngIf="canViewFullCampaign">
			<ng-template tabHeading><em class="fa fa-id-card-o"></em> Primeiro Acesso</ng-template>
			<campaign-personal-data-params [(ngModel)]="participantData" [ngModelOptions]="{standalone: true}">
			</campaign-personal-data-params>
		</tab>

		<!-- <tab *ngIf="canViewFullCampaign">
			<ng-template tabHeading><em class="fa fa-shopping-basket"></em> Dados Resgate</ng-template>
			<campaign-registration-data-shopping-cart-params [(ngModel)]="registrationDataAtShoppingCart" [ngModelOptions]="{standalone: true}">
			</campaign-registration-data-shopping-cart-params>
		</tab> -->


		<tab *ngIf="canViewFullCampaign">
			<ng-template tabHeading><em class="fa fa-link"></em> Integração</ng-template>

			<app-campaign-integration-params [parametrizations]="campaign.parametrizations" [campaignId]="campaignId"
				[integrationSettings]="integrationSettings"
				[registrationDataFromRootCampaign]="isRegistrationDataFromRootCampaign">
			</app-campaign-integration-params>
		</tab>
	</tabset>

	<gp-card *ngIf="!loading">
    <button type="button" class="btn btn-default" [routerLink]="['/campanha']">
      <i class="fa fa-arrow-left"></i>
      Voltar
    </button>

    <gp-spinner-button text="Cancelar" bootstrapClass="danger" [disabled]="disableButtons" (click)="cancel()"
      *ngIf="canCreateOrEditCampaign">
    </gp-spinner-button>

    <gp-spinner-button type="submit" text="Salvar dados da campanha" [pink]="true" icon="send"
      [disabled]="disableButtons" [loading]="sending" loadingText="Salvando" *ngIf="canCreateOrEditCampaign">
    </gp-spinner-button>

    <gp-spinner-button type="button" text="Atualizar layouts" [pink]="true" icon="upload"
      [disabled]="disableButtons" [loading]="uploading" loadingText="Aguarde o upload" (click)="uploadFiles()"
      *ngIf="canCreateOrEditCampaign">
    </gp-spinner-button>
	</gp-card>
</gp-form-validate>
