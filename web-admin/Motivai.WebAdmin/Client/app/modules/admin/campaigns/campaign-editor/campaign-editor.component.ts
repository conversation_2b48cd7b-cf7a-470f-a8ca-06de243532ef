import { Component, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormControl
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { CustomValidators } from 'ng2-validation';

import { CampaignFactory } from '../campaign.factory';
import { Item } from '../../../../shared/models/item';
import { GpAlertComponent } from '../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { Campaign } from '../campaign';
import { AuthStore } from '../../../../core/auth/auth.store';
import { CampaignService } from '../campaign.service';
import { CompanyService } from '../../companies/company/company.service';
import { GpFileUploadComponent } from '../../../../shared/components/gp-fileupload/gp-fileupload.component';
import { CampaignStore } from '../campaign.store';
import { Subscription } from 'rxjs';
import { RxjsHelpers } from '../../../../shared/helpers/rxjs-helpers';
import { CampaignFeatureApproverService } from '../campaign-feature-approver.service';
import { PERMISSION_CAMPAIGNS_FEATURES_APPROVER_EDIT, PERMISSION_CAMPAIGNS_FEATURES_APPROVER_REMOVE, PERMISSION_CAMPAIGNS_FEATURES_APPROVER_VIEW } from '../../../../core/auth/access-points';
import { getTypeDescription, isB2b, isRewards } from '../../../../shared/components/business/campaigns/campaign-setttings/campaign-settings';

@Component({
  selector: 'campaign-editor',
  templateUrl: 'campaign-editor.component.html',
  styles: [
    `
      .param-panel .input-sm {
        height: 25px !important;
      }
      .trim {
        padding-left: 0px;
        padding-right: 0px;
        padding-bottom: 0.5em;
      }
      hr.small {
        margin-top: 0.7em;
        margin-bottom: 0.75em;
      }
      h5 {
        font-weight: bold;
        font-size: 16px;
        padding-top: 0.5em;
        padding-bottom: 0.3em;
      }
    `
  ]
})
export class CampaignEditorComponent implements OnInit, AfterViewInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @ViewChild('gpCatalogLayout') gpCatalogLayout: GpFileUploadComponent;
  @ViewChild('gpSiteLayout') gpSiteLayout: GpFileUploadComponent;

  campaignForm: FormGroup;
  formErrors = {};
  userAdministrator: {
    id, text
  };
  campaignFeaturesApprovers: any[];
  messages = {
    clientId: {
      required: 'Cliente é obrigatório'
    },
    name: {
      required: 'Nome é obrigatório',
      maxlength: 'Nome deve ter menos de 40 caracteres'
    },
    startDate: {
      required: 'Data de início é obrigatória',
      date: 'Data de início inválida'
    },
    endDate: {
      required: 'Data de término é obrigatória',
      date: 'Data de término inválida'
    },
    coinPrefix: {
      maxlength: 'Prefixo da moeda deve ter no máximo 8 caracteres'
    },
    coinName: { maxlength: 'Sufixo da moeda deve ter no máximo 8 caracteres' },
    pointsExpirationType: {
      required: 'Tipo de expiração de pontos é obrigatória'
    },
    pointsExpirationDate: { date: 'Data de início inválida' },
    pointsExpirationPeriod: {},
    pointsConversionFactor: {
      required: 'Fator de conversão é obrigatório',
      decimal: 'Fator de conversão inválido'
    },
    productConversionFactor: {
      required: 'Fator de conversão é obrigatório',
      decimal: 'Fator de conversão inválido'
    },
    'parametrizations.loginType': {
      required: 'Forma de login é obrigatória'
    },
    'parametrizations.extractType': {
      required: 'Tipo de extrato é obrigatório'
    },
    'parametrizations.minimumShippingTime': {
      required: 'Prazo mínimo é obrigatório',
      range: 'Prazo mínimo inválido'
    },
    'parametrizations.importMomentToNotify': {
      required: 'Momento da importação para notificação é obrigatório'
    }
  };
  clients: Array<Item> = [];
  campaignId: string;
  campaign: Campaign = new Campaign();
  isMakeYourCampaign: string;
  pointsExpirationType: any = {
    settings: 'None',
    fixedDate: {},
    period: {}
  };
  participantData: any = CampaignFactory.createFirstDataDefaultSettings();
  registrationDataAtShoppingCart: any = CampaignFactory.createRegistrationDataAtShoppingCart();
  preRegisterSettings: any = CampaignFactory.createFirstPreRegisterSettings();
  campaignSiteParams: any = CampaignFactory.createCampaignSiteWithDefaultPages();
  integrationSettings: any = {};

  campaignType: string = '';
  campaignPointsExpirationType: string = '';
  campaignModality: string = '';

  useMinimumShippingTime: boolean = false;
  allowBillPaymentSchedulement: boolean = false;
  useBillPaymentMinimumDueDays: boolean = false;
  isEnableCampaignParticipantsGoalsGroupLayout: boolean = false;
  isEnableCampaignRankingFixedPosition: boolean = false;
  limitCartItems: boolean = false;
  requireMinimumOrderTotalAmount: boolean = false;

  rootCampaign: boolean = false;
  childCampaign: boolean = false;
  registrationDataFromRootCampaign: boolean = false;

  loading: boolean = false;
  sending: boolean = false;
  uploading: boolean = false;
  removing: boolean = false;

  campaignMenuItens: Array<any> = [];

  private _subscriptions: Array<Subscription> = [];

  constructor(
    private _authStore: AuthStore,
    private _campaignService: CampaignService,
    private _clientService: CompanyService,
    private route: ActivatedRoute,
    private router: Router,
    private _fb: FormBuilder,
    private _campaignStore: CampaignStore,
    private _campaignFeatureApproverService: CampaignFeatureApproverService
  ) {
    this.buildForm();
  }

  get canViewCampaignFeaturesApprovers() {
    return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_FEATURES_APPROVER_VIEW);
  }

  get canAddCampaignFeaturesApprovers() {
    return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_FEATURES_APPROVER_EDIT);
  }

  get canRemoveCampaignFeaturesApprovers() {
    return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_FEATURES_APPROVER_REMOVE);
  }

  private buildForm() {
    const pointsExpirationTypeCrl = new FormControl(
      { value: '' },
      Validators.required
    );
    const typeCtrl = new FormControl({ value: '' }, Validators.required);
    const modalityCtrl = new FormControl({ value: '' }, Validators.required);
    const loginType = new FormControl({ value: 'Password' }, Validators.required);

    this.campaignForm = this._fb.group({
      active: [true],
      buId: [''],
      type: typeCtrl,
      modality: modalityCtrl,
      coinType: [''],
      license: [''],
      clientId: ['', Validators.required],
      name: [
        '',
        Validators.compose([Validators.required, Validators.maxLength(40)])
      ],
      startDate: [
        null,
        Validators.compose([
          Validators.required,
          CustomValidators.date('dd/mm/yyyy')
        ])
      ],
      endDate: [
        null,
        Validators.compose([
          Validators.required,
          CustomValidators.date('dd/mm/yyyy')
        ])
      ],
      coinPrefix: ['', Validators.maxLength(8)],
      coinName: ['', Validators.maxLength(8)],
      url: [''],
      pointsExpirationType: pointsExpirationTypeCrl,
      pointsExpirationDate: [null, CustomValidators.date('dd/mm/yyyy')],
      pointsExpirationPeriod: [null],
      pointsConversionFactor: [
        1,
        Validators.compose([Validators.required, CustomValidators.decimal])
      ],
      productConversionFactor: [
        0,
        Validators.compose([Validators.required, CustomValidators.decimal])
      ],
      attendancePhoneNumber: [''],
      attendancePeriod: [''],
      attendanceMobileNumber: [''],
      attendanceEmail: [''],
      googleAnalyticsCode: [''],
      campaignSite: this._fb.group({
        url: [''],
        googleAnalyticsCode: ['']
      }),
      parametrizations: this._fb.group({
        loginType: loginType,
        enableGroupCoalition: [false],
        joinParticipantBalance: [false],
        joinParticipantData: [false],
        campaignSiteLayout: ['Default'],
        enableLoginSite: [true],
        enableLoginCatalog: [true],
        enableLoginIntegration: [false],
        enableAutomaticPointsExpiration: [false],
        token: [null],
        createParticipantOnIntegration: [false],
        acceptNonIntegratedParticipant: [false],
        redirectAutomaticallyWhenLoginWithSso: [false],
        enableFirstAccess: [true],
        enableParticipantSelfRegistration: [false],
        skipFirstAccessRegistrationData: [false],
        skipParticipantSelfRegistrationApproval: [false],
        registrationReviewPeriodicity: ['Never'],
        enableClientOrderIntegration: [false],
        requireSuccessOnClientOrderIntegration: [false],
        useClientMechanicPoints: [false], // depreceado
        mechanicPriority: ['Boost'],
        enableMechanicRedemptionRules: [false],
        allowRedeemWithoutBalance: [false],
        acceptRegulationRequired: [false],
        enableCampaignWithMultiRegulations: [false],
        hasNewsletters: [false],
        canUploadPicture: [false],
        allowReversalPoints: [false],
        allowOnlyPartnerOrderRefund: [false],
        enableQuickRedeem: [false],
        allowBuyPoints: [false],
        enableMobileRecharge: [false],
        enableBillPayment: [false],
        enablePrepaidCard: [false],
        enableWithdrawableCard: [false],
        enableNoDrawableCard: [false],
        personTypeAllowedToInviteNewParticipant: ['BOTH'],
        showInvitedParticipantsByParent: [false],
        enableParentBlockInvitedParticipant: [false],
        allowViewInvitedParticipantsDynamicFormFilled: [false],
        skipPlatformMediaboxes: [false],
        skipPlatformSpecialShops: [false],
        allowDrawablePrepaidCardOnlyForPersonType: ['BOTH'],
        allowNoDrawablePrepaidCardOnlyForPersonType: ['BOTH'],
        enableBankTransfer: [false],
        enableP2pTransfer: [false],
        allowChangeShippingAddress: [true],
        allowChangeIntegrationShippingAddress: [false],
        allowAccountOperators: [false],
        allowLoginNotification: [false],
        enableAccountRepresentative: [false],
        enableBlockedBalanceAtCampaignSite: [false],
        enableDynamicFormsPendingBalance: [false],
        allowBillPaymentSchedulement: [false],
        showBillPaymentMessage: [false],
        billPaymentMessage: [''],
        billPaymentMinimumHour: ['7'],
        billPaymentMaximumHour: ['20'],
        useDefaultShippingAddress: [false],
        enableDiscountCoupon: [false],
        enableUpdateRegistrationDataAtShoppingCart: [false],
        originTaxIdForInvoicing: ['AccountDocument'],
        requiredShippingAddressDetails: [true],
        extractType: ['S', Validators.required],
        enableOrdersRegionalization: [false],
        enableSendNotificationPartner: [false],
        enableSendNotificationFactory: [false],
        enableSelectProductFactory: [false],
        enableShippingCalculation: [true],
        enableSelectShippingAddress: [true],
        enableEmbeddedShippingCost: [false],
        useMinimumShippingTime: [false],
        minimumShippingTime: [null],
        useBillPaymentMinimumDueDays: [false],
        billPaymentMinimumDueDays: [null],
        requireMinimumOrderTotalAmount: [false],
        minimumOrderTotalAmount: [null],
        enableLoginCustomErrorMessage: [false],
        loginCustomErrorMessage: [null],
        limitCartItems: [false],
        maximumNumberCartItems: [null],
        nameForTransactionalEmails: [''],
        emailForTransactionalEmails: [''],
        fromForTransactionalSms: [''],
        importMomentToNotify: ['Disabled'],
        requireRedeemToken: [false],
        enableMatchingByEan: [true],
        enableCampaignStock: [false],
        enableProductsRegionalization: [false],
        showProductsListingWithPointsPurchaseCost: [false],
        dynamicPriceDescription: [null],
        skipApprovalOnPointsImporting: [false],
        requireSpecificUserAdministratorApproval: [false],
        enableParticipantsHistoricAtImporting: [false],
        enableCustomMenuByGroup: [false],
        enableMetadataFields: [false],
        enableMetadataFieldsAtParticipantPasswordRecovery: [false],
        enableDocumentFieldAtFirstAccess: [false],
        enableParticipantMigrationAtFirstAccess: [false],
        enableCardRegisterAtFirstAccess: [false],
        allowInformParentParticipantOnPreRegistration: [true],
        requiredExistingParentParticipantOnPreRegistration: [true],
        parentParticipantSearchFieldOnPreRegistration: ['DOCUMENT'],
        allowControlOperatorsRoles: [false],
        campaignRankingType: ['CREDIT'],
        campaignRankingPageLayout: ['CARDS'],
        enableCampaignRankingFixedPosition: [false],
        campaignRankingFixedPosition: ['', [Validators.pattern('^[0-9]*$')]],
        enableCampaignParticipantsGoals: [false],
        enablePaymentMethodsSelection: [false],
        pointsImportTransactionCodeUniquenessRule: ['CAMPAIGN'],
        allowCreateParticipantOnPointsImporting: [false],
        executeTargetAudienceForNewParticipant: [false],
        configureCampaignGoalsForNewParticipant: [false],
        enableCampaignParticipantsGoalsGroupLayout: [false],
        limitVisualizationPreviousPerformanceGoals: ['', [Validators.pattern('^[0-9]*$')]],
        enablePointsExpiringNotification: [false],
        enableGenerateClientUserId: [false],
        limitRedemptionPeriod: [false],
        redemptionPeriodStart: '',
        redemptionPeriodEnd: '',
        allowPlaceOrderDuringRestrictedPeriod: [false],
        limitParticipantSelfRegistrationPeriod: [false],
        participantSelfRegistrationStartDate: '',
        participantSelfRegistrationEndDate: '',
        enableAuthenticationMfa: [false],
        authenticationMfaFormat: ['ALLOW_SELECT_ONCE'],
      }),
      pageSettings: this._fb.group({
        enableContact: [true],
        enableMechanic: [false],
        enableRegulation: [true],
        enablePolicy: [true],
        enableMyAccount: [true],
        enableParticipantData: [true],
        enablePassword: [true],
        enableExtract: [true],
        enableShippingPolicy: [true],
        enableOrders: [true],
        enableAddresses: [true],
        enableScheduledPayments: [true],
        enablePointsToExpireAndBlocked: [true],
        enableFaq: [true],
        pageAfterLogin: ['HomePage'],
        hideMenu: [false],
        showSpecialShopAsFeaturedInHomePage: [false],
        showCartMessage: [false],
        cartMessage: [''],
        showOrderMessage: [false],
        orderMessage: [''],
        showCustomMessageAfterOrderConfirmation: [false]
      })
    });

    typeCtrl.valueChanges.subscribe(value => (this.campaignType = value));
    modalityCtrl.valueChanges.subscribe(
      value => (this.campaignModality = value)
    );
    loginType.valueChanges.subscribe(value => {
      if (this.campaign && this.campaign.parametrizations) {
        this.campaign.parametrizations.loginType = value;
      }
    });
    pointsExpirationTypeCrl.valueChanges.subscribe(
      value => (this.campaignPointsExpirationType = value)
    );
  }

  ngOnInit() {
    if (this.route.parent.parent != null) {
      this._subscriptions.push(
        this.route.parent.parent.params.subscribe((params: any) => {
          this.campaignId = params['id'];
          if (this.campaignId === undefined) {
            this.campaign = new Campaign();
            this.loadClients();
          } else {
            this.loadCampaign(this.campaignId);
            this.getCampaignFeaturesApprovers();
          }
        })
      );
    } else {
      this.router.navigate(['/campanha']);
    }
  }

  ngAfterViewInit() {
    if (!this.canViewBasicCampaign) {
      this.gpAlert.showError(
        'Você não tem permissão para acessar este módulo, por favor, contate o administrador do sistema.'
      );
    }
  }

  // tslint:disable-next-line:use-life-cycle-interface
  ngOnDestroy() {
    RxjsHelpers.unsubscribeMany(this._subscriptions);
  }

  get isEdition() {
    return this.campaignId && this.campaign && this.campaign.id;
  }

  get canViewBasicCampaign(): boolean {
    return (
      this._authStore.role.PERMISSION_CAMPAIGNS_BASIC_VIEW ||
      this._authStore.role.PERMISSION_CAMPAIGNS_VIEW
    );
  }

  get canViewFullCampaign(): boolean {
    return this._authStore.role.PERMISSION_CAMPAIGNS_VIEW;
  }

  get canCreateOrEditCampaign(): boolean {
    return (
      this._authStore.role.PERMISSION_CAMPAIGNS_INS ||
      this._authStore.role.PERMISSION_CAMPAIGNS_EDIT
    );
  }

  get canEditCampaign(): boolean {
    return this._authStore.role.PERMISSION_CAMPAIGNS_EDIT;
  }

  get canAssignBu(): boolean {
    return this._authStore.role.PERMISSION_BUS_ASSIGN;
  }

  get isRootBu(): boolean {
    return this._authStore.loggedUser.isRootBu;
  }

  get canCreate(): boolean {
    return this._authStore.role.PERMISSION_CAMPAIGNS_INS;
  }

  get canEditSecurityParametrization(): boolean {
    return this._authStore.role.PERMISSION_CAMPAIGNS_SECURITY;
  }

  get canEditCampaignConfiguration() {
    if (!this.isEdition && !this.canCreate) return false;
    if (this.canViewFullCampaign) return true;
    if (this.canViewBasicCampaign) {
      if (this._campaignStore.isFullCampaign) return false;
      if (this._campaignStore.isMakeYourCampaign) return false;
    }

    return true;
  }

  get isCampaignB2b() {
    return isB2b(this.campaignType);
  }

  get isCampaignRewards() {
    return isRewards(this.campaignType);
  }

  get hasCatalog(): boolean {
    return (
      this.campaignModality == 'OnlyCatalog' ||
      this.campaignModality == 'CatalogWithSite'
    );
  }

  get hasCampaignSite(): boolean {
    return (
      this.campaignModality == 'OnlySite' ||
      this.campaignModality == 'CatalogWithSite'
    );
  }

  get disableButtons(): boolean {
    return (
      !this.campaignForm.valid ||
      this.loading ||
      this.sending ||
      this.uploading ||
      this.removing
    );
  }

  get hasSelectedCatalogLayout() {
    return this.gpCatalogLayout && this.gpCatalogLayout.hasSelectedFile();
  }

  get hasSelectedSiteLayout() {
    return this.gpSiteLayout && this.gpSiteLayout.hasSelectedFile();
  }

  get hasSelectedLayout(): boolean {
    return this.hasSelectedCatalogLayout || this.hasSelectedSiteLayout;
  }

  get hasCustomLayout(): boolean {
    return this.campaignId && this.campaign && this.campaign.hasTheme;
  }

  get hasCustomSiteLayout(): boolean {
    return (
      this.campaignId &&
      this.campaign &&
      this.campaign.campaignSite &&
      this.campaign.campaignSite.hasTheme
    );
  }

  get useExpirationPeriodSettings(): boolean {
    return this.campaignPointsExpirationType == 'Period';
  }

  get useExpirationFixedDateSettings(): boolean {
    return this.campaignPointsExpirationType == 'FixedDate';
  }

  get isRootCampaign(): boolean {
    return this.rootCampaign;
  }

  get isChildCampaign(): boolean {
    return this.childCampaign;
  }

  get isRegistrationDataFromRootCampaign(): boolean {
    return this.registrationDataFromRootCampaign;
  }

  loadCampaign(campaignId: string) {
    if (!this.canViewBasicCampaign) {
      return;
    }
    this.loading = true;
    this._campaignService.getById(campaignId).subscribe(
      campaign => {
        if (campaign == null) {
          this.router.navigate(['/campanha']);
        }
        this.prepareFormToEdit(campaign);
      },
      err => this.handleError(err),
      () => (this.loading = false)
    );
  }

  private prepareFormToEdit(campaign: Campaign) {
    this.campaignId = campaign.id;
    if (!campaign.campaignSite) campaign.campaignSite = {};

    if (campaign.parametrizations) {
      this.useMinimumShippingTime =
        campaign.parametrizations.useMinimumShippingTime;
      this.allowBillPaymentSchedulement =
        campaign.parametrizations.allowBillPaymentSchedulement;
      this.useBillPaymentMinimumDueDays =
        campaign.parametrizations.useBillPaymentMinimumDueDays;
      this.requireMinimumOrderTotalAmount =
        campaign.parametrizations.requireMinimumOrderTotalAmount;
      this.limitCartItems = campaign.parametrizations.limitCartItems;
      this.isEnableCampaignRankingFixedPosition =
        campaign.parametrizations.enableCampaignRankingFixedPosition;
      this.isEnableCampaignParticipantsGoalsGroupLayout =
        campaign.parametrizations.enableCampaignParticipantsGoalsGroupLayout;
    } else {
      campaign.parametrizations = {};
    }
    this.handleCoalitionCampaign(campaign);
    if (!campaign.pageSettings) campaign.pageSettings = {};

    this.campaignSiteParams =
      campaign.sitePageSettings ||
      CampaignFactory.createCampaignSiteWithDefaultPages();

    if (!this.campaignSiteParams.homePagePostsOrigin) {
      this.campaignSiteParams.homePagePostsOrigin = 'ALL';
    }

    this.participantData =
      campaign.pageSettings.participantData ||
      CampaignFactory.createFirstDataDefaultSettings();

    this.registrationDataAtShoppingCart =
      campaign.pageSettings.registrationDataAtShoppingCart ||
      CampaignFactory.createRegistrationDataAtShoppingCart();

    this.preRegisterSettings =
      campaign.pageSettings.preRegisterSettings ||
      CampaignFactory.createFirstPreRegisterSettings();

    this.campaignPointsExpirationType = campaign.pointsExpirationType;

    this.integrationSettings = campaign.integrationSettings || { integrator: 'None' };

    this.campaign = campaign;
    this.campaignForm.patchValue(campaign);
  }

  private handleCoalitionCampaign(campaign: Campaign) {
    if (!campaign.parametrizations.groupColiationParametrization) {
      campaign.parametrizations.groupColiationParametrization = {
        enableGroupCoalition: false,
        campaignRole: {
          DISABLED: 'DISABLED',
        },
        features: {
          enableRegistrationData: false
        }
      }
    }
    if (campaign.parametrizations.groupColiationParametrization.enableGroupCoalition &&
      campaign.parametrizations.groupColiationParametrization.campaignRole == 'CHILD_CAMPAIGN' &&
      campaign.parametrizations.groupColiationParametrization.features.enableRegistrationData) {
      this.childCampaign = true;
      this.registrationDataFromRootCampaign = true;
    }
    if (campaign.parametrizations.groupColiationParametrization.enableGroupCoalition &&
      campaign.parametrizations.groupColiationParametrization.campaignRole == 'ROOT_CAMPAIGN') {
      this.rootCampaign = true;
    }
  }

  loadClients() {
    if (!this.isEdition) {
      this._clientService.getClientsToListing().subscribe(
        clients => {
          if (clients) {
            this.clients = clients.map(c => Item.of(c.id, c.name));
          }
        },
        err => this.handleError(err)
      );
    }
  }

  private setClientName() {
    if (this.campaign && this.campaign.clientId && this.clients) {
      const client = this.clients.find(c => c.id == this.campaign.clientId);
      if (client) {
        this.campaign.clientName = client.text;
      }
    }
  }

  public handleError(err) {
    let errorMessage = err
      ? err.message
        ? err.message
        : err.toString()
      : 'Ocorreu um erro ao efetuar a operação.';
    this.loading = false;
    this.sending = false;
    this.removing = false;
    this.gpAlert.showError(errorMessage);
  }

  useMinimumShippingTimeChange(event) {
    this.useMinimumShippingTime = event;
  }

  allowBillPaymentSchedulementChange(event) {
    this.allowBillPaymentSchedulement = event;
  }

  useBillPaymentMinimumDueDaysChange(event) {
    this.useBillPaymentMinimumDueDays = event;
  }

  useEnableCampaignRankingFixedPosition(event) {
    this.isEnableCampaignRankingFixedPosition = event;
  }

  useEnableCampaignParticipantsGoalsGroupLayout(event) {
    this.isEnableCampaignParticipantsGoalsGroupLayout = event;
  }

  limitCartItemsChange(event) {
    this.limitCartItems = event;
  }

  cancel() {
    if (confirm('Tem certeja que deseja cancelar as alterações?')) {
      this.campaignForm.patchValue(this.campaign);
    }
  }

  validate(campaign): boolean {
    if (campaign.type === 'Rewards') {
      if (
        campaign.coinType === 'Pontos' &&
        !campaign.coinPrefix &&
        !campaign.coinName
      ) {
        this.gpAlert.showWarning(
          'Para campanhas de incentivo é obrigatório informar o prefixo ou sufixo para a moeda.'
        );
        return false;
      } else if (
        !campaign.pointsExpirationDate &&
        campaign.pointsExpirationType == 'FixedDate'
      ) {
        this.gpAlert.showWarning(
          'Para campanhas de incentivo é obrigatório informar a data de expiração dos pontos.'
        );
        return false;
      } else if (
        !campaign.pointsExpirationPeriod &&
        campaign.pointsExpirationType == 'Period'
      ) {
        this.gpAlert.showWarning(
          'Para campanhas de incentivo é obrigatório informar o prazo de expiração dos pontos.'
        );
        return false;
      }

      if (campaign.parametrizations.allowBillPaymentSchedulement) {
        if (campaign.parametrizations.billPaymentMinimumHour) {
          if (
            campaign.parametrizations.billPaymentMinimumHour < 0 ||
            campaign.parametrizations.billPaymentMinimumHour > 20
          ) {
            this.gpAlert.showWarning(
              'O horário de início do período de pagamento precisa estar entre 0h e 20h.'
            );
            return false;
          }
        }
        if (campaign.parametrizations.billPaymentMaximumHour) {
          if (
            campaign.parametrizations.billPaymentMaximumHour < 0 ||
            campaign.parametrizations.billPaymentMaximumHour > 20
          ) {
            this.gpAlert.showWarning(
              'O horário final do período de pagamento precisa estar entre 0h e 20h.'
            );
            return false;
          }
        }
        if (
          campaign.parametrizations.billPaymentMinimumHour &&
          campaign.parametrizations.billPaymentMaximumHour
        ) {
          if (
            campaign.parametrizations.billPaymentMinimumHour >
            campaign.parametrizations.billPaymentMaximumHour
          ) {
            this.gpAlert.showWarning(
              'O horário inicial do período não pode ser maior que o horário final do período.'
            );
            return false;
          }
        }
      }
    }
    try {
      Object.keys(campaign.sitePageSettings).forEach(e => {
        const fields = campaign.sitePageSettings[e];
        if (fields.enable && fields.visibleMenu && (!fields.text || !fields.text.length)) {
          // tslint:disable-next-line: no-string-throw
          throw 'Preencha o texto dos menus visíveis';
        }
      });
    } catch (err) {
      this.gpAlert.showWarning(err);
      return false;
    }
    return true;
  }

  onSubmit(event) {
    if (!event.valid) return;
    event.model.clientName = this.campaign.clientName;
    event.model.hasTheme = this.campaign.hasTheme;

    if (!this.canEditSecurityParametrization) {
      event.model.parametrizations.allowPlaceOrderDuringRestrictedPeriod = this.campaign.parametrizations.allowPlaceOrderDuringRestrictedPeriod;
    }

    this.campaign = event.model;
    if (this.campaignId) {
      this.campaign.id = this.campaignId;
    } else {
      this.campaign.id = '';
      this.setClientName();
    }

    if (this.participantData) {
      if (!this.campaign.pageSettings) this.campaign.pageSettings = {};
      this.campaign.pageSettings.participantData = this.participantData;
    }
    if (this.registrationDataAtShoppingCart) {
      if (!this.campaign.pageSettings) this.campaign.pageSettings = {};
      this.campaign.pageSettings.registrationDataAtShoppingCart = this.registrationDataAtShoppingCart;
    }
    if (this.preRegisterSettings) {
      if (!this.campaign.pageSettings) this.campaign.pageSettings = {};
      this.campaign.pageSettings.preRegisterSettings = this.preRegisterSettings;
    }

    if (this.integrationSettings) {
      this.campaign.integrationSettings = this.integrationSettings;
    }

    this.campaign.sitePageSettings = this.campaignSiteParams;

    if (!this.validate(this.campaign)) {
      return;
    }

    Object.keys(this.campaign.sitePageSettings).forEach(e => {
      const fields = this.campaign.sitePageSettings[e];
      if (fields.menuItemIndex == 0) {
        this.campaign.sitePageSettings[e].menuItemIndex = 999;
      }
    });

    this.sending = true;
    this.gpAlert.clear();

    this._campaignService.save(this.campaign).subscribe(
      createdCampaign => {
        this._campaignStore.setStateUsing(this.campaign);
        if (createdCampaign) {
          if (!this.campaignId) {
            this.prepareFormToEdit(createdCampaign);
          }

          if (this.hasSelectedLayout) {
            this.gpAlert.showSuccess(
              'Campanha salva com sucesso. Por favor, aguarde o upload do tema.'
            );
            this.uploadFiles();
            this.loadCampaign(this.campaignId);
          } else {
            this.gpAlert.showSuccess('Campanha salva com sucesso.');
            this.loadCampaign(this.campaignId);
          }
        } else {
          this.gpAlert.showWarning(
            'Não foi possível salvar os dados da campanha, por favor, tente novamente.'
          );
        }
      },
      err => this.handleError(err),
      () => (this.sending = false)
    );
  }

  removeCurrentCatalogLayout() {
    this.gpAlert.clear();
    if (!confirm('Deseja remover o layout customizado atual do catálogo?')) {
      return;
    }
    this.removing = true;
    this._campaignService.removeCurrentCatalogLayout(this.campaignId).subscribe(
      result => {
        if (result) {
          this.gpAlert.showSuccess('Layout do catálogo removido com sucesso.');
          this.campaign.hasTheme = false;
        } else {
          this.gpAlert.showWarning(
            'Não foi possível remover o layout do catálogo, por favor, tente novamente.'
          );
        }
        this.removing = false;
      },
      err => this.handleError(err)
    );
  }

  removeCurrentSiteLayout() {
    this.gpAlert.clear();
    if (
      !confirm('Deseja remover o layout customizado atual do site campanha?')
    ) {
      return;
    }
    this.removing = true;
    this._campaignService.removeCurrentSiteLayout(this.campaignId).subscribe(
      result => {
        if (result) {
          this.gpAlert.showSuccess(
            'Layout do site campanha removido com sucesso.'
          );
          this.campaign.campaignSite.hasTheme = false;
        } else {
          this.gpAlert.showWarning(
            'Não foi possível remover o layout do site campanha, por favor, tente novamente.'
          );
        }
        this.removing = false;
      },
      err => this.handleError(err)
    );
  }

  uploadFiles() {
    this.gpAlert.clear();
    if (!this.hasSelectedLayout) {
      this.gpAlert.showWarning('Selecione um arquivo para upload.');
      this.sending = false;
      return;
    }
    this.uploading = true;
    if (this.hasSelectedCatalogLayout) {
      this.gpCatalogLayout.path = `api/campaigns/${this.campaignId}/cataloglayout`;
      this.gpCatalogLayout.uploadFile();
    }
    if (this.hasSelectedSiteLayout) {
      this.gpSiteLayout.path = `api/campaigns/${this.campaignId}/sitelayout`;
      this.gpSiteLayout.uploadFile();
    }
  }

  onCompleteCatalog(event) {
    this.gpAlert.clear();
    if (event.success) {
      this.gpAlert.showSuccess(
        'Arquivo de layout do catálogo enviado com sucesso.'
      );
      this.campaign.hasTheme = true;
      this.gpCatalogLayout.createUploader();
    } else {
      this.gpAlert.showError(event.errorMessage);
    }
    this.sending = false;
    this.uploading = false;
  }

  onCompleteSite(event) {
    this.gpAlert.clear();
    if (event.success) {
      this.gpAlert.showSuccess(
        'Arquivo de layout do site campanha enviado com sucesso.'
      );
      if (!this.campaign.campaignSite) this.campaign.campaignSite = {};
      this.campaign.campaignSite.hasTheme = true;
      this.gpSiteLayout.createUploader();
    } else {
      this.gpAlert.showError(event.errorMessage);
    }
    this.sending = false;
    this.uploading = false;
  }

  handleLicenseDescription(license: string) {
    switch (license) {
      case 'MAKE_YOUR_CAMPAIGN':
        return 'Monte sua campanha';
      case 'FULL':
        return 'Campanha completa';
      default:
        return '';
    }
  }
  handleModalityDescription(modality: string) {
    switch (modality) {
      case 'OnlyCatalog':
        return 'Catálogo';
      case 'OnlySite':
        return 'Site Campanha';
      case 'CatalogWithSite':
        return 'Catálogo + Site Campanha';
      default:
        '';
    }

    return '';
  }

  handleTypeCampaign(type: string) {
    return getTypeDescription(type);
  }

  setUserAdministrator(event) {
    this.userAdministrator = event;
  }

  addCampaignFeaturesApprover() {
    this.loading = true;
    this._campaignFeatureApproverService.addCampaignFeaturesApprover(this.campaignId, 'PointsImport', { id: this.userAdministrator.id, name: this.userAdministrator.text })
      .subscribe(
        response => {
          this.gpAlert.showSuccess('Administrador adicionado.')
          this.loading = false;
          this.getCampaignFeaturesApprovers();
        }, err => {
          this.loading = false;
          this.gpAlert.showError(err);
        }
      );
  }

  getCampaignFeaturesApprovers() {
    this.loading = true;
    this._campaignFeatureApproverService.getCampaignFeaturesApprovers(this.campaignId, 'PointsImport')
      .subscribe(
        response => {
          this.campaignFeaturesApprovers = response || [];
          this.loading = false;

        }, err => {
          this.loading = false;
          this.gpAlert.showError(err);
        }
      );
  }

  removeCampaignFeaturesApprover(approver) {
    this.loading = true;
    this._campaignFeatureApproverService.removeCampaignFeaturesApprover(this.campaignId, approver.id, 'PointsImport')
      .subscribe(
        response => {
          this.gpAlert.showSuccess('Administrador removido.')
          this.loading = false;
          this.getCampaignFeaturesApprovers();

        }, err => {
          this.loading = false;
          this.gpAlert.showError(err);
        }
      );
  }
}
