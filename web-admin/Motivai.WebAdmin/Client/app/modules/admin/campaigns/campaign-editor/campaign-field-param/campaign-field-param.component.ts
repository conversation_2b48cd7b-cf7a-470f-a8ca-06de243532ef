import { Component, Input, forwardRef  } from '@angular/core';
import { NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';

@Component({
    selector: 'campaign-field-param',
    template: `
        <div class="row">
            <div grid="12">
                <label>{{ label }}</label>
            </div>
            <div grid="3">
                <div class="form-group">
                    <label>Label do campo</label>
                    <input name="name_label" [(ngModel)]="value.label" type="text" class="form-control" placeholder="{{ label }}" />
                </div>
            </div>
            <div grid="2">
                <label>Obrigatório</label>
                <div><gp-switch name="name_required" [(ngModel)]="value.required" title="Obrigatório"></gp-switch></div>
            </div>
            <div grid="2">
                <label>Visível</label>
                <div><gp-switch name="name_visible" [(ngModel)]="value.visible" [ngModelOptions]="{standalone: true}" title="Visível"></gp-switch></div>
            </div>
            <div grid="2">
                <label>Desabilitado</label>
                <div><gp-switch name="name_disabled" [(ngModel)]="value.disabled" [ngModelOptions]="{standalone: true}" title="Desabilitado"></gp-switch></div>
            </div>
        </div>
    `,
    providers: [
        { provide: NG_VALUE_ACCESSOR, useExisting: forwardRef(() => CampaignFieldParamComponent), multi: true }
    ]
})
export class CampaignFieldParamComponent implements ControlValueAccessor {
    @Input() label: string;

    value: any = {};

    constructor() { }

    private onChange: any = () => {};
    private onTouched: any = () => {};

    registerOnChange(fn: any): void {
        this.onChange = fn;
    }

    registerOnTouched(fn: any): void {
        this.onTouched = fn;
    }

    writeValue(obj: any): void {
        if (obj) {
            this.value = obj;
        } else {
            this.value = {};
        }
    }
}
