import { Component, Input, ViewChild, OnInit, AfterViewInit  } from '@angular/core';
import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { Item } from '../../../../../shared/models/item';
import { CampaignService } from '../../campaign.service';
import { getCampaignIntegrationParamsTemplate } from './models/campaign-integration-params-template';
import { isNullOrUndefined } from 'util';

@Component({
  selector: 'app-campaign-integration-params',
  templateUrl: './campaign-integration-params.component.html'
})
export class CampaignIntegrationParamsComponent implements OnInit, AfterViewInit {
  @ViewChild('alert') alert: GpAlertComponent;
  integrationSettings: any = {
    integrator: 'None',
    dotzSettings: {},
    fzcomSettings: {},
    gogoDigitalSettings: {},
    oAuth2Settings: {},
    externalIntegrationSettings: []
  };

  ssoUrl: string = '';
  parametrizations: any = {};
  externalIntegrationParameters: any = {};

  @Input('parametrizations')
  public set setParametrizations(parametrizations: any) {
    this.parametrizations = parametrizations;
    if (parametrizations && parametrizations.token) {
      this.ssoUrl = `https://login-sso.motivai.com.br/${parametrizations.token}`;
    } else {
      this.ssoUrl = '';
    }
  }

  @Input('integrationSettings')
  public set setIntegrationSettings(integrationSettings: any) {
    this.integrationSettings = integrationSettings;

    if (!this.integrationSettings.dotzSettings) this.integrationSettings.dotzSettings = {};
    if (!this.integrationSettings.fzcomSettings) this.integrationSettings.fzcomSettings = {};
    if (!this.integrationSettings.samlSettings) this.integrationSettings.samlSettings = {};
    if (!this.integrationSettings.oAuth2Settings) this.integrationSettings.oAuth2Settings = {};
  }

  @Input() registrationDataFromRootCampaign: boolean = false;
  @Input()
  campaignId: string;

  defaultIntegrations: Array<Item> = [];

  hasTemplateIntegrationParameters() : boolean {
    return !isNullOrUndefined(this.templateIntegrationParameters) && this.templateIntegrationParameters.length > 0;
  }

  get templateIntegrationParameters() : Array<any> {
    return getCampaignIntegrationParamsTemplate()[this.integrationSettings.integrator];
  }

  constructor(private campaignService: CampaignService) {}

  ngOnInit(): void {}

  ngAfterViewInit() {
    this.loadExternalIntegrations();
  }

  loadExternalIntegrations(): void {
    this.campaignService
      .getCampaignExternalIntegrationsParametrization(this.campaignId)
      .subscribe(
        response => {
          this.defaultIntegrations = response.map(integration =>
            Item.of(integration.name, integration.description)
          );
        },
        err => {
          console.log(err);
        }
      );
  }

  get useAnyIntegrator(): boolean {
    return this.integrationSettings && this.integrationSettings.integrator != 'None';
  }

  get useNoneIntegrator(): boolean {
    return (
      this.integrationSettings && this.integrationSettings.integrator == 'None'
    );
  }

  get useGopagueIntegrator(): boolean {
    return this.integrationSettings && this.integrationSettings.integrator == 'Gopague';
  }

  get useFzcomIntegrator(): boolean {
    return this.integrationSettings && this.integrationSettings.integrator == 'Fzcom';
  }

  get isAuthenticationWithOAuth2(): boolean {
    if (!this.parametrizations) return false;
    return this.parametrizations.loginType === 'PasswordAndSsoOAuth';
  }

  get isRegistrationDataFromRootCampaign() {
    return this.registrationDataFromRootCampaign;
  }

  get isDefaultIntegration(): boolean {
    return (
      this.useFzcomIntegrator ||
      this.useGopagueIntegrator
    );
  }

  get hasExternalIntegrationSettings(): boolean {
    return (
      this.integrationSettings.externalIntegrationSettings &&
      this.integrationSettings.externalIntegrationSettings.length > 0
    );
  }

  addParamsExternalIntegration(parameters: any) {
    if (!this.integrationSettings.externalIntegrationSettings) {
      this.integrationSettings.externalIntegrationSettings = [];
    }

    this.integrationSettings.externalIntegrationSettings.push(parameters);
    this.externalIntegrationParameters = {};
  }

  removeParamsExternalIntegration(name: string) {
    if (!this.integrationSettings.externalIntegrationSettings) {
      return;
    }

    this.integrationSettings.externalIntegrationSettings = this.integrationSettings.externalIntegrationSettings.filter(
      (e: { name: string }) => e.name != name
    );
  }
}
