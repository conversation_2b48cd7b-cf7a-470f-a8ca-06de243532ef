import { Component, forwardRef } from '@angular/core';
import { NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';

import { CampaignFactory } from '../../campaign.factory';

@Component({
    selector: 'campaign-personal-data-params',
    templateUrl: 'campaign-personal-data-params.component.html',
    providers: [
        { provide: NG_VALUE_ACCESSOR, useExisting: forwardRef(() => CampaignPersonalDataParamsComponent), multi: true }
    ]
})
export class CampaignPersonalDataParamsComponent implements ControlValueAccessor {
    value: any = CampaignFactory.createFirstDataDefaultSettings();

    writeValue(obj: any): void {
        if (obj) {
            this.value = obj;
        } else {
            this.value = CampaignFactory.createFirstDataDefaultSettings();
        }
    }

    onChange = () => {};
    onTouched = () => {};

    registerOnChange(fn: any): void {
        this.onChange = fn;
    }

    registerOnTouched(fn: any): void {
        this.onTouched = fn;
    }
}
