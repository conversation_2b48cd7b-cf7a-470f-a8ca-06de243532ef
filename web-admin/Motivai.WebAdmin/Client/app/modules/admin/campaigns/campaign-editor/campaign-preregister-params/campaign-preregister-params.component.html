<gp-card title="Configurações do pré-cadastro" [first]="true">
	<div class="row">
		<div grid="4">
			<label>Tipo de pessoa que pode realizar o pré-cadastro:</label>
			<div>
				<select class="form-control input-sm" [(ngModel)]="value.personTypeAllowedInPreRegister"
					[attr.disabled]="isRegistrationDataFromRootCampaign === true ? true : null" required>
					<option value="BOTH">Ambos</option>
					<option value="Fisica">Física</option>
					<option value="Juridica">Jurídica</option>
				</select>
			</div>
		</div>
	</div>
	<br>
	<div class="row">
		<div grid="2">
			<label>Habilitar Dados Pessoais</label>
			<div>
				<gp-switch name="enablePersonalData" [(ngModel)]="value.enablePersonalData"
					title="Habilitar Dados Pessoais"></gp-switch>
			</div>
		</div>
		<div grid="8">
			<div class="form-group">
				<label>T<PERSON><PERSON><PERSON> (Dados Pessoais)</label>
				<input name="personalDataTitle" [(ngModel)]="value.personalDataTitle" type="text" class="form-control"
					placeholder="Dados pessoais" />
			</div>
		</div>
	</div>
	<campaign-field-param name="name" ngDefaultControl [(ngModel)]="value.personalData.name" label="Nome"
		[ngModelOptions]="{standalone:true}"></campaign-field-param>
	<campaign-field-param name="companyName" ngDefaultControl [(ngModel)]="value.personalData.companyName"
		label="Razão Social" [ngModelOptions]="{standalone:true}"></campaign-field-param>
	<campaign-field-param name="tradingName" ngDefaultControl [(ngModel)]="value.personalData.tradingName"
		label="Nome Fantasia" [ngModelOptions]="{standalone:true}"></campaign-field-param>
	<campaign-field-param name="document" ngDefaultControl [(ngModel)]="value.personalData.document"
		label="Documento (CPF/CNPJ)" [ngModelOptions]="{standalone:true}"></campaign-field-param>
	<campaign-field-param name="stateInscription" ngDefaultControl [(ngModel)]="value.personalData.stateInscription"
		label="Inscrição Estadual" [ngModelOptions]="{standalone:true}"></campaign-field-param>
	<campaign-field-param name="stateInscriptionUf" ngDefaultControl [(ngModel)]="value.personalData.stateInscriptionUf"
		label="Inscrição Estadual (UF)" [ngModelOptions]="{standalone:true}"></campaign-field-param>
	<campaign-field-param name="rg" ngDefaultControl [(ngModel)]="value.personalData.rg" label="RG"
		[ngModelOptions]="{standalone:true}"></campaign-field-param>
	<campaign-field-param name="birthDate" ngDefaultControl [(ngModel)]="value.personalData.birthDate"
		label="Data de Nascimento" [ngModelOptions]="{standalone:true}"></campaign-field-param>
	<campaign-field-param name="maritalStatus" ngDefaultControl [(ngModel)]="value.personalData.maritalStatus"
		label="Estado Civil" [ngModelOptions]="{standalone:true}"></campaign-field-param>
	<campaign-field-param name="Sexo" ngDefaultControl [(ngModel)]="value.personalData.gender" label="Sexo"
		[ngModelOptions]="{standalone:true}"></campaign-field-param>
	<hr />
	<div class="row">
		<div grid="3">
			<label>Habilitar Endereço Residencial</label>
			<div>
				<gp-switch name="enableHomeAddress" [(ngModel)]="value.enableHomeAddress"
					title="Habilitar Endereço Residencial"></gp-switch>
			</div>
		</div>
		<div grid="9">
			<div class="form-group">
				<label>Título (Endereço Residencial)</label>
				<input name="homeAddressTitle" [(ngModel)]="value.homeAddressTitle" type="text" class="form-control"
					placeholder="Endereço Residencial" />
			</div>
		</div>
	</div>
	<div class="row">
		<div grid="3">
			<label>Habilitar Endereço Comercial</label>
			<div>
				<gp-switch name="enableBusinessAddress" [(ngModel)]="value.enableBusinessAddress"
					title="Habilitar Endereço Comercial"></gp-switch>
			</div>
		</div>
		<div grid="9">
			<div class="form-group">
				<label>Título (Endereço Comercial)</label>
				<input name="businessAddressTitle" [(ngModel)]="value.businessAddressTitle" type="text"
					class="form-control" placeholder="Endereço Comercial" />
			</div>
		</div>
	</div>
	<campaign-field-param name="zipcode" ngDefaultControl [(ngModel)]="value.address.zipcode" label="CEP">
	</campaign-field-param>
	<campaign-field-param name="street" ngDefaultControl [(ngModel)]="value.address.street" label="Logradouro">
	</campaign-field-param>
	<campaign-field-param name="number" ngDefaultControl [(ngModel)]="value.address.number" label="Número">
	</campaign-field-param>
	<campaign-field-param name="complement" ngDefaultControl [(ngModel)]="value.address.complement" label="Complemento">
	</campaign-field-param>
	<campaign-field-param name="neighborhood" ngDefaultControl [(ngModel)]="value.address.neighborhood" label="Bairro">
	</campaign-field-param>
	<campaign-field-param name="city" ngDefaultControl [(ngModel)]="value.address.city" label="Cidade">
	</campaign-field-param>
	<campaign-field-param name="state" ngDefaultControl [(ngModel)]="value.address.state" label="Estado">
	</campaign-field-param>
	<campaign-field-param name="reference" ngDefaultControl [(ngModel)]="value.address.reference"
		label="Ponto de Referência"></campaign-field-param>
	<hr />
	<div class="row">
		<div grid="3">
			<label>Habilitar Telefones</label>
			<div>
				<gp-switch name="enablePhones" [(ngModel)]="value.enablePhones" title="Habilitar Telefones"></gp-switch>
			</div>
		</div>
		<div grid="9">
			<div class="form-group">
				<label>Título (Telefones)</label>
				<input name="telephonesTitle" [(ngModel)]="value.phonesTitle" type="text" class="form-control"
					placeholder="Telefones" />
			</div>
		</div>
	</div>
	<campaign-field-param name="homePhone" ngDefaultControl [(ngModel)]="value.phones.homePhone"
		label="Telefone Residencial"></campaign-field-param>
	<campaign-field-param name="businessPhone" ngDefaultControl [(ngModel)]="value.phones.businessPhone"
		label="Telefone Comercial"></campaign-field-param>
	<campaign-field-param name="mobilePhone" ngDefaultControl [(ngModel)]="value.phones.mobilePhone"
		label="Telefone Celular"></campaign-field-param>
	<campaign-field-param name="mobileOperator" ngDefaultControl [(ngModel)]="value.phones.mobileOperator"
		label="Operadora"></campaign-field-param>
	<hr />
	<div class="row">
		<div grid="3">
			<label>Habilitar E-mails</label>
			<div>
				<gp-switch name="enableEmails" [(ngModel)]="value.enableEmails" title="Habilitar E-mails"></gp-switch>
			</div>
		</div>
		<div grid="9">
			<div class="form-group">
				<label>Título (E-mails)</label>
				<input name="emailsTitle" [(ngModel)]="value.emailsTitle" type="text" class="form-control"
					placeholder="E-mails" />
			</div>
		</div>
	</div>
	<campaign-field-param name="personalEmail" ngDefaultControl [(ngModel)]="value.emails.personalEmail"
		label="E-mail Pessoal"></campaign-field-param>
	<campaign-field-param name="businessEmail" ngDefaultControl [(ngModel)]="value.emails.businessEmail"
		label="E-mail Comercial"></campaign-field-param>
	<hr />
	<div class="row">
		<div grid="3">
			<label>Habilitar Senha</label>
			<div>
				<gp-switch name="enablePassword" [(ngModel)]="value.enablePassword" title="Habilitar Senha"></gp-switch>
			</div>
		</div>
		<div grid="9">
			<div class="form-group">
				<label>Título (Senha)</label>
				<input name="passwordTitle" [(ngModel)]="value.passwordTitle" type="text" class="form-control"
					placeholder="Senha" />
			</div>
		</div>
	</div>
	<campaign-field-param name="password" ngDefaultControl [(ngModel)]="value.password.password" label="Senha">
	</campaign-field-param>
	<hr />
	<campaign-field-param name="parentParticipantSearchField" ngDefaultControl
		[(ngModel)]="value.parentParticipant.searchField" label="Campo de pesquisa do participante pai">
	</campaign-field-param>
	<hr />
	<div class="row">
		<div grid="3">
			<label>Habilitar Campos Dinâmicos</label>
			<div>
				<gp-switch name="enableMetadataAtPreRegister"
					[(ngModel)]="value.personalData.enableMetadataAtPreRegister"
					title="Habilitar campos dinâmicos no pré-cadastro"></gp-switch>
			</div>
		</div>
	</div>
	<hr />
	<div class="row">
		<div grid="3">
			<label>Exibir mensagem de pós-cadastro</label>
			<div>
				<gp-switch name="enablePreRegistrationFinishMessage" [(ngModel)]="value.viewParametrizations.enablePreRegistrationFinishMessage"
					title="Exibir mensagem de pós-cadastro">
				</gp-switch>
			</div>
		</div>
		<div grid="9">
			<div class="form-group">
				<label>Mensagem para exibição após o cadastro</label>
				<input name="preRegistrationFinishMessage" [(ngModel)]="value.viewParametrizations.preRegistrationFinishMessage"
					type="text" class="form-control" />
			</div>
		</div>
	</div>
</gp-card>
