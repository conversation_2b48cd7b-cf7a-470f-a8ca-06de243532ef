<gp-card title="Configuração de Integração OAuth2">
  <gp-form-row>
    <gp-form-col cols="12 6">
      <gp-simple-input label="Client ID">
        <input type="text" class="form-control" name="clientId"
          [(ngModel)]="oAuth2Settings.clientId" />
      </gp-simple-input>
    </gp-form-col>

    <gp-form-col cols="12 6">
      <gp-simple-input label="Client Secret">
        <input type="text" class="form-control" name="clientSecret"
          [(ngModel)]="oAuth2Settings.clientSecret" />
      </gp-simple-input>
    </gp-form-col>
  </gp-form-row>

  <gp-form-row>
    <gp-form-col cols="12 6">
      <gp-simple-input label="Scope">
        <input type="text" class="form-control" name="scopes"
          [(ngModel)]="oAuth2Settings.scopes" />
      </gp-simple-input>
    </gp-form-col>
  </gp-form-row>

  <gp-form-row>
    <gp-form-col cols="12 6">
      <gp-simple-input label="Authorization Endpoint">
        <input type="text" class="form-control" name="authorizationEndpoint"
          [(ngModel)]="oAuth2Settings.authorizationEndpoint" />
      </gp-simple-input>
    </gp-form-col>
    <gp-form-col cols="12 6">
      <gp-simple-input label="Token Endpoint">
        <input type="text" class="form-control" name="tokenEndpoint"
          [(ngModel)]="oAuth2Settings.tokenEndpoint" />
      </gp-simple-input>
    </gp-form-col>
  </gp-form-row>
</gp-card>
