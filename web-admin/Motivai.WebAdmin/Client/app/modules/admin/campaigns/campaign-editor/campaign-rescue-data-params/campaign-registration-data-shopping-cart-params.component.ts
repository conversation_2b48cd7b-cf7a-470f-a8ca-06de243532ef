import { Component, forwardRef } from '@angular/core';
import { NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';

import { CampaignFactory } from '../../campaign.factory';

@Component({
    selector: 'campaign-registration-data-shopping-cart-params',
    templateUrl: './campaign-registration-data-shopping-cart-params.component.html',
    providers: [
        { provide: NG_VALUE_ACCESSOR, useExisting: forwardRef(() => CampaignRegistrationDataAtShoppingCartComponent), multi: true }
    ]
})
export class CampaignRegistrationDataAtShoppingCartComponent implements ControlValueAccessor {
    value: any = CampaignFactory.createRegistrationDataAtShoppingCart();

    writeValue(obj: any): void {
        if (obj) {
            this.value = obj;
        } else {
            this.value = CampaignFactory.createRegistrationDataAtShoppingCart();
        }
    }

    onChange = () => {};
    onTouched = () => {};

    registerOnChange(fn: any): void {
        this.onChange = fn;
    }

    registerOnTouched(fn: any): void {
        this.onTouched = fn;
    }
}
