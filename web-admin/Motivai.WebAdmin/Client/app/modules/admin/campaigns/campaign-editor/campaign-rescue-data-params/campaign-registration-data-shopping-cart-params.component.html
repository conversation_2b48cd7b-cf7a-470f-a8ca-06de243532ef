<gp-card title="Configurações do resgate" [first]="true">
	<h4></h4>
	<div class="row">
		<div grid="3">
			<label>Habilitar Dados Pessoais</label>
			<div>
				<gp-switch name="enablePersonalData" [(ngModel)]="value.enablePersonalData" title="Habilitar Dados Pessoais"></gp-switch>
			</div>
		</div>
		<div grid="9">
			<div class="form-group">
				<label><PERSON><PERSON><PERSON><PERSON> (Dados Pessoais)</label>
				<input name="personalDataTitle" [(ngModel)]="value.personalDataTitle" type="text" class="form-control" placeholder="Dados pessoais" />
			</div>
		</div>
	</div>
	<campaign-field-param name="name" [(ngModel)]="value.personalData.name" label="Nome"></campaign-field-param>
	<campaign-field-param name="cpf" [(ngModel)]="value.personalData.cpf" label="CPF"></campaign-field-param>
	<campaign-field-param name="cnpj" [(ngModel)]="value.personalData.cnpj" label="CNPJ"></campaign-field-param>
	<campaign-field-param name="tradingName" [(ngModel)]="value.personalData.tradingName" label="Nome Fantasia"></campaign-field-param>
	<campaign-field-param name="companyName" [(ngModel)]="value.personalData.companyName" label="Razão Social"></campaign-field-param>
	<campaign-field-param name="stateInscription" [(ngModel)]="value.personalData.stateInscription" label="Incrição Estadual"></campaign-field-param>
	<campaign-field-param name="stateInscriptionUf" [(ngModel)]="value.personalData.stateInscriptionUf" label="Estado da Incrição Estadual"></campaign-field-param>

	<hr />
	<div class="row">
		<div grid="3">
			<label>Habilitar Endereço Residencial</label>
			<div>
				<gp-switch name="enableHomeAddress" [(ngModel)]="value.enableHomeAddress" title="Habilitar Endereço Residencial"></gp-switch>
			</div>
		</div>
		<div grid="9">
			<div class="form-group">
				<label>Título (Endereço de Entrega)</label>
				<input name="homeAddressTitle" [(ngModel)]="value.homeAddressTitle" type="text" class="form-control" placeholder="Endereço de Entrega" />
			</div>
		</div>
	</div>

	<campaign-field-param name="cep" [(ngModel)]="value.address.cep" label="CEP"></campaign-field-param>
	<campaign-field-param name="street" [(ngModel)]="value.address.street" label="Logradouro"></campaign-field-param>
	<campaign-field-param name="number" [(ngModel)]="value.address.number" label="Número"></campaign-field-param>
	<campaign-field-param name="complement" [(ngModel)]="value.address.complement" label="Complemento"></campaign-field-param>
	<campaign-field-param name="neighborhood" [(ngModel)]="value.address.neighborhood" label="Bairro"></campaign-field-param>
	<campaign-field-param name="city" [(ngModel)]="value.address.city" label="Cidade"></campaign-field-param>
	<campaign-field-param name="state" [(ngModel)]="value.address.state" label="Estado"></campaign-field-param>
	<campaign-field-param name="reference" [(ngModel)]="value.address.reference" label="Referência"></campaign-field-param>

	<hr />
	<div class="row">
		<div grid="3">
			<label>Habilitar E-mails</label>
			<div>
				<gp-switch name="enableEmails" [(ngModel)]="value.enableEmails" title="Habilitar E-mails"></gp-switch>
			</div>
		</div>
		<div grid="9">
			<div class="form-group">
				<label>Título (E-mails)</label>
				<input name="emailsTitle" [(ngModel)]="value.emailsTitle" type="text" class="form-control" placeholder="E-mails" />
			</div>
		</div>
	</div>
	<campaign-field-param name="mainEmail" [(ngModel)]="value.emails.mainEmail" label="E-mail principal"></campaign-field-param>

	<hr />
	<div class="row">
		<div grid="3">
			<label>Habilitar Telefone</label>
			<div>
				<gp-switch name="enableTelephones" [(ngModel)]="value.enableTelephones" title="Habilitar Telefones"></gp-switch>
			</div>
		</div>
		<div grid="9">
			<div class="form-group">
				<label>Título (Telefone)</label>
				<input name="telephonesTitle" [(ngModel)]="value.telephonesTitle" type="text" class="form-control" placeholder="Telefones" />
			</div>
		</div>
	</div>
	<campaign-field-param name="mobilePhone" [(ngModel)]="value.telephones.mobilePhone" label="Telefone Celular"></campaign-field-param>

</gp-card>
