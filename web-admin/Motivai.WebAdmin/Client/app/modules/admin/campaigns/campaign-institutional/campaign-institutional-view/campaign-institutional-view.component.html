<gp-card *ngIf="_campaignStore.isFullCampaign" [noPaddingTop]="true" [noPaddingBottom]="true">
	<div class="row">
		<accordion [closeOthers]="true">
			<accordion-group panelClass="b0 mb-sm panel-default no-shadow" [isOpen]="true">
				<div accordion-heading>
					<label><em class="fa fa-navicon"></em> Features do Institucional</label>
				</div>
				<div class="row menublock" style="margin-top:-1em;">
					<gp-menublock-item size="xs" icon="file-text-o" color="text-primary" text="Regulamento e Políticas" routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'institucional', 'regulamento']" *ngIf="canViewRegPol"></gp-menublock-item>
					<gp-menublock-item size="xs" icon="gear" color="text-primary" text="Mecânica" routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'institucional', 'mecanica']" *ngIf="canViewMechanic"></gp-menublock-item>
					<gp-menublock-item size="xs" icon="info" color="text-primary" text="Como Participar" routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'institucional', 'como-participar']" *ngIf="canViewHowToParticipate"></gp-menublock-item>
					<gp-menublock-item size="xs" icon="list-ol" color="text-primary" text="FAQ" routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'institucional', 'faq']" *ngIf="canViewFaqs"></gp-menublock-item>
					<gp-menublock-item size="xs" icon="envelope-o" color="text-primary" text="Fale Conosco" routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'institucional', 'faleconosco']" *ngIf="canViewContacts"></gp-menublock-item>
				</div>
			</accordion-group>
		</accordion>
	</div>
</gp-card>
<div>
	<router-outlet></router-outlet>
</div>