import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TabsetComponent } from 'ng2-bootstrap/tabs';

import { CampaignSubjectEditorComponent } from '../campaign-subject-editor/campaign-subject-editor.component';
import { ContactSubject, Campaign } from '../../../campaign';
import { CampaignContactUsService } from '../campaign-contactus.service';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';

@Component({
  selector: 'campaign-subjects',
  templateUrl: 'campaign-subjects-listing.component.html'
})
export class CampaignSubjectsListingComponent implements OnInit {
  @ViewChild('tabset') tabs: TabsetComponent;
  @ViewChild('subjectEditor') subjectEditor: CampaignSubjectEditorComponent;
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @ViewChild('gpAlertContact') gpAlertContact: GpAlertComponent;

  subjects: Array<ContactSubject>;
  campaignId: string;
  loading: boolean = false;
  sendingContact: boolean = false;
  contact: any = {
    text: null
  };

  constructor(private campaignContactService: CampaignContactUsService, private route: ActivatedRoute, private router: Router) { }

  ngOnInit() {
    if (this.route.parent != null && this.route.parent.parent != null) {
      this.route.parent.parent.params.subscribe((params: any) => {
        this.campaignId = params['id'];
        this.search();
      });
    } else {
      this.router.navigate(['/campanha']);
    }

    this.loadContactUsPageContent();
  }

  search() {
    this.gpAlert.clear();
    this.loading = true;
    this.campaignContactService.getSubjects(this.campaignId)
      .subscribe(
        subjects => {
          this.subjects = subjects;
        },
        err => this.handleError(err),
        () => this.loading = false
      );
  }

  handleError(err) {
    let errorMessage = (err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao efetuar a operação.');
    this.gpAlert.showError(errorMessage);
    this.loading = false;
  }

  addToTable(event) {
    this.subjects.push(event.value);
  }

  prepareEdit(subject) {
    this.subjectEditor.prepareEdit(subject);
    this.tabs.tabs[1].active = true;
  }

  remove(contactSubject) {
    if (!confirm(`Deseja excluir o assunto '${contactSubject.subject}'?`)) return;
    this.loading = true;
    this.campaignContactService.deleteSubject(this.campaignId, contactSubject.id)
      .subscribe(
        result => {
          if (result) {
            this.search();
            this.gpAlert.showSuccess('Assunto removido com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível remover o assunto, por favor, tente novamente.');
          }
        },
        err => this.handleError(err),
        () => this.loading = false
      );
  }

  saveContactUsPageContent() {
    this.sendingContact = true;
    this.gpAlertContact.clear();
    this.campaignContactService.saveContactUsPageContent(this.campaignId, this.contact.text)
      .subscribe(
        result => {
          if (result) {
            this.gpAlertContact.showSuccess('Texto do Fale Conosco atualizado com sucesso.');
          } else {
            this.gpAlertContact.showWarning('Não foi possível atualizar o texto Fale Conosco, por favor, tente novamente.');
          }
        },
        err => this.contactHandleError(err, this.gpAlertContact),
        () => this.sendingContact = false
      );
  }

  loadContactUsPageContent() {
    this.loading = true;
    this.campaignContactService.getContactUsPageContent(this.campaignId)
      .subscribe(
        result => {
          if (result)
            this.contact.text = result;

          this.loading = false;
        },
        err => this.contactHandleError(err, this.gpAlertContact)
      );
  }

  get disableButtons() {
    return this.loading || this.sendingContact;
  }

  public contactHandleError(err, gpAlert: GpAlertComponent) {
    let errorMessage = (err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao efetuar a operação.');
    gpAlert.showError(errorMessage);
    this.loading = false;
    this.sendingContact = false;
  }
}
