import { Component, ViewChild, OnInit } from '@angular/core';

import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignStore } from '../../../campaign.store';
import { CampaignInstitutionalService } from '../../campaign-institutional.service';
import { EMPTY } from '../../../../../../core/constants/constants-value';

@Component({
  selector: 'campaign-institutional-privacy-policy-editor',
  templateUrl: 'campaign-institutional-privacy-policy-editor.component.html'
})
export class CampaignInstitutionalPrivacyPolicyEditorComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;

  loading: boolean = false;
  sending: boolean = false;

  participantGroupFilter: string = null;
  groupPrivacyPolicy: any = {};

  constructor(private _campaignStore: CampaignStore, private _institutionalService: CampaignInstitutionalService) {}

  get disableButtons() {
    return this.loading || this.sending;
  }

  ngOnInit() {
    this.searchPrivacyPolicyByGroup();  
  }

  searchPrivacyPolicyByGroup() {
    if (!this.participantGroupFilter || !this.participantGroupFilter.length) {
      this.participantGroupFilter = EMPTY;
    }
    this.loading = true;
    this.groupPrivacyPolicy = {};
    this._institutionalService.getPrivacyPolicyByGroupId(this._campaignStore.id, this.participantGroupFilter)
      .subscribe(
        groupPrivacyPolicy => {
          this.loading = false;
          if (groupPrivacyPolicy) {
            this.groupPrivacyPolicy = groupPrivacyPolicy;
          }
        },
        err => {
          this.loading = false;
          this.gpAlert.handleAndShowError(err);
        }
      );
  }

  savePrivacyPolicy() {
    this.sending = true;
    this.gpAlert.clear();
    this._institutionalService.updateGroupPrivacyPolicy(this._campaignStore.id, this.groupPrivacyPolicy)
      .subscribe(
        result => {
          this.sending = false;
          if (result) {
            this.gpAlert.showSuccess('Política de privacidade atualizada com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível atualizar a política de privacidade, por favor, tente novamente.');
          }
        },
        err => {
          this.sending = false;
          this.gpAlert.handleAndShowError(err);
        }
      );
  }
}
