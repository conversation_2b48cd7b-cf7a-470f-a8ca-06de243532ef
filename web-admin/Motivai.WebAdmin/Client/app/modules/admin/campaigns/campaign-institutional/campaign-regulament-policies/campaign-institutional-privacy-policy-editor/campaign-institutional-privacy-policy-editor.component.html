<gp-card [first]="true" [last]="true">
	<form #frmRegul="ngForm">
		<spinner [overlay]="true" [show]="loading"></spinner>
		<gp-alert [overlay]="true" #gpAlert></gp-alert>

		<gp-form-row>
			<campaign-participants-group-selector cols="12 12" name="participantGroup"
				[(ngModel)]="participantGroupFilter" (ngModelChange)="searchPrivacyPolicyByGroup()"></campaign-participants-group-selector>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 12">
				<gp-editor id="privacyPolicy" name="privacyPolicy" [(ngModel)]="groupPrivacyPolicy.content"></gp-editor>
			</gp-form-col>
		</gp-form-row>
		<hr>
		<h4>Aceite de Cookies Customizado</h4>
		<gp-form-row>
			<gp-form-col cols="12 12">
				<gp-editor id="cookieAcceptanceContent" name="cookieAcceptanceContent" [(ngModel)]="groupPrivacyPolicy.cookieAcceptanceContent"></gp-editor>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 12" [inputGroup]="false">
				<gp-spinner-button type="submit" text="Salvar" [pink]="true" icon="send" [disabled]="disableButtons"
					[loading]="sending" loadingText="Salvando" (click)="savePrivacyPolicy()"></gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</form>
</gp-card>
