<tabset class="bg-white p0" [justified]="true" #tabset>
    <tab>
        <ng-template tabHeading><em class="fa fa-list-ul fa-fw"></em>Listagem</ng-template>
        <gp-card [first]="true" [last]="true">
            <gp-form-row>
                <gp-form-col cols="12 12">
                    <gp-grid name="faqsGrid" [rows]="faqs" [loading]="loading"
                        [columns]="['Título']" [fields]="['title']" emptyMessage="Nenhum FAQ cadastrado."
                        [showActive]="false" [showPagination]="false" [showEdit]="true" [showDelete]="true"
                        (onEdit)="prepareEdit($event)" (onDelete)="remove($event)">
                    </gp-grid>
                </gp-form-col>
            </gp-form-row>
            <div class="row">
                <div class="col-xs-12">
                    <button type="button" class="btn btn-default" [routerLink]="['/campanha', campaignId, 'edicao']">Voltar</button>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <gp-alert #gpAlert></gp-alert>
                </div>
            </div>
        </gp-card>
    </tab>
    <tab>
        <ng-template tabHeading><em class="fa fa-edit fa-fw"></em>Cadastro</ng-template>
        <campaign-faq-editor [campaignId]="campaignId" (updated)="search()" #faqEditor></campaign-faq-editor>
    </tab>
</tabset>