import { Component, ViewChild, OnInit } from '@angular/core';

import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignStore } from '../../campaign.store';
import { CampaignInstitutionalService } from '../campaign-institutional.service';
import { EMPTY } from '../../../../../core/constants/constants-value';

@Component({
    selector: 'campaign-institutional-howtoparticipate',
    templateUrl: 'campaign-howtoparticipate.component.html'
})
export class CampaignInstitutionalHowToParticipateComponent implements OnInit {
    @ViewChild('alert') alert: GpAlertComponent;

    participantGroupFilter: string;
    howParticiapteText: any = {};

    loading: boolean = false;
    sending: boolean = false;

    constructor(private _campaignStore: CampaignStore, private _campaignService: CampaignInstitutionalService) { }

    ngOnInit() {
        this.searchHowParticipateTextByGroup();
    }

    searchHowParticipateTextByGroup() {
        if (!this.participantGroupFilter || !this.participantGroupFilter.length) {
            this.participantGroupFilter = EMPTY;
        }
        this.loading = true;
        this._campaignService.getInstitutionalHowToParticipate(this._campaignStore.id, this.participantGroupFilter)
            .subscribe(
                response => {
                    if (response) {
                        this.howParticiapteText = response;
                    } else {
                        this.howParticiapteText = {};
                    }
                    this.loading = false;
                },
                err => {
                    this.alert.handleAndShowError(err);
                    this.loading = false;
                }
            );
    }

    saveText() {
        this.sending = true;
        this._campaignService.saveInstitutionalHowToParticipate(this._campaignStore.id, this.participantGroupFilter, this.howParticiapteText)
            .subscribe(
                response => {
                    if (response) {
                        this.alert.showSuccess('Texto institucional salvo com sucesso.');
                    } else {
                        this.alert.showError('Não foi possível salvar o texto institucional, por favor, tente novamente.');
                    }
                    this.sending = false;
                },
                err => {
                    this.alert.handleAndShowError(err);
                    this.sending = false;
                }
            );
    }
}
