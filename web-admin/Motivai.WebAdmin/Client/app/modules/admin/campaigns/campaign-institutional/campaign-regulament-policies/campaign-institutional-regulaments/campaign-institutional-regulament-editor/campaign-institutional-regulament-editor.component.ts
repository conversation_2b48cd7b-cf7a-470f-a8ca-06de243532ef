import { <PERSON>mponent, OnInit, ViewChild, OnD<PERSON>roy, Input } from '@angular/core';
import { Subscription } from 'rxjs';

import { EMPTY } from '../../../../../../../core/constants/constants-value';
import { CampaignStore } from '../../../../campaign.store';
import { CampaignInstitutionalService } from '../../../campaign-institutional.service';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { RxjsHelpers } from '../../../../../../../shared/helpers/rxjs-helpers';

@Component({
  selector: 'campaign-institutional-regulament-editor',
  templateUrl: 'campaign-institutional-regulament-editor.component.html'
})
export class CampaignInstitutionalRegulamentEditorComponent implements OnInit, OnD<PERSON>roy {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;

  @Input('multiRegulations') multiRegulations: boolean = false;

  loading: boolean = false;
  sending: boolean = false;

  participantGroupFilter: string = null;
  groupRegulation: any = {};

  private _campaign$: Subscription;

  constructor(private _campaignStore: CampaignStore, private _institutionalService: CampaignInstitutionalService) {}

  get disableButtons() {
    return this.loading || this.sending;
  }

  ngOnInit() {
    this._campaign$ = this._campaignStore.asObservable
      .subscribe(campaignId => {
        this.searchRegulationByGroup();
      });
  }

  ngOnDestroy() {
    RxjsHelpers.unsubscribe(this._campaign$);
  }

  searchRegulationByGroup() {
    if (this.loading || this.multiRegulations) {
      return;
    }
    if (!this.participantGroupFilter || !this.participantGroupFilter.length) {
      this.participantGroupFilter = EMPTY;
    }
    this.loading = true;
    this.groupRegulation = {};
    this._institutionalService.getRegulationByGroupId(this._campaignStore.id, this.participantGroupFilter)
      .subscribe(
        groupRegulation => {
          this.loading = false;
          if (groupRegulation) {
            this.groupRegulation = groupRegulation;
          }
        },
        err => {
          this.loading = false;
          this.gpAlert.handleAndShowError(err);
        }
      );
  }

  saveRegulation() {
    this.sending = true;
    this.gpAlert.clear();
    this._institutionalService.updateGroupRegulation(this._campaignStore.id, this.groupRegulation)
      .subscribe(
        result => {
          this.sending = false;
          if (result) {
            this.gpAlert.showSuccess('Regulamento atualizado com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível atualizar o regulamento, por favor, tente novamente.');
          }
        },
        err => {
          this.sending = false;
          this.gpAlert.handleAndShowError(err);
        }
      );
  }
}
