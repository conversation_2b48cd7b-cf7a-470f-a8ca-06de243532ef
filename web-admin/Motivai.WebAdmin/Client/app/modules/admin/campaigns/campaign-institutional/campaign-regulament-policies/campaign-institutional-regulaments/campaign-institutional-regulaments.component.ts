import { Subscription } from 'rxjs/Subscription';
import { Component, ViewChild, OnInit, OnDestroy } from '@angular/core';

import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignInstitutionalService } from '../../campaign-institutional.service';
import { RxjsHelpers } from '../../../../../../shared/helpers/rxjs-helpers';
import { CampaignStore } from '../../../campaign.store';
import { CampaignSettingsService } from '../../../campaign-settings.service';

@Component({
  selector: 'campaign-institutional-regulaments',
  templateUrl: 'campaign-institutional-regulaments.component.html'
})
export class CampaignInstitutionalRegulamentsComponent implements OnInit, OnDestroy {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  private _campaign$: Subscription;

  loading: boolean = false;
  enableCampaignWithMultiRegulations: boolean = false;

  constructor(private _campaignStore: CampaignStore,
    private _campaignSettingsService: CampaignSettingsService,
    private _institutionalService: CampaignInstitutionalService) {}

  ngOnInit() {
    // this._campaign$ = this._campaignStore.asObservable
    //   .subscribe(campaignId => {
    //     this.loadCampaignParametrization();
    //   });
  }

  ngOnDestroy() {
    RxjsHelpers.unsubscribe(this._campaign$);
  }

  private loadCampaignParametrization() {
    this.loading = true;
    this._campaignSettingsService.getCampaignParametrization(this._campaignStore.id)
      .subscribe(
        settings => {
          this.loading = false;
          if (settings) {
            this.enableCampaignWithMultiRegulations = settings.parametrizations.enableCampaignWithMultiRegulations;
          }
        },
        err => {
          this.loading = false;
          this.gpAlert.handleAndShowError(err);
        }
      );
  }
}
