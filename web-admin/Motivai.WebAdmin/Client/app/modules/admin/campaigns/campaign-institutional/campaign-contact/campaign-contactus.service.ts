import { Observable } from 'rxjs/Observable';
import { Injectable } from '@angular/core';

import { ApiService } from '../../../../../core/api/api.service';
import { ContactSubject } from '../../campaign';

@Injectable()
export class CampaignContactUsService {
    constructor(private _api: ApiService) { }

    getContactUsPageContent(campaignId: string): Observable<string> {
        return this._api.get(`/api/campaigns/${campaignId}/contactus/pagecontent`);
    }

    saveContactUsPageContent(campaignId: string, pageContent: string) {
        return this._api.post(`/api/campaigns/${campaignId}/contactus/pagecontent`, { pageContent: pageContent });
    }

    getSubjects(campaignId: string): Observable<Array<ContactSubject>> {
        return this._api.get(`/api/campaigns/${campaignId}/contactus/subjects`);
    }

    addSubject(campaignId: string, subject: ContactSubject) {
        return this._api.post(`/api/campaigns/${campaignId}/contactus/subjects`, subject);
    }

    updateSubject(campaignId: string, subjectId: string, subject: ContactSubject) {
        return this._api.put(`/api/campaigns/${campaignId}/contactus/subjects/${subjectId}`, subject);
    }

    deleteSubject(campaignId: string, subjectId: string): Observable<boolean> {
        return this._api.delete(`/api/campaigns/${campaignId}/contactus/subjects/${subjectId}`);
    }
}
