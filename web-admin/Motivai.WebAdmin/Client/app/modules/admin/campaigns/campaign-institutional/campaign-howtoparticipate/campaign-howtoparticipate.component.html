<form #textForm="ngForm" (ngSubmit)="saveText()" novalidate>
	<gp-alert [overlay]="true" #alert></gp-alert>
	<gp-card title="Como Participar">
		<spinner [overlay]="true" [show]="loading"></spinner>

		<gp-form-row>
			<campaign-participants-group-selector cols="12 12" name="participantGroup"
				[(ngModel)]="participantGroupFilter" (ngModelChange)="searchHowParticipateTextByGroup()"></campaign-participants-group-selector>
		</gp-form-row>
		
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-simple-input label="Título" [required]="true" errorMessage="Título é obrigatório">
					<input type="text" class="form-control" name="title" required [(ngModel)]="howParticiapteText.title" />
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<label>Descrição</label>
				<gp-editor id="description" name="description" [(ngModel)]="howParticiapteText.description"></gp-editor>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 12 12" [inputGroup]="true">
				<gp-spinner-button type="submit" [pink]="true" icon="send" text="Salvar" pull="right" [loading]="sending"></gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</gp-card>
</form>
