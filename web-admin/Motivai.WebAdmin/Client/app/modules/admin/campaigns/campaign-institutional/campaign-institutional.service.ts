import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { ApiService } from '../../../../core/api/api.service';

@Injectable()
export class CampaignInstitutionalService {
	constructor(private _api: ApiService) { }

	// Regulamento
	getPoliciesByCampaign(campaignId: string): Observable<any> {
		return this._api.get(`/api/campaigns/${campaignId}/institutional/policies`);
	}

	getRegulationByGroupId(campaignId: string, groupId: string): Observable<any> {
		return this._api.get(`/api/campaigns/${campaignId}/institutional/regulations/groups/${groupId}`);
	}

	updateGroupRegulation(campaignId: string, groupRegulation: any): Observable<boolean> {
		return this._api.putWithStaticContent(`/api/campaigns/${campaignId}/institutional/regulations/groups/${groupRegulation.participantGroupId}`, groupRegulation);
	}

	// Politica de Privacidade
	getPrivacyPolicyByGroupId(campaignId: string, groupId: string): Observable<any> {
		return this._api.get(`/api/campaigns/${campaignId}/institutional/privacypolicies/groups/${groupId}`);
	}

	updateGroupPrivacyPolicy(campaignId: string, groupPrivacyPolicy: any): Observable<boolean> {
		return this._api.putWithStaticContent(`/api/campaigns/${campaignId}/institutional/privacypolicies/groups/${groupPrivacyPolicy.participantGroupId}`, groupPrivacyPolicy);
	}

	// Politica de Entrega
	getShippingPolicyByGroupId(campaignId: string, groupId: string): Observable<any> {
		return this._api.get(`/api/campaigns/${campaignId}/institutional/shippingpolicies/groups/${groupId}`);
	}

	updateGroupShippingPolicy(campaignId: string, groupShippingPolicy: any): Observable<boolean> {
		return this._api.putWithStaticContent(`/api/campaigns/${campaignId}/institutional/shippingpolicies/groups/${groupShippingPolicy.participantGroupId}`, groupShippingPolicy);
	}

	// Mecanica da Campanha
	getInstitutionalMechanicByGroup(campaignId: string, groupId: string): Observable<any> {
		return this._api.get(`/api/campaigns/${campaignId}/institutional/mechanics/groups/${groupId}`);
	}

	saveInstitutionalMechanic(campaignId: string, groupId: string, mechanicText: any): Observable<boolean> {
		return this._api.putWithStaticContent(`/api/campaigns/${campaignId}/institutional/mechanics/groups/${groupId}`, mechanicText);
	}

	// Institutional: How to Participate
	getInstitutionalHowToParticipate(campaignId: string, groupId: string): Observable<any> {
		return this._api.get(`/api/campaigns/${campaignId}/institutional/howtoparticipate/groups/${groupId}`);
	}

	saveInstitutionalHowToParticipate(campaignId: string, groupId: string, howParticipateText: any): Observable<boolean> {
		return this._api.putWithStaticContent(`/api/campaigns/${campaignId}/institutional/howtoparticipate/groups/${groupId}`, howParticipateText);
	}

	// FAQ
	getFaqsByCampaign(campaignId: string): Observable<Array<any>> {
		return this._api.get(`/api/campaigns/${campaignId}/institutional/faqs`);
	}

	getFaqById(campaignId: string, faqId: string): Observable<any> {
		return this._api.get(`/api/campaigns/${campaignId}/institutional/faqs/${faqId}`);
	}

	saveFaq(campaignId: string, faq: any): Observable<boolean> {
		if (faq.id) {
			return this._api.putWithStaticContent(`/api/campaigns/${campaignId}/institutional/faqs/${faq.id}`, faq);
		}
		return this._api.postWithStaticContent(`/api/campaigns/${campaignId}/institutional/faqs`, faq);
	}

	deleteFaq(campaignId: string, faqId: string): Observable<boolean> {
		return this._api.delete(`/api/campaigns/${campaignId}/institutional/faqs/${faqId}`);
	}
}
