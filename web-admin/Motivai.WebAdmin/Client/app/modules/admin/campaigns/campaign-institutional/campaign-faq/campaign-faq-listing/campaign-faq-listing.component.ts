import { Component, ViewChild } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { TabsetComponent } from "ng2-bootstrap/tabs";

import { AuthStore } from '../../../../../../core/auth/auth.store';
import { CampaignFaqEditorComponent } from '../campaign-faq-editor/campaign-faq-editor.component';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignInstitutionalService } from '../../campaign-institutional.service';

@Component({
  selector: 'campaign-faq',
  templateUrl: 'campaign-faq-listing.component.html'
})
export class CampaignFaqListingComponent {
  @ViewChild('tabset') tabs: TabsetComponent;
  @ViewChild('faqEditor') faqEditor: CampaignFaqEditorComponent;
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  campaignId: string;

  loading: boolean = false;
  sending: boolean = false;

  faqs: Array<any> = [];

  constructor(private _authStore: AuthStore, private _campaignService: CampaignInstitutionalService,
    private route: ActivatedRoute, private router: Router) {
  }

  ngOnInit() {
    if (this.route.parent != null && this.route.parent.parent != null) {
      this.route.parent.parent.params.subscribe((params: any) => {
        this.campaignId = params['id'];
        this.search();
      });
    } else {
      this.router.navigate(['/campanha']);
    }
  }

  get disableButtons() {
    return this.loading || this.sending;
  }

  handleError(err) {
    let errorMessage = (err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao efetuar a operação.');
    this.gpAlert.showError(errorMessage);
    this.loading = false;
    this.sending = false;
  }

  search() {
    this.gpAlert.clear();
    this.loading = true;
    this._campaignService.getFaqsByCampaign(this.campaignId)
      .subscribe(
        faqs => {
          this.faqs = faqs;
        },
        err => this.handleError(err),
        () => this.loading = false
      );
  }

  prepareEdit(faq: any) {
    if (!faq) return;
    this.faqEditor.prepareEdit(faq.id);
    this.tabs.tabs[1].active = true;
  }

  remove(faq) {
    if (!confirm(`Deseja excluir o FAQ '${faq.title}'?`)) return;
    this.loading = true;
    this._campaignService.deleteFaq(this.campaignId, faq.id)
      .subscribe(
        result => {
          if (result) {
            this.search();
            this.gpAlert.showSuccess('FAQ removido com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível remover o FAQ, por favor, tente novamente.');
          }
        },
        err => this.handleError(err),
        () => this.loading = false
      );
  }
}
