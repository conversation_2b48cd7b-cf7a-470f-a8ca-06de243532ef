import { SelectComponent } from 'ng2-select';
import { Component, ViewChild, EventEmitter, Output, Input } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';

import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { Item } from '../../../../../../shared/models/item';
import { CampaignInstitutionalService } from '../../campaign-institutional.service';

@Component({
  selector: 'campaign-faq-editor',
  templateUrl: 'campaign-faq-editor.component.html'
})
export class CampaignFaqEditorComponent {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @Output()
  updated: EventEmitter<boolean> = new EventEmitter<boolean>();

  formGroup: FormGroup;
  messages = {
    'title': {
      'required': '<PERSON><PERSON><PERSON><PERSON> é obrigatório'
    },
    'answer': {
      'required': 'Resposta é obrigatório'
    }
  };

  @Input()
  campaignId: string;
  faqId: string;
  faq: any = {};
  selectedParticipantsGroups: Array<any> = [];

  loading: boolean = false;
  sending: boolean = false;

  constructor(private _campaignService: CampaignInstitutionalService, fb: FormBuilder) {
    this.formGroup = fb.group({
      title: ['', Validators.required],
      answer: ['', Validators.required]
    });
  }

  newFaq() {
    this.gpAlert.clear();
    this.faqId = '';
    this.faq = {
      id: '',
      answer: '',
      participantsGroups: ''
    };
    this.selectedParticipantsGroups = [];
    this.formGroup.reset();
  }

  prepareEdit(faqId: string) {
    this.newFaq();
    if (faqId) {
      this.loading = true;
      this._campaignService.getFaqById(this.campaignId, faqId)
        .subscribe(
          faq => {
            this.loading = false;
            if (faq) {
              this.faq = faq;
              this.faqId = faqId;
              this.formGroup.patchValue(faq);
              if (faq.participantsGroups && faq.participantsGroups.length) {
                this.selectedParticipantsGroups = faq.participantsGroups; // .map(g => ({ id: g }));
              }
            } else {
              this.gpAlert.showWarning('FAQ não encontrado.');
            }
          },
          err => {
            this.gpAlert.handleAndShowError(err);
            this.loading = false;
          }
        );
    }
  }

  onFormSubmit(event: any) {
    if (!event.valid) {
      this.gpAlert.showWarning('Preencha os campos para continuar.');
      return;
    }
    this.sending = true;
    this.faq = event.model;
    if (this.faqId) {
      this.faq.id = this.faqId;
    }
    if (this.selectedParticipantsGroups && this.selectedParticipantsGroups.length) {
      this.faq.participantsGroups = this.selectedParticipantsGroups; // .map(g => g.id);
    } else {
      this.faq.participantsGroups = [];
    }
    this._campaignService.saveFaq(this.campaignId, this.faq)
      .subscribe(
        result => {
          this.sending = false;
          if (result) {
            this.newFaq();
            this.gpAlert.showSuccess('FAQ salvo com sucesso.');
            this.updated.emit(true);
          } else {
            this.gpAlert.showWarning('Não foi possível salvar o FAQ, por favor, tente novamente.');
          }
        },
        err => {
          this.gpAlert.handleAndShowError(err);
          this.sending = false;
        }
      );
  }
}
