import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

import { PERMISSION_CAMPAIGNS_REGPOL, PERMISSION_CAMPAIGNS_CONTACT, PERMISSION_CAMPAIGNS_FAQ, PERMISSION_CAMPAIGNS_INSTITUTIONAL_MECHANIC, PERMISSION_CAMPAIGNS_INSTITUTIONAL_HOWTOPARTICIPATE } from '../../../../../core/auth/access-points';
import { AuthStore } from '../../../../../core/auth/auth.store';
import { CampaignStore } from '../../campaign.store';

@Component({
	selector: 'campaign-institutional-view',
	templateUrl: 'campaign-institutional-view.component.html'
})
export class CampaignInstitutionalViewComponent implements OnInit {
  campaignId: string;

  constructor(private _authStore: AuthStore, private route: ActivatedRoute, private router: Router , private _campaignStore: CampaignStore) {}

  ngOnInit() {
    if (this.route.parent != null) {
      this.route.parent.params.subscribe((params: any) => {
        this.campaignId = params['id'];
        if (!this.campaignId) {
          this.router.navigate(['/campanha']);
        }
      });
    } else {
      this.router.navigate(['/campanha']);
    }
  }

	get canViewRegPol() {
		return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_REGPOL);
	}

	get canViewContacts() {
		return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_CONTACT);
	}

	get canViewFaqs() {
		return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_FAQ);
  }

  get canViewMechanic() {
    return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_INSTITUTIONAL_MECHANIC);
  }

  get canViewHowToParticipate() {
    return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_INSTITUTIONAL_HOWTOPARTICIPATE);
  }
}
