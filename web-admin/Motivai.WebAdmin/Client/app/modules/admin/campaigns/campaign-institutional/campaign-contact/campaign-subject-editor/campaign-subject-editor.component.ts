import { Component, ViewChild, Input, Output, EventEmitter } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';


import { ContactSubject } from '../../../campaign';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignContactUsService } from '../campaign-contactus.service';

@Component({
  selector: 'campaign-subject-editor',
  templateUrl: 'campaign-subject-editor.component.html'
})
export class CampaignSubjectEditorComponent {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @Output()
  updated: EventEmitter<boolean> = new EventEmitter<boolean>();

  formGroup: FormGroup;
  messages = {
    'subject': {
      'required': 'Assunto é obrigatório'
    },
    'email': {
      'required': 'E-mail é obrigatório',
      'email': 'E-mail inválido'
    }
  };

  @Input()
  campaignId: string;
  contactId: string;
  contact: ContactSubject = new ContactSubject();
  sending: boolean = false;

  constructor(private campaignContactService: CampaignContactUsService, fb: FormBuilder) {
    this.formGroup = fb.group({
      subject: ['', Validators.required],
      // email: ['', Validators.compose([Validators.required, CustomValidators.email])]
      email: ['', Validators.compose([Validators.required])]
    });
  }

  handleError(err) {
    let errorMessage = (err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao efetuar a operação.');
    this.gpAlert.showError(errorMessage);
    this.sending = false;
  }

  newSubject() {
    this.gpAlert.clear();
    this.contactId = '';
    this.contact = new ContactSubject();
    this.formGroup.reset();
  }

  prepareEdit(subject: ContactSubject) {
    this.gpAlert.clear();
    if (subject) {
      this.contact = subject;
      this.contactId = subject.id;
      this.formGroup.patchValue(subject);
    }
  }

  onFormSubmit(event) {
    if (!event.valid) {
      this.gpAlert.showWarning('Preencha os campos para continuar.');
      return;
    }
    this.sending = true;
    this.gpAlert.clear();
    this.contact = event.model;
    if (this.contactId) {
      this.contact.id = this.contactId;
    }

    if (!this.contactId)
      this.campaignContactService.addSubject(this.campaignId, this.contact)
        .subscribe(
          result => {
            if (result) {
              this.newSubject();
              this.gpAlert.showSuccess('Assunto criado com sucesso.');
              this.updated.emit(true);
            } else {
              this.gpAlert.showWarning('Não foi possível criar o assunto, por favor, tente novamente.');
            }
          },
          err => this.handleError(err),
          () => this.sending = false
        );
    else
      this.campaignContactService.updateSubject(this.campaignId, this.contactId, this.contact)
        .subscribe(
          result => {
            if (result) {
              this.newSubject();
              this.gpAlert.showSuccess('Assunto atualizado com sucesso.');
              this.updated.emit(true);
            } else {
              this.gpAlert.showWarning('Não foi possível atualizar o assunto, por favor, tente novamente.');
            }
          },
          err => this.handleError(err),
          () => this.sending = false
        );
  }
}
