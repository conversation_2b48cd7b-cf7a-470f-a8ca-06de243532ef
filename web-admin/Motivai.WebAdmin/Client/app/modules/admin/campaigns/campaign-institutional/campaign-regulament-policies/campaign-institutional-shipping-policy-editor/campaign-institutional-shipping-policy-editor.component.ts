import { Component, ViewChild, OnInit } from '@angular/core';

import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignStore } from '../../../campaign.store';
import { CampaignInstitutionalService } from '../../campaign-institutional.service';
import { EMPTY } from '../../../../../../core/constants/constants-value';

@Component({
  selector: 'campaign-institutional-shipping-policy-editor',
  templateUrl: 'campaign-institutional-shipping-policy-editor.component.html'
})
export class CampaignInstitutionalShippingPolicyEditorComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;

  loading: boolean = false;
  sending: boolean = false;

  participantGroupFilter: string = null;
  groupShippingPolicy: any = {};

  constructor(private _campaignStore: CampaignStore, private _institutionalService: CampaignInstitutionalService) {}

  get disableButtons() {
    return this.loading || this.sending;
  }

  ngOnInit() {
    this.searchShippingPolicyByGroup();
  }

  searchShippingPolicyByGroup() {
    if (!this.participantGroupFilter || !this.participantGroupFilter.length) {
      this.participantGroupFilter = EMPTY;
    }
    this.loading = true;
    this.groupShippingPolicy = {};
    this._institutionalService.getShippingPolicyByGroupId(this._campaignStore.id, this.participantGroupFilter)
      .subscribe(
        groupShippingPolicy => {
          this.loading = false;
          if (groupShippingPolicy) {
            this.groupShippingPolicy = groupShippingPolicy;
          }
        },
        err => {
          this.loading = false;
          this.gpAlert.handleAndShowError(err);
        }
      );
  }

  saveShippingPolicy() {
    this.sending = true;
    this.gpAlert.clear();
    this._institutionalService.updateGroupShippingPolicy(this._campaignStore.id, this.groupShippingPolicy)
      .subscribe(
        result => {
          this.sending = false;
          if (result) {
            this.gpAlert.showSuccess('Política de entrega e troca atualizada com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível atualizar a política de entrega e troca, por favor, tente novamente.');
          }
        },
        err => {
          this.sending = false;
          this.gpAlert.handleAndShowError(err);
        }
      );
  }
}
