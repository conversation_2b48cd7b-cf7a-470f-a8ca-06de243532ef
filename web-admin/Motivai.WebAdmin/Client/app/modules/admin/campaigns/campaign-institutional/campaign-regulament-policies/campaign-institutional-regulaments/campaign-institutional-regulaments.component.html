<gp-alert [overlay]="true" #gpAlert></gp-alert>

<spinner [overlay]="true" [show]="loading"></spinner>

<div *ngIf="!loading">
	<gp-card [first]="true" [last]="true">
		<campaign-institutional-regulament-editor [multiRegulations]="enableCampaignWithMultiRegulations"></campaign-institutional-regulament-editor>
	</gp-card>

	<!-- <gp-card [first]="true" [last]="true">
		<form #frmRegul="ngForm">
			<gp-form-row>
				<gp-form-col cols="12">
					<gp-spinner-button icon="plus" bootstrapClass="primary" text="Novo"></gp-spinner-button>
				</gp-form-col>
			</gp-form-row>

			<gp-form-row>
				<gp-form-col cols="12">
					<gp-grid name="regulationsGrid"
						[columns]="['Título','Obrigatório','Habilitado no Primeiro Acesso']"
						[fields]="['title','acceptanceRequired','enableAtFirstAccess']">
					</gp-grid>
				</gp-form-col>
			</gp-form-row>
		</form>
	</gp-card> -->
</div>
