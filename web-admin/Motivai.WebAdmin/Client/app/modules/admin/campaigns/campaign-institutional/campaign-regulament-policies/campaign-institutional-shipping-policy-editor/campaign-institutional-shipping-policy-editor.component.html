<gp-card [first]="true" [last]="true">
	<form #frmRegul="ngForm">
		<spinner [overlay]="true" [show]="loading"></spinner>
		<gp-alert [overlay]="true" #gpAlert></gp-alert>

		<gp-form-row>
			<campaign-participants-group-selector cols="12 12" name="participantGroup"
				[(ngModel)]="participantGroupFilter" (ngModelChange)="searchShippingPolicyByGroup()"></campaign-participants-group-selector>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 12">
				<gp-editor id="shippingPolicy" name="shippingPolicy" [(ngModel)]="groupShippingPolicy.content"></gp-editor>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 12" [inputGroup]="false">
				<gp-spinner-button type="submit" text="Salvar" [pink]="true" icon="send" [disabled]="disableButtons"
					[loading]="sending" loadingText="Salvando" (click)="saveShippingPolicy()"></gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</form>
</gp-card>
