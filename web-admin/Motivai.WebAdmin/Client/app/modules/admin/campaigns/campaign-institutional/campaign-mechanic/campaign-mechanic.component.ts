import { Component, ViewChild, OnInit } from '@angular/core';

import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignInstitutionalService } from '../campaign-institutional.service';
import { CampaignStore } from '../../campaign.store';
import { EMPTY } from '../../../../../core/constants/constants-value';

@Component({
    selector: 'campaign-institutional-mechanic',
    templateUrl: 'campaign-mechanic.component.html'
})
export class CampaignInstitutionalMechanicComponent implements OnInit {
    @ViewChild('alert') alert: GpAlertComponent;

    participantGroupFilter: string;
    mechanicText: any = {};

    loading: boolean = false;
    sending: boolean = false;

    constructor(private _campaignStore: CampaignStore, private _campaignService: CampaignInstitutionalService) { }

    ngOnInit() {
        this.searchMechanicTextByGroup();
    }

    searchMechanicTextByGroup() {
        if (!this.participantGroupFilter || !this.participantGroupFilter.length) {
            this.participantGroupFilter = EMPTY;
        }
        this.loading = true;
        this._campaignService.getInstitutionalMechanicByGroup(this._campaignStore.id, this.participantGroupFilter)
            .subscribe(
                response => {
                    if (response) {
                        this.mechanicText = response;
                    } else {
                        this.mechanicText = {};
                    }
                    this.loading = false;
                },
                err => {
                    this.alert.handleAndShowError(err);
                    this.loading = false;
                }
            );
    }

    saveText() {
        this.sending = true;
        this._campaignService.saveInstitutionalMechanic(this._campaignStore.id, this.participantGroupFilter, this.mechanicText)
            .subscribe(
                response => {
                    if (response) {
                        this.alert.showSuccess('Texto institucional salvo com sucesso.');
                    } else {
                        this.alert.showError('Não foi possível salvar o texto institucional, por favor, tente novamente.');
                    }
                    this.sending = false;
                },
                err => {
                    this.alert.handleAndShowError(err);
                    this.sending = false;
                }
            );
    }
}
