<form #frmRegul="ngForm">
	<gp-alert [overlay]="true" #gpAlert></gp-alert>

	<gp-form-row>
		<campaign-participants-group-selector cols="12 12" name="participantGroup"
			[(ngModel)]="participantGroupFilter" (ngModelChange)="searchRegulationByGroup()">
		</campaign-participants-group-selector>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12">
			<gp-simple-input label="Título">
				<input type="text" name="title" class="form-control" [(ngModel)]="groupRegulation.title" />
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>

	<gp-form-row>
		<gp-form-col cols="12 12">
			<gp-editor id="regulament" name="regulation" [(ngModel)]="groupRegulation.regulationContent"></gp-editor>
		</gp-form-col>
	</gp-form-row>

	<gp-form-row>
		<gp-form-col cols="12 12" [inputGroup]="false">
			<gp-spinner-button type="submit" text="Salvar" [pink]="true" icon="send" [disabled]="disableButtons"
				[loading]="sending" loadingText="Salvando" (click)="saveRegulation()"></gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
</form>
