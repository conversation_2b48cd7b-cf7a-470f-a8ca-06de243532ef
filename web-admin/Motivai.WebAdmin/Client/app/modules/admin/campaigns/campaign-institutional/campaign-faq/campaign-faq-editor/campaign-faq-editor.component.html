<gp-card [first]="true" [last]="true" title="FAQ">
	<gp-form-validate [formGroup]="formGroup" [validationMessages]="messages" (onSubmit)="onFormSubmit($event)">
		<gp-alert [overlay]="true" #gpAlert></gp-alert>
		<gp-form-row>
			<gp-input-validate cols="12 12 6" label="Título" formControlName="title"></gp-input-validate>
		</gp-form-row>
		<gp-form-row>
			<campaign-participants-group-selector cols="12 12" name="participantGroup" [multiple]="true"
				[(ngModel)]="selectedParticipantsGroups" [ngModelOptions]="{'standalone':true}"></campaign-participants-group-selector>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 12">
				<label for="answer">Resposta</label>
				<gp-editor id="answer" name="answer" formControlName="answer"></gp-editor>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 12">
				<gp-spinner-button text="Novo" bootstrapClass="default" icon="plus" [disabled]="sending" (click)="newFaq()"
					[showSpinner]="false" [loading]="sending"></gp-spinner-button>

				<gp-spinner-button type="submit" text="Salvar" [pink]="true" icon="send" [disabled]="formGroup.invalid || sending || loading"
					[loading]="sending" loadingText="Salvando"></gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
		<spinner [overlay]="true" [show]="loading"></spinner>
	</gp-form-validate>
</gp-card>
