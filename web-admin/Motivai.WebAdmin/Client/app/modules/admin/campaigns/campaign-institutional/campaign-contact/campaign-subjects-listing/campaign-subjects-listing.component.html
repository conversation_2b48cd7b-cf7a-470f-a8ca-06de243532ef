<tabset class="bg-white p0" [justified]="true" #tabset>
	<tab>
		<ng-template tabHeading>
			<em class="fa fa-list-ul fa-fw"></em>Listagem</ng-template>
		<gp-card [first]="true" [last]="true">
			<accordion [closeOthers]="true">
				<accordion-group panelClass="b0 mb-sm panel-default">
					<div accordion-heading>
						<label>Cadastro do Texto do Fale Conosco</label>
					</div>
					<form #frmRegul="ngForm">
						<div class="row" grid="12">
							<gp-editor id="pageContent" name="pageContent" [(ngModel)]="contact.text"></gp-editor>
						</div>
						<div class="row" grid="12">
							<br />
							<button type="button" class="btn btn-default"
								[routerLink]="['/campanha', campaignId, 'edicao']">Voltar</button>
							<gp-spinner-button type="submit" text="Salvar" [pink]="true" icon="send"
								[disabled]="disableButtons" [loading]="sendingContact" loadingText="Salvando"
								(click)="saveContactUsPageContent()">
							</gp-spinner-button>
						</div>
						<div class="row" grid="12">
							<gp-alert #gpAlertContact></gp-alert>
						</div>
					</form>
				</accordion-group>

				<accordion-group [isOpen]="true" panelClass="b0 mb-sm panel-default">
					<div accordion-heading>
						<label>Listagem de Assuntos</label>
					</div>
					<gp-form-row>
						<gp-form-col cols="12 12">
							<gp-grid name="subjectsGrid" [rows]="subjects" [loading]="loading"
								[columns]="['Assunto', 'E-mail']" [fields]="['subject', 'email']" [showActive]="false"
								[showPagination]="false" [showEdit]="true" [showDelete]="true"
								(onEdit)="prepareEdit($event)" (onDelete)="remove($event)">
							</gp-grid>
						</gp-form-col>
					</gp-form-row>
					<div class="row">
						<div class="col-xs-12">
							<button type="button" class="btn btn-default"
								[routerLink]="['/campanha', campaignId, 'edicao']">Voltar</button>
						</div>
					</div>
					<div class="row">
						<div class="col-xs-12">
							<gp-alert #gpAlert></gp-alert>
						</div>
					</div>
				</accordion-group>
			</accordion>
		</gp-card>
	</tab>
	<tab>
		<ng-template tabHeading>
			<em class="fa fa-edit fa-fw"></em>Cadastro</ng-template>
		<campaign-subject-editor [campaignId]="campaignId" (updated)="search()"
			#subjectEditor></campaign-subject-editor>
	</tab>
</tabset>
