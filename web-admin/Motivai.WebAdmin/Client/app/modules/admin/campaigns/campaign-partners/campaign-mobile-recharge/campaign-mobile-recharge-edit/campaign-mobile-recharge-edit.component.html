<form #formPartner="ngForm" (ngSubmit)="onFormSubmit()" novalidate>
	<gp-card title="Parâmetros da Integração" [first]="true">
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Empresa para Integração" [required]="true" errorMessage="Selecione a empresa para integração">
					<select id="company" name="company" required class="form-control" [(ngModel)]="partnerConfig.partner">
						<option value="" selected>Selecione</option>
						<option value="IS2B">IS2B</option>
						<option value="VIVO">VIVO</option>
					</select>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 3 3">
				<label><PERSON><PERSON><PERSON> Principal</label>
				<div>
					<label class="switch switch-lg">
						<input type="checkbox" checked="checked" id="master" name="master" [(ngModel)]="partnerConfig.master" /><span></span>
					</label>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 3 3">
				<label>Ativado</label>
				<div>
					<label class="switch switch-lg">
						<input type="checkbox" checked="checked" id="active" name="active" [(ngModel)]="partnerConfig.active" /><span></span>
					</label>
				</div>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row *ngIf="canEditIntegrationConfig">
			<gp-form-col cols="12 6 6">
				<div class="form-group">
					<label>Usuário*</label>
					<input type="text" id="username" name="username" class="form-control" [(ngModel)]="partnerConfig.username" required />
				</div>
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<div class="form-group">
					<label>Senha*</label>
					<input type="text" id="password" name="password" class="form-control" [(ngModel)]="partnerConfig.password" required />
				</div>
			</gp-form-col>
		</gp-form-row>
		<spinner [show]="loading" [overlay]="true"></spinner>
	</gp-card>

	<gp-card title="Configuração de Taxas" *ngIf="canEditFees">
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Taxa do Parceiro (%)" [required]="true" errorMessage="Taxa do parceiro é obrigatória">
					<gp-input-group symbol="%">
						<gp-input-mask name="partnerFee" required [onlyDecimal]="true" integers="2" [(ngModel)]="partnerConfig.partnerFee"></gp-input-mask>
					</gp-input-group>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Taxa da Plataforma (%)" [required]="true" errorMessage="Taxa da Plataforma é obrigatória">
					<gp-input-group symbol="%">
						<gp-input-mask name="gpFee" required [onlyDecimal]="true" integers="2" [(ngModel)]="partnerConfig.gpFee"></gp-input-mask>
					</gp-input-group>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 12">
				<p><label>Fórmula:</label> (Valor de Recarga / Fator de Conversão de Pontos) + Taxa Parceiro (%) + Taxa Plataforma (%)</p>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card title="Configuração dos Pontos">
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<label>Alterar fator de conversão</label>
				<div>
					<label class="switch switch-lg">
						<input type="checkbox" id="changeConversionFactor" name="changeConversionFactor"
							[(ngModel)]="partnerConfig.changeConversionFactor" /><span></span>
					</label>
				</div>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Fator de conversão da campanha (1 ponto = R$ {{campaignConversionFactor}})">
					<input type="text" id="campaignConversionFactor" name="campaignConversionFactor" class="form-control" disabled="disabled"
						[(ngModel)]="campaignConversionFactor" />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Novo fator de conversão (1 ponto = R$ ?)" [required]="partnerConfig.changeConversionFactor" errorMessage="Fator de conversão é obrigatório">
					<gp-input-mask id="conversionFactor" name="conversionFactor" [onlyDecimal]="true"
						[disabled]="!partnerConfig.changeConversionFactor" [required]="partnerConfig.changeConversionFactor"
						[(ngModel)]="partnerConfig.conversionFactor">
					</gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card [last]="true">
		<div class="row">
			<div class="col-sm-12 col-md-12">
				<gp-spinner-button type="submit" [pink]="true" icon="send" pull="right" text="Salvar"
					loadingText="Processando" [loading]="sending" [disabled]="!formPartner.valid">
				</gp-spinner-button>

				<gp-spinner-button type="button" bootstrapClass="default" icon="plus" pull="right" text="Novo" marginRight="1em"
					[loading]="sending" (click)="resetForm()">
				</gp-spinner-button>
			</div>
		</div>
		<div class="row">
			<div class="col-md-12">
				<gp-alert #gpAlert></gp-alert>
			</div>
		</div>
	</gp-card>
</form>
