import { Component, OnInit, ViewChild, Input, OnDestroy } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs/Subscription';

import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { Item } from '../../../../../../shared/models/item';
import { AuthStore } from '../../../../../../core/auth/auth.store';
import { BenefitsClubService } from '../benefits-club.service';
import { CampaignStore } from '../../../campaign.store';
import { RxjsHelpers } from '../../../../../../shared/helpers/rxjs-helpers';

@Component({
    selector: 'benefits-club-categories',
    templateUrl: './benefits-club-categories.component.html'
})
export class BenefitsClubCategoriesComponent implements OnInit, OnDestroy {
    private campaignStore$: Subscription;
    @ViewChild('alert') alert: GpAlertComponent;
    @Input() includeEmptyOption: boolean = false;

    partnerCategory: any = {};
    categories: any = [];
    newCategory: boolean = false;
    partnersIcons: Item[] = [
        Item.of('x', 'Selecione... '),
        Item.of('fa fa-plane', '&#xf072; Avião'),
        Item.of('fa fa-flag-checkered', '&#xf11e; Bandeira'),
        Item.of('fa fa-coffee', '&#xf0f4; Café'),
        Item.of('fa fa-shopping-cart', '&#xf07a; Carrinho'),
        Item.of('fa fa-mobile', '&#xf10b; Celular '),
        Item.of('fa fa-university', '&#xf19c; Edificio'),
        Item.of('fa fa-laptop', '&#xf109; laptop'),
        Item.of('fa fa-gift', '&#xf06b; Presente'),
        Item.of('fa fa-paw', '&#Xf1b0; Pata de cachorro'),
        Item.of('fa fa-taxi', '&#Xf1ba; Taxi'),
        Item.of('fa fa-wrench', '&#xf0ad; Serviços'),
        Item.of('fa fa-plus', '&#xf067; Saúde'),
        Item.of('fa fa-television', '&#xf26c; Tv'),
        Item.of('fa fa-futbol-o', '&#xf1e3; Bola'),
        Item.of('fa fa-cutlery', '&#xf0f5; Acessórios de cozinha'),
        Item.of('fa fa-ambulance', '&#xf0f9; Ambulancia')
    ];
    campaignId: string;
    loading: boolean = false;

    constructor(private _authStore: AuthStore, private _service: BenefitsClubService,
        private route: ActivatedRoute, private router: Router, private _campaignStore: CampaignStore) { }

    get canEdit() {
        return this._authStore.role.PERMISSION_CAMPAIGNS_INSTITUTIONAL_PARTNER_EDIT;
    }

    get canCreate() {
        return this._authStore.role.PERMISSION_CAMPAIGNS_INSTITUTIONAL_PARTNER_CREATE;
    }

    get canView() {
        return this._authStore.role.PERMISSION_CAMPAIGNS_INSTITUTIONAL_PARTNER_VIEW;
    }

    get renderIconClass(): string {
        if (!this.partnerCategory || !this.partnerCategory.icon)
          return '';
        return `${this.partnerCategory.icon} fa-3x`;
    }

    ngOnInit(): void {
        this.campaignStore$ = this._campaignStore.asObservable
            .subscribe(campaignId => {
                this.campaignId = campaignId;
                this.getCategories();
            });
    }

    ngOnDestroy(): void {
        RxjsHelpers.unsubscribe(this.campaignStore$);
    }

    save() {
        if (this.canCreate || this.canEdit) {
            this.loading = true;
            this._service.saveInstitutionalPartnerCategory(this.campaignId, this.partnerCategory).subscribe(
                category => {
                    if (category && category.id) {
                        this.alert.showSuccess('Categoria salva com sucesso.');
                        this.partnerCategory.id = category.id;
                        this.getCategories();
                    } else {
                        this.alert.showWarning('Não foi possível salvar a categoria, por favor, tente novamente.');
                    }
                    this.loading = false;
                },
                err => {
                    this.alert.showError(err, true);
                    this.loading = false;
                });
        } else {
            this.alert.showInfo('O Usuário não possue permissão para criar/editar categoria! ');
        }
    }

    getCategories() {
        if (this.canView) {
            this.loading = true;
            this._service.getCategories(this.campaignId).subscribe(
                categories => {
                    this.categories = categories;
                    this.loading = false;
                },
                err => {
                    this.alert.showError(err, true);
                    this.loading = false;
                });
        } else {
            this.alert.showInfo('Usuário não possue permissão para visualizar as Categorias!');
        }
    }

    cancel() {
        this.newCategory = false;
        this.getCategories();
    }

    edit(event: any) {
        if (event) {
            this.partnerCategory = event;
            this.newCategory = true;
        }
    }

    showNewCategory() {
        this.newCategory = true;
        this.partnerCategory = {};
    }
}
