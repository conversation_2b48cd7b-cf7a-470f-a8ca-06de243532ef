import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { Component, OnInit, Input, Output, EventEmitter, ViewChild } from '@angular/core';

import { CampaignService } from '../../../campaign.service';
import { PaymentGatewaySettings } from '../../../campaign';

@Component({
    selector: 'campaign-gateway-list',
    templateUrl: 'campaign-gateway-list.component.html'
})
export class GpCampaignGatewayListComponent implements OnInit {

    private _campaignId: string;
    @Input() set campaignId(v: string) {
        if (v) {
            this._campaignId = v;
            this.getPaymentGateways();
        }
    }

    @ViewChild('gpAlert') gpAlert: GpAlertComponent;

    private loading: boolean = false;
    private payments: PaymentGatewaySettings[] = [];
    private columns: string[] = [ 'Empresa' ];
    private fields: string[] = [ 'gatewayCompany' ];
    @Output('onEditGateway') onEditGateway: EventEmitter<any> = new EventEmitter();

    constructor(private _campaignService: CampaignService) { }

    ngOnInit() { }

    getPaymentGateways() {
        this.loading = true;
        this._campaignService.getPaymentGateways(this._campaignId)
            .subscribe(payments => {
                this.payments = payments;
                this.loading = false;
            }, err => {
                console.log(err);
                this.loading = false;
            });
    }

    onEdit($event: any) {
        this.onEditGateway.emit($event);
    }

    onDelete($event: any) {
        this.loading = true;
        this._campaignService.removePaymentGateway(this._campaignId, $event.gatewayCompany)
            .subscribe(response => {
                this.getPaymentGateways();
                this.loading = false;
            }, err => {
                this.gpAlert.showError(err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao excluir o gateway');
                this.loading = false;
            });
    }
}
