import { Output, ViewChild } from '@angular/core';
import { Component, Input, EventEmitter } from '@angular/core';

import { CampaignService } from '../../../campaign.service';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';

@Component({
  selector: 'campaign-mobile-recharge-list',
  templateUrl: 'campaign-mobile-recharge-list.component.html'
})
export class CampaignMobileRechargeListComponent {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @Output('onEdit') onEdit: EventEmitter<any> = new EventEmitter<any>();

	private _campaignId: string;

	partners: Array<any> = [];
	loading: boolean = false;

	constructor(private _campaignService: CampaignService) {}

  @Input()
  set campaignId(campaignId: string) {
		this._campaignId = campaignId;
		if (this._campaignId) {
			this.loadMobileRechargePartners();
		}
	}

  private handleError(err) {
		this.gpAlert.showError(err || 'Ocorreu um erro ao efetuar a operação.');
		this.loading = false;
  }

	onRowEdit(row: any) {
		if (row && row.id) {
			this.onEdit.emit(row.id);
		}
	}

  loadMobileRechargePartners() {
		if (!this._campaignId) return;
		this.loading = true;
		this.gpAlert.clear();
		this._campaignService.getMobilePartners(this._campaignId)
			.subscribe(
				partners => {
					this.loading = false;
					if (partners) {
						this.partners = partners;
						this.partners.forEach(p => p.masterText = p.master ? 'Sim' : 'Não');
					} else {
						this.partners = [];
					}
				},
				err => this.handleError(err)
			);
	}
}
