import { Component, OnInit, ViewChild } from '@angular/core';
import { GpAlertComponent } from '../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignPartnersService } from '../../../../campaign-partners.service';
import { CampaignStore } from '../../../../../campaign.store';
import { GpEmailTemplateComponent } from '../../../../../../../../shared/components/gp-email-template-editor/gp-email-template-editor.component';
import { RxjsHelpers } from '../../../../../../../../shared/helpers/rxjs-helpers';
import { Subscription } from 'rxjs';
import { handlerOrder } from '../../../models/campaing-dynamic-order.helper';
@Component({
  selector: 'campaigns-dynamic-orders-edit',
  templateUrl: './campaigns-dynamic-orders-edit.component.html'
})
export class CampaignsDynamicOrdersEditComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @ViewChild('notificationTemplateOrdersReceivedEditor') notificationTemplateOrdersReceivedEditor: GpEmailTemplateComponent;

  campaignId: string = '';
  private _campaign$: Subscription;

  constructor(private _campaignStore: CampaignStore, private _campaignPartnersService: CampaignPartnersService) {}

  dynamicOrder: any = {};
  loading = false;

  ngOnInit() {
    this.campaignId = this._campaignStore.id;
  }

  ngOnDestroy() {
    RxjsHelpers.unsubscribe(this._campaign$);
  }

  showDetails(dynamicOrder: any) {
    if (!dynamicOrder || !dynamicOrder.id) {
      return;
    }
    this.loadDynamicOrderDetails(dynamicOrder.id);
  }

  loadDynamicOrderDetails(dynamicOrderId: string) {
    this.loading = true;
    this._campaignPartnersService.findDynamicOrderById(this.campaignId, dynamicOrderId).subscribe(
      dynamicOrder => {
        this.loading = false;
        this.dynamicOrder = dynamicOrder;
        handlerOrder(this.dynamicOrder)

        if (!this.dynamicOrder.debitTransactionReceipt) {
          this.dynamicOrder.debitTransactionReceipt = {};
        }

        if (!this.dynamicOrder.debitTransactionReceipt) {
          this.dynamicOrder.debitTransactionReceipt = {};
        }

        if (!this.dynamicOrder.orderParametrization) {
          this.dynamicOrder.orderParametrization = {};
        }

      },
      err => {
        this.loading = false;
        this.gpAlert.handleAndShowError(err);
      }
    );
  }

  clear() {
    this.dynamicOrder = {};
  }
}
