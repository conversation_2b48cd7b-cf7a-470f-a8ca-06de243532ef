<gp-alert [overlay]="true" #gpAlert></gp-alert>

<gp-card title="Detalhes do Pedido" [first]="true">
    <spinner [overlay]="true" [show]="loading"></spinner>
    <div>
        <div class="col-lg-6 col-xs-12 br pv">
            <div class="clearfix">
                <p class="pull-left"><strong>Número do pedido: </strong></p>
                <p class="pull-right mr">{{ (dynamicOrder.orderNumber) || '-'}}</p>
            </div>
            <div class="clearfix">
                <p class="pull-left"><strong>Data do pedido: </strong></p>
                <p class="pull-right mr">{{dynamicOrder.requestDate || '-'}}</p>
            </div>
            <div class="clearfix">
                <p class="pull-left"><strong>Pedido recebido: </strong></p>
                <p class="pull-right mr">{{dynamicOrder.createDate || '-'}}</p>
            </div>
            <div class="clearfix">
                <p class="pull-left"><strong>Valor do pedido: </strong></p>
                <p class="pull-right mr">R$ {{dynamicOrder.totalAmount | amount}}</p>
            </div>
            <div class="clearfix">
                <p class="pull-left"><strong>Subtotal do pedido: </strong></p>
                <p class="pull-right mr">R$ {{dynamicOrder.subtotalAmount | amount}}</p>
            </div>
            <div class="clearfix">
                <p class="pull-left"><strong>Taxa: </strong></p>
                <p class="pull-right mr">R$ {{dynamicOrder.totalFeeAmount | amount}}</p>
            </div>
        </div>

        <div class="col-lg-6 col-xs-12 br pv">
            <div class="clearfix">
                <p class="pull-left"><strong>Data da transação(débito): </strong></p>
                <p class="pull-right mr">{{ dynamicOrder.debitTransactionDate || '-'}}</p>
            </div>
            <div class="clearfix">
                <p class="pull-left"><strong>Status do pedido: </strong></p>
                <p class="pull-right mr">{{ dynamicOrder.status || '-'}}</p>
            </div>
            <div class="clearfix">
                <p class="pull-left"><strong>Erro: </strong></p>
                <p class="pull-right mr">{{ dynamicOrder.errorOccurred || '-'}}</p>
            </div>
            <div class="clearfix">
                <p class="pull-left"><strong>Mensagem de erro: </strong></p>
                <p class="pull-right mr">{{ dynamicOrder.errorMessage || '-'}}</p>
            </div>
            <div class="clearfix">
                <p class="pull-left"><strong>Detalhe do erro: </strong></p>
                <p class="pull-right mr">{{ dynamicOrder.errorDetails || '-'}}</p>
            </div>
        </div>
    </div>
</gp-card>

<gp-card title="Dados do Participante" *ngIf="dynamicOrder.userDocument">

    <div class="col-lg-6 col-xs-12 br pv">
        <div class="clearfix">
            <p class="pull-left"><strong>Nome: </strong></p>
            <p class="pull-right mr">{{dynamicOrder.userName || '-'}}</p>
        </div>
        <div class="clearfix">
            <p class="pull-left"><strong>CPF/CNPJ: </strong></p>
            <p class="pull-right mr">{{dynamicOrder.userDocument | document}}</p>
        </div>
    </div>

    <div class="col-lg-6 col-xs-12 br pv">
        <div class="clearfix">
            <p class="pull-left"><strong>E-mail: </strong></p>
            <p class="pull-right mr">{{dynamicOrder.userEmail || '-'}}</p>
        </div>
    </div>

</gp-card>

<gp-card title="Detalhes Dinâmicos" *ngIf="dynamicOrder.metadata">
    <div class="col-lg-6 col-xs-12 br pv">
        <div class="clearfix" *ngFor="let dynamicOrder of dynamicOrder.metadata">
            <p class="pull-left"><strong>{{dynamicOrder.label}}: </strong></p>
            <p class="pull-right mr">{{dynamicOrder.value || '-'}}</p>
        </div>
    </div>
</gp-card>