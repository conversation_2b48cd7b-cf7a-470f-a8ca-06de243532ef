import { Component, Output, ViewChild, EventEmitter, Input, OnInit } from '@angular/core';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignService } from '../../../../campaign.service';
import { PERMISSION_CAMPAIGNS_BILL_PAYMENT_PARTNERS_INTEGRATION, PERMISSION_CAMPAIGNS_BILL_PAYMENT_PARTNERS_FEES, PERMISSION_CAMPAIGNS_BILL_PAYMENT_PARTNERS, PERMISSION_CAMPAIGNS_BILL_PAYMENT_PARTNERS_FEATURES_PARAMETRIZATION } from '../../../../../../../core/auth/access-points';
import { AuthStore } from '../../../../../../../core/auth/auth.store';

@Component({
  selector: 'campaign-bill-payment-edit',
  templateUrl: 'campaign-bill-payment-parametrization-edit.component.html'
})
export class CampaignBillPaymentParametrizationEditComponent implements OnInit {
  @Output('onUpdate') onUpdate: EventEmitter<any> = new EventEmitter<any>();
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;

  canEditIntegrationConfig: boolean = false;
  canEditFees: boolean = false;
  canEditFeaturesParamentrization: boolean = false;

  private _campaignId: string;
  campaignConversionFactor: number = 1;

  partnerConfig: any = {
    parametrizations: {
      denyPaymentWithoutServiceTypeConfigured: false,
      filterRejectionMessage: '',
      rejectGenericBillsPaymentWithoutReceiverDetails:false,
      rejectUtilityBillsPaymentWithoutReceiverDetails:false
    },
    enabledFeatures: {
      queryBillDetails: false,
      payUtilityBills: false,
      payGenericBills: false,
      applyPayGenericBillsFilters: false,
      applyPayUltilityBillsFilters: false
    }
  };

  loading: boolean = false;
  sending: boolean = false;

  constructor(private _authStore: AuthStore, private _campaignService: CampaignService) { }

  ngOnInit() {
    this.canEditIntegrationConfig = this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_BILL_PAYMENT_PARTNERS_INTEGRATION);
    this.canEditFees = this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_BILL_PAYMENT_PARTNERS_FEES);
    this.canEditFeaturesParamentrization = this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_BILL_PAYMENT_PARTNERS_FEATURES_PARAMETRIZATION);
  }

  @Input()
  set campaignId(campaignId: string) {
    this._campaignId = campaignId;
  }

  private getCampaign() {
    this._campaignService.getById(this._campaignId)
      .subscribe(campaign => this.campaignConversionFactor = campaign.pointsConversionFactor);
  }

  private handleError(err) {
    this.gpAlert.showError(err || 'Ocorreu um erro ao efetuar a operação.');
    this.loading = false;
    this.sending = false;
  }

  resetForm() {
    this.partnerConfig = {
      parametrizations: {
        denyPaymentWithoutServiceTypeConfigured: false,
        filterRejectionMessage: '',
        rejectGenericBillsPaymentWithoutReceiverDetails:false,
        rejectUtilityBillsPaymentWithoutReceiverDetails:false
      },
      enabledFeatures: {
        queryBillDetails: false,
        payUtilityBills: false,
        payGenericBills: false,
        applyPayGenericBillsFilters: false,
        applyPayUltilityBillsFilters: false
      }
    };
    this.gpAlert.clear();
  }

  prepareEdit(partnerId: any) {
    this.loading = true;
    this.resetForm();
    if (partnerId) {
      this._campaignService.getBillPaymentPartnerById(this._campaignId, partnerId)
        .subscribe(
          partnerConfig => {
            this.loading = false;
            if (partnerConfig && partnerConfig.id) {
              if (!partnerConfig.enabledFeatures) {
                partnerConfig.enabledFeatures = {
                  queryBillDetails: false,
                  payUtilityBills: false,
                  payGenericBills: false,
                  applyPayGenericBillsFilters: false,
                  applyPayUltilityBillsFilters: false
                };
              }
              if (!partnerConfig.parametrizations) {
                partnerConfig.parametrizations = {
                denyPaymentWithoutServiceTypeConfigured: false,
                filterRejectionMessage: '',
                rejectGenericBillsPaymentWithoutReceiverDetails:false,
                rejectUtilityBillsPaymentWithoutReceiverDetails:false
                };
              }
              this.partnerConfig = partnerConfig;
            } else {
              this.partnerConfig = {
                parametrizations: {
                denyPaymentWithoutServiceTypeConfigured: false,
                filterRejectionMessage: '',
                rejectGenericBillsPaymentWithoutReceiverDetails:false,
                rejectUtilityBillsPaymentWithoutReceiverDetails:false
                },
                enabledFeatures: {
                  queryBillDetails: false,
                  payUtilityBills: false,
                  payGenericBills: false,
                  applyPayGenericBillsFilters: false,
                  applyPayUltilityBillsFilters: false
                }
              };
              this.gpAlert.showWarning('Parceiro de pagamento de contas não encontrado.');
            }
          },
          err => this.handleError(err)
        );
    }
  }

  onFormSubmit() {
    this.sending = true;
    this._campaignService.saveBillPaymentPartners(this._campaignId, this.partnerConfig)
      .subscribe(
        partnerConfig => {
          this.sending = false;
          if (partnerConfig && partnerConfig.id) {
            this.gpAlert.showSuccess('Parceiro de pagamento de contas salvo com sucesso.');
            if (!partnerConfig.enabledFeatures) {
              partnerConfig.enabledFeatures = {
                queryBillDetails: false,
                payUtilityBills: false,
                payGenericBills: false,
                applyPayGenericBillsFilters: false,
                applyPayUltilityBillsFilters: false
              };
            }
            if (!partnerConfig.parametrizations) {
              partnerConfig.parametrizations = {
                denyPaymentWithoutServiceTypeConfigured: false,
                filterRejectionMessage: '',
                rejectGenericBillsPaymentWithoutReceiverDetails:false,
                rejectUtilityBillsPaymentWithoutReceiverDetails:false
              };
            }
            this.partnerConfig = partnerConfig;
            this.onUpdate.emit(true);
          } else {
            this.gpAlert.showWarning('Não foi possível salvar o parceiro de pagamento de contas, por favor, tente novamente.');
          }
        },
        err => this.handleError(err)
      );
  }
}
