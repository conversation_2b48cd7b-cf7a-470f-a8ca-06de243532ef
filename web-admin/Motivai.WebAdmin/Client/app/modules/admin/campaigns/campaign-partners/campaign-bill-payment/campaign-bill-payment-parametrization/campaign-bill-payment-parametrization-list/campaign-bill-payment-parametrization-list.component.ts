import { Component, ViewChild, Output, EventEmitter, Input } from '@angular/core';

import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignService } from '../../../../campaign.service';

@Component({
  selector: 'campaign-bill-payment-list',
  templateUrl: 'campaign-bill-payment-parametrization-list.component.html'
})
export class CampaignBillPaymentParametrizationListComponent {
	@ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @Output('onEdit') onEdit: EventEmitter<any> = new EventEmitter<any>();

	private _campaignId: string;
	
	partners: Array<any> = [];
	loading: boolean = false;

	constructor(private _campaignService: CampaignService) {}

  @Input()
  set campaignId(campaignId: string) {
		this._campaignId = campaignId;
		if (this._campaignId) {
			this.loadBillPaymentPartners();
		}
	}

  private handleError(err) {
		this.gpAlert.showError(err || 'Ocorreu um erro ao efetuar a operação.');
		this.loading = false;
  }
	
	onRowEdit(row: any) {
		if (row && row.id) {
			this.onEdit.emit(row.id);
		}
	}

  loadBillPaymentPartners() {
		if (!this._campaignId) return;
		this.loading = true;
		this.gpAlert.clear();
		this._campaignService.getBillPaymentPartners(this._campaignId)
			.subscribe(
				partners => {
					this.loading = false;
					if (partners) {
						this.partners = partners;
						this.partners.forEach(p => p.masterText = p.master ? 'Sim' : 'Não');
					} else {
						this.partners = [];
					}
				},
				err => this.handleError(err)
			);
	}
}
