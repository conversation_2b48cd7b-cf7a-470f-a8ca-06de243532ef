import { Component, OnInit, ViewChild } from '@angular/core';
import { TabsetComponent } from 'ng2-bootstrap/tabs';

import { CampaignMobileRechargeEditComponent } from '../campaign-mobile-recharge-edit/campaign-mobile-recharge-edit.component';
import { CampaignMobileRechargeListComponent } from '../campaign-mobile-recharge-list/campaign-mobile-recharge-list.component';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs/Subscription';
import { CampaignStore } from '../../../campaign.store';

@Component({
  selector: 'campaign-mobile-recharge-view',
  templateUrl: 'campaign-mobile-recharge-view.component.html'
})
export class CampaignMobileRechargeViewComponent implements OnInit {
  private campaignStore$: Subscription;
  @ViewChild('listComponent') listComponent: CampaignMobileRechargeListComponent;
  @ViewChild('editComponent') editComponent: CampaignMobileRechargeEditComponent;
  @ViewChild('tabs') tabs: TabsetComponent;
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;

  campaignId: string;
  loading: boolean = false;

  constructor(private route: ActivatedRoute, private router: Router, private _campaignStore: CampaignStore) {}

  ngOnInit() {
    this.campaignStore$ = this._campaignStore.asObservable
    .subscribe(campaignId => {
      this.campaignId = campaignId;
    });
  }

  private handleError(err) {
    this.gpAlert.showError(err || 'Ocorreu um erro ao efetuar a operação.');
  }

  onUpdate() {
    this.listComponent.loadMobileRechargePartners();
  }

  onEditRow(partnerId: string) {
    this.editComponent.prepareEdit(partnerId);
    this.tabs.tabs[1].active = true;
  }

  clearEditForm() {
    this.editComponent.resetForm();
    this.listComponent.loadMobileRechargePartners();
  }
}
