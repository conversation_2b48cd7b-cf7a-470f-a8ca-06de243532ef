import { DecimalPipe } from '@angular/common';
import { NgForm } from '@angular/forms';
import { Component, OnInit, Input, ViewChild, Output } from '@angular/core';

import { PaymentGatewaySettings, GatewayCompany, AntiFraudFeeType } from '../../../campaign';
import { KeyValue } from '../../../../../../shared/models/keyvalue';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignService } from '../../../campaign.service';

@Component({
    selector: 'campaign-gateway-edit',
    templateUrl: 'campaign-gateway-edit.component.html'
})
export class GpCampaignGatewayEditComponent implements OnInit {

    private _campaignId: string;
    @Input() set campaignId(v: string) {
        if (v) {
            this._campaignId = v;
            this.getCampaign();
        }
    }

    private _gatewayCompany: string;
    @Input() set gatewayCompany(v: string) {
        if (v) {
            this._gatewayCompany = v;
            this.getPaymentGatewayByCompany();
        }
    }

    @ViewChild('campaignAlert') campaignAlert: GpAlertComponent;
    @ViewChild('gatewayForm') gatewayForm: NgForm;

    settings: PaymentGatewaySettings = new PaymentGatewaySettings();
    company = GatewayCompany;
    feeType = AntiFraudFeeType;
    installmentKey?: any;
    installment: KeyValue = new KeyValue();
    loadingInstallment: boolean = false;
    campaignConversionFactor: number;
    loading: boolean = false;

    simulation: any = {
        pointsValue: 100
    };

    constructor(private _campaignService: CampaignService) { }

    ngOnInit() { }

    resetForm() {
        this.campaignAlert.clear();
        this.gatewayForm.reset();

        Object.keys(this.gatewayForm.controls).forEach(key => {
            this.gatewayForm.controls[key].reset();
        });

        this._gatewayCompany = '';
        this.settings = new PaymentGatewaySettings();
        this.getCampaign();
    }

    getCampaign() {
        this._campaignService.getById(this._campaignId)
            .subscribe(campaign => {
                this.campaignConversionFactor = campaign.pointsConversionFactor;
            });
    }

    getPaymentGatewayByCompany() {
        this.loading = true;
        this._campaignService.getPaymentGatewayByCompany(this._campaignId, this._gatewayCompany)
            .subscribe(settings => {
                this.settings = settings;
                this.loading = false;
            }, err => {
                console.log(err);
                this.loading = false;
            });
    }

    saveInstallment() {
        if (this.installment) {
            this.loadingInstallment = true;
            if (!this.settings.installmentsFee)
                this.settings.installmentsFee = [];

            if (!this.installmentKey) {
                this.settings.installmentsFee.push(this.installment);
            } else {
                let install = this.settings.installmentsFee.find(x => x.key === this.installmentKey);
                if (install)
                    install.value = this.installment.value;
            }

            this.settings.installmentsFee.sort((x, y) => x.key - y.key);
            this.installment = new KeyValue();
            this.installmentKey = null;
            this.loadingInstallment = false;
        }
    }

    editInstallmentFee(key: any) {
        if (key) {
            const install = this.settings.installmentsFee.find(x => x.key === key);
            if (install) {
                this.installment = install;
                this.installmentKey = key;
            }
        }
    }

    deleteInstallmentFee(key: any) {
        if (key) {
            const install = this.settings.installmentsFee.find(x => x.key === key);
            if (install) {
                this.settings.installmentsFee.splice(this.settings.installmentsFee.indexOf(install), 1);
            }
        }
    }

    gatewaySubmit() {
        this.loading = true;
        this.campaignAlert.clear();
        this._campaignService.savePaymentGateway(this._campaignId, this.settings)
            .subscribe(response => {
                this.campaignAlert.showSuccess('Gateway de pagamento atualizado com sucesso');
                this.loading = false;
            }, err => {
                const msg = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao salvar o gateway de pagamento';
                this.campaignAlert.showError(msg);
                this.loading = false;
            });
    }

    get simulationTotal(): number {
        let total: number = 0;
        total = (this.simulation.pointsValue || 0) * (this.settings.changeConversionFactor ? (this.settings.conversionFactor || 0) : this.campaignConversionFactor);
        total = total + Number(this.settings.transactionCurrencyValue || 0);
        total = total + Number(this.settings.bankTransferFee || 0);
        total = total + Number(this.settings.antiFraudTransactionCurrencyValue || 0);
        
        let transactionBaseAmount: number = total;

        if (this.settings.additionalFee) {
            transactionBaseAmount = transactionBaseAmount * Number(this.settings.additionalFee / 100 + 1);
        }

        let governmentAndInstallmentFee: number = 0;
        if (this.settings.governmentFee) {
            governmentAndInstallmentFee = governmentAndInstallmentFee + Number(this.settings.governmentFee);
        }
        if (this.simulation.installment) {
          governmentAndInstallmentFee = governmentAndInstallmentFee + Number(this.simulation.installment);
        }

        if (this.settings.anticipationFee) {
            const feeFactor = (this.settings.anticipationFee + governmentAndInstallmentFee) / 100;
            const anticipationFee = transactionBaseAmount + ((transactionBaseAmount * feeFactor) / (1 - feeFactor));
            // 78m = Regra para cálcular: 12 * (12 + 1) / 2
            const totalFee = (anticipationFee / 12) * Number((78 * this.settings.anticipationFee) / 100);
            transactionBaseAmount = transactionBaseAmount + totalFee;
        }

        if (governmentAndInstallmentFee > 0) {
            const feeFactor = governmentAndInstallmentFee / 100;
            const governmentFee = (transactionBaseAmount * feeFactor) / (1 - feeFactor);
            total = transactionBaseAmount + governmentFee;
        }
        return Number(total.toFixed(2));
    }
}
