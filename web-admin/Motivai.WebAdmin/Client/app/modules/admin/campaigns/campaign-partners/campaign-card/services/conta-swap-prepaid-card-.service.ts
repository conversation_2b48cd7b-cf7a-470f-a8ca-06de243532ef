import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { ApiService } from '../../../../../../core/api/api.service';
import { CardType } from '../enums/card-type';

@Injectable()
export class ContaSwapPrepaidCardService {
    constructor(private _api: ApiService) {}

    getProducts(): Observable<Array<any>> {
        return this._api.get(`api/partners/prepaidcards/contaswap/products`, 20000);
    }

    getWithDrawableCardBalanceAccountsByProductId(productId: string): Observable<Array<any>> {
        const params: any = {
            cardType: CardType.WITHDRAWABLE
        };
        return this._api.get(`api/partners/prepaidcards/contaswap/products/${productId}/balance-accounts`, params, 20000);
    }

    getNoDrawableCardBalanceAccountsByProductId(productId: string): Observable<Array<any>> {
        const params: any = {
            cardType: CardType.NODRAWABLE
        };
        return this._api.get(`api/partners/prepaidcards/contaswap/products/${productId}/balance-accounts`, params, 20000);
    }
}
