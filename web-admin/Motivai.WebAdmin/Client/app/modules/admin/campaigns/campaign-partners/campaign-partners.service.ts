import { Injectable } from '@angular/core';
import { Observable } from 'rxjs/Observable';

import { ApiService } from '../../../../core/api/api.service';
import { CampaignDynamicOrderParametrization } from './campaign-dynamic-order/models/campaign-dynamic-order.model';
import { FormatHelper } from '../../../../shared/formatters/format-helper';

@Injectable()
export class CampaignPartnersService {
  constructor(private _api: ApiService) {}

  getGeneralBankTransferConfiguration(campaignId: string): Observable<any> {
    return this._api.get(`/api/campaigns/${campaignId}/partners/services/cashback/general`, null, 20000);
  }

  getBankTransferConfiguration(campaignId: string): Observable<any> {
    return this._api.get(`/api/campaigns/${campaignId}/partners/services/cashback`, null, 20000);
  }

  saveBankTransferConfiguration(campaignId: string, cashback: any): any {
    return this._api.put(`/api/campaigns/${campaignId}/partners/services/cashback`, cashback, 20000);
  }

  getCashbackConfiguration(campaignId: string): Observable<any> {
    return this._api.get(`/api/campaigns/${campaignId}/partners/services/pointscashback`, null, 20000);
  }

  saveCashbackConfiguration(campaignId: string, cashback: any): any {
    return this._api.put(`/api/campaigns/${campaignId}/partners/services/pointscashback`, cashback, 20000);
  }

  getP2pTransferConfiguration(campaignId: string): Observable<any> {
    return this._api.get(`/api/campaigns/${campaignId}/partners/services/p2ptransfers`, null, 20000);
  }

  saveP2pTransferConfiguration(campaignId: string, transferConfig: any): Observable<any> {
    return this._api.put(`/api/campaigns/${campaignId}/partners/services/p2ptransfers`, transferConfig, 20000);
  }

  saveDynamicOrderParametrization(campaignId: string, dynamicOrderParametrization: CampaignDynamicOrderParametrization): Observable<any> {
    if (dynamicOrderParametrization.id) {
      return this._api.put(
        `/api/campaigns/${campaignId}/partners/services/dynamic-orders/parametrizations/${dynamicOrderParametrization.id}`,
        dynamicOrderParametrization,
        20000
      );
    }
    return this._api.post(`/api/campaigns/${campaignId}/partners/services/dynamic-orders/parametrizations`, dynamicOrderParametrization, 20000);
  }

  loadDynamicOrdersParametrizations(campaignId: string): Observable<any[]> {
    return this._api.get(`/api/campaigns/${campaignId}/partners/services/dynamic-orders/parametrizations`);
  }

  findDynamicOrderParametrizationById(campaignId: string, dynamicOrderParametrizationId: string): Observable<any> {
    return this._api.get(`/api/campaigns/${campaignId}/partners/services/dynamic-orders/parametrizations/${dynamicOrderParametrizationId}`, null, 20000);
  }

  loadDynamicOrder(campaignId: string, params: any, skip: number, limit: number) {
    const parameters = { ...params };

    if (skip) parameters.skip = skip || 0;
    if (limit) parameters.limit = limit || 20;

    if (params.dateFrom) parameters.dateFrom = FormatHelper.formatDateToQuery(params.dateFrom);
    if (params.dateTo) parameters.dateTo = FormatHelper.formatDateToQuery(params.dateTo);

    return this._api.get(`/api/campaigns/${campaignId}/partners/services/dynamic-orders`, parameters, 20000);
  }

  findDynamicOrderById(campaignId: string, dynamicOrderId: string) {
    return this._api.get(`/api/campaigns/${campaignId}/partners/services/dynamic-orders/${dynamicOrderId}`, null, 20000);
  }

  exportDynamicOrdersDetails(campaignId: string, parameters: any) {
    if (parameters.dateFrom) parameters.dateFrom = FormatHelper.formatDateToQuery(parameters.dateFrom);
    if (parameters.dateTo) parameters.dateTo = FormatHelper.formatDateToQuery(parameters.dateTo);

    return this._api.get(`/api/campaigns/${campaignId}/partners/services/dynamic-orders/export/csv`, parameters, 20000);
  }

  linkPaymentMethodToCampaign(campaignId: string, paymentMethod: any) {
    if (paymentMethod.id) {
        return this._api.put(`/api/campaigns/${campaignId}/partner/services/paymentmethods/${paymentMethod.id}`, paymentMethod);
    }
    return this._api.post(`/api/campaigns/${campaignId}/partner/services/paymentmethods`, paymentMethod);
  }

  getPaymentMethods(campaignId: string, params: any): Observable<any[]> {
    return this._api.get(`/api/campaigns/${campaignId}/partner/services/paymentmethods`, params);
  }
}
