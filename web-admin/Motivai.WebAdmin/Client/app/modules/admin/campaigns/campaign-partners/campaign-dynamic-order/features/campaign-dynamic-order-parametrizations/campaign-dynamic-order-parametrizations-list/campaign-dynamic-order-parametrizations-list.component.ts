import { Component, EventEmitter, OnInit, Output, ViewChild } from '@angular/core';
import { GpAlertComponent } from '../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { Subscription } from 'rxjs';
import { CampaignPartnersService } from '../../../../campaign-partners.service';
import { CampaignStore } from '../../../../../campaign.store';
import { RxjsHelpers } from '../../../../../../../../shared/helpers/rxjs-helpers';
import { FormatHelper } from '../../../../../../../../shared/formatters/format-helper';


@Component({
  selector: 'campaign-dynamic-order-parametrizations-list',
  templateUrl: './campaign-dynamic-order-parametrizations-list.component.html'
})
export class CampaignDynamicOrderParametrizationListComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @Output('select') selectEmitter: EventEmitter<any> = new EventEmitter<any>();

  campaignId: string = '';
  private _campaign$: Subscription;
  loading: boolean = false;

  dynamicOrdersParametrizations: Array<any> = [];

  constructor(private _campaignStore: CampaignStore, private _campaignPartnersService: CampaignPartnersService) {}

  ngOnInit() {
    this._campaign$ = this._campaignStore.asObservable.subscribe(id => this.campaignId = id);
    this.loadDynamicOrdersParametrizations();
  }

  ngOnDestroy(): void {
    RxjsHelpers.unsubscribe(this._campaign$);
  }

  onSelect(row: any) {
    if (!row || !row.id) {
      return;
    }

    this.selectEmitter.emit(row);
  }

  private handleError(err) {
    this.gpAlert.showError(err || 'Ocorreu um erro ao efetuar a operação.');
    this.loading = false;
  }

  loadDynamicOrdersParametrizations() {
    if (!this.campaignId) return;
    this.loading = true;
    this.gpAlert.clear();
    this._campaignPartnersService.loadDynamicOrdersParametrizations(this.campaignId).subscribe(
      dynamicOrdersParametrizations => {
        this.loading = false;
        if (dynamicOrdersParametrizations) {
          dynamicOrdersParametrizations.forEach(p => this.handleParametrizations(p));
          this.dynamicOrdersParametrizations = dynamicOrdersParametrizations;
        } else {
          this.dynamicOrdersParametrizations = [];
        }
      },
      err => this.handleError(err)
    );
  }

  handleParametrizations(dynamicOrdersParametrization: any): void {
    dynamicOrdersParametrization.formattedCreateDate = FormatHelper.formatDateWithTime(dynamicOrdersParametrization.createDate);
    if (!dynamicOrdersParametrization.orderParametrizations) {
      dynamicOrdersParametrization.orderParametrizations = {};
    }
    this.handleParametrizationValues(dynamicOrdersParametrization.orderParametrizations);
  }

  handleParametrizationValues(orderParametrizations: any): void {
    orderParametrizations.formattedFeeAmount = FormatHelper.formatCurrency(orderParametrizations.feeAmount);
    orderParametrizations.formattedMininumAmount = FormatHelper.formatCurrency(orderParametrizations.mininumAmount);
    orderParametrizations.formattedMaximumAmount = FormatHelper.formatCurrency(orderParametrizations.maximumAmount);

  }
}
