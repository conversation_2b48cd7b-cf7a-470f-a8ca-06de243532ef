import { Component, On<PERSON>nit, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { Subscription } from 'rxjs/Subscription';

import { CompanyService } from '../../../companies/company/company.service';
import { RxjsHelpers } from '../../../../../shared/helpers/rxjs-helpers';
import { CampaignPartnersService } from '../campaign-partners.service';
import { CampaignStore } from '../../campaign.store';
import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';

@Component({
  selector: 'campaign-cashback',
  templateUrl: 'campaign-cashback.component.html',
})
export class CampaignCashbackComponent implements OnInit, OnDestroy {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;

  loading: boolean = false;
  sending: boolean = false;

  campaign: any = {};
  cashbackParametrization: any = {
    pointsExpirationRule: {},
    triggers: {}
  };

  // Filtros
  filters: any = {};
  campaignPartners: Array<any> = [];
  product: any = {};

  private _campaign$: Subscription;

  constructor(private _campaignStore: CampaignStore, private _campaignPartnersService: CampaignPartnersService,
    private _companyService: CompanyService) {}

  get campaignId() {
    return this._campaignStore.id;
  }

  ngOnInit() {
    this._campaign$ = this._campaignStore.asObservable
      .subscribe(campaignId => {
        if (campaignId) {
          this.loadCampaignCashbackParametrization();
        }
      });
  }

  ngOnDestroy() {
    RxjsHelpers.unsubscribe(this._campaign$);
  }

  private newCashbackConfiguration() {
    return {
      pointsExpirationRule: {},
      triggers: {}
    };
  }

  private loadCampaignCashbackParametrization() {
    this.loading = true;
    this._campaignPartnersService.getCashbackConfiguration(this.campaignId)
      .subscribe(
        cashback => {
          this.loading = false;
          this.cashbackParametrization = cashback || this.newCashbackConfiguration();
        },
        err => {
          this.loading = false;
          this.gpAlert.handleAndShowError(err);
        }
      );
  }

  saveConfig() {
    this.sending = true;
    this.cashbackParametrization.changePointsExpirationRule = this.cashbackParametrization.pointsExpirationRule.changePointsExpirationRule;

    this._campaignPartnersService.saveCashbackConfiguration(this.campaignId, this.cashbackParametrization)
      .subscribe(
        result => {
          this.sending = false;
          if (result) {
            this.gpAlert.showSuccess('Configurações salva com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível salvar as configurações, por favor, tente novamente.');
          }
        },
        err => {
          this.gpAlert.handleAndShowError(err);
          this.sending = false;
        }
      );
  }

  // filtros
  selectPartner(partner) {

  }

  addPartnerToFilter() {

  }

  removePartnerFromFilter(partner) {

  }

  addDepartmentToFilter() {

  }

  removeDepartmentFromFilter(department) {

  }

  addProductToFilter() {

  }

  removeProductFromFilter(sku) {

  }
}
