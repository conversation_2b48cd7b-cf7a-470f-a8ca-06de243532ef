import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs/Subscription';

import { AuthStore } from '../../../../../core/auth/auth.store';
import { CampaignStore } from '../../campaign.store';

@Component({
  selector: 'campaign-partners-view',
  templateUrl: 'campaign-partners-view.component.html'
})
export class CampaignPartnersViewComponent implements OnInit {
  private campaignStore$: Subscription;
  campaignId: string;

  constructor(private _authStore: AuthStore, private route: ActivatedRoute, private router: Router , private _campaignStore: CampaignStore) {}

  ngOnInit() {
      this.campaignStore$ = this._campaignStore.asObservable
      .subscribe(campaignId => {
        this.campaignId = campaignId;
      });
  }

  get showBlockMenu() {
    return this._campaignStore.isFullCampaignOrUserWithGpBu;
  }

  get canViewProductsPartners() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_PRODUCT_PARTNERS;
  }

  get canViewMobilePartners() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_MOBILE_PARTNERS;
  }

  get canViewBillPaymentPartners() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_BILL_PAYMENT_PARTNERS;
  }

  get canViewGateways() {
      return this._authStore.role.PERMISSION_CAMPAIGNS_PAYMENT_GATEWAYS;
  }

  get canViewInstitutionalPartners() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_INSTITUTIONAL_PARTNER_VIEW;
  }

  get canViewPrePaidCard() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_PARTNERS_PRE_PAID_CARD_VIEW;
  }

  get canViewBankTransfers() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_PARTNERS_BANK_TRANSFERS;
  }

  get canViewP2pTransfers() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_PARTNERS_P2P_TRANSFERS;
  }

   get canViewPaymentMethods() {
      return this._authStore.role.PERMISSION_CAMPAIGNS_PAYMENT_METHODS_VIEW;
   }
}
