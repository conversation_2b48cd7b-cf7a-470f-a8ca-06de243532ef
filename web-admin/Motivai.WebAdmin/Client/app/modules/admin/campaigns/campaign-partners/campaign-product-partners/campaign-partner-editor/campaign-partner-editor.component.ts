import { Component, ViewChild, EventEmitter, Output, Input } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';

import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignService } from '../../../campaign.service';
import { PartnerSetting } from '../../../campaign';
import { CompanyService } from '../../../../companies/company/company.service';
import { BusinessUnitService } from '../../../../../security/business-unit/business-unit.service';
import { AuthStore } from '../../../../../../core/auth/auth.store';
import { PERMISSION_CAMPAIGNS_PRODUCT_PARTNERS_INTEGRATION } from '../../../../../../core/auth/access-points';

@Component({
  selector: 'campaign-partner-editor',
  templateUrl: 'campaign-partner-editor.component.html'
})
export class CampaignPartnerEditorComponent {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @Output()
  updated: EventEmitter<boolean> = new EventEmitter<boolean>();

  formGroup: FormGroup;
  messages = {
    boost: {
      number: 'Boost inválido'
    },
    receiverName: {
      required: 'Nome é obrigatório'
    },
    receiverEmail: {
      required: 'E-mail é obrigatório',
      email: 'E-mail inválido'
    }
  };
  partners: Array<{id: string, text: string}> = [];
  @Input()
  campaignId: string;
  settingsId: string;
  partnerId: string;
  loading: boolean = false;
  sending: boolean = false;
  private _mailSubscription: Subscription;

  constructor(private _authStore: AuthStore, private _buService: BusinessUnitService,
    private _campaignService: CampaignService, private _companyService: CompanyService, fb: FormBuilder) {
    let ctrlEnableEmail = new FormControl(false);
    let ctrlName = new FormControl('');
    let ctrlEmail = new FormControl('');
    this.formGroup = fb.group({
      active: [false],
      partnerId: ['', Validators.required],
      administrativeTax: [''],
      boost: ['0'],
      campaignIntegrationId: [''],
      cnpjIntegration: [''],
      partnerIdentifier: [''],
      enableOrdersEmail: ctrlEnableEmail,
      receiverName: ctrlName,
      receiverEmail: ctrlEmail
    });
    this._mailSubscription = ctrlEnableEmail.valueChanges.subscribe(value => {
      if (value) {
        ctrlName.setValidators(Validators.required);
        // ctrlEmail.setValidators([Validators.required, Validators.email]);
        ctrlEmail.setValidators([Validators.required]);
      } else {
        ctrlName.clearValidators();
        ctrlEmail.clearValidators();
      }
      this.formGroup.markAsTouched();
    });
  }

  ngOnInit() {
    // this._companyService.getPartners()
    this._buService.getPartners()
      .subscribe(
        partners => {
          if (partners && partners.allowedPartners) {
            this.partners = partners.allowedPartners.map(p => {
              return { id: p.id, text: p.name};
            });
          } else {
            this.partners = [];
          }
        },
        err => this.handleError(err)
      );
  }

  ngOnDestroy() {
    if (this._mailSubscription)
      this._mailSubscription.unsubscribe();
  }

  get canEditPartnerIntegrationConfig() {
    return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_PRODUCT_PARTNERS_INTEGRATION);
  }

  isEdition() {
    return !!this.partnerId;
  }

  handleError(err) {
    let errorMessage = (err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao efetuar a operação.');
    this.gpAlert.showError(errorMessage);
    this.loading = false;
    this.sending = false;
  }

  newLink() {
    this.gpAlert.clear();
    this.partnerId = '';
    this.settingsId = '';
    this.formGroup.reset();
    this.formGroup.patchValue(new PartnerSetting());
  }

  prepareEdit(partnerId: string) {
    this.loading = true;
    this.newLink();
    if (partnerId) {
      this._campaignService.getLinkedPartner(this.campaignId, partnerId)
        .subscribe(
          partnerSetting => {
            if (partnerSetting) {
              this.settingsId = partnerId;
              this.partnerId = partnerId;
              this.formGroup.patchValue(partnerSetting);
            } else {
              this.gpAlert.showWarning('Parceiro não encontrado.');
            }
            this.loading= false;
          },
          err => this.handleError(err)
        );
    }
  }

  onFormSubmit(event) {
    if (!event.valid) {
      this.gpAlert.showWarning('Preencha os campos para continuar.');
      return;
    }
    this.sending = true;
    this.gpAlert.clear();
    let partnerSetting = event.model;
    if (this.settingsId) {
      partnerSetting.id = this.settingsId;
      partnerSetting.partnerId = this.partnerId;
    }
    this._campaignService.savePartnerSetting(this.campaignId, partnerSetting)
      .subscribe(
        result => {
          if (result) {
            this.newLink();
            this.gpAlert.showSuccess('Configurações do parceiro salvo com sucesso.');
            this.updated.emit(true);
          } else {
            this.gpAlert.showWarning('Não foi possível salvar as configurações, por favor, tente novamente.');
          }
        },
        err => this.handleError(err),
        () => this.sending = false
      );
  }
}
