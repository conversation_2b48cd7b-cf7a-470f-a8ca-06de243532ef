<gp-card title="Parceiros de Produtos">
    <spinner [show]="loading" [overlay]="true"></spinner>
    <gp-row>
        <gp-form-col cols="12 12 12">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th><PERSON><PERSON><PERSON><PERSON></th>
                        <th class="pull-right">Ativo</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let p of partners">
                        <td>{{p.partnerName}}</td>
                        
                        <td><gp-switch class="pull-right"type="checkbox" id="active"  name="active" [(ngModel)]="p.active" ></gp-switch></td>
                    </tr>
                </tbody>
            </table>
        </gp-form-col>
    </gp-row>
</gp-card>
<gp-card>
    <gp-row>
        <gp-form-col cols="12 12 12">
            <gp-spinner-button type="button" [pink]="true" icon="send" size="lg" pull="right" text="Sal<PERSON>" 
            loadingText="Processando"[loading]="loadingPartners" [overlay]="true" (click)="savePartners()"></gp-spinner-button>
        </gp-form-col>
    </gp-row>
</gp-card>
<gp-alert #alert [overlay]=true></gp-alert>
