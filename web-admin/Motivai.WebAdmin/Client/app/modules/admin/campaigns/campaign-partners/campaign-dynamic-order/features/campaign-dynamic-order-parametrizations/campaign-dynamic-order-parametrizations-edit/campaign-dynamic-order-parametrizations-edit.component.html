<gp-alert [overlay]="true" #gpAlert></gp-alert>

<gp-card title="Parametrização da Modalidade" [first]="true">
	<spinner [overlay]="true" [show]="loading"></spinner>
	<gp-form-row>
		<gp-form-col cols="12 6">
			<gp-simple-input label="Nome">
				<input type="text" name="name" class="form-control" [(ngModel)]="dynamicOrderParametrization.name" />
			</gp-simple-input>
		</gp-form-col>

		<gp-form-col cols="12 6">
			<gp-simple-input label="Descrição">
				<input type="text" name="description" class="form-control"
					[(ngModel)]="dynamicOrderParametrization.description" />
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>

	<gp-form-row>
		<gp-form-col cols="12 6">
			<gp-simple-input label="Descrição para Exibição">
				<input type="text" name="descriptionDisplay" class="form-control"
					[(ngModel)]="dynamicOrderParametrization.descriptionDisplay" />
			</gp-simple-input>
		</gp-form-col>

		<gp-form-col cols="12 6">
			<gp-simple-input label="Código do Produto">
				<input type="text" name="productCode" class="form-control"
					[(ngModel)]="dynamicOrderParametrization.productCode" />
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>

	<gp-form-row>
		<gp-form-col cols="12 6">
			<label>Ativo</label>
			<div>
				<gp-switch [disabled]="hasStarted" [(ngModel)]="dynamicOrderParametrization.active"></gp-switch>
			</div>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card title="Parametrização de View">

	<h4>Icone</h4>
	<label>Icone para a tela incial de como usar seu saldo site/app</label>
	<gp-form-row>
		<gp-form-col cols="12 6">
			<div class="form-group">
				<gp-fileupload-default-folder name="homeIconUrl" [onlyImages]="true" [showRender]="true"
					[skipImageUrlWidth]="true" [campaignId]="campaignId" (error)="onUploadError($event)"
					imageWidth="525" renderWidth="250"
					[(ngModel)]="dynamicOrderParametrization.viewParametrizations.homeIconUrl">
				</gp-fileupload-default-folder>
			</div>
		</gp-form-col>
		<gp-form-col cols="12 6">
			<label>Remova a imagem</label>
			<gp-spinner-button style="display: flex;" type="button" [pink]="true" icon="trash" text="Remover a imagem"
				marginRight="5px" (click)="removeHomeIconUrl()">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>

	<h4>Cartão</h4>

	<gp-form-row>
		<gp-form-col cols="12 6">
			<label>Defina a cor que representa o cartão</label>
			<input class="form-control" [(colorPicker)]="cardColor"
				[(ngModel)]="dynamicOrderParametrization.viewParametrizations.cardColor" cpOutputFormat="hex"
				[style.background]="dynamicOrderParametrization.viewParametrizations.cardColor" outputColor="#000">
		</gp-form-col>
	</gp-form-row>

	<label>Cartão para exibição na tela de transferência site/app</label>
	<gp-form-row>
		<gp-form-col cols="12 6">
			<div class="form-group">
				<gp-fileupload-default-folder name="imageCardUrl" [onlyImages]="true" [showRender]="true"
					[skipImageUrlWidth]="true" [campaignId]="campaignId" (error)="onUploadError($event)"
					imageWidth="525" renderWidth="350"
					[(ngModel)]="dynamicOrderParametrization.viewParametrizations.imageCardUrl">
				</gp-fileupload-default-folder>
			</div>
		</gp-form-col>
		<gp-form-col cols="12 6">
			<label>Remova a imagem</label>
			<gp-spinner-button style="display: flex;" type="button" [pink]="true" icon="trash" text="Remover a imagem"
				marginRight="5px" (click)="removeBannerUrl()">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>

	<hr>

	<h4>Instruções da Modalidade</h4>
	<label>Texto dinâmico para instrução do modulo site/app</label>
	<gp-dynamic-contents-editor [dynamicContents]="dynamicOrderParametrization.viewParametrizations.instructions">
	</gp-dynamic-contents-editor>

	<h4>Mensagem pós Processamento</h4>
	<label>Notificação dinâmica de pedido processado no site/app</label>
	<gp-form-row>
		<gp-form-col cols="12 6">
			<gp-simple-input label="Título" tooltip="Título exibido na mensagem pós processamento.">
				<input type="text" name="title" class="form-control"
					[(ngModel)]="dynamicOrderParametrization.viewParametrizations.processingResultMessage.title" />
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<gp-simple-input label="Subtítulo" tooltip="Subtítulo exibido na mensagem pós processamento.">
				<textarea name="description" class="form-control" rows="5"
					[(ngModel)]="dynamicOrderParametrization.viewParametrizations.processingResultMessage.subtitle">
				</textarea>
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card title="Regras da Modalidade">
	<gp-form-row>
		<gp-form-col cols="12 6 6">
			<label>
				Taxa em cima do valor total
			</label>
			<gp-tooltip>Taxa cobrada do valor total da transferência de saldo.</gp-tooltip>
			<gp-input-mask name="feeAmount" [thousandsSeparator]="true" [onlyDecimal]="true"
				[(ngModel)]="dynamicOrderParametrization.orderParametrizations.feeAmount">
			</gp-input-mask>
		</gp-form-col>

		<gp-form-col cols="12 6 6">
			<label>
				Valor mínimo
			</label>
			<gp-tooltip>Valor mínimo para transferência de saldo.</gp-tooltip>
			<gp-input-mask name="minimumAmount" [thousandsSeparator]="true" [onlyDecimal]="true"
				[(ngModel)]="dynamicOrderParametrization.orderParametrizations.minimumAmount">
			</gp-input-mask>
		</gp-form-col>

		<gp-form-col cols="12 6 6">
			<label>
				Valor maxímo
			</label>
			<gp-tooltip>Valor maxímo para transferência de saldo.</gp-tooltip>
			<gp-input-mask name="maximumAmount" [thousandsSeparator]="true" [onlyDecimal]="true"
				[(ngModel)]="dynamicOrderParametrization.orderParametrizations.maximumAmount">
			</gp-input-mask>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card title="Parametrização do Processo">

	<h4>Notificação de Pedido</h4>
	<gp-form-row>
		<gp-form-col cols="12 6">
			<label>Habilitar notificação de pedido recebido</label>
			<div>
				<gp-switch name="enableNotificationTemplateOrdersReceived"
					[(ngModel)]="dynamicOrderParametrization.orderParametrizations.enableNotificationTemplateOrdersReceived">
				</gp-switch>
			</div>
		</gp-form-col>
		<gp-form-col cols="12 6" [inputGroup]="false" additionalClasses="col-with-button">
			<gp-spinner-button bootstrapClass="primary" text="Editar Template Premiado" icon="envelope"
				(click)="showNotificationTemplateOrdersReceivedModal()">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>

	<label>Exibir quantidade de dias para processamento site/app </label>
	<gp-form-row>
		<gp-form-col cols="12 6">
			<gp-input-mask name="processingDays" [onlyInteger]="true"
				[(ngModel)]="dynamicOrderParametrization.orderParametrizations.processingDays">
			</gp-input-mask>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-email-template-editor editorId="notificationTemplateOrdersReceivedModal" [showSubject]="true"
	[availableVarsHelper]="notificationTemplateOrdersReceivedVariables"
	[subject]="dynamicOrderParametrization.orderParametrizations.notificationTemplateOrdersReceived.emailSubject"
	[template]="dynamicOrderParametrization.orderParametrizations.notificationTemplateOrdersReceived.emailTemplate"
	(save)="saveNotificationTemplateOrdersReceived($event)" #notificationTemplateOrdersReceivedEditor>
</gp-email-template-editor>

<gp-card title="Segmentação">
	<gp-form-row>
		<gp-form-col cols="12 6">
			<label>Habilitar segmentação por grupo</label>
			<div>
				<gp-switch [disabled]="hasStarted"
					[(ngModel)]="dynamicOrderParametrization.segmentations.enableByParticipantsGroups"></gp-switch>
			</div>
		</gp-form-col>

		<gp-campaign-participants-group-selector
			*ngIf="dynamicOrderParametrization.segmentations.enableByParticipantsGroups" name="participantsGroups"
			cols="12 6" [enableEmpty]="false" [campaignId]="campaignId" [multiple]="true"
			[(ngModel)]="dynamicOrderParametrization.segmentations.participantsGroups">
		</gp-campaign-participants-group-selector>
	</gp-form-row>

	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<div class="form-group">
				<gp-form-row>
					<gp-form-col cols="6">
					  <label>Habilitar segmentação por participante pai</label>
					  <div>
						<gp-switch name="enableByParentsParticipants" [(ngModel)]="dynamicOrderParametrization.segmentations.enableByParentsParticipants">
						</gp-switch>
					  </div>
					</gp-form-col>
				  </gp-form-row>
				<gp-form-row *ngIf="dynamicOrderParametrization.segmentations.enableByParentsParticipants">
					<gp-form-col cols="12 3 3">
						<label>CPF/CNPJ</label>
						<gp-input-mask mask="00000000000000"
							[(ngModel)]="parentParticipantDocument"></gp-input-mask>
					</gp-form-col>

					<gp-form-col cols="12 3 3">
						<gp-spinner-button bootstrapClass="primary" text="Adicionar" pull="left" icon="plus-circle"
							(click)="addParticipantParent()" marginTop="24px">
						</gp-spinner-button>
					</gp-form-col>
				</gp-form-row>

				<gp-form-row *ngIf="dynamicOrderParametrization.segmentations.enableByParentsParticipants">
					<gp-form-col cols="12 12 12">
						<gp-grid name="parentsParticipants"
							[rows]="dynamicOrderParametrization.segmentations.parentsParticipants" [loading]="loading"
							[showDelete]="true" [showEdit]="false" [columns]="['CPF/CNPJ']" [fields]="['document']"
							[showActive]="false" [showPagination]="false" [pageSize]="limit" [showTotalPages]="false"
							(onDelete)="removeParentParticipant($event)">
						</gp-grid>
					</gp-form-col>
				</gp-form-row>
			</div>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card>
	<gp-form-row>
		<gp-form-col cols="12 12 12" [inputGroup]="false">
			<gp-spinner-button type="button" [pink]="true" icon="send" text="Salvar" marginRight="5px" pull="right"
				(click)="save()" [loading]="loading" loadingText="Processando">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
</gp-card>