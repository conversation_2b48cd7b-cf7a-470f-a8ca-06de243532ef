import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { ApiService } from '../../../../../../core/api/api.service';

@Injectable()
export class CampaignCardService {
    constructor(private _api: ApiService) {}

    getCardsSettings(campaignId: string): Observable<any> {
        return this._api.get(`/api/campaigns/${campaignId}/cards/prepaid`, null, 20000);
    }

    saveCardsSettings(campaignId: string, card: any): Observable<any> {
        return this._api.put(`/api/campaigns/${campaignId}/cards/prepaid`, card, 20000);
    }

    removeWithdrawableImage(campaignId: string): Observable<boolean> {
		return this._api.delete(`/api/campaigns/${campaignId}/cards/prepaid/withdrawable/image`, 20000);
    }

    removeNoDrawableImage(campaignId: string): Observable<boolean> {
        return this._api.delete(`/api/campaigns/${campaignId}/cards/prepaid/nodrawable/image`, 20000);
    }
}
