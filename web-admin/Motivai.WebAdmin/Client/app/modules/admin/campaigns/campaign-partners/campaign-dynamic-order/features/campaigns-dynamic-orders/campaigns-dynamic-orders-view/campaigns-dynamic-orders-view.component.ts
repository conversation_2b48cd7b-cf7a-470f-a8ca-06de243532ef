import { Component, ViewChild } from '@angular/core';
import { CampaignStore } from '../../../../../campaign.store';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { TabsetComponent } from 'ng2-bootstrap';
import { GpAlertComponent } from '../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignsDynamicOrdersListComponent } from '../campaigns-dynamic-orders-list/campaigns-dynamic-orders-list.component';
import { CampaignsDynamicOrdersEditComponent } from '../campaigns-dynamic-orders-edit/campaigns-dynamic-orders-edit.component';

@Component({
  selector: 'campaigns-dynamic-orders-view',
  templateUrl: 'campaigns-dynamic-orders-view.component.html'
})
export class CampaignsDynamicOrdersViewComponent {
  private _campaign$: Subscription;

  @ViewChild('listComponent') listComponent: CampaignsDynamicOrdersListComponent;
  @ViewChild('editComponent') editComponent: CampaignsDynamicOrdersEditComponent;
  @ViewChild('tabs') tabs: TabsetComponent;
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;

  campaignId: string;
  loading: boolean = false;

  constructor(private route: ActivatedRoute, private router: Router, private _campaignStore: CampaignStore) { }

  ngOnInit() {
    this._campaign$ = this._campaignStore.asObservable.subscribe(campaignId => {
      this.campaignId = campaignId;
      this.setDetailTabDisabled(true)
    });
  }

  onSelect(row: any) {
    if (!row.id) {
      return;
    }

    this.editComponent.showDetails(row);
    this.setDetailTabDisabled(false);
    this.tabs.tabs[1].active = true;
  }

  clearEditForm() {
    this.editComponent.clear();
    this.setDetailTabDisabled();
    this.listComponent.loadDynamicOrders();
  }

  setDetailTabDisabled(disabled: boolean = true) {
    this.tabs.tabs[1].disabled = disabled;
  }
}
