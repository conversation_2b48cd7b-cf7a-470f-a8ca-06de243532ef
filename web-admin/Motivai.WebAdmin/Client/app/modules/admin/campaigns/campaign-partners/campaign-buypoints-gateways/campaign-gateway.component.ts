import { GpCampaignGatewayListComponent } from './campaign-gateway-list/campaign-gateway-list.component';
import { TabsetComponent } from 'ng2-bootstrap/tabs';
import { GpCampaignGatewayEditComponent } from './campaign-gateway-edit/campaign-gateway-edit.component';
import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { Router, ActivatedRoute } from '@angular/router';
import { Campaign } from '../../campaign';
import { Component, OnInit, ViewChild } from '@angular/core';
import { CampaignStore } from '../../campaign.store';
import { Subscription } from 'rxjs/Subscription';

@Component({
  selector: 'campaign-gateway',
  templateUrl: 'campaign-gateway.component.html'
})
export class GpCampaignGatewayComponent implements OnInit {
  private campaignStore$: Subscription;
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @ViewChild('editComponent') editComponent: GpCampaignGatewayEditComponent;
  @ViewChild('listComponent') listComponent: GpCampaignGatewayListComponent;
  @ViewChild('tabs') tabs: TabsetComponent;

  campaignId: string;
  gatewayCompany: string;
  loading: boolean = false;

  constructor(private route: ActivatedRoute, private router: Router, private _campaignStore: CampaignStore) {}

  ngOnInit() {
    this.campaignStore$ = this._campaignStore.asObservable
    .subscribe(campaignId => {
      this.campaignId = campaignId;
    });
  }

  handleError(err) {
    let errorMessage = err
      ? err.message ? err.message : err.toString()
      : 'Ocorreu um erro ao efetuar a operação.';
    this.gpAlert.showError(errorMessage);
  }

  onEditGateway($event: any) {
    this.gatewayCompany = $event.gatewayCompany;
    this.tabs.tabs[1].active = true;
  }

  clearEditForm() {
    this.editComponent.resetForm();
    this.gatewayCompany = '';
    this.listComponent.getPaymentGateways();
  }
}
