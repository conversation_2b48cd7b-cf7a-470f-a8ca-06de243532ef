import { Component, ViewChild, OnInit } from '@angular/core';
import { TabsetComponent } from 'ng2-bootstrap/tabs';

import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { ActivatedRoute, Router } from '@angular/router';
import { CampaignBillPaymentParametrizationListComponent } from './campaign-bill-payment-parametrization-list/campaign-bill-payment-parametrization-list.component';
import { CampaignBillPaymentParametrizationEditComponent } from './campaign-bill-payment-parametrization-edit/campaign-bill-payment-parametrization-edit.component';
import { Subscription } from 'rxjs/Subscription';
import { CampaignStore } from '../../../campaign.store';

@Component({
  selector: 'campaign-bill-payment-view',
  templateUrl: 'campaign-bill-payment-parametrization-view.component.html'
})
export class CampaignBillPaymentParametrizationViewComponent implements OnInit {
  @ViewChild('listComponent') listComponent: CampaignBillPaymentParametrizationListComponent;
  @ViewChild('editComponent') editComponent: CampaignBillPaymentParametrizationEditComponent;
  @ViewChild('tabs') tabs: TabsetComponent;
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  private campaignStore$: Subscription;
  campaignId: string;
  loading: boolean = false;

  constructor(private route: ActivatedRoute, private router: Router, private _campaignStore: CampaignStore) { }

  ngOnInit() {
    this.campaignStore$ = this._campaignStore.asObservable
      .subscribe(
        campaignId => {
          this.campaignId = campaignId
        });
  }

  private handleError(err) {
    this.gpAlert.showError(err || 'Ocorreu um erro ao efetuar a operação.');
  }

  onUpdate() {
    this.listComponent.loadBillPaymentPartners();
  }

  onEditRow(partnerId: string) {
    this.editComponent.prepareEdit(partnerId);
    this.tabs.tabs[1].active = true;
  }

  clearEditForm() {
    this.editComponent.resetForm();
    this.listComponent.loadBillPaymentPartners();
  }
}
