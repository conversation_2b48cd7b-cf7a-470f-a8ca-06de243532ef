import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, ViewChild } from "@angular/core";
import { Item } from "../../../../../../shared/models/item";
import { GeneralSettingsService } from "../../../../general-settings/services/generalSettings.service";
import { GpAlertComponent } from "../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component";
import { CampaignPartnersService } from "../../campaign-partners.service";
import { Subscription } from "rxjs";
import { CampaignStore } from "../../../campaign.store";
import { RxjsHelpers } from "../../../../../../shared/helpers/rxjs-helpers";

@Component({
	selector: 'campaign-payment-methods-edit',
	templateUrl: './campaign-payment-methods-edit.component.html'
})
export class CampaignPaymentMethodsEditComponent implements OnInit, <PERSON><PERSON><PERSON>roy {
	@ViewChild('alert') alert: GpAlertComponent;
	loading: boolean = false;
	isEdition: boolean = false;
	paymentMethod: any = {
		parameters: []
	};
	enabledPlataformPaymentMethodsForSelection: Array<Item> = [];
	enabledPlataformPaymentMethods: Array<any> = [];
	paymentMethodParameter: any = {};

	private _campaign$: Subscription;

	get campaignId() {
		return this._campaignStore.id;
	}

	get isPaymentMethodValid() {
		return this.paymentMethod != null;
	}

	get hasPaymentMethodParameters(): boolean {
        const { parameters } = this.paymentMethod;
		if (parameters) {
			return parameters.length > 0;
		}
        
		return false;
    }

	constructor(private _generalSettingsService: GeneralSettingsService, private _partnerService: CampaignPartnersService, private _campaignStore: CampaignStore) { }

	ngOnInit() {
		this._campaign$ = this._campaignStore.asObservable
			.subscribe(id => {
				if (id)
					this.getEnabledPlataformPaymentMethods();
			});
	}

	ngOnDestroy() {
		RxjsHelpers.unsubscribe(this._campaign$);
	}

	prepareToEdit($event) {
		this.isEdition = true;
		this.getEnabledPlataformPaymentMethods();
		this.paymentMethod = {
			id: $event.id,
			name: $event.name,
			description: $event.description,
			code: $event.code,
			active: $event.active,
			type: $event.type,
			parameters: $event.parameters || []
		};
	}

	getEnabledPlataformPaymentMethods() {
		this.loading = true;
		this._generalSettingsService.fetchAllPaymentMethods().subscribe(
			response => {
				if (response && response.length > 0) {
					let methodsForSelection = [];
					response.forEach(r => {
						methodsForSelection.push(Item.of(r.code, r.name))
					});
					this.enabledPlataformPaymentMethods = response;
					this.enabledPlataformPaymentMethodsForSelection = methodsForSelection;
				}
				this.loading = false;
			},
			err => {
				this.loading = false;
				this.alert.handleAndShowError(err);
			}
		);
	}

	onEnabledPaymentMethodSelected(event) {
		this.paymentMethod = {};
		const selected = this.enabledPlataformPaymentMethods.find(m => m.code === event);
		if (selected) {
			this.paymentMethod = {
				name: selected.name,
				description: selected.description,
				code: selected.code,
				active: selected.active,
				type: selected.type,
				parameters: selected.parameters
			};
		}
	}

	linkPaymentMethodToCampaign() {
		this.loading = true;
		this._partnerService.linkPaymentMethodToCampaign(this.campaignId, this.paymentMethod)
			.subscribe(response => {
				if (response) {
					this.alert.showSuccess("Método de pagamento vinculado.");
					this.clear();
				}

				this.loading = false
			},
			err => {
				this.loading = false;
				this.alert.handleAndShowError(err);
			})
	}

	addPaymentMethodParameters(parameter?: string, value?: string) {
        if (!this.paymentMethodParameter.parameter || !this.paymentMethodParameter.value) {
            this.alert.showWarning("Precisa preencher os campos para adicionar.");
            return;
        }

		if (this.paymentMethod.parameters) {
			this.paymentMethod.parameters
            	.push({ parameter, value });
		} else {
			this.paymentMethod.parameters = [{parameter: parameter, value: value}];
		}

		this.paymentMethodParameter = {};
    }

	deletePaymentMethodParameters($event) {
        const { parameters } = this.paymentMethod;
        if (parameters) {
            const findParameter = parameters.find(e => e.parameter === $event.parameter);
            const index = parameters.indexOf(findParameter);
            parameters.splice(index, 1);
        }
    }

	clear() {
		this.paymentMethod = { type: '', parameters: [] };
		this.getEnabledPlataformPaymentMethods();
		this.paymentMethodParameter = {};
		this.loading = false;
		this.isEdition = false;
	}
}
