import { Component, OnInit, ViewChild } from '@angular/core';
import { GpAlertComponent } from '../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignPartnersService } from '../../../../campaign-partners.service';
import { CampaignStore } from '../../../../../campaign.store';
import { TemplateVariable } from '../../../../../../../../shared/components/gp-email-template-editor/template-variable';
import { GpEmailTemplateComponent } from '../../../../../../../../shared/components/gp-email-template-editor/gp-email-template-editor.component';
import { NotificationTemplate } from '../../../../../campaign-site/raffle/models/CampaignRaffle.model';
import { TemplateSaveEvent } from '../../../../../../../../shared/components/gp-email-template-editor/template-save-event';
import { CampaignDynamicOrderParametrization, OrderParametrizations, Segmentations } from '../../../models/campaign-dynamic-order.model';
import { RxjsHelpers } from '../../../../../../../../shared/helpers/rxjs-helpers';
import { Subscription } from 'rxjs';

@Component({
  selector: 'campaign-dynamic-order-parametrizations-edit',
  templateUrl: './campaign-dynamic-order-parametrizations-edit.component.html'
})
export class CampaignDynamicOrderParametrizationEditComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @ViewChild('notificationTemplateOrdersReceivedEditor') notificationTemplateOrdersReceivedEditor: GpEmailTemplateComponent;

  campaignId: string = '';
  private _campaign$: Subscription;

  constructor(private _campaignStore: CampaignStore, private _campaignPartnersService: CampaignPartnersService) { }

  dynamicOrderParametrization: CampaignDynamicOrderParametrization = new CampaignDynamicOrderParametrization();
  defaultColor = '#00be28';
  parentParticipantDocument: string;

  ngOnInit() {
    this._campaign$ = this._campaignStore.asObservable.subscribe(id => this.campaignId = id);
  }

  ngOnDestroy() {
    RxjsHelpers.unsubscribe(this._campaign$);
  }

  loading = false;

  loadDynamicOrderParametrizationDetails(dynamicOrderId: string) {
    this.loading = true;
    this._campaignPartnersService.findDynamicOrderParametrizationById(this.campaignId, dynamicOrderId).subscribe(
      dynamicOrder => {
        this.loading = false;
        this.dynamicOrderParametrization = dynamicOrder;

        if (!this.dynamicOrderParametrization.orderParametrizations) {
          this.dynamicOrderParametrization.orderParametrizations = new OrderParametrizations();
        }
        if (!this.dynamicOrderParametrization.segmentations) {
          this.dynamicOrderParametrization.segmentations = new Segmentations();
        }
      },
      err => {
        this.loading = false;
        this.gpAlert.handleAndShowError(err);
      }
    );
  }

  save() {
    this.loading = true;
    this._campaignPartnersService.saveDynamicOrderParametrization(this._campaignStore.id, this.dynamicOrderParametrization).subscribe(
      dynamicOrder => {
        this.dynamicOrderParametrization = dynamicOrder;
        this.loading = false;
        this.gpAlert.showSuccess('Modalidade de resgate salva com sucesso!');
      },
      err => {
        this.loading = false;
        this.gpAlert.showError(err);
      }
    );
  }

  showDetails(dynamicOrder: any) {
    this.clear();
    if (!dynamicOrder || !dynamicOrder.id) {
      return;
    }
    this.loadDynamicOrderParametrizationDetails(dynamicOrder.id);
  }

  clear() {
    this.dynamicOrderParametrization = new CampaignDynamicOrderParametrization();
  }

  removeHomeIconUrl() {
    delete this.dynamicOrderParametrization.viewParametrizations.homeIconUrl;
  }

  removeBannerUrl() {
    delete this.dynamicOrderParametrization.viewParametrizations.imageCardUrl;
  }

  get cardColor() {
    if (!this.dynamicOrderParametrization.viewParametrizations) {
      this.dynamicOrderParametrization.viewParametrizations.cardColor = this.defaultColor;
      return this.dynamicOrderParametrization.viewParametrizations.cardColor;
    }

    return this.dynamicOrderParametrization.viewParametrizations.cardColor;
  }

  set cardColor(value) {
    this.dynamicOrderParametrization.viewParametrizations.cardColor = value;
  }

  notificationTemplateOrdersReceivedVariables: any[] = [
    TemplateVariable.of('NOMEPARTICIPANTE', 'Nome do participante'),
    TemplateVariable.of('VALORDOPEDIDO', 'Valor do pedido'),
    TemplateVariable.of('DATADOPEDIDO', 'Data do pedido'),
    TemplateVariable.of('METADATA', 'Detalhes dinâmicos do pedido')
  ];

  showNotificationTemplateOrdersReceivedModal() {
    this.notificationTemplateOrdersReceivedEditor.openEmailTemplateEditor();
  }

  saveNotificationTemplateOrdersReceived(event: TemplateSaveEvent) {
    if (!this.dynamicOrderParametrization.orderParametrizations.notificationTemplateOrdersReceived) {
      this.dynamicOrderParametrization.orderParametrizations.notificationTemplateOrdersReceived = new NotificationTemplate();
    }
    this.dynamicOrderParametrization.orderParametrizations.notificationTemplateOrdersReceived.enableEmail = this.dynamicOrderParametrization.orderParametrizations.enableNotificationTemplateOrdersReceived;
    this.dynamicOrderParametrization.orderParametrizations.notificationTemplateOrdersReceived.emailSubject = event.subject;
    this.dynamicOrderParametrization.orderParametrizations.notificationTemplateOrdersReceived.emailTemplate = event.template;
  }

  addParticipantParent(): void {
    if (!this.parentParticipantDocument) {
      return this.gpAlert.showWarning('Preencha o documento');

    }
    if (!this.dynamicOrderParametrization.segmentations.parentsParticipants) {
      this.dynamicOrderParametrization.segmentations.parentsParticipants = [];
    }

    if (this.dynamicOrderParametrization.segmentations.parentsParticipants.some(p => p.document == this.parentParticipantDocument)) {
      return this.gpAlert.showWarning('Documento já cadastrado');
    }

    this.dynamicOrderParametrization.segmentations.parentsParticipants.push({ document: this.parentParticipantDocument });
    this.parentParticipantDocument = '';
  }

  removeParentParticipant(parentParticipant: any): void {
    this.dynamicOrderParametrization.segmentations.parentsParticipants =  this.dynamicOrderParametrization.segmentations.parentsParticipants.filter(p => p.document != parentParticipant.document);
  }
}
