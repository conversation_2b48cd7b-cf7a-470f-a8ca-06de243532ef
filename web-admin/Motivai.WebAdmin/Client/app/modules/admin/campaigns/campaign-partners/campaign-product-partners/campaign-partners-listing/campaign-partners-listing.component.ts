import { Component, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TabsetComponent } from 'ng2-bootstrap/tabs';

import { CampaignPartnerEditorComponent } from '../campaign-partner-editor/campaign-partner-editor.component';
import { GpAlertComponent } from "../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component";
import { CompanyService } from '../../../../companies/company/company.service';
import { AuthStore } from "../../../../../../core/auth/auth.store";
import { Campaign, PartnerSetting } from "../../../campaign";
import { CampaignService } from "../../../campaign.service";
import { BusinessUnitService } from '../../../../../security/business-unit/business-unit.service';
import { CampaignStore } from '../../../campaign.store';
import { Subscription } from 'rxjs/Subscription';

@Component({
    selector: 'campaign-partners-listing',
    templateUrl: 'campaign-partners-listing.component.html'
})
export class CampaignPartnersListingComponent {
  private campaignStore$: Subscription;
  @ViewChild('tabset') tabs: TabsetComponent;
  @ViewChild('partnerEditor') partnerEditor: CampaignPartnerEditorComponent;
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  campaignId: string;

  loading: boolean = false;
  sending: boolean = false;

  // partners: Array<any> = [];
  linkedPartners: Array<PartnerSetting> = [];

  constructor(private _authStore: AuthStore, private _buService: BusinessUnitService, private _campaignService: CampaignService,
    private _companyService: CompanyService, private route: ActivatedRoute, private router: Router, private _campaignStore: CampaignStore) {
  }

  ngOnInit() {

    this.campaignStore$ = this._campaignStore.asObservable
    .subscribe(campaignId => {
      this.campaignId = campaignId;
      this.search();
    });
  }

  get disableButtons() {
    return this.loading || this.sending;
  }

  handleError(err) {
    let errorMessage = (err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao efetuar a operação.');
    this.gpAlert.showError(errorMessage);
    this.loading = false;
    this.sending = false;
  }

  search() {
    this.gpAlert.clear();
    this.loading = true;
    this._campaignService.getLinkedPartnersByCampaign(this.campaignId)
      .subscribe(
        partners => {
          this.linkedPartners = partners;
          // this.setPartner();
          this.loading = false;
        },
        err => this.handleError(err)
      );
  }

  prepareEdit(partner) {
    this.partnerEditor.prepareEdit(partner.partnerId);
    this.tabs.tabs[1].active = true;
  }

  remove(partner) {
    if (!confirm(`Deseja desvincular o parceiro '${partner.partnerName}'?`)) return;
    this.loading = true;
    this._campaignService.unlinkPartner(this.campaignId, partner.partnerId)
      .subscribe(
        result => {
          if (result) {
            this.search();
            this.gpAlert.showSuccess('Parceiro desvinculado com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível desvincular o parceiro, por favor, tente novamente.');
          }
        },
        err => this.handleError(err),
        () => this.loading = false
      );
  }
}
