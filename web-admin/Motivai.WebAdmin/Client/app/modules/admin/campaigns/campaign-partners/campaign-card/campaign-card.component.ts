import { PERMISSION_CAMPAIGNS_PARTNERS_PRE_PAID_CARD_EDIT } from '../../../../../core/auth/access-points';
import { Component, OnInit, ViewChild } from '@angular/core';
import { CardsSettingsService } from '../../../general-settings/cards-settings/services/cards-setttings.service';

import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignCardService } from './services/campaign-card.service';
import { CampaignStore } from '../../campaign.store';
import { GpFileUploadComponent } from '../../../../../shared/components/gp-fileupload/gp-fileupload.component';
import { AuthStore } from '../../../../../core/auth/auth.store';
import { Item } from '../../../../../shared/models/item';
import { ContaSwapPrepaidCardService } from './services/conta-swap-prepaid-card-.service';

@Component({
    selector: 'campaign-card',
    templateUrl: './campaign-card.component.html'
})
export class CampaignCardSettingsComponent implements OnInit {
    generalSettings: any = this.newGeneralSettings();
    card: any = this.newCampaignCardSettings();
    loadingSave: boolean = false;
    loadingGeneralSettings: boolean = false;
    loadingCampaignCardSettings: boolean = false;
    uploadingWithdrawableImage: boolean = false;
    uploadingNoDrawableImage: boolean = false;
    withdrawableImage: string = '';
    noDrawableImage: string = '';
    loadingProducts: boolean = false;
    loadingBalanceAccounts: boolean = false;

    @ViewChild('alert') alert: GpAlertComponent;
    @ViewChild('withdrawableUploader') withdrawableUploader: GpFileUploadComponent;
    @ViewChild('noDrawableUploader') noDrawableUploader: GpFileUploadComponent;

    payerFees = [
        Item.of('PARTICIPANT_PAYS', 'Cobrar do Participante'),
        Item.of('CLIENT_PAYS', 'Cobrar do Cliente'),
        Item.of('PARTICIPANT_PAYS_IF_REISSUING', 'Cobrar do Participante Apenas na Segunda Via')
    ];

    providers = [
        Item.of('ACESSOCARD', 'Acesso Card'),
        Item.of('CONTA_SWAP', 'Conta SWAP')
    ]

    userValidationTypes = [
        Item.of('ALL', 'Liberar as duas opções para usuário selecionar'),
        Item.of('SMS', 'Apenas SMS'),
        Item.of('EMAIL', 'Apenas E-mail')
    ];

    cardBalanceModes = [
        Item.of('MULTIPLE_CAMPAIGN_BALANCES', 'Múltiplos Saldos na Campanha'),
        Item.of('SINGLE_BALANCE_BY_CAMPAING', 'Saldo Único na Campanha')
    ];

    contaSwapProducts: Array<Item> = new Array<Item>();
    withDrawableCardBalanceAccounts: Array<Item> = new Array<Item>();
    noDrawableCardBalanceAccounts: Array<Item> = new Array<Item>();

    get isContaSwapProvider() {
        return this.card && this.card.provider == 'CONTA_SWAP';
    }

    constructor(
        private generalSettingsService: CardsSettingsService,
        private campaignCardsService: CampaignCardService,
        private campaignStore: CampaignStore,
        private _authStore: AuthStore,
        private _contaSwapPrepaidCardService: ContaSwapPrepaidCardService
    ) { }

    ngOnInit() {
        this.getGeneralCardSettings();
        this.getCampaignCardSettings();
        this.getProducts();
    }

    getGeneralCardSettings() {
        this.loadingGeneralSettings = true;
        this.generalSettingsService.getSettings().subscribe(
            settings => {
                this.generalSettings = settings || this.newGeneralSettings();
                this.loadingGeneralSettings = false;
            },
            err => {
                this.alert.showError(err, true);
                this.loadingGeneralSettings = false;
            }
        );
    }

    getProducts() {
        this.loadingProducts = true;
        this._contaSwapPrepaidCardService.getProducts().subscribe(
            products => {
                if (products) {
                    this.contaSwapProducts = products.map(r => Item.of(r.id, r.description));
                }
                this.loadingProducts = false;
                this.getBalanceAccountsByProductId();
            },
            err => {
                this.alert.showError(err, true);
                this.loadingProducts = false;
            }
        );
    }

    getBalanceAccountsByProductId() {
        this.getNoDrawableCardBalanceAccountsByProductId();
        this.getWithDrawableCardBalanceAccountsByProductId();
    }

    getNoDrawableCardBalanceAccountsByProductId() {
        this.loadingBalanceAccounts = true;
        if (!this.card || !this.card.parametrizations || !this.card.parametrizations.productId) {
            this.noDrawableCardBalanceAccounts = [];
            return
        }
        this._contaSwapPrepaidCardService.getNoDrawableCardBalanceAccountsByProductId(this.card.parametrizations.productId).subscribe(
            balanceAccounts => {
                if (balanceAccounts) {
                    this.noDrawableCardBalanceAccounts = balanceAccounts.map(r => Item.of(r.id, r.description));
                }
                this.loadingBalanceAccounts = false;
            },
            err => {
                this.alert.showError(err, true);
                this.loadingBalanceAccounts = false;
            }
        );
    }

    getWithDrawableCardBalanceAccountsByProductId() {
        this.loadingBalanceAccounts = true;
        if (!this.card || !this.card.parametrizations || !this.card.parametrizations.productId) {
            this.withDrawableCardBalanceAccounts = [];
            return
        }
        this._contaSwapPrepaidCardService.getWithDrawableCardBalanceAccountsByProductId(this.card.parametrizations.productId).subscribe(
            balanceAccounts => {
                if (balanceAccounts) {
                    this.withDrawableCardBalanceAccounts = balanceAccounts.map(r => Item.of(r.id, r.description));
                }
                this.loadingBalanceAccounts = false;
            },
            err => {
                this.alert.showError(err, true);
                this.loadingBalanceAccounts = false;
            }
        );
    }

    getCampaignCardSettings() {
        this.loadingCampaignCardSettings = true;
        this.campaignCardsService.getCardsSettings(this.campaignStore.id).subscribe(
            settings => {
                this.card = settings || this.newCampaignCardSettings();
                this.handleCardSettings();
                this.loadingCampaignCardSettings = false;
            },
            err => {
                this.alert.showError(err, true);
                this.loadingCampaignCardSettings = false;
            }
        );
    }

    private handleCardSettings() {
        if (this.card.withdrawableCard) {
            if (this.card.withdrawableCard.cardDetailedImage)
                this.withdrawableImage = this.card.withdrawableCard.cardDetailedImage + '/600';
        } else {
            this.card.withdrawableCard = {};
        }
        if (this.card.noDrawableCard) {
            if (this.card.noDrawableCard.cardDetailedImage)
                this.noDrawableImage = this.card.noDrawableCard.cardDetailedImage + '/600';
        } else {
            this.card.noDrawableCard = {};
        }
        if (!this.card.parametrizations) {
            this.card.parametrizations = this.newPrepaidCardParametrizations();
        }
    }

    savePrepaidCard() {
        this.loadingSave = true;
        this.campaignCardsService.saveCardsSettings(this.campaignStore.id, this.card).subscribe(
            response => {
                if (!response) {
                    this.loadingSave = false;
                    return this.alert.showError('Ocorreu um erro ao salvar as configurações');
                }

                // upload da imagem do cartão com saque
                if (this.withdrawableUploader)
                    this.uploadWithdrawableImage();

                // upload da imagem do cartão sem saque
                if (this.noDrawableUploader)
                    this.uploadNoDrawableImage();

                // se nenhum upload estiver em andamento, solta mensagem com sucesso
                if (!this.uploadingNoDrawableImage && !this.uploadingWithdrawableImage) {
                    this.alert.showSuccess('Configurações salvas com sucesso');
                }

                this.loadingSave = false;
            },
            err => {
                this.alert.showError(err, true);
                this.loadingSave = false;
            }
        );
    }

    uploadWithdrawableImage() {
        if (this.withdrawableUploader && this.withdrawableUploader.hasSelectedFile()) {
            this.uploadingWithdrawableImage = true;
            this.withdrawableUploader.path = `api/campaigns/${this.campaignStore.id}/cards/prepaid/withdrawable/image`;
            this.withdrawableUploader.onComplete = uploaded => {
                this.uploadingWithdrawableImage = false;
                if (!uploaded) {
                    return this.alert.showError('Ocorreu um erro ao fazer o upload da imagem do cartão com saque');
                } else if (!uploaded.success) {
                    return this.alert.showError(uploaded.errorMessage || 'Ocorreu um erro ao fazer o upload da imagem do cartão com saque');
                }

                if (!this.card.withdrawableCard)
                    this.card.withdrawableCard = {};

                const response = JSON.parse(uploaded.response);
                this.withdrawableImage = `${response.return}/600`;
                this.showSuccessMessage();
                this.withdrawableUploader.createUploader();
            };

            this.withdrawableUploader.uploadFile();
        }
    }

    uploadNoDrawableImage() {
        if (this.noDrawableUploader && this.noDrawableUploader.hasSelectedFile()) {
            this.uploadingNoDrawableImage = true;
            this.noDrawableUploader.path = `api/campaigns/${this.campaignStore.id}/cards/prepaid/nodrawable/image`;
            this.noDrawableUploader.onComplete = uploaded => {
                this.uploadingNoDrawableImage = false;
                if (!uploaded) {
                    return this.alert.showError('Ocorreu um erro ao fazer o upload da imagem do cartão sem saldo');
                } else if (!uploaded.success) {
                    return this.alert.showError(uploaded.errorMessage || 'Ocorreu um erro ao fazer o upload da imagem do cartão sem saldo');
                }

                if (!this.card.noDrawableCard)
                    this.card.noDrawableCard = {};

                const response = JSON.parse(uploaded.response);
                this.noDrawableImage = `${response.return}/600`;
                this.showSuccessMessage();
                this.noDrawableUploader.createUploader();
            };

            this.noDrawableUploader.uploadFile();
        }
    }

    showSuccessMessage() {
        if (!this.uploadingWithdrawableImage && !this.uploadingNoDrawableImage) {
            this.alert.showSuccess('Configurações salvas com sucesso');
        }
    }

    removeWithdrawableImage() {
        this.loadingSave = true;
        this.campaignCardsService.removeWithdrawableImage(this.campaignStore.id).subscribe(
            response => {
                if (response) {
                    this.alert.showSuccess('Imagem removida com sucesso');
                    this.withdrawableImage = '';
                } else {
                    this.alert.showError('Ocorreu um erro ao remover a imagem');
                }
                this.loadingSave = false;
            },
            err => {
                this.loadingSave = false;
                this.alert.showError(err, true);
            }
        );
    }

    removeNoDrawableImage() {
        this.loadingSave = true;
        this.campaignCardsService.removeNoDrawableImage(this.campaignStore.id).subscribe(
            response => {
                if (response) {
                    this.alert.showSuccess('Imagem removida com sucesso');
                    this.noDrawableImage = '';
                } else {
                    this.alert.showError('Ocorreu um erro ao remover a imagem');
                }
                this.loadingSave = false;
            },
            err => {
                this.loadingSave = false;
                this.alert.showError(err, true);
            }
        );
    }

    newGeneralSettings() {
        return {
            withdrawableCard: {},
            noDrawableCard: {}
        };
    }

    newCampaignCardSettings() {
        return {
            enablePrePaidCard: false,
            enableCardWithCashout: false,
            enableCardWithoutCashout: false,
            withdrawableCard: {},
            noDrawableCard: {},
            provider: '',
            parametrizations: this.newPrepaidCardParametrizations()
        };
    }

    private newPrepaidCardParametrizations() {
        return {
            enableMinimumCreditAmountLimit: false,
            minimumCreditAmountLimit: 0,
            disableShippingAddress: false,
            payerFees: 'PARTICIPANT_PAYS'
        }
    }

    get showSpinner(): boolean {
        return this.loadingCampaignCardSettings || this.loadingGeneralSettings;
    }

    get canEditPrePaidCard() {
        return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_PARTNERS_PRE_PAID_CARD_EDIT);
    }
}
