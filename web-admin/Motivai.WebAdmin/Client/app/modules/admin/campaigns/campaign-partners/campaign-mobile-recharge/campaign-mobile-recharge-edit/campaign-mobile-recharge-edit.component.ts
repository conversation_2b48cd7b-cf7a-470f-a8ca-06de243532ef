import { ViewChild } from '@angular/core';
import { Component, Output, EventEmitter, Input } from '@angular/core';

import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignService } from '../../../campaign.service';
import { AuthStore } from '../../../../../../core/auth/auth.store';
import { PERMISSION_CAMPAIGNS_MOBILE_PARTNERS_INTEGRATION, PERMISSION_CAMPAIGNS_MOBILE_PARTNERS_FEES } from '../../../../../../core/auth/access-points';

@Component({
  selector: 'campaign-mobile-recharge-edit',
  templateUrl: 'campaign-mobile-recharge-edit.component.html'
})
export class CampaignMobileRechargeEditComponent {
  @Output('onUpdate') onUpdate: EventEmitter<any> = new EventEmitter<any>();
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;

  canEditIntegrationConfig: boolean = false;
  canEditFees: boolean = false;
  
  private _campaignId: string;
  campaignConversionFactor: number = 1;
  
  partnerConfig: any = {};
  
  loading: boolean = false;
  sending: boolean = false;

  constructor(private _authStore: AuthStore, private _campaignService: CampaignService) { }

  ngOnInit() {
    this.canEditIntegrationConfig = this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_MOBILE_PARTNERS_INTEGRATION);
    this.canEditFees = this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_MOBILE_PARTNERS_FEES);
    
  }
  
  @Input()
  set campaignId(campaignId: string) {
    this._campaignId = campaignId;
  }

  private getCampaign() {
    this._campaignService.getById(this._campaignId)
      .subscribe(campaign => this.campaignConversionFactor = campaign.pointsConversionFactor);
  }

  private handleError(err) {
    this.gpAlert.showError(err || 'Ocorreu um erro ao efetuar a operação.');
    this.loading = false;
    this.sending = false;
  }
  
  resetForm() {
    this.partnerConfig = {};
    this.gpAlert.clear();
  }

  prepareEdit(partnerId: any) {
    this.loading = true;
    this.resetForm();
    if (partnerId) {
      this._campaignService.getMobilePartnerById(this._campaignId, partnerId)
        .subscribe(
          partnerConfig => {
            this.loading = false;
            if (partnerConfig && partnerConfig.id) {
              this.partnerConfig = partnerConfig;
            } else {
              this.partnerConfig = {};
              this.gpAlert.showWarning('Parceiro de recarga não encontrado.');
            }
          },
          err => this.handleError(err)
        );
    }
  }

  onFormSubmit() {
    this.sending = true;
    this.gpAlert.clear();
    this._campaignService.saveMobilePartners(this._campaignId, this.partnerConfig)
      .subscribe(
        partnerConfig => {
          this.sending = false;
          if (partnerConfig && partnerConfig.id) {
            this.gpAlert.showSuccess('Parceiro de recarga salvo com sucesso.');
            this.partnerConfig = partnerConfig;
            this.onUpdate.emit(true);
          } else {
            this.gpAlert.showWarning('Não foi possível salvar o parceiro de recarga, por favor, tente novamente.');
          }
        },
        err => this.handleError(err)
      );
  }
}
