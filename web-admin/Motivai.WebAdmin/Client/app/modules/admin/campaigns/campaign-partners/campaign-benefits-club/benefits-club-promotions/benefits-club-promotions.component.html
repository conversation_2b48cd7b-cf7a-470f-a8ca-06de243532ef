<gp-alert #alert [overlay]="true"></gp-alert>

<div *ngIf="isListing">
	<gp-card [first]="true">
		<div class="row">
			<div class="col-md-12">
				<gp-spinner-button text="Nova Promoção" [actionSecondary]="true" pull="right" [loading]="loading"
					(click)="newPromotion()">
				</gp-spinner-button>
			</div>
		</div>
	</gp-card>
	<gp-card title="Pesquisar Promoção">
		<gp-row>
			<gp-form-col cols="12 6">
				<input type="text" class="form-control" name="name" placeholder="Por Descrição"
					[(ngModel)]="filterParams.name" />
			</gp-form-col>
			<gp-form-col cols="12 6">
				<gp-select name="categories" [items]="activeCategories" placeholder="Selecione uma categoria"
					[(ngModel)]="filterParams.categoryId">
				</gp-select>
			</gp-form-col>
		</gp-row>
		<gp-row>
			<gp-form-col cols="12 6">
				<gp-select name="partner" [items]="partners" placeholder="Selecione um parcerio" [allowClear]="true"
					(change)="onPartnerChange($event)">
				</gp-select>
			</gp-form-col>
			<gp-form-col cols="12 6">
				<gp-select name="active" [items]="activeItems" placeholder="Ativo ou inativo" [(ngModel)]="filterParams.active">
				</gp-select>
			</gp-form-col>
		</gp-row>
		<gp-row>
			<gp-form-col cols="12">
				<gp-spinner-button text="Pesquisar" [search]="true"  pull="right" (click)="getPromotions()" [loading]="loading"
					loadingText="Pesquisando">
				</gp-spinner-button>
			</gp-form-col>
		</gp-row>
	</gp-card>
	<gp-card [last]="true">
		<gp-form-row>
			<gp-form-col cols="12 12">
				<gp-grid #promotionsGrid name="promotions" [rows]="promotions" [loading]="loading"
					[columns]="['Nome', 'Descrição', 'Tipo da promoção', 'Tipo de processo', 'Data de expiração', 'Desconto (%)', 'Posição no Login']"
					[fields]="['name', 'discountDescription', 'promotionTypeFormatted', 'processTypeFormatted', 'formattedExpirationDate', 'discount', 'position']"
					[showActive]="true" [showPagination]="true" [showTotalPages]="false" [showEdit]="true" [pageSize]="40"
					(onPageChanged)="onPageChanged($event)" [showDelete]="false" (onEdit)="edit($event)">
				</gp-grid>
			</gp-form-col>
		</gp-form-row>
	</gp-card>
</div>

<div *ngIf="isShowingRegister">
	<spinner [show]="loading" [overlay]="true"></spinner>
	<gp-card [first]="true" title="Cadastro de Promoções">
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<div>
					<label>Parcerio</label>
				</div>
				<gp-select name="partners" [items]="partners" placeholder="Selecione um Parcerio" [allowClear]="true"
					[(ngModel)]="promotion.partnerId" (change)="onPartnerChange($event)" required>
				</gp-select>
			</gp-form-col>
			<gp-datepicker cols="12 3" label="Data de expiração" name="expirationDate" [(ngModel)]="promotion.expirationDate"
				[required]="false">
			</gp-datepicker>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Posição na Página de Login">
					<select class="form-control" [(ngModel)]="promotion.position">
						<option value="0">Não Exibir</option>
						<option value="1">1ª</option>
						<option value="2">2ª</option>
						<option value="3">3ª</option>
						<option value="4">4ª</option>
						<option value="5">5ª</option>
						<option value="6">6ª</option>
						<option value="7">7ª</option>
						<option value="8">8ª</option>
						<option value="9">9ª</option>
					</select>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 3 3">
				<gp-simple-input label="Tipo de Processo" [required]="true" errorMessage="Tipo de processo é obrigatorio">
					<gp-select name="processType" [items]="processTypeItems" placeholder="Selecione"
						[(ngModel)]="promotion.processType">
					</gp-select>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 3 3">
				<div>
					<label>Ativo:</label>
				</div>
				<gp-switch name="active" [(ngModel)]="promotion.active"></gp-switch>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Nome da Promoção">
					<input type="text" class="form-control" name="name" [(ngModel)]="promotion.name" />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Resumo da Regra">
					<input type="text" class="form-control" name="ruleSummary" [(ngModel)]="promotion.summarisedRules" />
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<div>
					<label>Criado via integração:</label>
				</div>
				<gp-switch name="promotionCreatedByIntegration" [disabled]="true"
					[(ngModel)]="promotion.promotionCreatedByIntegration"></gp-switch>
			</gp-form-col>
			<gp-form-col cols="12 6 6" *ngIf="promotion.promotionCreatedByIntegration">
				<gp-simple-input label="Nome do parceiro de integração">
					<input type="text" name="partnerIntegrationName" readonly class="form-control" placeholder="Nome do parceiro" required
						[(ngModel)]="promotion.partnerIntegrationName" />
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<!-- <gp-form-row *ngIf="isPromotionOnline">
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Status no Parceiro">
					<input type="text" class="form-control" name="ruleSummary" readonly [(ngModel)]="promotion.partnerIntegrationPromotion.formattedStatus" />
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row> -->
	</gp-card>

	<segmentation-parametrization [campaignId]="campaignId" [segmentations]="promotion.segmentations"
		[enableByPersonType]="false" [enableByParticipants]="false">
	</segmentation-parametrization>

	<gp-card title="Configuração do Desconto">
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Tipo de promoção">
					<gp-select name="promotionType" [items]="promotionTypeItems" placeholder="Selecione"
						[(ngModel)]="promotion.promotionType">
					</gp-select>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6">
				<gp-simple-input label="Formato do desconto">
					<gp-select name="discountAmountType" [items]="discountAmountTypeItems" placeholder="Selecione"
						[(ngModel)]="promotion.discountAmountType">
					</gp-select>
				</gp-simple-input>
			</gp-form-col>

			<gp-form-col cols="12 6" *ngIf="isInPercentage">
				<gp-simple-input label="Desconto">
					<div class="input-group">
						<span class="input-group-addon">%</span>
						<gp-input-mask [disabled]="!promotion.discountAmountType" name="discount" [onlyDecimal]="true"
							[(ngModel)]="promotion.discount">
						</gp-input-mask>
					</div>
				</gp-simple-input>
			</gp-form-col>

			<gp-form-col cols="12 6" *ngIf="!isInPercentage">
				<gp-simple-input label="Desconto">
					<div class="input-group">
						<span class="input-group-addon">R$</span>
						<gp-input-mask [disabled]="!promotion.discountAmountType" name="discount" [onlyDecimal]="true"
							[(ngModel)]="promotion.discount">
						</gp-input-mask>
					</div>
				</gp-simple-input>
			</gp-form-col>

		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6">
				<gp-simple-input label="Formato do cashback">
					<gp-select name="cashbackAmountType" [items]="cashbackAmountTypeItems" placeholder="Selecione"
						[(ngModel)]="promotion.cashbackAmountType">
					</gp-select>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6" *ngIf="isInCashbackPercentage">
				<gp-simple-input label="Cashback">
					<div class="input-group">
						<span class="input-group-addon">%</span>
						<gp-input-mask [disabled]="!promotion.cashbackAmountType" name="cashbackAmount" [onlyDecimal]="true"
							[(ngModel)]="promotion.cashbackAmount">
						</gp-input-mask>
					</div>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6" *ngIf="!isInCashbackPercentage">
				<gp-simple-input label="Cashback">
					<div class="input-group">
						<span class="input-group-addon">R$</span>
						<gp-input-mask [disabled]="!promotion.cashbackAmountType" name="cashbackAmount" [onlyDecimal]="true"
							[(ngModel)]="promotion.cashbackAmount">
						</gp-input-mask>
					</div>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 6">
				<div>
					<label>Ativar valores por intervalo</label>
				</div>
				<gp-switch name="active" [(ngModel)]="promotion.enablePriceRange"></gp-switch>
			</gp-form-col>
			<div *ngIf="promotion.enablePriceRange">
				<gp-form-col cols="12 3 3">
					<gp-simple-input label="De:">
						<div class="input-group">
							<span class="input-group-addon">R$</span>
							<gp-input-mask name="oldPrice" [onlyDecimal]="true" [(ngModel)]="promotion.oldPrice">
							</gp-input-mask>
						</div>
					</gp-simple-input>
				</gp-form-col>
				<gp-form-col cols="12 3 3">
					<gp-simple-input label="Por:">
						<div class="input-group">
							<span class="input-group-addon">R$</span>
							<gp-input-mask name="newPrice" [onlyDecimal]="true" [(ngModel)]="promotion.newPrice">
							</gp-input-mask>
						</div>
					</gp-simple-input>
				</gp-form-col>
			</div>
			<gp-form-col cols="12 6">
				<gp-simple-input label="Cupom de desconto">
					<input type="text" class="form-control" name="discountCode" [(ngModel)]="promotion.discountCode" />
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<hr>

		<gp-form-row>
			<gp-form-col cols="12 6">
				<label>Defina a cor de fundo da promoção</label>
				<input class="form-control" [(colorPicker)]="discountCodeBackgroundColor"
					[(ngModel)]="promotion.discountCodeBackgroundColor" cpOutputFormat="hex"
					[style.background]="promotion.discountCodeBackgroundColor" outputColor="#000">
			</gp-form-col>
			<gp-form-col cols="12 6">
				<gp-simple-input label="Descrição do desconto">
					<input type="text" class="form-control" name="discountDescription"
						[(ngModel)]="promotion.discountDescription" />
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-simple-input label="URL da Promoção">
					<input type="text" class="form-control" [readonly]="isPromotionOnline" name="romotionPartnerUrl"
						[(ngModel)]="promotion.promotionPartnerUrl" />
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card title="Regras de Utilização">
		<gp-editor id="content" name="content" [(ngModel)]="promotion.utilizationRules">
		</gp-editor>
	</gp-card>

	<gp-card title="Imagem conceito">
		<gp-form-row>
			<gp-form-col cols="12 6">
				<div class="form-group">
					<gp-fileupload-default-folder name="conceptImageUrl" [onlyImages]="true" [showRender]="true"
						[skipImageUrlWidth]="true" [campaignId]="campaignId" (error)="onUploadError($event)" imageWidth="525"
						renderWidth="400" [(ngModel)]="promotion.conceptImageUrl">
					</gp-fileupload-default-folder>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 6">
				<label>Remova a imagem</label>
				<gp-spinner-button style="display: flex;" type="button" [pink]="true" icon="trash" text="Remover a imagem"
					marginRight="5px" (click)="removeConceptImageUrl()">
				</gp-spinner-button>
			</gp-form-col>
			<gp-form-col cols="12 6">
				<div class="format-preview" [style.background-color]="discountCodeBackgroundColor">
					<div class="format-preview-text-two">
						{{promotion.name}}
					</div>
					<div class="format-preview-two">
						<div class="format-preview-text-three">
							CUPOM DE DESCONTO
						</div>
					</div>
					<div class="format-preview-three">
						<div class="format-preview-text-four">
							<span class="big-text-one">{{formattedDiscountValue}}</span> Off
						</div>
						<div class="format-preview-text-five">
							+ {{formattedCashbackValue}} cash<span class="big-text-two">back</span>
						</div>
					</div>
					<div class="format-preview-four">
						<div class="format-preview-text">
							use o cupom: <span class="big-text"> {{promotion.discountCode}}</span>
						</div>
					</div>
					<div class="format-preview-image">
						<img [src]="promotion.conceptImageUrl + '/512'" alt="">
					</div>
				</div>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card title="Imagem conceito fixa">
		<gp-form-row>
			<gp-form-col cols="12 6">
				<div class="form-group">
					<gp-fileupload-default-folder inputName="fixedConceptImageUrl" [uploadPath]="fixedConceptImageUploadPath"
						[onlyImages]="true" [uploadPath]="" [showRender]="true" [skipImageUrlWidth]="true" [campaignId]="campaignId"
						(error)="onUploadError($event)" imageWidth="525" renderWidth="400"
						[(ngModel)]="promotion.fixedConceptImageUrl">
					</gp-fileupload-default-folder>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 6">
				<label>Remova a imagem</label>
				<gp-spinner-button style="display: flex;" type="button" [pink]="true" icon="trash" text="Remover a imagem"
					marginRight="5px" (click)="removeFixedConceptImageUrl()">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card title="Categorias da promoção">
		<gp-form-row>
			<gp-form-col cols="12 8 8">
				<gp-select name="categories" styleClass="font-awesome" [items]="activeCategories"
					placeholder="Selecione uma categoria" [allowClear]="true" [(ngModel)]="selectedCategoryId">
				</gp-select>
			</gp-form-col>
			<gp-fomr-col cols="12 4 4">
				<gp-spinner-button type="button" [pink]="true" text="Vincular" icon="plus" (click)="linkCategory()"
					[loading]="loading">
				</gp-spinner-button>
			</gp-fomr-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-grid name="grid" [rows]="linkedCategories" [columns]="['Categorias']" [fields]="['value']"
					[showActive]="false" [showPagination]="false" [showDelete]="true" [showEdit]="false"
					(onDelete)="unLinkCategory($event)">
				</gp-grid>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card title="Regulamento da Promoção">
		<gp-dynamic-contents-editor [dynamicContents]="promotion.regulaments">
		</gp-dynamic-contents-editor>
	</gp-card>

	<gp-card>
		<gp-form-row>
			<gp-form-col cols="12 12 12" [inputGroup]="false">
				<gp-spinner-button type="button" [actionPrimary]="true" text="Salvar Promoção" marginRight="5px" pull="right"
					(click)="prepareToSave()" [loading]="loading" [disabled]="disableSaveButton" loadingText="Processando">
				</gp-spinner-button>

				<gp-spinner-button type="button" [actionClear]="true" text="Limpar" marginRight="5px" pull="right"
					(click)="clear()">
				</gp-spinner-button>

				<gp-spinner-button type="button" [actionBack]="true" text="Voltar" marginRight="5px" pull="right"
					(click)="back()">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</gp-card>
</div>
