import { Component, ViewChild } from '@angular/core';
import { TabsetComponent, TabDirective } from 'ng2-bootstrap';

import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { BenefitsClubPromotionsComponent } from '../benefits-club-promotions/benefits-club-promotions.component';
import { BenefitsClubPartnersComponent } from '../benefits-club-partners/benefits-club-partners.component';
import { BenefitsClubCategoriesComponent } from '../benefits-club-categories/benefits-club-categories.component';

@Component({
    selector: 'benefits-club-view',
    templateUrl: './benefits-club-view.component.html'
})
export class BenefitsClubViewComponent {
    partnerId: string = '';

    @ViewChild('tabset') tabs: TabsetComponent;
    @ViewChild('partnerEditor') partnerEditor: BenefitsClubPartnersComponent;
    @ViewChild('partnerCategory') partnerCategory: BenefitsClubCategoriesComponent;
    @ViewChild('partnerPromotion') partnerPromotion: BenefitsClubPromotionsComponent;
    @ViewChild('alert') alert: GpAlertComponent;

    onEdit($event) {
        this.partnerId = $event.id;
        this.tabs.tabs[1].active = true;
    }

    refresh(event: any) {
        if (event instanceof TabDirective) {
            this.partnerCategory.newCategory = false;
            this.partnerCategory.getCategories();
            this.partnerCategory.partnerCategory = [];
        }
    }

    refreshPromotions(event: any) {
        if (event instanceof TabDirective) {
            this.partnerPromotion.skip = 0;
            this.partnerPromotion.limit = 40;
            this.partnerPromotion.showNewPromotion = false;
            this.partnerPromotion.getPartners();
            this.partnerPromotion.getCategories();
            this.partnerPromotion.getPromotions();
        }
    }

    refreshPartner(event: any) {
        if (event instanceof TabDirective) {
            this.partnerEditor.newPartner = false;
            this.partnerEditor.getCategories();
            this.partnerEditor.getPartners();
        }
    }
}
