import { Component, ViewChild } from "@angular/core";
import { TabsetComponent } from "ng2-bootstrap";
import { CampaignPaymentMethodsEditComponent } from "./campaign-payment-methods-edit/campaign-payment-methods-edit.component";
import { CampaignPaymentMethodsListComponent } from "./campaign-payment-methods-list/campaign-payment-methods-list.component";
import { GpAlertComponent } from "../../../../../shared/components/gp-alert/gp-alert/gp-alert.component";


@Component({
	selector: 'campaign-payment-methods-view',
	templateUrl: './campaign-payment-methods-view.component.html'
})
export class CampaignPaymentMethodsViewComponent {
	@ViewChild('tabset') tabs: TabsetComponent;
	@ViewChild('alert') alert: GpAlertComponent;
	@ViewChild('paymentMethodEditor') paymentMethodEditor: CampaignPaymentMethodsEditComponent;
	@ViewChild('paymentMethodList') paymentMethodList: CampaignPaymentMethodsListComponent;

	onEdit($event) {
		this.tabs.tabs[0].active = false;
		this.paymentMethodEditor.prepareToEdit($event);
		this.tabs.tabs[1].active = true;
	}

	refresh() {
		this.clear();
		this.paymentMethodList.getCampaignPaymentMethods();
	}

	clear() {
		this.paymentMethodEditor.clear();
		this.tabs.tabs[1].active = false;
	}
}
