import { Component, ViewChild } from '@angular/core';
import { CampaignDynamicOrderParametrizationEditComponent } from '../campaign-dynamic-order-parametrizations-edit/campaign-dynamic-order-parametrizations-edit.component';
import { CampaignDynamicOrderParametrizationListComponent as CampaignDynamicOrderParametrizationListComponent } from '../campaign-dynamic-order-parametrizations-list/campaign-dynamic-order-parametrizations-list.component';
import { CampaignStore } from '../../../../../campaign.store';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { TabsetComponent } from 'ng2-bootstrap';
import { GpAlertComponent } from '../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';

@Component({
  selector: 'campaign-dynamic-order-parametrizations-view',
  templateUrl: 'campaign-dynamic-order-parametrizations-view.component.html'
})
export class CampaignDynamicOrderParametrizationViewComponent {
  private _campaign$: Subscription;

  @ViewChild('listComponent') listComponent: CampaignDynamicOrderParametrizationListComponent;
  @ViewChild('editComponent') editComponent: CampaignDynamicOrderParametrizationEditComponent;
  @ViewChild('tabs') tabs: TabsetComponent;
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;

  campaignId: string;
  loading: boolean = false;

  constructor(private route: ActivatedRoute, private router: Router, private _campaignStore: CampaignStore) {}

  ngOnInit() {
    this._campaign$ = this._campaignStore.asObservable.subscribe(campaignId => {
      this.campaignId = campaignId;
    });
  }

  onSelect(row: any) {
    if (!row.id) {
      return;
    }

    this.tabs.tabs[1].disabled = false;
    this.tabs.tabs[1].active = true;
    this.editComponent.showDetails(row);
  }

  clearEditForm() {
    this.editComponent.clear();
    this.tabs.tabs[1].disabled = false;
    this.listComponent.loadDynamicOrdersParametrizations();
  }
}
