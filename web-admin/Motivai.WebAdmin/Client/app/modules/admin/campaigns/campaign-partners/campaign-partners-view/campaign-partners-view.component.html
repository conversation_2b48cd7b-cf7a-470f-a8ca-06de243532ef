<gp-card [noPaddingTop]="true" [noPaddingBottom]="true" *ngIf="showBlockMenu">
	<div class="row">
		<accordion [closeOthers]="true">
			<accordion-group panelClass="b0 mb-sm panel-default no-shadow" [isOpen]="true">
				<div accordion-heading>
					<label><em class="fa fa-navicon"></em> Features de Parceiros</label>
				</div>
				<div class="row menublock text-center">
					<gp-menublock-item size="xs" icon="industry" color="text-success" text="Produtos" routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'parceiros', 'produtos']" *ngIf="canViewProductsPartners"></gp-menublock-item>

					<gp-menublock-item size="xs" icon="credit-card" color="text-success" text="Compra de Pontos" routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'parceiros', 'comprapontos']" *ngIf="canViewGateways"></gp-menublock-item>

					<gp-menublock-item size="xs" icon="mobile" color="text-success" text="Recarga de Celular" routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'parceiros', 'recargacelular']" *ngIf="canViewMobilePartners"></gp-menublock-item>

					<gp-menublock-item size="xs" icon="barcode" color="text-success" text="Pague Contas" routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'parceiros', 'paguecontas']" *ngIf="canViewBillPaymentPartners"></gp-menublock-item>

					<gp-menublock-item size="xs" icon="credit-card" color="text-success" text="Cartão Pré-Pago" routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'parceiros', 'cartao-pre-pago']" *ngIf="canViewPrePaidCard"></gp-menublock-item>

					<gp-menublock-item size="xs" icon="retweet" color="text-success" text="Cashback" routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'parceiros', 'cashback']" *ngIf="canViewBankTransfers"></gp-menublock-item>

					<gp-menublock-item size="xs" icon="money" color="text-success" text="Transferência Bancária" routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'parceiros', 'transferencias-bancarias']" *ngIf="canViewBankTransfers"></gp-menublock-item>

					<gp-menublock-item size="xs" icon="exchange" color="text-success" text="Transferência P2P" routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'parceiros', 'transferencias-p2p']" *ngIf="canViewP2pTransfers"></gp-menublock-item>

					<gp-menublock-item size="xs" icon="gift" color="text-success" text="Clube de Benefícios" routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'parceiros', 'clube-beneficios']" *ngIf="canViewInstitutionalPartners"></gp-menublock-item>

					<gp-menublock-item size="xs" icon="cogs" color="text-success" text="Resgate Dinâmico" routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'parceiros', 'resgate-dinamico']" *ngIf="canViewInstitutionalPartners"></gp-menublock-item>

					<gp-menublock-item size="xs" icon="money" color="text-success" text="Métodos de Pagamento" routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'parceiros', 'metodos-pagamento']" *ngIf="canViewPaymentMethods"></gp-menublock-item>
				</div>
			</accordion-group>
		</accordion>
	</div>
</gp-card>
<router-outlet></router-outlet>
