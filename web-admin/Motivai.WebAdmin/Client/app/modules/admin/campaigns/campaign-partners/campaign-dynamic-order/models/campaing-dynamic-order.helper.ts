import { FormatHelper } from '../../../../../../shared/formatters/format-helper';

const STATUS = {
  RECEIVED: 'Recebido',
  PROCESSING: 'Processamento',
  COMPLETED: 'Processado',
  PROCESSED_WITH_ERROR: 'Processado com erro',
  REFUNDED: 'Estornado',
  CANCELED: 'Cancelado'
};

export function handlerOrder(order: any): void {
  if (order) {
    order.requestDate = FormatHelper.formatDateWithTime(order.requestDate);
    order.createDate = FormatHelper.formatDateWithTime(order.createDate);
    order.errorOccurred = order.errorOccurred ? 'Sim' : 'Não';
    order.updateDate = FormatHelper.formatDateWithTime(order.updateDate);
    order.totalAmount = order.totalAmount.currency;
    order.status = STATUS[order.status];
    order.totalFeeAmount = order.totalFeeAmount;
    if (order.debitTransactionReceipt) {
      order.debitTransactionDate = FormatHelper.formatDateWithTime(order.debitTransactionReceipt.transactionDate);
    }
  }
}

export function handlerOrders(orders: any[]): any[] {
  return orders.map(order => ({
    ...order,
    status: STATUS[order.status],
    requestDate: FormatHelper.formatDateWithTime(order.requestDate),
    userDocument: FormatHelper.formatDocument(order.userDocument),
    totalAmount: FormatHelper.formatCurrency(order.totalAmount.currency)
  }));
}
