<form>
  <gp-card title="Configuração de Transferência P2P">
    <gp-alert [overlay]="true" #gpAlert></gp-alert>
    <spinner [overlay]="true" [show]="loading"></spinner>

    <gp-form-row>
      <gp-form-col cols="12 6">
        <gp-simple-input label="Ativo" [required]="false">
          <div>
            <gp-switch name="active" [(ngModel)]="transferConfig.active"></gp-switch>
          </div>
        </gp-simple-input>
      </gp-form-col>
    </gp-form-row>

    <gp-form-row>
      <gp-form-col cols="12 6">
        <gp-simple-input label="Beneficiários permitidos" [required]="true">
          <gp-select name="recipientAllowed" [multiple]="false" placeholder="Selecione"
            [items]="recipientAllowedItems" [(ngModel)]="transferConfig.recipientAllowed" [required]="true">
          </gp-select>
        </gp-simple-input>
      </gp-form-col>

      <gp-form-col cols="12 6">
        <gp-simple-input label="Permite selecionar participante de origem" [required]="true">
          <div>
            <gp-switch name="active" [(ngModel)]="transferConfig.allowSelectRelatedAccountAsSource"></gp-switch>
          </div>
        </gp-simple-input>
      </gp-form-col>
    </gp-form-row>
  </gp-card>
</form>

<gp-card [last]="true">
  <gp-form-row>
    <gp-form-col cols="12" [inputGroup]="false">
      <gp-spinner-button type="submit" [pink]="true" text="Salvar" pull="right" icon="send"
        [loading]="sendingFlag" loadingText="Aguarde" (click)="save()">
      </gp-spinner-button>
    </gp-form-col>
  </gp-form-row>
</gp-card>
