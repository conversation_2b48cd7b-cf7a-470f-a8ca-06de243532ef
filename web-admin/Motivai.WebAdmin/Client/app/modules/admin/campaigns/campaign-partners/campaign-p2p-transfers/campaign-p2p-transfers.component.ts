import { Component, OnInit, <PERSON><PERSON>hild, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { Subscription } from 'rxjs/Subscription';

import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { RxjsHelpers } from '../../../../../shared/helpers/rxjs-helpers';
import { CampaignPartnersService } from '../campaign-partners.service';
import { CampaignStore } from '../../campaign.store';
import { Item } from '../../../../../shared/models/item';

@Component({
  selector: 'campaign-p2p-transfers',
  templateUrl: './campaign-p2p-transfers.component.html'
})
export class CampaignP2pTransfersComponent implements OnInit, OnDestroy {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;

  transferConfig: any = {
    recipientAllowed: 'ANY'
  };

  loading: boolean = false;
  sendingFlag: boolean = false;

  recipientAllowedItems: Array<Item> = [
    Item.of('ANY', 'Qualquer participante'),
    Item.of('SAME_PARENT', 'Participante mesma hierarquia')
  ];

  private campaign$: Subscription;

  constructor(private _campaignStore: CampaignStore, private _partnerService: CampaignPartnersService) { }

  ngOnInit() {
    this.campaign$ = this._campaignStore.asObservable
      .subscribe(_ => this.loadTransferConfig());
  }

  ngOnDestroy() {
    RxjsHelpers.unsubscribe(this.campaign$);
  }

  private loadTransferConfig() {
    this.loading = true;
    this._partnerService.getP2pTransferConfiguration(this._campaignStore.id)
      .subscribe(
        config => {
          this.loading = false;
          this.transferConfig = config || {};
        },
        err => {
          this.loading = false;
          this.gpAlert.handleAndShowError(err);
        }
      );
  }

  save() {
    this.sendingFlag = true;
    this._partnerService.saveP2pTransferConfiguration(this._campaignStore.id, this.transferConfig)
      .subscribe(
        result => {
          this.sendingFlag = false;
          if (result) {
            this.gpAlert.showSuccess('Configurações salvas com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível salvar as configurações, por favor, tente novamente.');
          }
        },
        err => {
          this.sendingFlag = false;
          this.gpAlert.handleAndShowError(err);
        }
      );
  }
}
