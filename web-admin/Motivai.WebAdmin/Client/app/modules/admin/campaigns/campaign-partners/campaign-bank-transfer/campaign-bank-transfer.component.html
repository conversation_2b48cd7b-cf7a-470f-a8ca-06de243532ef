<spinner [overlay]="true" [show]="loading"></spinner>
<gp-card title="Configurações do Transferência Bancária">
	<gp-form-row>
		<gp-form-col cols="12 6 6">
			<div class="form-group">
				<label>Habilitar transferência</label>
				<div>
					<gp-switch [(ngModel)]="bankTransfer.enableBankTransfer"></gp-switch>
				</div>
			</div>
		</gp-form-col>
	</gp-form-row>

	<gp-form-row>
		<gp-form-col cols="12 6">
			<gp-simple-input label="Forma de transferência permitida" [required]="true"
					tooltip="Ambos: o participante poderá escolher qual forma de transferência deseja receber.">
				<gp-select name="transferMethodAllowed" [multiple]="false" placeholder="Selecione"
					[items]="transferMethods" [(ngModel)]="bankTransfer.transferMethodAllowed" [required]="true">
				</gp-select>
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>

	<gp-form-row>
		<gp-form-col cols="12 6">
			<div>
				<label>Habilitar valor mínimo para transferência</label>
				<div>
					<gp-switch name="enableMinimumTransferAmountLimit" [(ngModel)]="bankTransfer.enableMinimumTransferAmountLimit">
					</gp-switch>
				</div>
			</div>
		</gp-form-col>
		<gp-form-col cols="12 6">
			<gp-simple-input label="Valor mínimo para transferência" [required]="bankTransfer.enableMinimumTransferAmountLimit">
				<div class="input-group">
					<span class="input-group-addon">R$</span>
					<gp-input-mask name="minimumTransferAmount" [(ngModel)]="bankTransfer.minimumTransferAmount"
						[onlyDecimal]="true" placeholder="0,00" [required]="bankTransfer.enableMinimumTransferAmountLimit">
					</gp-input-mask>
				</div>
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>

	<gp-form-row>
		<gp-form-col cols="12 6 6">
			<gp-simple-input label="Limite de Transferência" [required]="true">
				<div class="input-group">
					<span class="input-group-addon">%</span>
					<gp-input-mask name="transferLimit" [onlyDecimal]="true" [(ngModel)]="bankTransfer.transferLimit" required></gp-input-mask>
				</div>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 6 6">
			<div class="form-group">
				<label>Valor máximo</label>
				<div class="input-group">
					<span class="input-group-addon">%</span>
					<gp-input-mask name="minTransferLimit" [onlyDecimal]="true" [ngModel]="generalConfig.transferLimit" [disabled]="true"></gp-input-mask>
				</div>
			</div>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card title="Taxas">
	<gp-form-row>
		<gp-form-col cols="12 6 6">
			<gp-simple-input label="Custo da TED" [required]="true">
				<div class="input-group">
					<span class="input-group-addon">R$</span>
					<gp-input-mask name="bankTransferFee" [onlyDecimal]="true" [(ngModel)]="bankTransfer.bankTransferFee" required></gp-input-mask>
				</div>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 6 6">
			<div class="form-group">
				<label>Valor mínimo</label>
				<div class="input-group">
					<span class="input-group-addon">R$</span>
					<gp-input-mask name="minBankTransferFee" [onlyDecimal]="true" [ngModel]="generalConfig.bankTransferFee" [disabled]="true"></gp-input-mask>
				</div>
			</div>
		</gp-form-col>
	</gp-form-row>

	<gp-form-row>
		<gp-form-col cols="12 6 6">
			<gp-simple-input label="Custo do PIX" [required]="true">
				<div class="input-group">
					<span class="input-group-addon">R$</span>
					<gp-input-mask name="bankTransferPixFee" [onlyDecimal]="true" [(ngModel)]="bankTransfer.bankTransferPixFee" required>
					</gp-input-mask>
				</div>
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>

	<gp-form-row>
		<gp-form-col cols="12 6 6">
			<gp-simple-input label="Taxa Adicional" [required]="true">
				<div class="input-group">
					<span class="input-group-addon">%</span>
					<gp-input-mask name="additionalFee" [onlyDecimal]="true" [(ngModel)]="bankTransfer.additionalFee" required></gp-input-mask>
				</div>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 6 6">
			<div class="form-group">
				<label>Valor mínimo</label>
				<div class="input-group">
					<span class="input-group-addon">%</span>
					<gp-input-mask name="minAdditionalFee" [onlyDecimal]="true" [ngModel]="generalConfig.additionalFee" [disabled]="true"></gp-input-mask>
				</div>
			</div>
		</gp-form-col>
	</gp-form-row>

	<gp-form-row>
		<gp-form-col cols="12 6 6">
			<gp-simple-input label="Imposto" [required]="true">
				<div class="input-group">
					<span class="input-group-addon">%</span>
					<gp-input-mask name="governmentFee" [onlyDecimal]="true" [(ngModel)]="bankTransfer.governmentFee" required></gp-input-mask>
				</div>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 6 6">
			<div class="form-group">
				<label>Valor mínimo</label>
				<div class="input-group">
					<span class="input-group-addon">%</span>
					<gp-input-mask name="minGovernmentFee" [onlyDecimal]="true" [ngModel]="generalConfig.governmentFee" [disabled]="true"></gp-input-mask>
				</div>
			</div>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card title="Regulamento">
	<gp-form-row>
		<gp-form-col cols="12">
			<gp-editor id="regulation" name="regulation" [(ngModel)]="bankTransfer.regulation"></gp-editor>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card [last]="true">
	<div class="row">
		<div class="col-md-12">
			<gp-spinner-button type="button" [pink]="true" text="Salvar" icon="send" pull="right"
				(click)="saveConfig()" [loading]="sending"></gp-spinner-button>
		</div>
	</div>
</gp-card>
<gp-alert [overlay]="true" #gpAlert></gp-alert>
