<gp-card title="Cartões pré-pago">
	<gp-form-row>
		<gp-form-col cols="12 3 3">
			<div class="form-group">
				<label>Habilitar cartão pré-pago</label>
				<div>
					<gp-switch [(ngModel)]="card.enablePrepaidCard"></gp-switch>
				</div>
			</div>
		</gp-form-col>
		<gp-form-col cols="12 3 3">
			<div class="form-group">
				<label>Habilitar cartão com saque</label>
				<div>
					<gp-switch [(ngModel)]="card.enableWithdrawableCard"></gp-switch>
				</div>
			</div>
		</gp-form-col>
		<gp-form-col cols="12 3 3">
			<div class="form-group">
				<label>Habilitar cartão sem saque</label>
				<div>
					<gp-switch [(ngModel)]="card.enableNoDrawableCard"></gp-switch>
				</div>
			</div>
		</gp-form-col>
		<gp-form-col cols="12 3 3">
			<label>Parceiros</label>
			<gp-select name="parceiro" [(ngModel)]="card.provider" [items]="providers" placeholder="Selecione">
			</gp-select>
		</gp-form-col>
	</gp-form-row>
	<gp-alert #alert [overlay]="true"></gp-alert>
	<spinner [overlay]="true" [show]="showSpinner"></spinner>
</gp-card>

<gp-card title="Parametrizações">
	<gp-form-row>
		<gp-form-col cols="12 6 3">
			<label>Habilitar valor mínimo de transferência</label>
			<div>
				<gp-switch [(ngModel)]="card.parametrizations.enableMinimumCreditAmountLimit"></gp-switch>
			</div>
		</gp-form-col>

		<gp-form-col cols="12 6 3" *ngIf="card.parametrizations.enableMinimumCreditAmountLimit">
			<gp-simple-input label="Valor mínimo para transferência" [required]="true">
				<div class="input-group">
					<span class="input-group-addon">R$</span>
					<gp-input-mask name="enableMinimumCreditAmountLimit" [onlyDecimal]="true"
						[(ngModel)]="card.parametrizations.minimumCreditAmountLimit">
					</gp-input-mask>
				</div>
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>

	<hr />

	<gp-form-row>
		<gp-form-col cols="12 12 6">
			<label>Desabilitar endereço de entrega</label>
			<div>
				<gp-switch name="disableShippingAddress" [(ngModel)]="card.parametrizations.disableShippingAddress">
				</gp-switch>
			</div>
		</gp-form-col>
	</gp-form-row>

	<hr />

	<gp-form-row>
		<gp-form-col cols="12 12 4">
			<gp-simple-input label="Pagamento das taxas">
				<gp-tooltip message="Taxas de emissão: todos os custos e frete.
					Carga e Recarga - Taxas adicionais: taxa adicional.">(?)</gp-tooltip>
				<gp-select name="payerFees" [multiple]="false" placeholder="Selecione" [items]="payerFees"
					[(ngModel)]="card.parametrizations.payerFees" [required]="true">
				</gp-select>
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>

	<div *ngIf="isContaSwapProvider">
		<hr />
		<h5>Cartão Conta SWAP</h5>
		<gp-form-row>
			<gp-form-col cols="12 6">
				<gp-simple-input label="Produto">
					<gp-tooltip message="Produto: produto com as regras base utilizado como template criar o cartão.">
						(?)</gp-tooltip>
					<gp-select name="product" [multiple]="false" placeholder="Selecione" [items]="contaSwapProducts"
						[(ngModel)]="card.parametrizations.productId" [required]="true"
						(change)="getBalanceAccountsByProductId()">
					</gp-select>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6">
				<gp-simple-input label="Modalidade de Saldo">
					<gp-tooltip
						message="Modalidade de Saldo: define se cada cartão emitido tem um saldo único ou se é compartilhado com os outros cartões.">
						(?)</gp-tooltip>
					<gp-select name="product" [multiple]="false" placeholder="Selecione" [items]="cardBalanceModes"
						[(ngModel)]="card.parametrizations.cardBalanceMode" [required]="true"
						(change)="getBalanceAccountsByProductId()">
					</gp-select>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 12 6">
				<label>Desabilitar acesso ao cartão e seus detalhes</label>
				<div>
					<gp-switch name="disableCardDetailsAccess"
						[(ngModel)]="card.parametrizations.disableCardDetailsAccess">
					</gp-switch>
				</div>
			</gp-form-col>

			<gp-form-col cols="12 12 6" *ngIf="!card.parametrizations.disableCardDetailsAccess">
				<label>Desabilitar extrato</label>
				<div>
					<gp-switch name="disableCardStatement" [(ngModel)]="card.parametrizations.disableCardStatement">
					</gp-switch>
				</div>
			</gp-form-col>

		</gp-form-row>

		<gp-form-row *ngIf="!card.parametrizations.disableCardDetailsAccess">
			<gp-form-col cols="12 12 6">
				<label>Desabilitar bloqueio/ativação do cartão</label>
				<div>
					<gp-switch name="disableUpdateCardStatus"
						[(ngModel)]="card.parametrizations.disableUpdateCardStatus">
					</gp-switch>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 12 6">
				<label>Habilitar trazer todos os cartões (de todas as campanhas)</label>
				<div>
					<gp-switch name="enableShowCardsFromAllCampaigns"
						[(ngModel)]="card.parametrizations.enableShowCardsFromAllCampaigns">
					</gp-switch>
				</div>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row *ngIf="!card.parametrizations.disableCardDetailsAccess">
			<gp-form-col cols="12 12 6">
				<div>
					<label>Forma de confirmação de usuário</label>
					<gp-select name="userValidationType" [multiple]="false" placeholder="Selecione"
						[items]="userValidationTypes" [(ngModel)]="card.parametrizations.userValidationType"
						[required]="true">
					</gp-select>
				</div>
			</gp-form-col>
		</gp-form-row>
	</div>
</gp-card>

<tabset #tabs class="bg-white p0 tab-no-border" [justified]="true">
	<tab>
		<ng-template tabHeading>Cartão c/ saque</ng-template>
		<gp-card [first]="true">
			<gp-form-row *ngIf="isContaSwapProvider">
				<gp-form-col cols="12 12 12">
					<gp-card title="Cartão Conta Swap">
						<div>
							<gp-form-row>
								<gp-form-col cols="12 6">
									<gp-simple-input label="Tipo de Saldo">
										<gp-tooltip message="Tipo de Saldo: limite de saldo que o cartão terá.">
											(?)</gp-tooltip>
										<gp-select name="balanceAccount-withdrawableCard" [multiple]="false"
											placeholder="Selecione" [items]="withDrawableCardBalanceAccounts"
											[(ngModel)]="card.withdrawableCard.balanceAccountId" [required]="true">
										</gp-select>
									</gp-simple-input>
								</gp-form-col>
							</gp-form-row>
						</div>
					</gp-card>
				</gp-form-col>
			</gp-form-row>

			<gp-form-row>
				<gp-form-col cols="12 12 12">
					<gp-card title="Taxas de emissão">
						<gp-form-row>
							<gp-form-col cols="12 6 6">
								<gp-simple-input label="Custo do plástico" [required]="true">
									<div class="input-group">
										<span class="input-group-addon">R$</span>
										<gp-input-mask name="plasticCost" [onlyDecimal]="true"
											[(ngModel)]="card.withdrawableCard.plasticCost" required></gp-input-mask>
									</div>
								</gp-simple-input>
							</gp-form-col>
							<gp-form-col cols="12 6 6">
								<gp-simple-input label="Custo mínimo" [required]="true">
									<div class="input-group">
										<span class="input-group-addon">R$</span>
										<gp-input-mask name="generalPlasticCost" [onlyDecimal]="true"
											[(ngModel)]="generalSettings.withdrawableCard.plasticCost"
											[disabled]="true"></gp-input-mask>
									</div>
								</gp-simple-input>
							</gp-form-col>
						</gp-form-row>
						<gp-form-row>
							<gp-form-col cols="12 6 6">
								<gp-simple-input label="Frete" tooltip="" [required]="true">
									<div class="input-group">
										<span class="input-group-addon">R$</span>
										<gp-input-mask name="plasticCost" [onlyDecimal]="true"
											[(ngModel)]="card.withdrawableCard.shippingCost" required></gp-input-mask>
									</div>
								</gp-simple-input>
							</gp-form-col>
							<gp-form-col cols="12 6 6">
								<gp-simple-input label="Custo mínimo" [required]="true">
									<div class="input-group">
										<span class="input-group-addon">R$</span>
										<gp-input-mask name="generalPlasticCost" [onlyDecimal]="true"
											[(ngModel)]="generalSettings.withdrawableCard.shippingCost"
											[disabled]="true"></gp-input-mask>
									</div>
								</gp-simple-input>
							</gp-form-col>
						</gp-form-row>
						<gp-form-row>
							<gp-form-col cols="12 6 6">
								<gp-simple-input label="Custo de 2ª via" [required]="true">
									<div class="input-group">
										<span class="input-group-addon">R$</span>
										<gp-input-mask name="plasticCost" [onlyDecimal]="true"
											[(ngModel)]="card.withdrawableCard.reissueCost" required></gp-input-mask>
									</div>
								</gp-simple-input>
							</gp-form-col>
							<gp-form-col cols="12 6 6">
								<gp-simple-input label="Custo mínimo" [required]="true">
									<div class="input-group">
										<span class="input-group-addon">R$</span>
										<gp-input-mask name="generalPlasticCost" [onlyDecimal]="true"
											[(ngModel)]="generalSettings.withdrawableCard.reissueCost"
											[disabled]="true"></gp-input-mask>
									</div>
								</gp-simple-input>
							</gp-form-col>
						</gp-form-row>
					</gp-card>
				</gp-form-col>
			</gp-form-row>
			<gp-form-row>
				<gp-form-col cols="12 12 12">
					<gp-card title="Carga/Recarga">
						<gp-form-row>
							<gp-form-col cols="12 6 6">
								<gp-simple-input label="Custo de carga" [required]="true">
									<div class="input-group">
										<span class="input-group-addon">R$</span>
										<gp-input-mask name="plasticCost" [onlyDecimal]="true"
											[(ngModel)]="card.withdrawableCard.firstChargeFee" required></gp-input-mask>
									</div>
								</gp-simple-input>
							</gp-form-col>
							<gp-form-col cols="12 6 6">
								<gp-simple-input label="Custo mínimo" [required]="true">
									<div class="input-group">
										<span class="input-group-addon">R$</span>
										<gp-input-mask name="generalPlasticCost" [onlyDecimal]="true"
											[(ngModel)]="generalSettings.withdrawableCard.firstChargeFee"
											[disabled]="true"></gp-input-mask>
									</div>
								</gp-simple-input>
							</gp-form-col>
						</gp-form-row>
						<gp-form-row>
							<gp-form-col cols="12 6 6">
								<gp-simple-input label="Custo de recarga" [required]="true">
									<div class="input-group">
										<span class="input-group-addon">R$</span>
										<gp-input-mask name="plasticCost" [onlyDecimal]="true"
											[(ngModel)]="card.withdrawableCard.rechargeFee" required></gp-input-mask>
									</div>
								</gp-simple-input>
							</gp-form-col>
							<gp-form-col cols="12 6 6">
								<gp-simple-input label="Custo mínimo" [required]="true">
									<div class="input-group">
										<span class="input-group-addon">R$</span>
										<gp-input-mask name="generalPlasticCost" [onlyDecimal]="true"
											[(ngModel)]="generalSettings.withdrawableCard.rechargeFee"
											[disabled]="true"></gp-input-mask>
									</div>
								</gp-simple-input>
							</gp-form-col>
						</gp-form-row>
					</gp-card>
				</gp-form-col>
			</gp-form-row>
			<gp-form-row>
				<gp-form-col cols="12 12 12">
					<gp-card title="Taxas adicionais">
						<gp-form-row>
							<gp-form-col cols="12 6 6">
								<gp-simple-input label="Taxa de estorno" [required]="true">
									<div class="input-group">
										<span class="input-group-addon">%</span>
										<gp-input-mask name="plasticCost" [onlyDecimal]="true"
											[(ngModel)]="card.withdrawableCard.refundFee" required></gp-input-mask>
									</div>
								</gp-simple-input>
							</gp-form-col>
							<gp-form-col cols="12 6 6">
								<gp-simple-input label="Taxa mínima" [required]="true">
									<div class="input-group">
										<span class="input-group-addon">%</span>
										<gp-input-mask name="plasticCost" [onlyDecimal]="true"
											[(ngModel)]="generalSettings.withdrawableCard.refundFee" [disabled]="true">
										</gp-input-mask>
									</div>
								</gp-simple-input>
							</gp-form-col>
						</gp-form-row>
						<gp-form-row>
							<gp-form-col cols="12 6 6">
								<gp-simple-input label="Taxa adicional" [required]="true">
									<div class="input-group">
										<span class="input-group-addon">%</span>
										<gp-input-mask name="plasticCost" [onlyDecimal]="true"
											[(ngModel)]="card.withdrawableCard.additionalFee" required></gp-input-mask>
									</div>
								</gp-simple-input>
							</gp-form-col>
						</gp-form-row>
					</gp-card>
				</gp-form-col>
			</gp-form-row>
			<gp-form-row>
				<gp-form-col cols="12 12 12">
					<gp-card title="Regulamento">
						<gp-form-row>
							<gp-form-col cols="12 12 12">
								<gp-editor id="withdrawableRegulation" name="withdrawableRegulation"
									[(ngModel)]="card.withdrawableCard.regulation"></gp-editor>
							</gp-form-col>
						</gp-form-row>
					</gp-card>
				</gp-form-col>
			</gp-form-row>
			<gp-form-row>
				<gp-form-col cols="12 6 6">
					<gp-card title="Imagem do cartão">
						<div style="min-height: 400px">
							<div *ngIf="withdrawableImage">
								<img [src]="withdrawableImage" style="max-height:250px;" *ngIf="withdrawableImage" />
								<br /><br />
								<gp-spinner-button type="button" [pink]="true" icon="close" text="Remover imagem"
									[loading]="loadingSave" (click)="removeWithdrawableImage()"></gp-spinner-button>
								<br /><br />
							</div>
							<gp-fileupload name="withdrawableImage" label="Selecione a imagem do cartão"
								#withdrawableUploader></gp-fileupload>
						</div>
					</gp-card>
				</gp-form-col>
				<gp-form-col cols="12 6 6">
					<gp-card title="Imagem padrão do sistema">
						<div style="min-height: 400px">
							<img *ngIf="generalSettings.withdrawableCard.cardDetailedImage"
								[src]="generalSettings.withdrawableCard.cardDetailedImage + '/600'"
								style="max-height:250px;" />
						</div>
					</gp-card>
				</gp-form-col>
			</gp-form-row>
		</gp-card>
	</tab>
	<tab>
		<ng-template tabHeading>Cartão s/ saque</ng-template>
		<gp-card [first]="true">
			<gp-form-row>
				<gp-form-col cols="12 12 12">
					<gp-card title="Cartão Conta Swap" *ngIf="isContaSwapProvider">
						<div>
							<gp-form-row>
								<gp-form-col cols="12 6">
									<gp-simple-input label="Tipo de Saldo">
										<gp-tooltip message="Tipo de Saldo: limite de saldo que o cartão terá.">
											(?)</gp-tooltip>
										<gp-select name="balanceAccount-noDrawableCard" [multiple]="false"
											placeholder="Selecione" [items]="noDrawableCardBalanceAccounts"
											[(ngModel)]="card.noDrawableCard.balanceAccountId" [required]="true">
										</gp-select>
									</gp-simple-input>
								</gp-form-col>
							</gp-form-row>
						</div>
					</gp-card>
				</gp-form-col>
			</gp-form-row>
			<gp-form-row>
				<gp-form-col cols="12 12 12">
					<gp-card title="Taxas de emissão">
						<gp-form-row>
							<gp-form-col cols="12 6 6">
								<gp-simple-input label="Custo do plástico" [required]="true">
									<div class="input-group">
										<span class="input-group-addon">R$</span>
										<gp-input-mask name="plasticCost" [onlyDecimal]="true"
											[(ngModel)]="card.noDrawableCard.plasticCost" required></gp-input-mask>
									</div>
								</gp-simple-input>
							</gp-form-col>
							<gp-form-col cols="12 6 6">
								<gp-simple-input label="Custo mínimo" [required]="true">
									<div class="input-group">
										<span class="input-group-addon">R$</span>
										<gp-input-mask name="generalPlasticCost" [onlyDecimal]="true"
											[(ngModel)]="generalSettings.noDrawableCard.plasticCost" [disabled]="true">
										</gp-input-mask>
									</div>
								</gp-simple-input>
							</gp-form-col>
						</gp-form-row>
						<gp-form-row>
							<gp-form-col cols="12 6 6">
								<gp-simple-input label="Frete" tooltip="" [required]="true">
									<div class="input-group">
										<span class="input-group-addon">R$</span>
										<gp-input-mask name="plasticCost" [onlyDecimal]="true"
											[(ngModel)]="card.noDrawableCard.shippingCost" required></gp-input-mask>
									</div>
								</gp-simple-input>
							</gp-form-col>
							<gp-form-col cols="12 6 6">
								<gp-simple-input label="Custo mínimo" [required]="true">
									<div class="input-group">
										<span class="input-group-addon">R$</span>
										<gp-input-mask name="generalPlasticCost" [onlyDecimal]="true"
											[(ngModel)]="generalSettings.noDrawableCard.shippingCost" [disabled]="true">
										</gp-input-mask>
									</div>
								</gp-simple-input>
							</gp-form-col>
						</gp-form-row>
						<gp-form-row>
							<gp-form-col cols="12 6 6">
								<gp-simple-input label="Custo de 2ª via" [required]="true">
									<div class="input-group">
										<span class="input-group-addon">R$</span>
										<gp-input-mask name="plasticCost" [onlyDecimal]="true"
											[(ngModel)]="card.noDrawableCard.reissueCost" required></gp-input-mask>
									</div>
								</gp-simple-input>
							</gp-form-col>
							<gp-form-col cols="12 6 6">
								<gp-simple-input label="Custo mínimo" [required]="true">
									<div class="input-group">
										<span class="input-group-addon">R$</span>
										<gp-input-mask name="generalPlasticCost" [onlyDecimal]="true"
											[(ngModel)]="generalSettings.noDrawableCard.reissueCost" [disabled]="true">
										</gp-input-mask>
									</div>
								</gp-simple-input>
							</gp-form-col>
						</gp-form-row>
					</gp-card>
				</gp-form-col>
			</gp-form-row>
			<gp-form-row>
				<gp-form-col cols="12 12 12">
					<gp-card title="Carga/Recarga">
						<gp-form-row>
							<gp-form-col cols="12 6 6">
								<gp-simple-input label="Custo de carga" [required]="true">
									<div class="input-group">
										<span class="input-group-addon">R$</span>
										<gp-input-mask name="plasticCost" [onlyDecimal]="true"
											[(ngModel)]="card.noDrawableCard.firstChargeFee" required></gp-input-mask>
									</div>
								</gp-simple-input>
							</gp-form-col>
							<gp-form-col cols="12 6 6">
								<gp-simple-input label="Custo mínimo" [required]="true">
									<div class="input-group">
										<span class="input-group-addon">R$</span>
										<gp-input-mask name="generalPlasticCost" [onlyDecimal]="true"
											[(ngModel)]="generalSettings.noDrawableCard.firstChargeFee"
											[disabled]="true"></gp-input-mask>
									</div>
								</gp-simple-input>
							</gp-form-col>
						</gp-form-row>
						<gp-form-row>
							<gp-form-col cols="12 6 6">
								<gp-simple-input label="Custo de recarga" [required]="true">
									<div class="input-group">
										<span class="input-group-addon">R$</span>
										<gp-input-mask name="plasticCost" [onlyDecimal]="true"
											[(ngModel)]="card.noDrawableCard.rechargeFee" required></gp-input-mask>
									</div>
								</gp-simple-input>
							</gp-form-col>
							<gp-form-col cols="12 6 6">
								<gp-simple-input label="Custo mínimo" [required]="true">
									<div class="input-group">
										<span class="input-group-addon">R$</span>
										<gp-input-mask name="generalPlasticCost" [onlyDecimal]="true"
											[(ngModel)]="generalSettings.noDrawableCard.rechargeFee" [disabled]="true">
										</gp-input-mask>
									</div>
								</gp-simple-input>
							</gp-form-col>
						</gp-form-row>
					</gp-card>
				</gp-form-col>
			</gp-form-row>
			<gp-form-row>
				<gp-form-col cols="12 12 12">
					<gp-card title="Taxas adicionais">
						<gp-form-row>
							<gp-form-col cols="12 6 6">
								<gp-simple-input label="Taxa de estorno" [required]="true">
									<div class="input-group">
										<span class="input-group-addon">%</span>
										<gp-input-mask name="plasticCost" [onlyDecimal]="true"
											[(ngModel)]="card.noDrawableCard.refundFee" required></gp-input-mask>
									</div>
								</gp-simple-input>
							</gp-form-col>
							<gp-form-col cols="12 6 6">
								<gp-simple-input label="Taxa mínima" [required]="true">
									<div class="input-group">
										<span class="input-group-addon">%</span>
										<gp-input-mask name="plasticCost" [onlyDecimal]="true"
											[(ngModel)]="generalSettings.noDrawableCard.refundFee" [disabled]="true">
										</gp-input-mask>
									</div>
								</gp-simple-input>
							</gp-form-col>
						</gp-form-row>
						<gp-form-row>
							<gp-form-col cols="12 6 6">
								<gp-simple-input label="Taxa adicional" [required]="true">
									<div class="input-group">
										<span class="input-group-addon">%</span>
										<gp-input-mask name="plasticCost" [onlyDecimal]="true"
											[(ngModel)]="card.noDrawableCard.additionalFee" required></gp-input-mask>
									</div>
								</gp-simple-input>
							</gp-form-col>
						</gp-form-row>
					</gp-card>
				</gp-form-col>
			</gp-form-row>
			<gp-form-row>
				<gp-form-col cols="12 12 12">
					<gp-card title="Regulamento">
						<gp-form-row>
							<gp-form-col cols="12 12 12">
								<gp-editor id="noDrawableRegulation" name="noDrawableRegulation"
									[(ngModel)]="card.noDrawableCard.regulation"></gp-editor>
							</gp-form-col>
						</gp-form-row>
					</gp-card>
				</gp-form-col>
			</gp-form-row>
			<gp-form-row>
				<gp-form-col cols="12 6 6">
					<gp-card title="Imagem do cartão">
						<div style="min-height: 400px">
							<div *ngIf="noDrawableImage">
								<img [src]="noDrawableImage" style="max-height:250px;" *ngIf="noDrawableImage" />
								<br /><br />
								<gp-spinner-button type="button" [pink]="true" icon="close" text="Remover imagem"
									[loading]="loadingSave" (click)="removeNoDrawableImage()"></gp-spinner-button>
								<br /><br />
							</div>
							<gp-fileupload name="noDrawableImage" label="Selecione a imagem do cartão" [images]="true"
								#noDrawableUploader></gp-fileupload>
						</div>
					</gp-card>
				</gp-form-col>
				<gp-form-col cols="12 6 6">
					<gp-card title="Imagem padrão do sistema">
						<div style="min-height: 400px">
							<img *ngIf="generalSettings.noDrawableCard.cardDetailedImage"
								[src]="generalSettings.noDrawableCard.cardDetailedImage + '/600'"
								style="max-height:250px;" />
						</div>
					</gp-card>
				</gp-form-col>
			</gp-form-row>
		</gp-card>
	</tab>
</tabset>

<gp-card *ngIf="canEditPrePaidCard">
	<div class="row">
		<div class="col-md-12">
			<gp-spinner-button type="button" [pink]="true" text="Salvar dados do cartão" icon="send" pull="right"
				(click)="savePrepaidCard()"
				[loading]="loadingSave || uploadingWithdrawableImage || uploadingNoDrawableImage || loadingProducts || loadingBalanceAccounts">
			</gp-spinner-button>
		</div>
	</div>
</gp-card>
