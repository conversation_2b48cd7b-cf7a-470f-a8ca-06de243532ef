import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { ApiService } from '../../../../../core/api/api.service';

@Injectable()
export class BenefitsClubService {
	constructor(private _api: ApiService) { }

	saveInstitutionalPartnerCategory(campaignId: string, partnerCategory: any): Observable<any> {
		if (partnerCategory.id)
			return this._api.put(`/api/campaigns/${campaignId}/benefitsclub/categories/${partnerCategory.id}`, partnerCategory);
		return this._api.post(`/api/campaigns/${campaignId}/benefitsclub/categories`, partnerCategory);
	}

	getCategories(campaignId: string): Observable<any> {
		return this._api.get(`/api/campaigns/${campaignId}/benefitsclub/categories`);
	}

	updateInstitutionalPartnerCategory(campaignId: string, partnerCategory: any): Observable<boolean> {
		return this._api.put(`/api/campaigns/${campaignId}/benefitsclub/categories/${partnerCategory.id}`, partnerCategory);
	}

	savePartnerInstitutional(campaignId: string, id: string, partner: any): Observable<boolean> {
		if (!partner.id)
			return this._api.post(`/api/campaigns/${campaignId}/benefitsclub/partners`, partner);
		return this._api.put(`/api/campaigns/${campaignId}/benefitsclub/partners/${id}`, partner);
	}

	getPartnersInstitutional(campaignId: string, name: string, categoryId: string, active?: boolean, skip?: number, limit?: number): Observable<Array<any>> {
		let params: any = {};

		if (name) params.name = name;
		if (categoryId) params.categoryId = categoryId;
		if (active) params.active = active;

		params.skip = skip || 0;
		params.limit = limit || 100;
		return this._api.get(`/api/campaigns/${campaignId}/benefitsclub/partners` , params);
	}

	getPartnersInstitutionalById(campaignId: string, id: string): Observable<any> {
		return this._api.get(`/api/campaigns/${campaignId}/benefitsclub/partners/${id}`);
	}

	saveInstitutionalPartnerPromotion(campaignId: string, promotion: any): Observable<any> {
		if (promotion.id) {
			return this._api.put(`/api/campaigns/${campaignId}/benefitsclub/promotions/${promotion.id}`, promotion);
		}
		return this._api.post(`/api/campaigns/${campaignId}/benefitsclub/promotions`, promotion);
	}

	getInstitutionalPromotions(campaignId: string, partnerId: string, name: string, categoryId: string, active?: boolean, skip?: number, limit?: number): Observable<any[]> {
		let params: any = {};

		if (name) params.name = name;
		if (categoryId) params.categoryId = categoryId;
		if (partnerId) params.partnerId = partnerId;

    params.active = active;
		params.skip = skip || 0;
		params.limit = limit || 40;
		return this._api.get(`/api/campaigns/${campaignId}/benefitsclub/promotions`, params);
	}

	getInstitutionalPromotionById(campaignId: string, promotionId: string): Observable<any> {
		return this._api.get(`/api/campaigns/${campaignId}/benefitsclub/promotions/${promotionId}`);
	}
}
