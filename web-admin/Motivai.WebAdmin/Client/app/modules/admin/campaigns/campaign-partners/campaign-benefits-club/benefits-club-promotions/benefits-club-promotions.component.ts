import { Component, OnInit, ViewChild } from '@angular/core';

import { CampaignStore } from '../../../campaign.store';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { Item } from '../../../../../../shared/models/item';
import { BenefitsClubService } from '../benefits-club.service';
import { FormatHelper } from '../../../../../../shared/formatters/format-helper';

@Component({
  selector: 'benefits-club-promotions',
  templateUrl: './benefits-club-promotions.component.html'
})
export class BenefitsClubPromotionsComponent implements OnInit {
  @ViewChild('alert') alert: GpAlertComponent;

  defaultColor = "#00be28"
  promotion: any = {
    partnerId: '',
    expirationDate: '',
    utilizationRules: '',
    enablePriceRange: false,
    discountAmountType: '',
    cashbackAmountType: '',
    regulaments: [],
    segmentations: {},
    partnerIntegrationPromotion: {}
  };

  selectedPartner: any[] = [];
  activeCategories: Item[] = [];
  linkedCategories: any[] = [];
  selectedCategoryId: string;

  partners: any[] = [];
  categories: any[] = [];

  promotions: any[] = [];
  filterParams: any = {};

  conceptImageUploadPath: string = '';

  campaignId: string = '';
  partnerId: string = '';
  promotionId: string = '';

  fixedConceptImageUploadPath: string;

  skip: number = 0;
  limit: number = 40;
  disableSaveButton: boolean = false;
  showNewPromotion: boolean = false;
  loading: boolean = false;
  discountAmountTypeItems: Array<Item> = [Item.of('PERCENTAGE', 'Desconto por Percentual'),
  Item.of('CURRENCY', 'Desconto por Reais')];
  cashbackAmountTypeItems: Array<Item> = [Item.of('PERCENTAGE', 'Cashback por Percentual'),
  Item.of('CURRENCY', 'Cashback por Reais')];

  processTypeItems: Array<Item> = [
    Item.of('OFFLINE', 'Offline'),
    Item.of('ONLINE', 'Online')
  ];

  promotionTypeItems: Array<Item> = [
    Item.of('DISCOUNT_CODE', 'Cupom de desconto'),
    Item.of('ONLINE_STORE', 'Loja online'),
  ];

  activeItems: Array<Item> = [
    Item.of('ALL', 'Todos'),
    Item.of('true', 'Ativo'),
    Item.of('false', 'Inativo'),
  ]

  constructor(private _service: BenefitsClubService, private _store: CampaignStore) { }

  get name() {
    return this.filterParams.name;
  }

  get isListing() {
    return !this.showNewPromotion;
  }

  get isShowingRegister() {
    return this.showNewPromotion;
  }

  get isPromotionOnline() {
    return this.promotion.processType == 'ONLINE' || this.promotion.processType == 'BOTH';
  }

  get isInPercentage() {
    return this.promotion.discountAmountType == "PERCENTAGE"
  }

  get isInCashbackPercentage() {
    return this.promotion.cashbackAmountType == "PERCENTAGE"
  }

  get discountValue() {
    return this.promotion.discount;
  }

  get cashbackValue() {
    return this.promotion.cashbackAmount;
  }

  get formattedCashbackValue() {
    if (this.isInCashbackPercentage) {
      return FormatHelper.formatPercentage(this.cashbackValue);
    } else {
      return FormatHelper.formatCurrency(this.cashbackValue);
    }
  }

  get formattedDiscountValue() {
    if (this.isInPercentage) {
      return FormatHelper.formatPercentage(this.discountValue);
    } else {
      return FormatHelper.formatCurrency(this.discountValue);
    }
  }

  get disable() {
    if (!this.promotion.partnerId && !this.promotion.partnerId.length || !this.promotion.expirationDate.length || !this.promotion.utilizationRules.length) {
      return true;
    }
    return this.loading;
  }

  get discountCodeBackgroundColor() {
    if (!this.promotion.discountCodeBackgroundColor) {
      this.promotion.discountCodeBackgroundColor = this.defaultColor
      return this.promotion.discountCodeBackgroundColor;
    }

    return this.promotion.discountCodeBackgroundColor;
  }

  set discountCodeBackgroundColor(value) {
    this.promotion.discountCodeBackgroundColor = value;
  }

  ngOnInit(): void {
    this.campaignId = this._store.id;
    this.getCategories();
    this.getPartners();
    this.getPromotions();
  }

  getPromotions() {
    const expirationDateFormat = require('dateformat');
    this.loading = true;
    this._service.getInstitutionalPromotions(this.campaignId, this.filterParams.partnerId, this.filterParams.name, this.filterParams.categoryId, this.filterParams.active, this.skip, this.limit).subscribe(
      promotions => {
        this.promotions = promotions;
        this.promotions.forEach(x => {
          x.formattedExpirationDate = expirationDateFormat(x.expirationDate, 'dd/mm/yyyy');
          x.processTypeFormatted = this.handleProcessType(x.processType);
          x.promotionTypeFormatted = this.handlePromotionType(x.promotionType);
          if (x.position < 1) {
            x.position = '-';
          } else {
            x.position = `${x.position}ª`;
          }
        });
        this.loading = false;
      },
      err => {
        this.alert.showError(err);
        this.loading = false;

      });
  }

  getPromotionById() {
    this.loading = true;
    this._service.getInstitutionalPromotionById(this.campaignId, this.promotionId).subscribe(
      promotion => {
        if (!promotion.regulaments) {
          promotion.regulaments = [];
        }

        if (!promotion.processType) {
          promotion.processType = 'OFFLINE';
        }

        if (!promotion.segmentations) {
          promotion.segmentations = {};
        }

        if (!promotion.partnerIntegrationPromotion) {
          promotion.partnerIntegrationPromotion = {}
          promotion.partnerIntegrationPromotion.formattedStatus = this.handlePromotionIntegrationStatus(promotion.partnerIntegrationPromotion.status);
        }

        this.fixedConceptImageUploadPath = `api/campaigns/${this.campaignId}/benefitsclub/promotions/${this.promotionId}/fixedconceptimage`;
        this.promotion = promotion;
        this.linkedCategories = this.promotion.categories;
        this.selectedPartner = this.promotion.partnerId;

        this.loading = false;
      },
      err => {
        this.alert.showError(err);
        this.loading = false;
      }
    );
  }

  getCategories() {
    this.loading = true;
    this._service.getCategories(this.campaignId).subscribe(
      categories => {
        this.categories = categories;
        this.activeCategories = categories.filter(x => x.active)
          .map(category => Item.of(category.id, category.name));
        this.loading = false;
      },
      err => {
        this.alert.showError(err);
        this.loading = false;
      });
  }

  getPartners() {
    this.loading = true;
    this._service.getPartnersInstitutional(this.campaignId, null, null, null, 0, 1000).subscribe(
      partner => {
        this.partners = partner.map(x => ({ id: x.id, text: x.name }));
        this.loading = false;
      },
      err => {
        this.loading = false;
        this.alert.showError(err);
      });
  }

  linkCategory() {
    if (!this.selectedCategoryId) {
      return;
    }

    if (!this.linkedCategories)
      this.linkedCategories = [];

    const selectedCategory = this.activeCategories.find(i => i.id == this.selectedCategoryId);
    if (!selectedCategory) {
      return;
    }
    const categoryExists = this.linkedCategories.find(c => c.key === selectedCategory.id);
    if (categoryExists) {
      this.alert.showError('Categoria já vinculada.');
      return;
    }
    this.linkedCategories.push({ key: selectedCategory.id, value: selectedCategory.text });
  }

  unLinkCategory($event: any) {
    if ($event) {
      if (!this.linkedCategories) return;

      const category = this.linkedCategories.find(c => c.id === $event.id);
      if (!category) return;

      this.linkedCategories.splice(this.linkedCategories.indexOf(category), 1);
    }
  }

  prepareToSave() {
    if (this.linkedCategories) {
      this.promotion.categoriesIds = this.linkedCategories.map(x => x.key);
    }
    this.save();
  }

  private save() {
    this._service.saveInstitutionalPartnerPromotion(this.campaignId, this.promotion).subscribe(
      promotion => {
        if (!promotion.partnerIntegrationPromotion) {
          promotion.partnerIntegrationPromotion = {};
        }
        this.promotion = promotion;
        this.promotionId = this.promotion.id;
        this.loading = false;
        if (promotion) {
          this.alert.showSuccess('Parceiro salvo com sucesso.');
        } else {
          this.alert.showSuccess('Parceiro salvo com sucesso.');
        }
        this.back()
      },
      err => {
        this.loading = false;
        this.alert.showError(err);
      }
    );

  }

  handleProcessType(processType: string): string {
    if (!processType) {
     return 'Necessário atualização';
    }

    switch (processType) {
     case 'OFFLINE':
         return 'Offline';
     case 'ONLINE':
         return 'Online';
     case 'BOTH':
         return 'Ambos';
     default:
         return processType;
    }
  }

  handlePromotionType(promotionType: string): string {
    if (!promotionType) {
     return 'Necessário atualização';
    }

    switch (promotionType) {
     case 'ONLINE_STORE':
         return 'Loja online';
     case 'DISCOUNT_CODE':
         return 'Cupom de desconto';
     default:
         return promotionType;
    }
  }

  handlePromotionIntegrationStatus(status: string): string {
    if (!status) {
      return 'Mapeamento inválido';
     }

     switch (status) {
      case 'ACTIVE':
          return 'Ativo';
      case 'UPCOMING':
          return 'Fora do periodo';
      default:
          return status;
     }
  }

  removeConceptImageUrl() {
    this.promotion.conceptImageUrl = ''
  }

  removeFixedConceptImageUrl() {
    this.promotion.fixedConceptImageUrl = ''
  }

  clear() {
    this.promotion = {
      partnerIntegrationPromotion: {}
    };
    this.linkedCategories = [];
    this.filterParams = {};

  }

  onPartnerChange(partnerId) {
    this.partnerId = partnerId;
    this.filterParams.partnerId = partnerId;
    this.promotion.partnerId = partnerId;
  }

  onPageChanged($event) {
    if ($event) {
      this.skip = $event.skip;
      this.limit = $event.limit;
      this.getPromotions();
    }
  }

  edit($event) {
    this.promotionId = $event.id;
    this.getPromotionById();
    this.showNewPromotion = true;
  }

  newPromotion() {
    this.showNewPromotion = true;
  }

  back() {
    this.skip = 0;
    this.limit = 40;
    this.clear();
    this.getPromotions();
    this.selectedPartner = [];
    this.showNewPromotion = false;
  }
}
