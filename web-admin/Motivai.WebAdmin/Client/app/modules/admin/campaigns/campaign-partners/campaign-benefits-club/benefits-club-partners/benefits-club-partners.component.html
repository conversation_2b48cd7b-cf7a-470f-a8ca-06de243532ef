<gp-alert #alert [overlay]="true"></gp-alert>

<div *ngIf="isListing">
	<gp-card [first]="true">
		<div class="row">
			<div class="col-md-12">
				<gp-spinner-button type="button" [actionSecondary]="true" pull="right" text="Novo Parceiro"
					(click)="showNewPartner()">
				</gp-spinner-button>
			</div>
		</div>
	</gp-card>

	<gp-card title="Pesquisar Parceiro">
		<gp-row>
			<gp-form-col cols="12 6 3">
				<gp-simple-input label="Nome do parceiro">
					<input type="text" class="form-control" name="name" [(ngModel)]="filterParams.name"
						placeholder="Por nome do parceiro" />
				</gp-simple-input>
			</gp-form-col>

			<gp-form-col cols="12 3 3">
				<gp-simple-input label="Ativos">
					<gp-select name="active" [items]="activeItems" placeholder="Selecione" [(ngModel)]="filterParams.active">
					</gp-select>
				</gp-simple-input>
			</gp-form-col>

			<gp-form-col cols="12 3 3">
				<gp-spinner-button text="Pesquisar" [search]="true" marginTop="24px" (click)="filterPartners()"
					[loading]="loading" loadingText="Pesquisando"></gp-spinner-button>
			</gp-form-col>
		</gp-row>
	</gp-card>

	<gp-card [last]="true">
		<gp-form-row>
			<gp-form-col cols="12 12">
				<gp-grid #grid name="partnersGrid" [rows]="partners" [loading]="loading"
					[columns]="['Nome', 'Tipo do Processo', 'Boost']" [fields]="['name', 'processTypeFormatted', 'boost']"
					[showActive]="true" [showPagination]="true" [showTotalPages]="false" [showEdit]="true" [showDelete]="false"
					(onEdit)="edit($event)" (onPageChanged)="onPageChanged($event)">
				</gp-grid>
			</gp-form-col>
		</gp-form-row>
	</gp-card>
</div>

<div *ngIf="isEditing">
	<spinner [overlay]="true" [show]="loading"></spinner>

	<gp-card title="Dados Básicos do parceiro" [first]="true">
		<gp-form-row>
			<gp-form-col cols="12 4" *ngIf="partner.partnerId">
				<gp-simple-input label="Identificação do parceiro">
					<input type="text" name="partnerId" class="form-control" disabled="true" [(ngModel)]="partner.partnerId"/>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 6">
				<gp-simple-input label="Nome do parceiro" [required]="true" errorMessage="Nome do parceiro é obrigatorio">
					<input type="text" name="partnerName" class="form-control" placeholder="Nome do parceiro" required
						[(ngModel)]="partner.name" />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 3">
				<gp-simple-input label="Tipo de processo" [required]="true" errorMessage="Tipo de processo é obrigatorio">
					<gp-select name="processType" [items]="processTypeItems" placeholder="Selecione"
						[(ngModel)]="partner.processType">
					</gp-select>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 3">
				<gp-simple-input label="Boost" [required]="true" errorMessage="Boost é obrigatorio">
					<gp-input-mask name="boost" [onlyInteger]="true" [integers]="2" required
						[(ngModel)]="partner.boost"></gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6">
				<div>
					<label>Ativo:</label>
				</div>
				<gp-switch name="active" [(ngModel)]="partner.active"></gp-switch>
			</gp-form-col>
			<gp-form-col cols="12 6">
				<div>
					<label>Destacar na home do app:</label>
				</div>
				<gp-switch name="highlightAtHome" [(ngModel)]="partner.highlightAtHome"></gp-switch>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6" *ngIf="isHighlightAtHome">
				<gp-simple-input label="Posição de destaque no app">
					<gp-input-mask name="homeAppPosition" [onlyInteger]="true" [integers]="2"
						[(ngModel)]="partner.homeAppPosition"></gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<div>
					<label>Criado via integração:</label>
				</div>
				<gp-switch name="partnerCreatedByIntegration" [disabled]="true"
					[(ngModel)]="partner.partnerCreatedByIntegration"></gp-switch>
			</gp-form-col>
			<gp-form-col cols="12 6 6" *ngIf="partner.partnerCreatedByIntegration">
				<gp-simple-input label="Nome do parceiro de integração">
					<input type="text" name="partnerIntegrationName" readonly class="form-control" placeholder="Nome do parceiro"
						required [(ngModel)]="partner.partnerIntegrationName" />
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<segmentation-parametrization [campaignId]="campaignId" [segmentations]="partner.segmentations"
		[enableByPersonType]="false" [enableByParticipants]="false">
	</segmentation-parametrization>

	<tabset class="bg-white p0 tab-no-border" [justified]="true" #tabset>
		<tab>
			<ng-template tabHeading>Configurações offline</ng-template>
			<gp-card [first]="true" title="Configurações do processo Offline">
				<gp-card title="Configurações de desconto e cashback">
					<gp-form-row>
						<gp-form-col cols="12 6">
							<gp-simple-input label="Formato do desconto">
								<gp-select name="maxDiscountType" [items]="maxDiscountTypeItems" placeholder="Selecione"
									[(ngModel)]="partner.maxDiscountType">
								</gp-select>
							</gp-simple-input>
						</gp-form-col>
						<gp-form-col cols="12 6" *ngIf="isMaxDiscountAmountTypePercentage">
							<gp-simple-input label="Desconto máximo">
								<div class="input-group">
									<span class="input-group-addon">%</span>
									<gp-input-mask [disabled]="!partner.maxDiscountType" name="maxDiscountAmount"
										[(ngModel)]="partner.maxDiscountAmount" [onlyDecimal]="true">
									</gp-input-mask>
								</div>
							</gp-simple-input>
						</gp-form-col>
						<gp-form-col cols="12 6" *ngIf="!isMaxDiscountAmountTypePercentage">
							<gp-simple-input label="Desconto máximo">
								<div class="input-group">
									<span class="input-group-addon">R$</span>
									<gp-input-mask [disabled]="!partner.maxDiscountType" name="maxDiscountAmount" [onlyDecimal]="true"
										[(ngModel)]="partner.maxDiscountAmount">
									</gp-input-mask>
								</div>
							</gp-simple-input>
						</gp-form-col>
					</gp-form-row>
					<gp-form-row>
						<gp-form-col cols="12 6">
							<gp-simple-input label="Formato do cashback">
								<gp-select name="maxCashbackType" [items]="maxCashbackTypeItems" placeholder="Selecione"
									[(ngModel)]="partner.maxCashbackType">
								</gp-select>
							</gp-simple-input>
						</gp-form-col>
						<gp-form-col cols="12 6" *ngIf="isMaxCashbackAmountTypePercentage">
							<gp-simple-input label="Cashback máximo">
								<div class="input-group">
									<span class="input-group-addon">%</span>
									<gp-input-mask [disabled]="!partner.maxCashbackType" name="maxCashbackAmount"
										[(ngModel)]="partner.maxCashbackAmount" [onlyDecimal]="true">
									</gp-input-mask>
								</div>
							</gp-simple-input>
						</gp-form-col>
						<gp-form-col cols="12 6" *ngIf="!isMaxCashbackAmountTypePercentage">
							<gp-simple-input label="Cashback máximo">
								<div class="input-group">
									<span class="input-group-addon">R$</span>
									<gp-input-mask [disabled]="!partner.maxCashbackType" name="maxCashbackAmount" [onlyDecimal]="true"
										[(ngModel)]="partner.maxCashbackAmount">
									</gp-input-mask>
								</div>
							</gp-simple-input>
						</gp-form-col>
					</gp-form-row>
				</gp-card>

				<gp-card title="Banner de destaque do parceiro">
					<gp-form-row>
						<gp-form-col cols="12 6">
							<gp-simple-input label="Posição do banner">
								<gp-input-mask name="bannerPartnerPosition" [onlyInteger]="true" [integers]="2"
									[(ngModel)]="partner.bannerPartnerPosition"></gp-input-mask>
							</gp-simple-input>
						</gp-form-col>
					</gp-form-row>

					<gp-form-row>
						<gp-form-col cols="12 6">
							<div class="form-group">
								<gp-fileupload-default-folder name="partnerBannerUrl" [onlyImages]="true" [showRender]="true"
									[skipImageUrlWidth]="true" [campaignId]="campaignId" (error)="onUploadError($event)" imageWidth="525"
									renderWidth="400" [(ngModel)]="partner.partnerBannerUrl">
								</gp-fileupload-default-folder>
							</div>
						</gp-form-col>
						<gp-form-col cols="12 6">
							<label>Remova a imagem</label>
							<gp-spinner-button style="display: flex;" type="button" [pink]="true" icon="trash" text="Remover a imagem"
								marginRight="5px" (click)="removePartnerBannerUrl()">
							</gp-spinner-button>
						</gp-form-col>
					</gp-form-row>
				</gp-card>

				<gp-card title="Logotipo do parceiro">
					<gp-form-row>
						<gp-form-col cols="12 6">
							<div class="form-group">
								<gp-fileupload-default-folder name="logo" [onlyImages]="true" [showRender]="true"
									[skipImageUrlWidth]="true" [campaignId]="campaignId" (error)="onUploadError($event)" imageWidth="525"
									renderWidth="400" [(ngModel)]="partner.logo">
								</gp-fileupload-default-folder>
							</div>
						</gp-form-col>
						<gp-form-col cols="12 6">
							<label>Remova a imagem</label>
							<gp-spinner-button style="display: flex;" type="button" [pink]="true" icon="trash" text="Remover a imagem"
								marginRight="5px" (click)="removeLogoPartnerUrl()">
							</gp-spinner-button>
						</gp-form-col>
					</gp-form-row>
				</gp-card>
			</gp-card>
		</tab>
		<tab>
			<ng-template tabHeading>Configurações online</ng-template>
			<gp-card [first]="true" title="Configurações do processo Online">
				<gp-form-row *ngIf="partner.partnerCreatedByIntegration">
					<gp-form-col cols="12 3">
						<gp-simple-input label="Status no Parceiro">
							<input type="text" name="partnerIntegrationStatus" readonly class="form-control"
								placeholder="Status no parceiro" [(ngModel)]="partner.partnerIntegration.formattedStatus" />
						</gp-simple-input>
					</gp-form-col>
					<gp-form-col cols="12">
						<label>Descrição no parceiro</label>
						<textarea readonly name="partnerIntegrationDescription" class="form-control"
							[(ngModel)]="partner.partnerIntegration.description"></textarea>
					</gp-form-col>
				</gp-form-row>
				<gp-card title="Configurações de desconto e cashback">
					<gp-form-row>
						<gp-form-col cols="12 6">
							<gp-simple-input label="Formato do desconto">
								<gp-select name="maxPartnerIntegrationDiscountType" [items]="maxDiscountTypeItems"
									placeholder="Selecione" [(ngModel)]="partner.partnerIntegration.maxDiscountType">
								</gp-select>
							</gp-simple-input>
						</gp-form-col>
						<gp-form-col cols="12 6" *ngIf="isMaxPartnerIntegrationDiscountAmountTypePercentage">
							<gp-simple-input label="Desconto máximo">
								<div class="input-group">
									<span class="input-group-addon">%</span>
									<gp-input-mask [disabled]="!partner.partnerIntegration.maxDiscountType"
										name="maxPartnerIntegrationDiscountAmount" [onlyDecimal]="true"
										[(ngModel)]="partner.partnerIntegration.maxDiscountAmount">
									</gp-input-mask>
								</div>
							</gp-simple-input>
						</gp-form-col>
						<gp-form-col cols="12 6" *ngIf="!isMaxPartnerIntegrationDiscountAmountTypePercentage">
							<gp-simple-input label="Desconto máximo">
								<div class="input-group">
									<span class="input-group-addon">R$</span>
									<gp-input-mask [disabled]="!partner.partnerIntegration.maxDiscountType"
										name="maxPartnerIntegrationDiscountAmount" [onlyDecimal]="true"
										[(ngModel)]="partner.partnerIntegration.maxDiscountAmount">
									</gp-input-mask>
								</div>
							</gp-simple-input>
						</gp-form-col>
					</gp-form-row>
					<gp-form-row>
						<gp-form-col cols="12 6">
							<gp-simple-input label="Formato do cashback">
								<gp-select name="maxPartnerIntegrationCashbackType" [items]="maxCashbackTypeItems"
									placeholder="Selecione" [(ngModel)]="partner.partnerIntegration.maxCashbackType">
								</gp-select>
							</gp-simple-input>
						</gp-form-col>
						<gp-form-col cols="12 6" *ngIf="isMaxPartnerIntegrationCashbackAmountTypePercentage">
							<gp-simple-input label="Cashback máximo">
								<div class="input-group">
									<span class="input-group-addon">%</span>
									<gp-input-mask [disabled]="!partner.partnerIntegration.maxCashbackType" name="maxCashbackAmount" [onlyDecimal]="true"
										[(ngModel)]="partner.partnerIntegration.maxCashbackAmount">
									</gp-input-mask>
								</div>
							</gp-simple-input>
						</gp-form-col>
						<gp-form-col cols="12 6" *ngIf="!isMaxPartnerIntegrationCashbackAmountTypePercentage">
							<gp-simple-input label="Cashback máximo">
								<div class="input-group">
									<span class="input-group-addon">R$</span>
									<gp-input-mask [disabled]="!partner.partnerIntegration.maxCashbackType" name="maxCashbackAmount"
										[onlyDecimal]="true" [(ngModel)]="partner.partnerIntegration.maxCashbackAmount">
									</gp-input-mask>
								</div>
							</gp-simple-input>
						</gp-form-col>
					</gp-form-row>
				</gp-card>

				<gp-card title="Banner de destaque do parceiro">
					<gp-form-row>
						<gp-form-col cols="12 6">
							<gp-simple-input label="Posição do banner">
								<gp-input-mask name="bannerPartnerPosition" [onlyInteger]="true" [integers]="2"
									[(ngModel)]="partner.partnerIntegration.bannerPartnerPosition"></gp-input-mask>
							</gp-simple-input>
						</gp-form-col>
					</gp-form-row>

					<gp-form-row>
						<gp-form-col cols="12 6">
							<div class="form-group">
								<gp-fileupload-default-folder name="partnerIntegrationBannerUrl" [onlyImages]="true" [showRender]="true"
									[skipImageUrlWidth]="true" [campaignId]="campaignId" (error)="onUploadError($event)" imageWidth="525"
									renderWidth="400" [(ngModel)]="partner.partnerIntegration.partnerBannerUrl">
								</gp-fileupload-default-folder>
							</div>
						</gp-form-col>
						<gp-form-col cols="12 6">
							<label>Remova a imagem</label>
							<gp-spinner-button style="display: flex;" type="button" [pink]="true" icon="trash" text="Remover a imagem"
								marginRight="5px" (click)="removePartnerIntegrationBannerUrl()">
							</gp-spinner-button>
						</gp-form-col>
					</gp-form-row>
				</gp-card>

				<gp-card title="Logotipo do parceiro">
					<gp-form-row>
						<gp-form-col cols="12 6">
							<div class="form-group">
								<gp-fileupload-default-folder name="partnerIngretationLogo" [onlyImages]="true" [showRender]="true"
									[skipImageUrlWidth]="true" [campaignId]="campaignId" (error)="onUploadError($event)" imageWidth="525"
									renderWidth="400" [(ngModel)]="partner.partnerIntegration.logo">
								</gp-fileupload-default-folder>
							</div>
						</gp-form-col>
						<gp-form-col cols="12 6">
							<label>Remova a imagem</label>
							<gp-spinner-button style="display: flex;" type="button" [pink]="true" icon="trash" text="Remover a imagem"
								marginRight="5px" (click)="removeLogoPartnerIntegrationUrl()">
							</gp-spinner-button>
						</gp-form-col>
					</gp-form-row>
				</gp-card>
			</gp-card>
		</tab>
	</tabset>

	<gp-card title="Categorias do Parceiro">
		<gp-form-row>
			<gp-form-col cols="12 8 8">
				<gp-select name="categories" styleClass="font-awesome" [items]="activeCategories"
					placeholder="Selecione uma categoria" [allowClear]="true" [(ngModel)]="selectedCategoryId">
				</gp-select>
			</gp-form-col>
			<gp-fomr-col cols="12 4 4">
				<gp-spinner-button type="button" [pink]="true" text="Vincular" icon="plus" (click)="linkCategory()"
					[loading]="loading">
				</gp-spinner-button>
			</gp-fomr-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-grid name="grid" [rows]="linkedCategories" [columns]="['Categorias']" [fields]="['value']"
					[showActive]="false" [showPagination]="false" [showDelete]="true" [showEdit]="false"
					(onDelete)="unLinkCategory($event)">
				</gp-grid>
			</gp-form-col>
		</gp-form-row>
	</gp-card>


	<gp-card title="Redes Sociais">
		<gp-form-row>
			<gp-form-col cols="12 4 4">
				<div class="form-group">
					<label>Facebook</label>
					<div class="input-group">
						<span class="input-group-addon "><i class="fa fa-facebook" aria-hidden="true"></i></span>
						<gp-simple-input>
							<input type="text" name="partnerSocialNetworks" class="form-control" placeholder="Url Facebook"
								[(ngModel)]="partner.socialNetworks.facebook" />
						</gp-simple-input>
					</div>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 4 4">
				<div class="form-group">
					<label>Pinterest</label>
					<div class="input-group">
						<span class="input-group-addon "><i class="fa fa-pinterest" aria-hidden="true"></i></span>
						<gp-simple-input>
							<input type="text" name="partnerSocialNetworks" class="form-control" placeholder="Url Pinterest"
								[(ngModel)]="partner.socialNetworks.pinterest" />
						</gp-simple-input>
					</div>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 4 4">
				<div class="form-group">
					<label>Twitter</label>
					<div class="input-group">
						<span class="input-group-addon "><i class="fa fa-twitter" aria-hidden="true"></i></span>
						<gp-simple-input>
							<input type="text" name="partnerSocialNetworks" class="form-control" placeholder="Url Twitter"
								[(ngModel)]="partner.socialNetworks.twitter" />
						</gp-simple-input>
					</div>
				</div>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>

			<gp-form-col cols="12 4 4">
				<div class="form-group">
					<label>Linkedin</label>
					<div class="input-group">
						<span class="input-group-addon "><i class="fa fa-linkedin" aria-hidden="true"></i></span>
						<gp-simple-input>
							<input type="text" name="partnerSocialNetworks" class="form-control" placeholder="Url Linkedin"
								[(ngModel)]="partner.socialNetworks.linkedin" />
						</gp-simple-input>
					</div>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 4 4">
				<div class="form-group">
					<label>Google</label>
					<div class="input-group">
						<span class="input-group-addon "><i class="fa fa-google" aria-hidden="true"></i></span>
						<gp-simple-input>
							<input type="text" name="partnerSocialNetworks" class="form-control" placeholder="Url Google"
								[(ngModel)]="partner.socialNetworks.google" />
						</gp-simple-input>
					</div>
				</div>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card title="Regulamento do Parceiro">
		<gp-dynamic-contents-editor [dynamicContents]="partner.regulaments">
		</gp-dynamic-contents-editor>
	</gp-card>

	<gp-card>
		<gp-form-row>
			<gp-form-col cols="12 12 12" [inputGroup]="false">
				<gp-spinner-button type="button" [pink]="true" icon="send" text="Salvar Parceiro" marginRight="5px" pull="right"
					(click)="prepareToSave()" [loading]="loading" loadingText="Processando">
				</gp-spinner-button>
				<gp-spinner-button type="button" [clear]="true" icon="trash" text="Limpar" marginRight="5px" pull="right"
					(click)="clear()">
				</gp-spinner-button>

				<gp-spinner-button type="button" [actionBack]="true" text="Voltar" marginRight="5px" pull="right"
					(click)="back()">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</gp-card>
</div>
