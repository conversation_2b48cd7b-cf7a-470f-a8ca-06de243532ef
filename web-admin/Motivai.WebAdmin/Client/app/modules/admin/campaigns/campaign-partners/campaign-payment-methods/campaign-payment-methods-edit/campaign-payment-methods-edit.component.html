<spinner [overlay]="true" [show]="loading"></spinner>
<gp-alert [overlay]="true" #alert></gp-alert>

<gp-card [first]="true">
    <gp-form-row>
        <gp-form-col cols="6">
            <gp-simple-input label="Tipo">
                <gp-select name="type" placeholder="Selecione um método"
                    [items]="enabledPlataformPaymentMethodsForSelection"
                    (change)="onEnabledPaymentMethodSelected($event)" [(ngModel)]="paymentMethod.code"
                    [disabled]="isEdition"></gp-select>
            </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="6">
            <label>Ativo:</label>
            <div>
                <gp-switch name="active" [(ngModel)]="paymentMethod.active"></gp-switch>
            </div>
        </gp-form-col>
    </gp-form-row>
    <gp-form-row>
        <gp-form-col cols="6">
            <gp-simple-input label="Nome">
                <input type="text" class="form-control" disabled="true" [ngModel]="paymentMethod.name" />
            </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="6">
            <gp-simple-input label="Descrição">
                <input type="text" class="form-control" disabled="true" [ngModel]="paymentMethod.description" />
            </gp-simple-input>
        </gp-form-col>
    </gp-form-row>
    <gp-form-row>
        <gp-form-col cols="6">
            <gp-simple-input label="Código">
                <input type="text" class="form-control" disabled="true" [ngModel]="paymentMethod.code" />
            </gp-simple-input>
        </gp-form-col>
    </gp-form-row>
</gp-card>

<gp-card title="Parametrização">
    <gp-form-row>
        <gp-form-col cols="6 4">
            <gp-simple-input label="Parametro" [required]="true">
                <input type="text" class="form-control" name="paymentMethodParameters"
                    [(ngModel)]="paymentMethodParameter.parameter" />
            </gp-simple-input>
        </gp-form-col>

        <gp-form-col cols="6 4">
            <gp-simple-input label="Valor" [required]="true">
                <input type="text" class="form-control" name="paymentMethodParameterValue"
                    [(ngModel)]="paymentMethodParameter.value" />
            </gp-simple-input>
        </gp-form-col>

        <gp-form-col cols="6 4">
            <gp-spinner-button type="button" icon="send" marginTop="26px" icon="plus" [search]="true"
                (click)="addPaymentMethodParameters(paymentMethodParameter.parameter, paymentMethodParameter.value)" text="Adicionar"></gp-spinner-button>
        </gp-form-col>
    </gp-form-row>


    <gp-form-row *ngIf="hasPaymentMethodParameters">
        <gp-form-col cols="12">
            <gp-grid [rows]="paymentMethod.parameters" [columns]="['Parametro','Valor']"
                [fields]="['parameter','value']" [showDelete]="true" [showEdit]="false" [pageSize]="20"
                [showActive]="false" [showPagination]="false"
                (onDelete)="deletePaymentMethodParameters($event)"
                [showTotalPages]="false" (onPageChanged)="onPageChanged($event)">
            </gp-grid>
        </gp-form-col>
    </gp-form-row>
</gp-card>

<gp-card [last]="true">
    <gp-form-row>
        <gp-form-col cols="12" [inputGroup]="false">
            <gp-spinner-button type="button" text="Salvar" [pink]="true" [disabled]="!isPaymentMethodValid"
                (click)="linkPaymentMethodToCampaign()" marginLeft="5px" pull="right"></gp-spinner-button>
        </gp-form-col>
    </gp-form-row>
</gp-card>