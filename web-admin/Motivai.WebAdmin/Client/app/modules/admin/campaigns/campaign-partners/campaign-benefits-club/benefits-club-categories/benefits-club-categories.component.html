<gp-alert #alert [overlay]="true"></gp-alert>
<gp-card [first]="true" *ngIf="newCategory === true">
    <gp-form-row>
        <gp-form-col cols="12 4 4">
            <gp-simple-input label="Nome da categoria" [required]="true" erroMessage="Nome do Parceiro obrigatório!">
                <input type="text" class="form-control" name="name" [(ngModel)]="partnerCategory.name" />
            </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 6 6">
            <label>Escolha o Ícone do parceiro</label>
            <gp-select name="icons" styleClass="font-awesome" [items]="partnersIcons"
                placeholder="Selecione um ícone" (data)="selectIcon($event)" [(ngModel)]="partnerCategory.icon">
            </gp-select>
        </gp-form-col>

        <gp-form-col cols="12 1 1">
            <br />
            <i [class]="renderIconClass"></i>
        </gp-form-col>

        <gp-form-col cols="12 1 1">
            <div>
                <label>Ativo:</label>
            </div>
            <gp-switch name="active" [(ngModel)]="partnerCategory.active"></gp-switch>
        </gp-form-col>
    </gp-form-row>
    <gp-form-row>
        <gp-form-col cols="12 12 12">
            <gp-spinner-button type="button" [pink]="true" text="Salvar Categoria" icon="send" marginRight="5px" pull="right"
                (click)="save()" [loading]="loading">
            </gp-spinner-button>

            <gp-spinner-button type="button" [actionBack]="true" text="voltar" marginRight="5px" pull="right" icon="arrow-left"
                (click)="cancel()" [loading]="loading">
            </gp-spinner-button>
        </gp-form-col>
    </gp-form-row>
</gp-card>

<div *ngIf="newCategory === false">
    <gp-card [first]="true">
        <div class="row">
            <div class="col-md-12">
                <gp-spinner-button type="button" text="Nova Categoria" [actionSecondary]="true" pull="right"
                    [loading]="loading" (click)="showNewCategory()">
                </gp-spinner-button>
            </div>
        </div>
    </gp-card>

    <gp-card title="Lista de Categorias Cadastradas">
        <gp-form-row>
            <gp-form-col cols="12 12 12">
                <gp-grid name="categoriesGrid" [rows]="categories" [loading]="loading" [columns]="['Categoria']"
                    [fields]="['name']" [showActive]="true" [showPagination]="false" [showEdit]="true" [showDelete]="false"
                    (onEdit)="edit($event)">
                </gp-grid>
            </gp-form-col>
        </gp-form-row>
    </gp-card>
</div>
