<gp-card title="Filtros" first="true">
  <gp-form-row>
    <gp-form-col cols="12 4 4">
      <gp-simple-input label="CPF/CNPJ">
        <gp-input-mask name="document" mask="00000000000000" [(ngModel)]="parameters.userDocument"></gp-input-mask>
      </gp-simple-input>
    </gp-form-col>

    <gp-form-col cols="12 4 4">
      <gp-simple-input label="Número do Pedido">
        <input type="text" class="form-control" name="orderNumber" [(ngModel)]="parameters.orderNumber" />
      </gp-simple-input>
    </gp-form-col>

    <gp-form-col cols="12 4 4">
      <gp-simple-input label="Status do Pedido">
        <select class="form-control" name="status" [(ngModel)]="parameters.status">
          <option value=''>Todos</option>
          <option value="RECEIVED">Recebido</option>
          <option value="PROCESSING">Processando</option>
          <option value="COMPLETED">Processado</option>
          <option value="PROCESSED_WITH_ERROR">Processado com erro</option>
          <option value="REFUNDED">Estornado</option>
          <option value="CANCELED">Cancelado</option>
        </select>
      </gp-simple-input>
    </gp-form-col>
  </gp-form-row>

  <gp-form-row>
    <gp-datepicker cols="12 6" label="Data do pedido (de)" name="dateFrom"
      [(ngModel)]="parameters.dateFrom"></gp-datepicker>
    <gp-datepicker cols="12 6" label="Data do pedido (até)" name="dateTo"
      [(ngModel)]="parameters.dateTo"></gp-datepicker>
  </gp-form-row>

  <gp-form-row>
    <gp-form-col cols="12 12 12" [inputGroup]="false">
      <gp-spinner-button [actionSearch]="true" text="Pesquisar" (click)="loadDynamicOrders()" marginRight="5px" pull="right">
      </gp-spinner-button>

      <gp-spinner-button type="button" icon="download" marginRight="5px" pull="right" text="Exportar"
        [loading]="exporting" loadingText="Exportando..." (click)="onExport()">
      </gp-spinner-button>
    </gp-form-col>
  </gp-form-row>

</gp-card>

<gp-card title="Detalhes Dinâmicos" [last]="true">
  <gp-form-row>
    <gp-form-col cols="12 12 12">
      <gp-grid name="grid" [loading]="loading" [rows]="dynamicOrders"
        [columns]="[ 'Data do pedido', 'Numero do pedido', 'Participante', 'CPF/CNPJ', 'Valor do pedido', 'Status']"
        [fields]="['requestDate', 'orderNumber', 'userName', 'userDocument', 'totalAmount', 'status']"
        [showPagination]="true" [showTotalPages]="false" [pageSize]="limit" [pageSize]="20" [showDelete]="false"
        (onEdit)="onSelect($event)" (onPageChanged)="pageChanged($event)">
      </gp-grid>
    </gp-form-col>
  </gp-form-row>
  <div class="row col-md-12">
    <gp-alert [overlay]="true" #gpAlert></gp-alert>
  </div>
</gp-card>