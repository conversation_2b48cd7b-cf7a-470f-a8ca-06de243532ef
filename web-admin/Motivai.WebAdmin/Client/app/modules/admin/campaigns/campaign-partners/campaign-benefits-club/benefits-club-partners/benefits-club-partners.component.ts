import { Component, OnInit, ViewChild, Input, Output, EventEmitter } from '@angular/core';
import { SelectComponent } from 'ng2-select';

import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpFileUploadComponent } from '../../../../../../shared/components/gp-fileupload/gp-fileupload.component';
import { CampaignStore } from '../../../campaign.store';
import { GpGridComponent } from '../../../../../../shared/components/gp-grid/gp-grid.component';
import { BenefitsClubService } from '../benefits-club.service';
import { Item } from '../../../../../../shared/models/item';
import { Subscription } from 'rxjs';

@Component({
    selector: 'benefits-club-partners',
    templateUrl: './benefits-club-partners.component.html'
})
export class BenefitsClubPartnersComponent implements OnInit {
    @ViewChild('alert') alert: GpAlertComponent;
    @ViewChild('categorySelect') categorySelect: SelectComponent;
    @ViewChild('gpLogo') gpLogo: GpFileUploadComponent;
    @ViewChild('gpConceptImage') gpConceptImage: GpFileUploadComponent;
    @Output('onEdit') onEdit: EventEmitter<any> = new EventEmitter();
    @ViewChild('grid') grid: GpGridComponent;

    private _partnerId: string;
    private _campaign$: Subscription;
    public campaignId: string;

    newPartner: boolean = false;
    selectedCat: any = {
        category: []
    };

    partner: any = {
        categories: [],
        socialNetworks: {},
        maxDiscountType: '',
        maxCashbackType: '',
        regulaments: [],
        segmentations: {},
        partnerIntegration: {}
    };

    activeCategories: Item[] = [];
    linkedCategories: any[] = [];

    selectedCategoryId: string;

    categories: any[] = [];

    logoUploadPath: string = '';
    loading: boolean = false;
    skip: number = 0;
    limit: number = 15;
    partners: any[] = [];
    category: string;
    filterParams = {
        name: '',
        category: {
            id: ''
        }, 
        active: false
    };
    maxDiscountTypeItems: Array<Item> = [Item.of('PERCENTAGE', 'Desconto por Percentual'),
    Item.of('CURRENCY', 'Desconto por Reais')];

    maxCashbackTypeItems: Array<Item> = [Item.of('PERCENTAGE', 'Cashback por Percentual'),
    Item.of('CURRENCY', 'Cashback por Reais')];

    processTypeItems: Array<Item> = [
        Item.of('OFFLINE', 'Offline'),
        Item.of('ONLINE', 'Online'),
        Item.of('BOTH', 'Ambos')
    ];

    activeItems: Array<Item> = [
        Item.of('ALL', 'Todos'),
        Item.of('true', 'Sim'),
        Item.of('false', 'Não'),
    ]

    constructor(private _service: BenefitsClubService, private _campaignStore: CampaignStore) { }

    get isListing() {
        return this.newPartner == false;
    }

    get isEditing() {
        return this.newPartner == true;
    }

    get isHighlightAtHome() {
      return this.partner.highlightAtHome == true;
    }

    get isMaxDiscountAmountTypePercentage() {
      return this.partner.maxDiscountType == 'PERCENTAGE';
    }
    
    get isMaxPartnerIntegrationDiscountAmountTypePercentage() {
      return this.partner.partnerIntegration.maxDiscountType == 'PERCENTAGE';
    }
    
    get isMaxCashbackAmountTypePercentage() {
      return this.partner.maxCashbackType == 'PERCENTAGE';
    }

    get isMaxPartnerIntegrationCashbackAmountTypePercentage() {
        return this.partner.partnerIntegration.maxCashbackType == 'PERCENTAGE';
    }

    get isProcessTypeOffline() {
        return this.partner.processType == 'OFFLINE' || this.partner.processType == 'BOTH';
    }

    get isProcessTypeOnline() {
        return this.partner.processType == 'ONLINE' || this.partner.processType == 'BOTH';
    }


    @Input() set partnerId(v: string) {
        if (v) {
            this._partnerId = v;
            this.findPartnerById();
        }
    }

    ngOnInit() {
        this.getPartners();
        this._campaign$ = this._campaignStore.asObservable.subscribe(campaignId => {
          this.campaignId = campaignId;
        });
    }

    prepareToSave() {
        if (this.linkedCategories) {
          this.partner.categoriesIds = this.linkedCategories.map(x => x.key);
        }
        this.save();
    }

    save() {
        this.loading = true;
        this._service.savePartnerInstitutional(this._campaignStore.id, this.partner.id, this.partner)
            .subscribe(
                _ => {
                    this.loading = false;
                    this.alert.showSuccess('Parceiro salvo com sucesso!');
                    this.back()
                },
                err => {
                    this.loading = false;
                    this.alert.showError(err);
                }
            );
    }

    findPartnerById() {
        this.loading = true;
        if (!this._partnerId) {
            this.loading = false;
            this.alert.showError('Id do parceiro não encontrado!');
        }
        if (this._partnerId) {
            this._service.getPartnersInstitutionalById(this._campaignStore.id, this._partnerId).subscribe(
                partner => {
                    if (!partner.regulaments){
                        partner.regulaments = []
                    }

                    if (!partner.segmentations) {
                        partner.segmentations = {};
                    }

                    if (!partner.processType) {
                        partner.processType = 'OFFLINE';
                    }

                    if (!partner.socialNetworks) {
                        partner.socialNetworks = {};
                    }

                    if (!partner.partnerIntegration) {
                        partner.partnerIntegration = {};
                    }
                    
                    if (partner.partnerIntegration.status) {
                        partner.partnerIntegration.formattedStatus = this.handlePartnerIntegrationStatus(partner.partnerIntegration.status);
                    }

                    this.partner = partner;
                    this.linkedCategories = this.partner.categories;
                    this.loading = false;
                },
                err => {
                    this.loading = false;
                    this.alert.showError(err);
                });
        }

    }

    getCategories() {
        this.loading = true;
        this._service.getCategories(this.campaignId).subscribe(
          categories => {
            this.categories = categories;
            this.activeCategories = categories.filter(x => x.active).map(category => Item.of(category.id, category.name));
            this.loading = false;
          },
          err => {
            this.alert.showError(err);
            this.loading = false;
          }
        );
    }

    linkCategory() {
        if (!this.selectedCategoryId) {
          return;
        }
    
        if (!this.linkedCategories)
          this.linkedCategories = [];
    
        const selectedCategory = this.activeCategories.find(i => i.id == this.selectedCategoryId);
        if (!selectedCategory) {
          return;
        }

        const categoryExists = this.linkedCategories.find(c => c.key === selectedCategory.id);
        if (categoryExists) {
          this.alert.showError('Categoria já vinculada.');
          return;
        }

        this.linkedCategories.push({ key: selectedCategory.id, value: selectedCategory.text });
    }

    unLinkCategory($event: any) {
        if ($event) {
            if (!this.linkedCategories) return;

            const category = this.linkedCategories.find(c => c.id === $event.id);
            if (!category) return;

            this.linkedCategories.splice(this.linkedCategories.indexOf(category), 1);
        }
    }

    clear() {
        this.partner = {
            categories: [],
            socialNetworks: {},
            maxDiscountType: '',
            maxCashbackType: '',
            regulaments: [],
            segmentations: {},
            partnerIntegration: {}
        };
        this.selectedCat = {};
        this.linkedCategories = [];
    }

    removePartnerBannerUrl() {
      this.partner.partnerBannerUrl = '';
    }

    removePartnerIntegrationBannerUrl() {
        this.partner.partnerIntegration.partnerBannerUrl = '';
    }

    removeLogoPartnerUrl() {
       this.partner.logo = '';
    }

    removeLogoPartnerIntegrationUrl() {
        this.partner.partnerIntegration.logo = '';
     }

    showNewPartner() {
        this.partner = {
            socialNetworks: {},
            partnerIntegration: {}
        };
        this.newPartner = true;
    }

    back() {
        this.newPartner = false;
        this.clear();
        this.getPartners();
    }

    getPartners(resetSearch: boolean = false) {
        if (resetSearch) {
            this.skip = 0;
            this.limit = 15;
            this.grid.resetPagination();
        }
        this.loading = true;
        this._service.getPartnersInstitutional(this._campaignStore.id, this.filterParams.name, this.filterParams.category.id, this.filterParams.active, this.skip, this.limit).subscribe(
            partners => {
                this.handlePartners(partners);
                this.partners = partners;
                this.loading = false;
            },
            err => {
                this.loading = false;
                this.alert.showError(err);
            });
    }

    handlePartners(partners: Array<any>):void {
       if(partners && partners.length > 0) {
        partners.forEach(p => {
            p.processTypeFormatted = this.handleProcessType(p.processType);
        })
       }
    }

    handleProcessType(processType: string): string {
       if (!processType) {
        return 'Necessário atualização';
       }

       switch (processType) {
        case 'OFFLINE':
            return 'Offline';
        case 'ONLINE':
            return 'Online';
        case 'BOTH':
            return 'Ambos';
        default:
            return processType;
       }
    }

    handlePartnerIntegrationStatus(status: string): string {
        if (!status) {
            return 'Status não mapeado';
        }

        switch (status) {
        case 'ACTIVE':
            return 'Ativo';
        case 'HIDDEN':
            return 'Inativo';
        default:
            return status;
        }
    }

    edit($event) {
        if ($event) {
            this._partnerId = $event.id;
            this.findPartnerById();
            this.newPartner = true;
        }
    }

    filterPartners() {
        if (!this.filterParams)
            this.alert.showInfo('Para filtrar insira o nome!');
        this.getPartners();
    }

    onPageChanged($event: any) {
        if ($event) {
            this.skip = $event.skip;
            this.limit = $event.limit;
            this.getPartners();
        }
    }
}
