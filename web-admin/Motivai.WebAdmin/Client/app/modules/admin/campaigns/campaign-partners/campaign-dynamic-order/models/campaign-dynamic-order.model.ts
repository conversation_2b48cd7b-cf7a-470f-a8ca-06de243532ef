import { NotificationTemplate } from '../../../campaign-site/raffle/models/CampaignRaffle.model';

export class CampaignDynamicOrderParametrization {
  id: string;
  name: string;
  active: boolean;
  productCode: string;
  description: string;
  descriptionDisplay: string;
  segmentations: Segmentations = new Segmentations();
  viewParametrizations: ViewParametrizations = new ViewParametrizations();
  orderParametrizations: OrderParametrizations = new OrderParametrizations();
}

export class ViewParametrizations {
  homeIconUrl: string;
  imageCardUrl: string;
  cardColor: string;
  instructions: Array<any> = [];
  processingResultMessage: ProcessingResultMessage = new ProcessingResultMessage();
}

export class Segmentations {
  enableByParticipantsGroups: boolean;
  participantsGroups: Array<any> = [];

  enableByParentsParticipants: boolean;
  parentsParticipants: Array<any> = [];
}

export class OrderParametrizations {
  feeAmount: number = 0;
  minimumAmount: number = 0;
  maximumAmount: number = 0;
  processingDays: number;

  enableNotificationTemplateOrdersReceived: boolean;
  notificationTemplateOrdersReceived: NotificationTemplate = new NotificationTemplate();
}

export class ProcessingResultMessage {
  title: string;
  subtitle: string;
}