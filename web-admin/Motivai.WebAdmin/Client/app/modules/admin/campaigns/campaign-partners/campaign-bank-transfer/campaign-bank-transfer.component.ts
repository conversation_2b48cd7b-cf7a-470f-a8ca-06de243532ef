import { Component, OnInit, ViewChild } from '@angular/core';

import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignPartnersService } from '../campaign-partners.service';
import { AuthStore } from '../../../../../core/auth/auth.store';
import { CampaignStore } from '../../campaign.store';
import { GeneralSettingsService } from '../../../general-settings/services/generalSettings.service';
import { Item } from '../../../../../shared/models/item';

@Component({
  selector: 'campaign-bank-transfer',
  templateUrl: 'campaign-bank-transfer.component.html'
})
export class CampaignBankTransferComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;

  loadingGeneralConfig: boolean = false;
  loadingCampaignConfig: boolean = false;
  sending: boolean = false;

  generalConfig: any = {};
  bankTransfer: any = {};

  transferMethods: Array<Item> = [Item.of('BANK_TRANSFER', 'TED'), Item.of('PIX', 'PIX'), Item.of('BOTH', 'Ambos')];

  constructor(
    private _authStore: AuthStore,
    private campaignStore: CampaignStore,
    private partnersService: CampaignPartnersService
  ) {}

  ngOnInit() {
    this.loadConfigs();
  }

  get loading() {
    return this.loadingCampaignConfig;
  }

  private loadConfigs() {
    this.loadingCampaignConfig = true;
    this.partnersService.getBankTransferConfiguration(this.campaignStore.id)
      .subscribe(
        bankTransfer => {
          this.loadingCampaignConfig = false;
          if (bankTransfer) {
            this.bankTransfer = bankTransfer;
          } else {
            this.bankTransfer = {};
          }
        },
        err => {
          this.loadingCampaignConfig = false;
          this.gpAlert.handleAndShowError(err);
        }
      );

    this.loadingGeneralConfig = true;
    this.partnersService.getGeneralBankTransferConfiguration(this.campaignStore.id)
      .subscribe(
        generalConfig => {
          this.loadingGeneralConfig = false;
          if (generalConfig) {
            this.generalConfig = generalConfig;
          } else {
            this.generalConfig = {};
          }
        },
        err => {
          this.loadingGeneralConfig = false;
          this.gpAlert.handleAndShowError(err);
        }
      );
  }

  private validateForm() {
    if (this.generalConfig.transferLimit < this.bankTransfer.transferLimit) {
      this.gpAlert.showWarning('Limite de transferência não pode ser maior que o valor máximo.');
      return false;
    }
    if (this.generalConfig.bankTransferFee > this.bankTransfer.bankTransferFee) {
      this.gpAlert.showWarning('Custo do TED não pode ser menor que o valor mínimo.');
      return false;
    }
    if (this.generalConfig.additionalFee > this.bankTransfer.additionalFee) {
      this.gpAlert.showWarning('Taxa adicional não pode ser menor que o valor mínimo.');
      return false;
    }
    if (this.generalConfig.governmentFee > this.bankTransfer.governmentFee) {
      this.gpAlert.showWarning('Imposto não pode ser menor que o valor mínimo.');
      return false;
    }
    return true;
  }

  saveConfig() {
    this.sending = true;
    if (!this.validateForm()) {
      this.sending = false;
      return;
    }
    this.partnersService.saveBankTransferConfiguration(this.campaignStore.id, this.bankTransfer).subscribe(
      result => {
        this.sending = false;
        if (result) {
          this.gpAlert.showSuccess('Configurações salva com sucesso.');
        } else {
          this.gpAlert.showWarning('Não foi possível salvar as configurações, por favor, tente novamente.');
        }
      },
      err => {
        this.sending = false;
        this.gpAlert.handleAndShowError(err);
      }
    );
  }
}
