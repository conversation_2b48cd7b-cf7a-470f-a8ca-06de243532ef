import { Component, Input, Output, EventEmitter, forwardRef } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

import { Item } from '../../../../../../shared/models/item';
import { CampaignService } from '../../../campaign.service';
import { CompanyService } from '../../../../companies/company/company.service';

@Component({
  selector: 'campaign-partner-sku-selector',
  templateUrl: 'campaign-partner-sku-selector.component.html',
  providers: [
    { provide: NG_VALUE_ACCESSOR, useExisting: forwardRef(() => CampaignPartnerSkuSelectorComponent), multi: true }
  ]
})
export class CampaignPartnerSkuSelectorComponent implements ControlValueAccessor {
  @Output('error') errorEmitter: EventEmitter<any> = new EventEmitter<any>();

  @Input() name: string;
  @Input() disabled: boolean = false;

  _campaignId: string;
  allPartners: boolean = false;
  campaignPartners: Array<Item>;

  loading: boolean = false;
  selectedParnerId: string;
  insertedSkuCode: string;

  product: any = { skuName: '' };
  stock: any = {};

  onChange: any = (_: any) => { };
  onTouched: any = () => { };

  constructor(private _campaignService: CampaignService, private _companyService: CompanyService) {}

  @Input('campaignId')
  set campaignId(campaignId: string) {
    if (!campaignId) return;
    this._campaignId = campaignId;
    this.loadPartners();
  }

  @Input('allPartners')
  set setAllPartners(allPartners: boolean) {
    this.allPartners = allPartners;
    this.loadPartners();
  }

  private handleError(err) {
    this.errorEmitter.emit(err);
  }

  private loadPartners() {
    if (this.allPartners) {
      this._companyService.getPartners()
        .subscribe(
          partners => {
            this.campaignPartners = partners.map(part => Item.of(part.id, part.name));
          },
          err => this.handleError(err)
        );

    } else {
      this._campaignService.getLinkedPartnersDataByCampaign(this._campaignId)
        .subscribe(
          linkedPartners => {
            this.campaignPartners = linkedPartners.map(part => Item.of(part.partnerId, part.partnerName));
          },
          err => this.handleError(err)
        );
    }
  }

  private setFeedback(text: string) {
    this.product.skuName = text;
  }

  private selectProduct(product: any) {
    this.product = product;
    this.onChange(product);
    this.onTouched();
  }

  searchSku() {
    this.loading = true;
    this.setFeedback('Pesquisando...');
    this._companyService.findPartnerProductBySkuCode(this.selectedParnerId, this.insertedSkuCode)
        .subscribe(
          product => {
            this.loading = false;
            if (product) {
              this.selectProduct({
                partnerId: product.partnerId,
                partnerName: product.partnerName,
                productId: product.id,
                skuId: product.skuId,
                skuCode: product.skuCode,
                skuName: product.name
              });
            } else {
              this.setFeedback('Produto não encontrado pelo código SKU informado.');
            }
          },
          err => this.handleError(err)
        );
  }

  registerOnChange(fn) {
    this.onChange = fn;
  }

  registerOnTouched(fn) {
    this.onTouched = fn;
  }

  writeValue(value) {
    if (value === this.product) {
      return;
    }
    if (!value || !value.skuName || !value.skuId) {
      this.product = { skuName: '' };
      this.selectedParnerId = null;
      this.insertedSkuCode = '';
    } else {
      this.product = value;
      if (value) {
        this.selectedParnerId = value.partnerId;
        this.insertedSkuCode = value.skuCode;
      }
    }
  }
}
