<gp-form-validate [formGroup]="formGroup" [validationMessages]="messages" (onSubmit)="onFormSubmit($event)">
	<gp-card [first]="true" title="Configuração do Parceiro na Campanha">
		<div class="row">
			<gp-select-validate cols="12 12 4 4" formControlName="partnerId" label="Parceiro" [disabled]="isEdition()"
				[searchable]="true" [items]="partners"  placeholder="Parceiro">
			</gp-select-validate>
			<gp-mask-validate cols="12 6 4 4" [onlyDecimal]="true" [guide]="false" label="Taxa Administrativa" formControlName="administrativeTax">
			</gp-mask-validate>
			<gp-mask-validate cols="12 6 4 4" [onlyDecimal]="true" [guide]="false" label="Boost" formControlName="boost">
			</gp-mask-validate>
		</div>
		<div class="row">
			<div grid="12 4 3 3" [group]="true">
				<label>Ativo:</label>
				<div>
					<gp-switch formControlName="active"></gp-switch>
				</div>
			</div>
		</div>
	</gp-card>

	<gp-card title="E-mail para recebimento de pedidos do parceiro">
		<div class="row">
			<div grid="4 2 2">
				<label>Enviar e-mail</label>
				<div>
					<gp-switch formControlName="enableOrdersEmail"></gp-switch>
				</div>
			</div>
			<gp-input-validate cols="12 6 4" formControlName="receiverName" label="Destinatário" placeholder="Nome do destinátario"></gp-input-validate>
			<gp-input-validate cols="12 6 6" formControlName="receiverEmail" label="E-mail destinatário" placeholder="<EMAIL>"></gp-input-validate>
		</div>
	</gp-card>

	<gp-card title="Parâmetros da Integração" *ngIf="canEditPartnerIntegrationConfig">
		<div class="row">
			<gp-input-validate cols="12 6 4" formControlName="campaignIntegrationId" label="ID da Campanha" [required]="false"></gp-input-validate>
			<gp-mask-validate cols="12 6 4" mask="00.000.000/0000-00" [guide]="false" placeholder="00.000.0000/000-00"
				formControlName="cnpjIntegration" label="CNPJ da Integração" [required]="false"></gp-mask-validate>
			<gp-input-validate cols="12 6 4" formControlName="partnerIdentifier" label="Identificador Parceiro" [required]="false"></gp-input-validate>
		</div>
	</gp-card>
	
	<gp-card [last]="true">
		<div class="row col-md-12">
			<gp-spinner-button text="Novo" bootstrapClass="default" icon="plus" [disabled]="sending" (click)="newLink()"
				[showSpinner]="false" [loading]="sending"></gp-spinner-button>

			<gp-spinner-button type="submit" text="Salvar" [pink]="true" icon="send" [disabled]="formGroup.invalid || sending"
				[loading]="sending" loadingText="Salvando"></gp-spinner-button>
		</div>
		<div class="row col-md-12">
			<gp-alert #gpAlert></gp-alert>
		</div>
		<spinner [overlay]="true" [show]="loading"></spinner>
	</gp-card>
</gp-form-validate>
