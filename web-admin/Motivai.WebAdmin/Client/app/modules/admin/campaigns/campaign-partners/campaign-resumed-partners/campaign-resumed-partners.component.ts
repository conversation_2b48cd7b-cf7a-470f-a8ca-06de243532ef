import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CampaignService } from '../../campaign.service';
import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';


@Component({
    selector: 'campaign-resumed-partners',
    templateUrl: './campaign-resumed-partners.component.html'
})
export class CampaignResumedPartnersComponent implements OnInit {
    @ViewChild('alert') alert: GpAlertComponent;
    partners: any = [];
    campaignId: string = '';
    loadingPartners: boolean = false;
    loading: boolean = false;
    constructor(private _campaignService: CampaignService, private route: ActivatedRoute, private router: Router) { }

    ngOnInit(): void {
        if (this.route.parent != null) {
            this.route.parent.params.subscribe((params: any) => {
              this.campaignId = params['id'];
              this.getPartners();
            });
          } else {
            this.router.navigate(['/campanha']);
          }
    }
    savePartners() {
        if (!this.partners) return null;
        this.loadingPartners = true;
        this._campaignService.savePartnersCampaign(this.campaignId, this.partners).subscribe(
            response => {
                this.alert.showSuccess('Parceiro atualizado com sucesso!');
                this.loadingPartners = false;
            },
            err => {
                this.alert.showError(err);
                this.loadingPartners = false;
            }
        );
    }
    getPartners() {
        if (this.partners) {
            this.loading = true;
            this._campaignService.getLinkedPartnersByCampaign(this.campaignId).subscribe(
                response => { 
                    this.partners = response;
                    this.loading = false;
                },
                err => {
                    this.alert.showError(err);
                    this.loading = false;
                }
            );
        }
    }
}
