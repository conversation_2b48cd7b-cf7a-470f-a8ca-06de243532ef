import { Component, EventEmitter, OnInit, Output, ViewChild } from "@angular/core";
import { Subscription } from "rxjs";
import { CampaignPartnersService } from "../../campaign-partners.service";
import { CampaignStore } from "../../../campaign.store";
import { RxjsHelpers } from "../../../../../../shared/helpers/rxjs-helpers";
import { GpAlertComponent } from "../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component";

@Component({
	selector: 'campaign-payment-methods-list',
	templateUrl: './campaign-payment-methods-list.component.html'
})
export class CampaignPaymentMethodsListComponent implements OnInit {
	@ViewChild('alert') alert: GpAlertComponent;

	private _campaign$: Subscription;
	campaignPaymentMethods: any[];

	params: any = { skip: 0, limit: 20 };
	loading: boolean = false;

	@Output() edit: EventEmitter<any> = new EventEmitter();
	get campaignId() {
		return this._campaignStore.id;
	}

	constructor(private _partnerService: CampaignPartnersService, private _campaignStore: CampaignStore) { }

	ngOnInit() {
		this._campaign$ = this._campaignStore.asObservable
			.subscribe(id => {
				if (id)
					this.getCampaignPaymentMethods();
			});
	}

	ngOnDestroy() {
		RxjsHelpers.unsubscribe(this._campaign$);
	}

	getCampaignPaymentMethods() {
		this.loading = true;
		this._partnerService.getPaymentMethods(this.campaignId, this.params)
			.subscribe(
				response => {
					this.campaignPaymentMethods = response || [];
					this.loading = false;
				},
				err => {
					this.loading = false;
					this.alert.handleAndShowError(err);
				}
			);
	}

	private editPaymentMethod($event) {
		if ($event) {
			this.edit.emit($event);
		}
	}

	private inactivePaymentMethod($event) {
		this.loading = true;
		$event.active = false;
		this._partnerService.linkPaymentMethodToCampaign(this.campaignId, $event)
			.subscribe(response => {
				if (response) {
					this.getCampaignPaymentMethods();
					this.alert.showSuccess("Método de pagamento desabilitado.");
				}
				this.loading = false;
			},
			err => {
				this.loading = false;
				this.alert.handleAndShowError(err);
			});
	}

	onPageChanged(event: any): void {
		this.params.skip = event.skip;
		this.params.limit = event.limit;
		this.getCampaignPaymentMethods();
	}
}
