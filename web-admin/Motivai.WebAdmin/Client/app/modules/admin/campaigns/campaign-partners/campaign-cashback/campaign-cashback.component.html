<gp-alert [overlay]="true" #gpAlert></gp-alert>

<form>
	<gp-card title="Configurações do Cashback">
		<spinner [overlay]="true" [show]="loading"></spinner>

		<gp-form-row>
			<gp-form-col cols="12 6 3 2">
				<div class="form-group">
					<label>Habilitar cashback</label>
					<div>
						<gp-switch name="enableCashback" [(ngModel)]="cashbackParametrization.enableCashback"></gp-switch>
					</div>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 6 3">
				<gp-simple-input label="Porcentagem de cashback" [required]="true">
					<div class="input-group">
						<span class="input-group-addon">%</span>
						<gp-input-mask name="cashbackPercentage" [onlyDecimal]="true" [(ngModel)]="cashbackParametrization.cashbackPercentage" required></gp-input-mask>
					</div>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 6 3 2">
				<div class="form-group">
					<label>Limitar valor do cashback</label>
					<div>
						<gp-switch name="limitCashbackAmount" [(ngModel)]="cashbackParametrization.limitCashbackAmount"></gp-switch>
					</div>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 6 3">
				<gp-simple-input label="Limite do cashback (pontos)" [required]="true">
					<gp-input-mask name="cashbackAmountLimit" [onlyDecimal]="true" [(ngModel)]="cashbackParametrization.cashbackAmountLimit" required></gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card title="Prazo de Liberação do Cashback">
		<gp-form-row>
			<gp-form-col cols="12 6 3 2">
				<div class="form-group">
					<label>Habilitar prazo de liberação</label>
					<div>
						<gp-switch name="enablePointsBlockingDuration" [(ngModel)]="cashbackParametrization.enablePointsBlockingDuration"></gp-switch>
					</div>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 6 3">
				<gp-simple-input label="Prazo de liberação dos pontos (dias)" [required]="cashbackParametrization.enablePointsBlockingDuration">
					<gp-input-mask name="pointsBlockingDurationInDays" [onlyInteger]="true" [integers]="3"
						[disabled]="!cashbackParametrization.enablePointsBlockingDuration"
						[required]="cashbackParametrization.enablePointsBlockingDuration"
						[(ngModel)]="cashbackParametrization.pointsBlockingDurationInDays">
					</gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<campaign-points-expiration-editor title="Regra de Expiração do Cashback"
		[campaignId]="campaignId" [pointsExpirationRule]="cashbackParametrization.pointsExpirationRule">
	</campaign-points-expiration-editor>

	<gp-card title="Gatilhos do Cashback">
		<accordion [closeOthers]="true">
			<accordion-group panelClass="b0 mb-sm panel-default">
				<div accordion-heading>
					<label>Por Parceiro</label>
				</div>
				<div class="row">
					<div grid="12 6 6" [group]="true">
						<label>Parceiro:</label>
						<ng-select [allowClear]="true" [items]="campaignPartners" (data)="selectPartner($event)" placeholder="Selecione um parceiro">
						</ng-select>
					</div>
					<div grid="12 6 6" class="top-p2 pull-right">
						<gp-spinner-button type="button" [pink]="true" icon="plus" text="Adicionar" loadingText="Adicionando" pull="right"
							[disabled]="!filters.partnerId" (click)="addPartnerToFilter()">
						</gp-spinner-button>
					</div>
				</div>
				<div class="row">
					<div grid="12">
						<gp-grid name="partnersGrid" [rows]="cashbackParametrization.triggers.partnersFilters" [columns]="['Parceiro']" [fields]="['partnerName']" [loading]="false"
							[showActive]="false" [showEdit]="true" [showDelete]="true" emptyMessage="Nenhum parceiro no filtro."
							(onDelete)="removePartnerFromFilter($event)">
						</gp-grid>
					</div>
				</div>
			</accordion-group>
	
			<accordion-group panelClass="b0 mb-sm panel-default">
				<div accordion-heading>
					<label>Por Classificação (Departamento, Categoria e/ou Subcategoria)</label>
				</div>
				<div class="row">
					<div grid="12 3 3" [group]="true">
						<gp-simple-input label="Departamento">
							<gp-categories-select id="department" name="department" [active]="true" [level]="1" required [(ngModel)]="filters.departmentId">
							</gp-categories-select>
						</gp-simple-input>
					</div>
					<div grid="12 3 3" [group]="true">
						<gp-simple-input label="Categoria">
							<gp-categories-select id="category" name="category" [active]="true" [level]="2" emptyMessage="Sem categorias"
								[parentId]="filters.departmentId" [disabled]="!filters.departmentId" [(ngModel)]="filters.categoryId">
							</gp-categories-select>
						</gp-simple-input>
					</div>
					<div grid="12 3 3" [group]="true">
						<gp-simple-input label="Subcategoria">
							<gp-categories-select id="category" name="subcategory" [active]="true" [level]="3" emptyMessage="Sem subcategorias"
								[parentId]="filters.categoryId" [disabled]="!filters.categoryId" [(ngModel)]="filters.subcategoryId">
							</gp-categories-select>
						</gp-simple-input>
					</div>
					<div grid="12 3 3" class="top-p2 pull-right">
						<gp-spinner-button type="button" [pink]="true" icon="plus" text="Adicionar" loadingText="Adicionando" pull="right"
							[disabled]="!filters.departmentId" (click)="addDepartmentToFilter()">
						</gp-spinner-button>
					</div>
				</div>
				<div class="row">
					<div grid="12">
						<gp-grid name="departmentsGrid" [rows]="cashbackParametrization.triggers.categoriesFilters"
							[columns]="['Departamento', 'Categoria', 'Subcategoria']" [fields]="['departmentName', 'categoryName', 'subcategoryName']"
							[loading]="false" [showActive]="false" [showEdit]="false" [showDelete]="true" emptyMessage="Nenhum departamento no filtro."
							(onDelete)="removeDepartmentFromFilter($event)">
						</gp-grid>
					</div>
				</div>
			</accordion-group>
	
			<accordion-group panelClass="b0 mb-sm panel-default">
				<div accordion-heading>
					<label>Por SKU</label>
				</div>

				<campaign-partner-sku-selector [campaignId]="campaignId"></campaign-partner-sku-selector>

				<gp-form-row>
					<gp-form-col cols="12">
						<gp-spinner-button type="button" [pink]="true" icon="plus" text="Adicionar" loadingText="Adicionando" pull="right"
							(click)="addProductToFilter()">
						</gp-spinner-button>
					</gp-form-col>
				</gp-form-row>
				<gp-form-row>
					<gp-form-col cols="12">
						<gp-grid name="productsGrid" [rows]="cashbackParametrization.triggers.skusFilters"
							[columns]="['Parceiro', 'Código SKU', 'Produto']" [fields]="['partnerName', 'skuCode', 'productName']"
							[loading]="false" [showActive]="false" [showEdit]="false" [showDelete]="true" emptyMessage="Nenhum produto no filtro."
							(onDelete)="removeProductFromFilter($event)">
						</gp-grid>
					</gp-form-col>
				</gp-form-row>
			</accordion-group>
		</accordion>
	</gp-card>

	<gp-card [last]="true">
		<div class="row">
			<div class="col-md-12">
				<gp-spinner-button type="button" [pink]="true" text="Salvar" icon="send" pull="right"
					(click)="saveConfig()" [loading]="sending"></gp-spinner-button>
			</div>
		</div>
	</gp-card>
</form>
