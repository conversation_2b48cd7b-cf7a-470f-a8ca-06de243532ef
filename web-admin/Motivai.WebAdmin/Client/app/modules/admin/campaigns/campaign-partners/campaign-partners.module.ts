import { NgModule, LOCALE_ID, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { DndModule } from 'ng2-dnd'

import { SharedModule } from '../../../../shared/shared.module';
import { CampaignP2pTransfersComponent } from './campaign-p2p-transfers/campaign-p2p-transfers.component';
import { CampaignPartnersService } from './campaign-partners.service';
import { CampaignBillPaymentParametrizationEditComponent } from './campaign-bill-payment/campaign-bill-payment-parametrization/campaign-bill-payment-parametrization-edit/campaign-bill-payment-parametrization-edit.component';
import { CampaignBillPaymentParametrizationListComponent } from './campaign-bill-payment/campaign-bill-payment-parametrization/campaign-bill-payment-parametrization-list/campaign-bill-payment-parametrization-list.component';
import { CampaignBillPaymentParametrizationViewComponent } from './campaign-bill-payment/campaign-bill-payment-parametrization/campaign-bill-payment-parametrization-view.component';
import { CampaignBillPaymentViewComponent } from './campaign-bill-payment/campaign-bill-payment-parametrization/campaign-bill-payment-view.component';
import { CampaignBankTransferComponent } from './campaign-bank-transfer/campaign-bank-transfer.component';
import { BenefitsClubViewComponent } from './campaign-benefits-club/benefits-club-view/benefits-club-view.component';
import { GpCampaignGatewayComponent } from './campaign-buypoints-gateways/campaign-gateway.component';
import { CampaignCardSettingsComponent } from './campaign-card/campaign-card.component';
import { CampaignCashbackComponent } from './campaign-cashback/campaign-cashback.component';
import { CampaignMobileRechargeViewComponent } from './campaign-mobile-recharge/campaign-mobile-recharge-view/campaign-mobile-recharge-view.component';
import { CampaignPartnersViewComponent } from './campaign-partners-view/campaign-partners-view.component';
import { CampaignPartnersListingComponent } from './campaign-product-partners/campaign-partners-listing/campaign-partners-listing.component';
import { CampaignPartnerEditorComponent } from './campaign-product-partners/campaign-partner-editor/campaign-partner-editor.component';
import { GpCampaignGatewayListComponent } from './campaign-buypoints-gateways/campaign-gateway-list/campaign-gateway-list.component';
import { GpCampaignGatewayEditComponent } from './campaign-buypoints-gateways/campaign-gateway-edit/campaign-gateway-edit.component';
import { CampaignMobileRechargeEditComponent } from './campaign-mobile-recharge/campaign-mobile-recharge-edit/campaign-mobile-recharge-edit.component';
import { CampaignMobileRechargeListComponent } from './campaign-mobile-recharge/campaign-mobile-recharge-list/campaign-mobile-recharge-list.component';
import { BenefitsClubCategoriesComponent } from './campaign-benefits-club/benefits-club-categories/benefits-club-categories.component';
import { BenefitsClubPartnersComponent } from './campaign-benefits-club/benefits-club-partners/benefits-club-partners.component';
import { BenefitsClubPromotionsComponent } from './campaign-benefits-club/benefits-club-promotions/benefits-club-promotions.component';
import { ColorPickerModule } from 'ngx-color-picker';
import { CampaignDynamicOrderParametrizationEditComponent } from './campaign-dynamic-order/features/campaign-dynamic-order-parametrizations/campaign-dynamic-order-parametrizations-edit/campaign-dynamic-order-parametrizations-edit.component';
import { CampaignDynamicOrderParametrizationViewComponent } from './campaign-dynamic-order/features/campaign-dynamic-order-parametrizations/campaign-dynamic-order-parametrizations-view/campaign-dynamic-order-parametrizations-view.component';
import { CampaignDynamicOrderParametrizationListComponent } from './campaign-dynamic-order/features/campaign-dynamic-order-parametrizations/campaign-dynamic-order-parametrizations-list/campaign-dynamic-order-parametrizations-list.component';
import { CampaignDynamicOrderViewComponent } from './campaign-dynamic-order/campaign-dynamic-order-view/campaign-dynamic-order-view.component';
import { CampaignsDynamicOrdersViewComponent } from './campaign-dynamic-order/features/campaigns-dynamic-orders/campaigns-dynamic-orders-view/campaigns-dynamic-orders-view.component';
import { CampaignsDynamicOrdersListComponent } from './campaign-dynamic-order/features/campaigns-dynamic-orders/campaigns-dynamic-orders-list/campaigns-dynamic-orders-list.component';
import { CampaignsDynamicOrdersEditComponent } from './campaign-dynamic-order/features/campaigns-dynamic-orders/campaigns-dynamic-orders-edit/campaigns-dynamic-orders-edit.component';
import { CampaignPaymentMethodsListComponent } from './campaign-payment-methods/campaign-payment-methods-list/campaign-payment-methods-list.component';
import { CampaignPaymentMethodsEditComponent } from './campaign-payment-methods/campaign-payment-methods-edit/campaign-payment-methods-edit.component';
import { CampaignPaymentMethodsViewComponent } from './campaign-payment-methods/campaign-payment-methods-view.component';
import { GeneralSettingsService } from '../../general-settings/services/generalSettings.service';

const COMPONENTS = [
  CampaignP2pTransfersComponent,
  CampaignBillPaymentParametrizationViewComponent,
  CampaignBillPaymentParametrizationListComponent,
  CampaignBillPaymentParametrizationEditComponent,
  CampaignBillPaymentViewComponent,
  CampaignPartnersViewComponent,
  CampaignPartnersListingComponent,
  GpCampaignGatewayComponent,
  GpCampaignGatewayListComponent,
  GpCampaignGatewayEditComponent,
  CampaignMobileRechargeViewComponent,
  CampaignMobileRechargeEditComponent,
  CampaignMobileRechargeListComponent,
  BenefitsClubViewComponent,
  BenefitsClubPromotionsComponent,
  BenefitsClubPartnersComponent,
  BenefitsClubCategoriesComponent,
  CampaignCardSettingsComponent,
  CampaignCashbackComponent,
  CampaignBankTransferComponent,
  CampaignPartnerEditorComponent,
  CampaignDynamicOrderParametrizationEditComponent,
  CampaignDynamicOrderParametrizationViewComponent,
  CampaignDynamicOrderParametrizationListComponent,
  CampaignDynamicOrderViewComponent,
  CampaignsDynamicOrdersViewComponent,
  CampaignsDynamicOrdersListComponent,
  CampaignsDynamicOrdersEditComponent,
  CampaignPaymentMethodsViewComponent,
  CampaignPaymentMethodsListComponent,
  CampaignPaymentMethodsEditComponent
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    DndModule,
    ColorPickerModule,
    SharedModule.forRoot(),
    RouterModule.forChild([
      {
        path: '', component: CampaignPartnersViewComponent,
        children: [
          { path: 'produtos', component: CampaignPartnersListingComponent },
          { path: 'comprapontos', component: GpCampaignGatewayComponent },
          { path: 'recargacelular', component: CampaignMobileRechargeViewComponent },
          {
            path: 'paguecontas', component: CampaignBillPaymentViewComponent,
            children: [
              { path: 'gerenciamento', component: CampaignBillPaymentParametrizationViewComponent },
              // { path: 'filtros', component: CampaignVouchersStocksReportComponent },
            ]
          },
          { path: 'clube-beneficios', component: BenefitsClubViewComponent },
          { path: 'resgate-dinamico', component: CampaignDynamicOrderViewComponent,
            children: [
                { path: 'parametrizacao', component: CampaignDynamicOrderParametrizationViewComponent },
                { path: 'pedidos-efetuados', component: CampaignsDynamicOrdersViewComponent },
                { path: '', redirectTo: 'parametrizacao', pathMatch: 'full'}
            ] 
          },
          { path: 'cartao-pre-pago', component: CampaignCardSettingsComponent },
          { path: 'cashback', component: CampaignCashbackComponent },
          { path: 'transferencias-bancarias', component: CampaignBankTransferComponent },
          { path: 'transferencias-p2p', component: CampaignP2pTransfersComponent },
          { path: 'metodos-pagamento', component: CampaignPaymentMethodsViewComponent }
        ]
      },
    ])
  ],
  declarations: COMPONENTS,
  providers: [
    { provide: LOCALE_ID, useValue: 'pt-br' },
    CampaignPartnersService,
    GeneralSettingsService
  ],
  exports: [
    RouterModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class CampaignPartnersModule { }
