import { Component, EventEmitter, OnInit, Output, ViewChild } from '@angular/core';
import { GpAlertComponent } from '../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { Subscription } from 'rxjs';
import { CampaignPartnersService } from '../../../../campaign-partners.service';
import { CampaignStore } from '../../../../../campaign.store';
import { handlerOrder, handlerOrders } from '../../../models/campaing-dynamic-order.helper';

@Component({
  selector: 'campaigns-dynamic-orders-list',
  templateUrl: './campaigns-dynamic-orders-list.component.html'
})
export class CampaignsDynamicOrdersListComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @Output('select') selectEmitter: EventEmitter<any> = new EventEmitter<any>();

  campaignId: string = '';
  private _campaign$: Subscription;
  loading: boolean = false;

  dynamicOrders: Array<any> = [];
  parameters: any = {
    dateTo: new Date(),
    dateFrom: new Date()
  };
  exporting: boolean = false;

  skip: number = 0;
  limit: number = 20;

  constructor(private _campaignStore: CampaignStore, private _campaignPartnersService: CampaignPartnersService) {}

  ngOnInit() { 
    this.campaignId = this._campaignStore.id;
    this.loadDynamicOrders();
  }

  onSelect(row: any) {
    this.selectEmitter.emit(row);
  }

  private handleError(err) {
    this.gpAlert.showError(err || 'Ocorreu um erro ao efetuar a operação.');
    this.loading = false;
  }

  loadDynamicOrders() {
    if (!this.campaignId) return;
    this.loading = true;
    this.gpAlert.clear();
    this._campaignPartnersService.loadDynamicOrder(this.campaignId, this.parameters, this.skip, this.limit).subscribe(
      dynamicOrders => {
        this.loading = false;
        if (dynamicOrders) {
          this.dynamicOrders = handlerOrders(dynamicOrders);
        } else {
          this.dynamicOrders = [];
        }
      },
      err => this.handleError(err)
    );
  }

  private pageChanged($event) {
    if ($event) {
      this.skip = $event.skip;
      this.limit = $event.limit;
      this.loadDynamicOrders();
    }
  }

  onExport() {
    this.exporting = true;
    this._campaignPartnersService.exportDynamicOrdersDetails(this.campaignId, this.parameters).subscribe(
      response => {
        window.open(response, '_blank');
        this.exporting = false;
      },
      err => {
        this.exporting = false;
        this.gpAlert.handleAndShowError(err);
      }
    );
  } 
}
