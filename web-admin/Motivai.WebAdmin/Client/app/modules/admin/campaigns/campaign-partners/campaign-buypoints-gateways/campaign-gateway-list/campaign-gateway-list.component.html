<gp-card [first]="true" [last]="true">
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<gp-grid name="grid" [loading]="loading"
				[rows]="payments" [columns]="columns" [fields]="fields"
				[showActive]="true" [showPagination]="false" [showEdit]="true" [showDelete]="true"
				activeField="isMaster" activeText="Sim" inactiveText="Não" activeHeaderText="Principal"
				(onEdit)="onEdit($event)" (onDelete)="onDelete($event)" (onPageChanged)="onPageChanged($event)">
			</gp-grid>
		</gp-form-col>
	</gp-form-row>

	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<button type="button" class="btn btn-default" [routerLink]="['/campanha', _campaignId]">Voltar</button>
		</gp-form-col>
	</gp-form-row>

	<div class="row col-md-12">
		<gp-alert #gpAlert></gp-alert>
	</div>
</gp-card>