<form #gatewayForm="ngForm" (ngSubmit)="gatewaySubmit()" novalidate>
	<gp-card [first]="true" title="Parâmetros de Integração">
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-simple-input label="Empresa para Integração" [required]="true" errorMessage="Selecione a empresa para integração">
					<select id="company" name="company" required class="form-control" [(ngModel)]="settings.gatewayCompany">
						<option value="" selected>Selecione</option>
						<option [value]='company.Pagarme.key'>{{ company.Pagarme.value }}</option>
						<!-- <option [value]='company.Mundipagg.key'>{{ company.Mundipagg.value }}</option>
						<option [value]='company.Blu.key'>{{ company.Blu.value }}</option> -->
					</select>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<div class="form-group">
					<label>Public Key <span style="color: red" tooltip="Chave pública de acesso ao gateway que será usada pelo catálogo para gerar o Token do cartão">(?)</span></label>
					<input type="text" id="publicKey" name="publicKey" class="form-control" [(ngModel)]="settings.publicKey" />
				</div>
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<div class="form-group">
					<label>Private Key <span style="color: red" tooltip="Chave privada de acesso ao gateway, utilizada para autenticação nas APIs">(?)</span></label>
					<input type="text" id="privateKey" name="privateKey" class="form-control" [(ngModel)]="settings.privateKey" />
				</div>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<div class="form-group">
					<label>Chave de Loja/Recebedor <span style="color: red" tooltip="Chave de identificação da Loja/Recebedor no gateway de pagamento">(?)</span></label>
					<input type="text" id="merchantKey" name="merchantKey" class="form-control" required [(ngModel)]="settings.merchantKey" />
				</div>
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<label>Gateway Principal</label>
				<div>
					<label class="switch switch-md">
						<input type="checkbox" checked="checked" id="isMaster" name="isMaster" [(ngModel)]="settings.isMaster" /><span></span>
					</label>
				</div>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card title="Taxas e Impostos">
		<gp-form-row>
			<gp-form-col cols="12 4 4">
				<div class="form-group">
					<label>Valor por transação (R$)</label>
					<div class="input-group">
						<span class="input-group-addon">R$</span>
						<gp-input-mask 
							id="transactionCurrencyValue" 
							name="transactionCurrencyValue" 
							[onlyDecimal]="true" 
							[(ngModel)]="settings.transactionCurrencyValue">
						</gp-input-mask>
					</div>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 4 4">
				<div class="form-group">
					<label>Taxa de antecipação (%)</label>
					<div class="input-group">
						<span class="input-group-addon">%</span>
						<gp-input-mask 
							id="anticipationAmount" 
							name="anticipationAmount" 
							[onlyDecimal]="true" 
							[(ngModel)]="settings.anticipationFee">
						</gp-input-mask>
					</div>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 4 4">
				<div class="form-group">
					<label>Custo do TED (R$)</label>
					<div class="input-group">
						<span class="input-group-addon">R$</span>
						<gp-input-mask 
							id="bankTransferFee" 
							name="bankTransferFee" 
							[onlyDecimal]="true" 
							[(ngModel)]="settings.bankTransferFee">
						</gp-input-mask>
					</div>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 4 4">
				<div class="form-group">
					<label>Taxa adicional (%)</label>
					<div class="input-group">
						<span class="input-group-addon">%</span>
						<gp-input-mask 
							id="additionalFee" 
							name="additionalFee" 
							[onlyInteger]="true" 
							[(ngModel)]="settings.additionalFee">
						</gp-input-mask>
					</div>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 4 4">
				<div class="form-group">
					<label>Imposto (%)</label>
					<div class="input-group">
						<span class="input-group-addon">%</span>
						<gp-input-mask 
							id="governmentFee" 
							name="governmentFee" 
							[onlyDecimal]="true" 
							[(ngModel)]="settings.governmentFee">
						</gp-input-mask>
					</div>
				</div>
			</gp-form-col>
		</gp-form-row>

		<h4>Parcelamento</h4>
		<br />

		<gp-form-row>
			<gp-form-col cols="12 4 4">
				<gp-simple-input label="Número da Parcela">
					<gp-input-mask 
						id="installmentKey" 
						name="installmentKey" 
						[disabled]="installmentKey"
						[onlyInteger]="true" 
						[(ngModel)]="installment.key">
					</gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 4 4">
				<div class="form-group">
					<label>Taxa (%)</label>
					<div class="input-group">
						<span class="input-group-addon">%</span>
						<gp-input-mask 
							id="installmentValue" 
							name="installmentValue" 
							[onlyDecimal]="true" 
							[(ngModel)]="installment.value">
						</gp-input-mask>
					</div>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 2 2">
				<gp-spinner-button type="button" [pink]="true"
					[text]="installmentKey ? 'Salvar Taxa' : 'Adicionar Taxa'" 
					[icon]="installmentKey ? 'send' : 'plus'"
					loadingText="Processando" size="lg" pull="right"
					[loading]="loadingInstallment" [disabled]="!installment.key || !installment.value"
					(click)="saveInstallment()"
					marginTop="22px">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<table class="table table-striped" *ngIf="settings.installmentsFee && settings.installmentsFee.length > 0">
					<thead>
						<tr>
							<th width="25%">Parcela</th>
							<th>Taxa</th>
							<th width="100px">Ações</th>
						</tr>
					</thead>
					<tbody>
						<tr *ngFor="let i of settings.installmentsFee">
							<td>{{ i.key }} x</td>
							<td>{{ i.value }}%</td>
							<td>
								<button type="button" title="Editar" class="btn btn-default btn-sm" (click)="editInstallmentFee(i.key)">
									<i title="Editar" class="fa fa-pencil-square-o grid-edit"></i>
								</button>
								<button type="button" title="Excluir" class="btn btn-danger btn-sm grid-remove" (click)="deleteInstallmentFee(i.key)">
									<i title="Excluir" class="fa fa-times"></i>
								</button>
							</td>
						</tr>
					</tbody>
				</table>
				<span *ngIf="!settings.installmentsFee || settings.installmentsFee.length === 0">Nenhuma taxa de parcelamento adicionada</span>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card title="Anti Fraude">
		<gp-form-row>
			<gp-form-col cols="12 3 3">
				<label>Habilitar Anti Fraude</label>
				<div>
					<label class="switch switch-md">
						<input type="checkbox" id="enableAntiFraud" name="enableAntiFraud" [(ngModel)]="settings.enableAntiFraud" /><span></span>
					</label>
				</div>
			</gp-form-col>
		</gp-form-row>
		
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-input-radio name="antiFrautFeeType" [disabled]="true" [label]="feeType.Percentage.value" [radioValue]="feeType.Percentage.key" [(ngModel)]="settings.antiFraudFeeType"></gp-input-radio>
				<gp-input-radio name="antiFrautFeeType" [label]="feeType.Fixed.value" [radioValue]="feeType.Fixed.key" [(ngModel)]="settings.antiFraudFeeType"></gp-input-radio>
				<gp-input-radio name="antiFrautFeeType" [disabled]="true" [label]="feeType.Both.value" [radioValue]="feeType.Both.key" [(ngModel)]="settings.antiFraudFeeType"></gp-input-radio>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<div class="form-group">
					<label>Taxa (%)</label>
					<div class="input-group">
						<span class="input-group-addon">%</span>
						<gp-input-mask 
							id="antiFraudFee" 
							name="antiFraudFee" 
							[onlyDecimal]="true" 
							[(ngModel)]="settings.antiFraudFee">
						</gp-input-mask>
					</div>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<div class="form-group">
					<label>Taxa Fixa (R$)</label>
					<div class="input-group">
						<span class="input-group-addon">R$</span>
						<gp-input-mask 
							id="antiFraudTransactionCurrencyValue" 
							name="antiFraudTransactionCurrencyValue" 
							[onlyDecimal]="true" 
							[(ngModel)]="settings.antiFraudTransactionCurrencyValue">
						</gp-input-mask>
					</div>
				</div>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card title="Configurações do Comprar Pontos">
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<label>Habilitar Parcelamento</label>
				<div>
					<label class="switch switch-lg">
						<input type="checkbox" id="enableInstallment" name="enableInstallment" [(ngModel)]="settings.enableInstallment" /><span></span>
					</label>
				</div>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Número máximo de parcelas">
					<gp-input-mask 
						id="maxInstallNumber" 
						name="maxInstallNumber" 
						[disabled]="maxInstallNumber"
						[onlyInteger]="true" 
						[(ngModel)]="settings.maxInstallNumber">
					</gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<div class="form-group">
					<label>Valor mínimo para habilitar parcelamento (R$)</label>
					<div class="input-group">
						<span class="input-group-addon">R$</span>
						<gp-input-mask 
							id="minInstallCurrencyValue" 
							name="minInstallCurrencyValue" 
							[disabled]="minInstallCurrencyValue"
							[onlyDecimal]="true" 
							[(ngModel)]="settings.minInstallCurrencyValue">
						</gp-input-mask>
					</div>
				</div>
			</gp-form-col>			
		</gp-form-row>
		<hr />
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<label>Alterar fator de conversão</label>
				<div>
					<label class="switch switch-lg">
						<input type="checkbox" id="changeConversionFactor" name="changeConversionFactor" [(ngModel)]="settings.changeConversionFactor" /><span></span>
					</label>
				</div>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Fator de conversão da campanha">
					<input type="text" id="campaignConversionFactor" name="campaignConversionFactor" class="form-control" disabled="disabled" [(ngModel)]="campaignConversionFactor" />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Novo fator de conversão" [required]="settings.changeConversionFactor" errorMessage="Fator de conversão é obrigatório">
					<gp-input-mask 
						id="conversionFactor" 
						name="conversionFactor" 
						[onlyDecimal]="true" 
						[disabled]="!settings.changeConversionFactor"
						[required]="settings.changeConversionFactor"
						[(ngModel)]="settings.conversionFactor">
					</gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<hr />
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<label>Lote de pontos liberado</label>
				<div>
					<label class="switch switch-lg">
						<input type="checkbox" id="enableFreeBatch" name="enableFreeBatch" [(ngModel)]="settings.enableFreeBatch" /><span></span>
					</label>
				</div>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Lote (100, 200, ...)" [required]="!settings.enableFreeBatch" errorMessage="Lote é obrigatório caso não seja liberado">
					<gp-input-mask 
						id="batch" 
						name="batch" 
						[onlyInteger]="true" 
						[required]="!settings.enableFreeBatch"
						[disabled]="settings.enableFreeBatch"
						[(ngModel)]="settings.batch">
					</gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card title="Simulador de Preço">
		<gp-form-row>
			<gp-form-col cols="12 3 3">
				<gp-simple-input label="Valor em pontos">
					<gp-input-mask 
						id="batch" 
						name="batch" 
						[onlyDecimal]="true"
						[(ngModel)]="simulation.pointsValue">
					</gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 3 3">
				<gp-simple-input label="Parcelamento">
					<select class="form-control" name="simulationInstallment" [(ngModel)]="simulation.installment">
						<option *ngFor="let i of settings.installmentsFee" [value]="i.value">{{ i.key }}x</option>
					</select>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 1 1">
				<label style="margin-top: 33px">=</label>
			</gp-form-col>
			<gp-form-col cols="12 3 3">
				<gp-simple-input label="Total com taxas (R$)">
					<input type="text" class="form-control" name="simulationTotal" disabled [(ngModel)]="simulationTotal" />
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card [last]="true">
		<div class="row">
			<div class="col-sm-12 col-md-12">
				<gp-spinner-button type="submit" [pink]="true" icon="send"
					size="lg" pull="right" text="Salvar dados do gateway" 
					loadingText="Processando" [loading]="loading" [disabled]="!gatewayForm.valid">
				</gp-spinner-button>
			</div>
			<div class="col-md-12">
				<gp-alert #campaignAlert></gp-alert>
			</div>
		</div>
	</gp-card>
</form>
