import { PERMISSION_CAMPAIGNS_CRM_REGISTER_SEGMENT } from '../../../../../core/auth/access-points';
import { CampaignService } from '../../campaign.service';
import { ActivatedRoute, Router } from '@angular/router';
import { Component, OnInit } from '@angular/core';
import { AuthStore } from '../../../../../core/auth/auth.store';
import { PERMISSION_CAMPAIGNS_CRM_TARGET_AUDIENCE_VIEW, PERMISSION_CAMPAIGNS_CRM_EXPORT_PRODUCTS } from '../../../../../core/auth/access-points';

@Component({
    selector: 'campaign-crm-view',
    templateUrl: 'campaign-crm-view.component.html'
})
export class GpCampaignCrmViewComponent implements OnInit {
    campaignId: string;

    constructor(private route: ActivatedRoute, private router: Router,
        private _authStore: AuthStore, private _campaignService: CampaignService) { }

    ngOnInit() {
        if (this.route.parent != null) {
            this.route.parent.params.subscribe((params: any) => {
                this.campaignId = params['id'];
                if (!this.campaignId) {
                    this.router.navigate(['/campanha']);
                }
                this.verifyAccess();
            });
        } else {
            this.router.navigate(['/campanha']);
        }
    }

    get canAccessTargetAudiences() {
        return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_CRM_TARGET_AUDIENCE_VIEW);
    }

    get canAccessExportProducts() {
        return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_CRM_EXPORT_PRODUCTS);
    }

    get canAccesssSegmentRegister() {
        return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_CRM_REGISTER_SEGMENT);
    }

    verifyAccess() {
        if (!this.canAccessTargetAudiences && this.canAccessExportProducts) {
            this.router.navigate(['/campanha', this.campaignId, 'crm', 'extracao-de-produtos']);
        } else if (!this.canAccessTargetAudiences) {
            this.router.navigate(['/campanha']);
        }
    }
}
