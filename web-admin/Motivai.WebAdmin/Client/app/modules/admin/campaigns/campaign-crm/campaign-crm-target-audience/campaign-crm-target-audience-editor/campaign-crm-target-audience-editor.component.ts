import { Component, OnInit, Input, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NgForm } from '@angular/forms';

import { targetAudienceOf, targetAudienceErrorMessagesValidation, MetadataValueModel, MetadataValuesModel } from '../targetAudience';
import { GpCategorySelectComponent } from '../../../../categories/categories-util/categories-select.component';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { PERMISSION_CAMPAIGNS_CRM_TARGET_AUDIENCE_EDIT } from '../../../../../../core/auth/access-points';
import { AuthStore } from '../../../../../../core/auth/auth.store';
import { TargetAudienceService } from '../targetAudience.service';
import { CampaignService } from '../../../campaign.service';
import { ParticipantsGroupService } from '../../../campaign-participants/campaign-participants-groups/participants-groups.service';
import { Item } from '../../../../../../shared/models/item';

@Component({
  selector: 'campaign-crm-target-audience-editor',
  templateUrl: 'campaign-crm-target-audience-editor.component.html'
})
export class GpCampaignCrmTargetAudienceEditorComponent implements OnInit {
  @ViewChild('targetAudienceEditForm') targetAudienceEditForm: NgForm;
  @ViewChild('subcategory') subcategory: GpCategorySelectComponent;
  @ViewChild('department') department: GpCategorySelectComponent;
  @ViewChild('category') category: GpCategorySelectComponent;
  @ViewChild('gpGridAlert') gpGridAlert: GpAlertComponent;
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  // @ViewChild('existentGroupSelect') existentGroupSelect: String;
  @Input()
  campaignId: string;

  categoriesParameters: any = {};
  parameters: any = {};
  partners: any[] = [];
  loading: boolean = false;
  targetAudience = targetAudienceOf(undefined);
  validationMessages = targetAudienceErrorMessagesValidation();
  existent: boolean = false;
  existentGroupSelect: String;
  items: Array<Item> = new Array<Item>();
  metadataValues: MetadataValueModel[];
  metadataValuesModel: any;

  constructor(private route: ActivatedRoute
    , private authStore: AuthStore
    , private targetAudienceService: TargetAudienceService
    , private campaignService: CampaignService
    , private participantsGroupService: ParticipantsGroupService) {
  }

  ngOnInit() {
    this.loadCampaignIdAndPartners();
    this.loadMetadaHeadersDetail();
    this.getAllExistentGroups();
  }

  loadMetadaHeadersDetail() {
    this.targetAudienceService.getMetadaHeadersDetail(this.campaignId).subscribe(headers => {
      if (headers && headers.length) {
        this.metadataValues = new Array<MetadataValueModel>();
        this.metadataValuesModel = {};

        for (const header of headers) {
          const d = new Array<any>();
          d.map(c => {
            return {} as MetadataValuesModel;
          });

          const metadata = {
            property: header.property,
            header: header.header,
            values: header.values.map(value => {
              return {
                id: value,
                text: value
              } as MetadataValuesModel;
            })
          } as MetadataValueModel;

          this.metadataValuesModel[header.property] = [];

          let cols = header.header.length / 10;

          try {
            cols = Number.parseInt(cols.toString());
          } catch (error) {
            cols = 0;
          }

          if (cols >= 2 && cols <= 3) {
            metadata.cssCols = `4 4 4`;
          } else if (cols > 3)
            metadata.cssCols = `4 ${cols} ${cols}`;
          else
            metadata.cssCols = '4 3 3';

          this.metadataValues.push(metadata);
        }
      }
    });
  }

  loadCampaignIdAndPartners() {
    if (this.route.parent != null && this.route.parent.parent != null) {
      this.route.parent.parent.params.subscribe((params: any) => {
        this.campaignId = params['id'];
        this.getLinkedPartners();
      });
    }
  }

  resetFields() {
    this.parameters.categories = [];
    this.targetAudience.id = '';
    this.gpAlert.clear();
    this.gpGridAlert.clear();
    this.existent = false;

    Object.keys(this.targetAudienceEditForm.controls).forEach(key => {
      this.targetAudienceEditForm.controls[key].reset();
    });
  }

  getLinkedPartners() {
    if (this.campaignId) {
      this.campaignService.getLinkedPartnersDataByCampaign(this.campaignId)
        .subscribe(partners => {
          this.partners = partners.map(p => {
            return { partnerId: p.partnerId, partnerName: p.partnerName };
          });
        });
    }
  }

  handleError(err) {
    let errorMessage = (err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao efetuar a operação.');
    this.gpAlert.showError(errorMessage);
    this.loading = false;
  }

  setCategoriesGridEdit(categories: any[]) {
    if (categories && categories.length > 0) {
      if (!this.parameters.categories) this.parameters.categories = [];

      categories.forEach(c => {
        const line: any = {};
        line.departmentId = c.departmentId;
        line.departmentName = c.departmentName;

        if (c.categoryId) {
          line.categoryId = c.categoryId;
          line.categoryName = c.categoryName;
        }

        if (c.subcategoryId) {
          line.subcategoryId = c.subcategoryId;
          line.subcategoryName = c.subcategoryName;
        }

        this.parameters.categories.push(line);
      });
    }
  }

  addCategory() {
    this.gpGridAlert.clear();
    const line: any = {};
    if (!this.parameters.categories) this.parameters.categories = [];

    if (this.categoriesParameters.departmentId) {
      const selectedDepartment = this.department.categories.find(x => x.id === this.categoriesParameters.departmentId);
      if (selectedDepartment) {
        const existDepartment = this.parameters.categories.find(x => x.departmentId === this.categoriesParameters.departmentId);
        if (!existDepartment) {
          line.departmentId = this.categoriesParameters.departmentId;
          line.departmentName = selectedDepartment.name;
        } else if (!this.categoriesParameters.categoryId)
          this.gpGridAlert.showWarning('Departamento já está incluso.');
      }
    }

    if (this.categoriesParameters.categoryId) {
      const selectedCategory = this.category.categories.find(x => x.id === this.categoriesParameters.categoryId);
      if (selectedCategory) {
        const existDepartment = this.parameters.categories.find(x => x.departmentId === selectedCategory.departmentId);
        const existDepartmentCategory = this.parameters.categories.find(x => x.departmentId === selectedCategory.departmentId && (x.categoryId && x.categoryId !== this.categoriesParameters.categoryId));

        if (!existDepartment || existDepartmentCategory) {
          if (!this.categoriesParameters.subcategoryId || this.categoriesParameters.subcategoryId === '') {
            const existDepartamentSubCategory = this.parameters.categories.find(x => x.departmentId === selectedCategory.departmentId && x.categoryId === this.categoriesParameters.categoryId);

            if (existDepartamentSubCategory) {
              this.gpGridAlert.showWarning('Sub-Categoria já está inclusa à esta Categoria no departamento selecionado.');
            } else {
              line.categoryId = this.categoriesParameters.categoryId;
              line.categoryName = selectedCategory.name;

              if (existDepartmentCategory) {
                line.departmentId = existDepartmentCategory.departmentId;
                line.departmentName = existDepartmentCategory.departmentName;
              } else {
                const department = this.department.categories.find(x => x.id === this.categoriesParameters.departmentId);

                if (department) {
                  line.departmentId = department.id;
                  line.departmentName = department.name;
                }
              }
            }
          }
        } else if (!this.categoriesParameters.subcategoryId)
          this.gpGridAlert.showWarning('Categoria já está inclusa no departamento selecionado.');
      }
    }

    if (this.categoriesParameters.subcategoryId) {
      const selectedSubcategory = this.subcategory.categories.find(x => x.id === this.categoriesParameters.subcategoryId);

      if (selectedSubcategory) {
        const existCategory = this.parameters.categories.find(x => x.categoryId === selectedSubcategory.parentId);
        const _existCategory = this.parameters.categories.find(x => x.categoryId === selectedSubcategory.parentId && (x.subcategoryId && x.subcategoryId !== this.categoriesParameters.subcategoryId));

        if (!existCategory || _existCategory) {
          line.subcategoryId = this.categoriesParameters.subcategoryId;
          line.subcategoryName = selectedSubcategory.name;

          if (_existCategory) {
            line.categoryId = _existCategory.categoryId;
            line.categoryName = _existCategory.categoryName;
          } else {
            const category = this.category.categories.find(x => x.id === this.categoriesParameters.categoryId);

            if (category) {
              line.categoryId = category.id;
              line.categoryName = category.name;
            }
          }

          const department = this.department.categories.find(x => x.id === this.categoriesParameters.departmentId);
          if (department) {
            line.departmentId = department.id;
            line.departmentName = department.name;
          }
        } else
          this.gpGridAlert.showWarning('Sub-Categoria já está inclusa na Categoria selecionada.');
      }
    }

    if (line.departmentId || line.categoryId) {
      const existingCombination = this.parameters.categories.find(x => JSON.stringify(x) === JSON.stringify(line));
      if (!existingCombination) {
        this.parameters.categories.push(line);
      } else
        this.gpGridAlert.showWarning('Combinações de Categorias já inclusa.');
    }
  }

  deleteCategory($event) {
    const line = this.parameters.categories.find(x => JSON.stringify(x) === JSON.stringify($event));
    if (line) {
      this.parameters.categories.splice(this.parameters.categories.indexOf(line), 1);
    }
  }

  upsertCategories() {
    if (this.parameters && (this.parameters.categories && this.parameters.categories.length > 0)) {
      this.targetAudience.targetAudienceRescueData.categories = [];
      this.parameters.categories.forEach(category => {
        const _category = {
          departmentId: category.departmentId,
          categoryId: category.categoryId,
          subcategoryId: category.subcategoryId
        };

        this.targetAudience.targetAudienceRescueData.categories.push(_category);
      });
    }
  }

  setMetadataValuesToEdit() {
    if (this.targetAudience.metadataValues) {
      if (this.metadataValues && this.metadataValues.length) {
        for (const extradataValue of this.metadataValues) {
          const values = this.targetAudience.metadataValues[extradataValue.property];

          if (values && values.length) {
            this.metadataValuesModel[extradataValue.property] = new Array<any>();
            for (const value of values) {
              this.metadataValuesModel[extradataValue.property].push(value);
            }
          }
        }
      }
    }
  }

  prepareEdit(targetAudienceId: string) {
    if (targetAudienceId) {
      this.loading = true;
      this.targetAudienceService.get(this.campaignId, targetAudienceId)
        .subscribe(
          targetAudience => {
            if (targetAudience) {
              this.targetAudience = targetAudienceOf(targetAudience);
              this.setCategoriesGridEdit(this.targetAudience.targetAudienceRescueData.categories);
              this.setMetadataValuesToEdit();

              if (this.targetAudience.groupId)
                this.existent = true;
            }
          },
          err => this.handleError(err),
          () => this.loading = false
        );
    }
  }

  prepareMetadataValuesToSave() {
    if (this.metadataValues && this.metadataValues.length) {
      for (const extradataValue of this.metadataValues) {
        const selectedValues = this.metadataValuesModel[extradataValue.property];

        this.targetAudience.metadataValues[extradataValue.property] = new Array<string>();

        if (selectedValues && selectedValues.length) {
          for (const selectedValue of selectedValues) {
            const value = selectedValue && selectedValue.text ? selectedValue.text : selectedValue;
            this.targetAudience.metadataValues[extradataValue.property].push(value);
          }
        }
      }
    }
  }

  get canEditTargetAudience() {
    return this.authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_CRM_TARGET_AUDIENCE_EDIT);
  }

  onFormSubmit() {
    this.upsertCategories();
    this.save();
  }

  save() {
    this.gpAlert.clear();
    this.prepareMetadataValuesToSave();
    if (this.targetAudience.id === '') {
      this.loading = true;
      this.targetAudienceService.create(this.campaignId, this.targetAudience)
        .subscribe(
          result => {
            if (result) {
              this.targetAudience.id = result;
              this.gpAlert.showSuccess('Público Alvo cadastrado com sucesso.');
              this.loading = false;
            } else {
              this.gpAlert.showWarning('Não foi possível cadastrar o Público Alvo, por favor, tente novamente.');
              this.loading = false;
            }
          },
          err => this.handleError(err)
        );
    } else {
      this.loading = true;
      this.targetAudienceService.update(this.campaignId, this.targetAudience.id, this.targetAudience)
        .subscribe(
          result => {
            if (result) {
              this.gpAlert.showSuccess('Público Alvo atualizado com sucesso.');
            } else {
              this.gpAlert.showWarning('Não foi possível atualizar o Público Alvo, por favor, tente novamente.');
            }

            this.loading = false;
          },
          err => this.handleError(err)
        );
    }
  }

  get isExistent() {
    return this.existent;
  }

  getAllExistentGroups() {
    if (this.campaignId) {
      this.loading = true;
      this.participantsGroupService.search(this.campaignId).subscribe(result => {
        if (result) {
          this.items = result.map(r => Item.of(r.id, r.name));
        }
      },
        err => this.gpAlert.showError(err)),
        this.loading = false;
    }

    return this.items;
  }

  groupPopulate(event) {
    this.targetAudience.groupId = event;
  }
}
