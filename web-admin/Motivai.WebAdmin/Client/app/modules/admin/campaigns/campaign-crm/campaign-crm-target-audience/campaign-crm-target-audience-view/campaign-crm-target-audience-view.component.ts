import { OnInit } from '@angular/core';
import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ViewChild } from '@angular/core';
import { GpCampaignCrmTargetAudienceEditorComponent } from '../campaign-crm-target-audience-editor/campaign-crm-target-audience-editor.component';
import { TabsetComponent, TabDirective } from 'ng2-bootstrap';
import { CampaignCrmTargetAudienceListComponent } from '../campaign-crm-target-audience-list/campaign-crm-target-audience-list.component';

@Component({
  selector: 'campaign-crm-target-audience-view',
  templateUrl: 'campaign-crm-target-audience-view.component.html'
})
export class CampaignCrmTargetAudienceViewComponent implements OnInit {
  campaignId: string;

  @ViewChild('tabs') tabs: TabsetComponent;
  @ViewChild('editComponent') editComponent: GpCampaignCrmTargetAudienceEditorComponent;
  @ViewChild('listComponent') listComponent: CampaignCrmTargetAudienceListComponent;

  constructor(private route: ActivatedRoute, private router: Router) { }

  ngOnInit() {
    if (this.route.parent != null && this.route.parent.parent != null) {
      this.route.parent.parent.params.subscribe((params: any) => {
        this.campaignId = params['id'];
        if (!this.campaignId)
          this.router.navigate(['/campanha']);
      });
    } else {
      this.router.navigate(['/campanha']);
    }
  }

  private refreshGrid() {
      this.editComponent.resetFields();
      this.listComponent.loadAudiences(true);
  }

  private onEdit($event) {
    this.editComponent.prepareEdit($event.id);
    this.tabs.tabs[1].active = true;
  }
}
