<gp-card [first]="true" [last]="true" title="Públicos alvos gerados">
	<div class="row">
		<div grid="12 4 3" [group]="true">
			<label>Descriçao</label>
			<input type="text" class="form-control" name="description" [(ngModel)]="filter.description" />
		</div>
		<div class="top-p2" grid="12 4 3">
			<gp-spinner-button text="Carregar" [search]="true" (click)="loadAudiences()" [loading]="loading" loadingText="Carregando"></gp-spinner-button>
		</div>
	</div>
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<gp-grid #targetAudienceGrid name="audiencesGrid" [rows]="audiences" [columns]="['Descrição']" [fields]="['description']"
			 [loading]="loading" [showActive]="true" [showPagination]="true" [showTotalPages]="false" (onPageChanged)="pageChange($event)"
			 [showEdit]="true" [showDelete]="false" (onEdit)="editAudience($event)" (onDelete)="removeAudience($event)">
			</gp-grid>
		</gp-form-col>
	</gp-form-row>
	<div class="row">
		<div class="col-md-12">
			<gp-alert #gpAlert></gp-alert>
		</div>
	</div>
</gp-card>