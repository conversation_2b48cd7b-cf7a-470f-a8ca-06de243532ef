import { Component, AfterViewInit, Input, Output, forwardRef, EventEmitter } from '@angular/core';
import { NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';
import { log } from 'util';
import { Item } from '../../../../../../shared/models/item';
import { CampaignService } from '../../../campaign.service';
import { ActivatedRoute } from '@angular/router';
import { TargetAudienceService } from '../targetAudience.service';

@Component({
    selector: 'gp-target-audience-select',
    template: `
        <gp-select [id]="inputName" [name]="inputName"
            [multiple]="true" [items]="items" [required]="required"
            [ngModel]="targetAudiences"
            (change)="onChance($event)" [disabled]="disabled"
            [placeholder]="placeholder">
        </gp-select>
    `,
    providers: [{
        provide: NG_VALUE_ACCESSOR,
        useExisting: forwardRef(() => GpTargetAudienceSelectComponent),
        multi: true
    }]
})
export class GpTargetAudienceSelectComponent {
    // tslint:disable-next-line:no-input-rename
    @Input('name') inputName: string;
    @Input() public required: boolean = false;
    @Input() public placeholder: string;
    @Input() public disabled: boolean;
    @Input() public multiple: boolean = false;

    items: Array<Item> = [];
    _items: Array<Item> = [];
    targetAudiences: string[];
    currentItems: Array<Item> = [];
    private _value: any = null;
    private allItem: Item = new Item('0', 'Todos os Públicos');

    onChange: any = (_: any) => { };
    onTouched: any = () => { };

    constructor(private route: ActivatedRoute
        , private campaignService: CampaignService
        , private targetAudienceService: TargetAudienceService) { }

    @Input()
    set campaignId(id) {
        this.loadTargetAudiences(id);
    }

    private loadTargetAudiences(campaignId: string) {
        this.targetAudienceService.getAll(campaignId)
            .subscribe(targetAudiences => {
                if (targetAudiences && targetAudiences.length > 0) {
                    const _targetAudiences = new Array<Item>();
                    _targetAudiences.push(this.allItem);

                    targetAudiences.forEach(t => {
                        _targetAudiences.push(new Item(t.id, t.description));
                    });

                    this.items = _targetAudiences;
                    this._items = _targetAudiences;
                } else
                    this.items = [];
            });
    }

    public onChance($event) {
        let targetAudience = this.existAlltarget($event);

        if (targetAudience)
            this.setAllTargetAudiences();
        else {
            this.items = this._items;
            this.value = $event;
            return;
        }
    }

    private existAlltarget(items: Item[]): boolean {
        if (!items || !items.length)
            return false;

        const item = items.find(t => t.id === '0');
        if (!item)
            return false;

        return true;
    }

    private setAllTargetAudiences() {
        this.value = this._items.filter(i => i.id !== this.allItem.id).map(i => i);
        this.items = [this.allItem];
        this.targetAudiences = [this.allItem.id];
    }

    get value() {
        return this._value;
    }

    set value(targetAudiences: Array<Item>) {
        const ids = targetAudiences && targetAudiences.length ? targetAudiences.map(t => t.id) : [];
        this._value = ids;
        this.onChange(ids);
        this.onTouched();
    }

    registerOnChange(fn) {
        this.onChange = fn;
    }

    registerOnTouched(fn) {
        this.onTouched = fn;
    }

    writeValue(ids: Array<string>) {
        if (ids && ids.length)
            this.targetAudiences = this._items.length - 1 === ids.length ? [this.allItem.id] : ids;
        else
            this.targetAudiences = [];
    }
}

