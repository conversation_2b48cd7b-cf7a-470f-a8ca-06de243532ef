import { Component, Input, OnInit, ViewChild, Output, EventEmitter } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpGridComponent } from '../../../../../../shared/components/gp-grid/gp-grid.component';
import { TargetAudienceService } from '../targetAudience.service';
import { TargetAudience } from '../targetAudience';

@Component({
  selector: 'campaign-crm-target-audience-list',
  templateUrl: 'campaign-crm-target-audience-list.component.html'
})
export class CampaignCrmTargetAudienceListComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @ViewChild('targetAudienceGrid') targetAudienceGrid: GpGridComponent;
  @Output('onedit') onedit: EventEmitter<any> = new EventEmitter();

  @Input()
  public campaignId: string;
  public loading: boolean = false;
  public skip: number = 0;
  public limit: number = 10;
  public filter: any = {
    description: '',
    extractionDate: null
  };
  public targetAudience: any = {
    description: '',
    status: ''
  };

  constructor(private route: ActivatedRoute, private targetAudienceService: TargetAudienceService) { }

  public ngOnInit() {
    // MAP PARTNERS AND CAMPAIGNID
    if (this.route.parent != null && this.route.parent.parent != null) {
      this.route.parent.parent.params.subscribe((params: any) => {
        this.campaignId = params['id'];
        this.loadAudiences();
      });
    }
  }

  audiences: Array<TargetAudience> = [];

  public loadAudiences(resetSearch: boolean = false) {
    if (resetSearch) {
      this.skip = 0;
      this.limit = 10;
      this.targetAudienceGrid.resetPagination();
    }

    this.gpAlert.clear();
    this.loading = true;
    this.audiences = [];
    this.targetAudienceService.getAll(this.campaignId, this.filter.description
      , this.skip, this.limit)
      .subscribe(
        targetAudiences => {
          this.audiences = targetAudiences;
        },
        err => this.handleError(err),
        () => this.loading = false);
  }

  private handleError(err) {
    let errorMessage = (err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao efetuar a operação.');
    this.gpAlert.showError(errorMessage);
    this.loading = false;
  }

  public pageChange($event) {
    if ($event) {
      this.skip = $event.skip;
      this.limit = $event.limit;
      this.loadAudiences();
    }
  }

  public editAudience(targetAudience: any) {
    this.onedit.emit(targetAudience);
  }
}
