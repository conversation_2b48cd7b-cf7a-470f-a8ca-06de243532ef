import { AuthStore } from '../../../../../core/auth/auth.store';
import { ActivatedRoute } from '@angular/router';
import { CampaignService } from '../../campaign.service';
import { Component, OnInit, ViewChild } from '@angular/core';
import axios from 'axios';
import { GpCategorySelectComponent } from '../../../categories/categories-util/categories-select.component';

@Component({
    selector: 'campaign-crm-extraction-products',
    templateUrl: 'campaign-crm-extraction-products.component.html'
})

export class CampaignCrmExtractionProductsComponent implements OnInit {

    @ViewChild('department') department: GpCategorySelectComponent;
    @ViewChild('category') category: GpCategorySelectComponent;
    @ViewChild('subcategory') subcategory: GpCategorySelectComponent;

    private categoriesParameters: any = {};
    private parameters: any = {};
    private partners: any[] = [];
    private campaignId: string;
    private loadingDownload: boolean = false;

    constructor(private route: ActivatedRoute, private _campaignService: CampaignService, private _authStore: AuthStore) { }

    ngOnInit() {
        if (this.route.parent != null && this.route.parent.parent != null) {
            this.route.parent.parent.params.subscribe((params: any) => {
                this.campaignId = params['id'];
                this.getLinkedPartners();
            });
        }
    }

    private getLinkedPartners() {
        if (this.campaignId) {
            this._campaignService.getLinkedPartnersDataByCampaign(this.campaignId)
                .subscribe(partners => {
                    this.partners = partners.map(p => {
                        return { partnerId: p.partnerId, partnerName: p.partnerName };
                    });
                });
        }
    }

    private addCategory() {
        const line: any = {};
        if (!this.parameters.categories) this.parameters.categories = [];

        if (this.categoriesParameters.departmentId) {
            const selectedDepartment = this.department.categories.find(x => x.id === this.categoriesParameters.departmentId);
            if (selectedDepartment) {
                line.departmentId = this.categoriesParameters.departmentId;
                line.departmentName = selectedDepartment.name;
            }
        }

        if (this.categoriesParameters.categoryId) {
            const selectedCategory = this.category.categories.find(x => x.id === this.categoriesParameters.categoryId);
            if (selectedCategory) {
                line.categoryId = this.categoriesParameters.categoryId;
                line.categoryName = selectedCategory.name;
            }
        }

        if (this.categoriesParameters.subcategoryId) {
            const selectedSubcategory = this.subcategory.categories.find(x => x.id === this.categoriesParameters.subcategoryId);
            if (selectedSubcategory) {
                line.subcategoryId = this.categoriesParameters.subcategoryId;
                line.subcategoryName = selectedSubcategory.name;
            }
        }

        const existingCombination = this.parameters.categories.find(x => JSON.stringify(x) === JSON.stringify(line));
        if (!existingCombination) {
            this.parameters.categories.push(line);
        }
    }

    private deleteCategory($event) {
        const line = this.parameters.categories.find(x => JSON.stringify(x) === JSON.stringify($event));
        if (line) {
            this.parameters.categories.splice(this.parameters.categories.indexOf(line), 1);
        }
    }

    private downloadFile() {
        this.loadingDownload = true;
        axios
            .post(`/api/campaigns/${this.campaignId}/crm/extraction/products`, this.parameters, {
                responseType: 'arraybuffer',
                headers: {
                    access_token: this._authStore.getToken()
                }
            })
            .then(response => {
                if (response.data) {
                    const blob = new Blob([response.data], { type: 'text/csv' });
                    let link = window.document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = response.headers['filename'];
                    link.click();
                }

                this.loadingDownload = false;
            })
            .catch(err => {
                console.log(err);
                this.loadingDownload = false;
            })
    }
}
