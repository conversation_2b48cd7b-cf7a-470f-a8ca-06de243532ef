<gp-card [noPaddingTop]="true" [noPaddingBottom]="true">
	<div class="row">
		<accordion [closeOthers]="true">
			<accordion-group panelClass="b0 mb-sm panel-default no-shadow" [isOpen]="true">
				<div accordion-heading>
					<label><em class="fa fa-navicon"></em> Features do CRM</label>
				</div>
				<div class="row menublock text-center">
					<gp-menublock-item size="xs" icon="bullseye" color="text-danger" text="Públicos Alvos"
						routerLinkActive="active" [routerLink]="['/campanha', campaignId, 'crm', 'publico-alvo']"
						*ngIf="canAccessTargetAudiences">
					</gp-menublock-item>
					<gp-menublock-item size="xs" icon="shopping-cart" color="text-danger" text="Extração de Produtos"
						routerLinkActive="active"
						[routerLink]="['/campanha', campaignId, 'crm', 'extracao-de-produtos']"
						*ngIf="canAccessExportProducts">
					</gp-menublock-item>
				</div>
			</accordion-group>
		</accordion>
	</div>
</gp-card>

<router-outlet></router-outlet>