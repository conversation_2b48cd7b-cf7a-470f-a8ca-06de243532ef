export interface TargetAudience {
  id: string;
  description: string;
  active: boolean;
  updateFrequency: string;
  metadataValues: { [header: string]: string[] };
  targetAudienceUserParticipant: {
    genre: string;
    state: string;
    ageOf: string;
    ageTo: string;
    balanceOf: string;
    balanceTo: string;
  };
  targetAudienceRescueData: {
    partnerId: string;
    categories: any[];
    averageTicketOf: string;
    averageTicketTo: string;
    lastRescueOf: string;
    lastRescueTo: string;
    expirationOfPointsOf: string;
    expirationOfPointsTo: string;
  };
  groupId: string;
}

const targetAudienceOf = (targetAudience: any): TargetAudience => {
  return {
    id: targetAudience && targetAudience.id ? targetAudience.id : '',
    description: targetAudience && targetAudience.description ? targetAudience.description : '',
    updateFrequency: targetAudience && targetAudience.updateFrequency ? targetAudience.updateFrequency : '',
    active: targetAudience && targetAudience.active ? targetAudience.active : null,
    enableService: targetAudience && targetAudience.enableService ? targetAudience.enableService : null,
    isMaster: targetAudience && targetAudience.isMaster ? targetAudience.isMaster : null,
    targetAudienceUserParticipant: {
      genre: targetAudience && targetAudience.targetAudienceUserParticipant && targetAudience.targetAudienceUserParticipant.genre ? targetAudience.targetAudienceUserParticipant.genre : '',
      state: targetAudience && targetAudience.targetAudienceUserParticipant && targetAudience.targetAudienceUserParticipant.state ? targetAudience.targetAudienceUserParticipant.state : '',
      ageOf: targetAudience && targetAudience.targetAudienceUserParticipant && targetAudience.targetAudienceUserParticipant.ageOf ? targetAudience.targetAudienceUserParticipant.ageOf : '',
      ageTo: targetAudience && targetAudience.targetAudienceUserParticipant && targetAudience.targetAudienceUserParticipant.ageTo ? targetAudience.targetAudienceUserParticipant.ageTo : '',
      balanceOf: targetAudience && targetAudience.targetAudienceUserParticipant && targetAudience.targetAudienceUserParticipant.balanceOf ? targetAudience.targetAudienceUserParticipant.balanceOf : '',
      balanceTo: targetAudience && targetAudience.targetAudienceUserParticipant && targetAudience.targetAudienceUserParticipant.balanceTo ? targetAudience.targetAudienceUserParticipant.balanceTo : ''
    },
    targetAudienceRescueData: {
      partnerId: targetAudience && targetAudience.targetAudienceRescueData && targetAudience.targetAudienceRescueData.partnerId ? targetAudience.targetAudienceRescueData.partnerId : '',
      categories: targetAudience && targetAudience.targetAudienceRescueData && targetAudience.targetAudienceRescueData.categories ? targetAudience.targetAudienceRescueData.categories : [],
      averageTicketOf: targetAudience && targetAudience.targetAudienceRescueData && targetAudience.targetAudienceRescueData.averageTicketOf ? targetAudience.targetAudienceRescueData.averageTicketOf : '',
      averageTicketTo: targetAudience && targetAudience.targetAudienceRescueData && targetAudience.targetAudienceRescueData.averageTicketTo ? targetAudience.targetAudienceRescueData.averageTicketTo : '',
      lastRescueOf: targetAudience && targetAudience.targetAudienceRescueData && targetAudience.targetAudienceRescueData.lastRescueOf ? targetAudience.targetAudienceRescueData.lastRescueOf : '',
      lastRescueTo: targetAudience && targetAudience.targetAudienceRescueData && targetAudience.targetAudienceRescueData.lastRescueTo ? targetAudience.targetAudienceRescueData.lastRescueTo : '',
      expirationOfPointsOf: targetAudience && targetAudience.targetAudienceRescueData && targetAudience.targetAudienceRescueData.expirationOfPointsOf ? targetAudience.targetAudienceRescueData.expirationOfPointsOf : '',
      expirationOfPointsTo: targetAudience && targetAudience.targetAudienceRescueData && targetAudience.targetAudienceRescueData.expirationOfPointsTo ? targetAudience.targetAudienceRescueData.expirationOfPointsTo : ''
    },
    groupId: targetAudience && targetAudience.groupId ? targetAudience.groupId : '',
    metadataValues: targetAudience && targetAudience.metadataValues ? targetAudience.metadataValues : {}
  } as TargetAudience;
};

const targetAudienceErrorMessagesValidation = (): any => {
  return {
    receiverName: {
      required: 'Nome é obrigatório'
    }
  };
};

export interface MetadataValueModel {
  header: string;
  property: string;
  cssCols: string;
  values: MetadataValuesModel[];
}

export interface MetadataValuesModel {
  id: string;
  text: string;
}

export { targetAudienceOf, targetAudienceErrorMessagesValidation };
