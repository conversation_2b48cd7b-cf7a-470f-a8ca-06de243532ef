<gp-card title="Filtros de Departamento">
	<gp-form-row>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Departamento">
				<gp-categories-select
					#department
					id="department"
					name="department"
					[active]="true"
					[level]="1"
					[(ngModel)]="categoriesParameters.departmentId">
				</gp-categories-select>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Categoria">
				<gp-categories-select
					#category
					id="category" 
					name="category" 
					[active]="true" 
					[level]="2" 
					[parentId]="categoriesParameters.departmentId" 
					[disabled]="!categoriesParameters.departmentId"
					emptyMessage="Sem categorias"
					[(ngModel)]="categoriesParameters.categoryId">
				</gp-categories-select>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Subcategoria">
				<gp-categories-select
					#subcategory
					id="subcategory"
					name="subcategory"
					[active]="true"
					[level]="3"
					[parentId]="categoriesParameters.categoryId"
					[disabled]="!categoriesParameters.categoryId"
					emptyMessage="Sem subcategorias"
					[(ngModel)]="categoriesParameters.subcategoryId">
				</gp-categories-select>
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<gp-spinner-button 
				type="button" bootstrapClass="default" icon="plus" [pink]="true"
				loadingText="Adicionando..." [loading]="loadingDownload" size="md" text="Adicionar Regra" pull="right" (click)="addCategory()">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<gp-grid
				#grid
				name="grid"
				[rows]="parameters.categories"
				[columns]="['Departamento', 'Categoria', 'Subcategoria']"
				[fields]="['departmentName', 'categoryName', 'subcategoryName']"
				[showActive]="false"
				[showPagination]="false"
				[showTotalPages]="false"
				[showDelete]="true"
				[showEdit]="false"
				[loading]="loading"
				(onDelete)="deleteCategory($event)"
				(onPageChanged)="onPageChanged($event)">
			</gp-grid>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card title="Filtros de Parceiro e Fabricante">
	<gp-form-row>
		<gp-form-col cols="12 6 6">
			<gp-simple-input label="Parceiro">
				<select name="partner" class="form-control" [(ngModel)]="parameters.partnerId">
					<option>Selecione</option>
					<option *ngFor="let p of partners" [value]="p.partnerId">{{ p.partnerName }}</option>
				</select>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 6 6">
			<gp-simple-input label="Fabricante" >
				<gp-manufacturers-select
					id="manufacturer"
					name="manufacturer"
					[(ngModel)]="parameters.manufacturerId">
				</gp-manufacturers-select>
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card title="Range de Valor">
	<gp-form-row>	
		<gp-form-col cols="12 6 6">
			<div class="form-group">
				<label>Valor de Venda (De)</label>
				<div class="input-group">
					<span class="input-group-addon">R$</span>
					<gp-input-mask name="saleValueFrom" [onlyDecimal]="true" placeholder="30,00" [(ngModel)]="parameters.saleValueFrom"></gp-input-mask>
				</div>
			</div>
		</gp-form-col>
		<gp-form-col cols="12 6 6">
			<div class="form-group">
				<label>Valor de Venda (Até)</label>
				<div class="input-group">
					<span class="input-group-addon">R$</span>
					<gp-input-mask name="saleValueTo" [onlyDecimal]="true" placeholder="2000,00" [(ngModel)]="parameters.saleValueTo"></gp-input-mask>
				</div>
			</div>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card title="Configuração dos Resultados">
	<gp-form-row>
		<gp-form-col cols="12 3 3">
			<gp-simple-input label="Ordenação">
				<select class="form-control" name="orderBy" [(ngModel)]="parameters.orderBy">
					<option value="relevance">Relevância</option>
				</select>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 3 3">
			<gp-simple-input label="Máximo de registros">
				<gp-input-mask name="maxRecords" [onlyInteger]="true" [(ngModel)]="parameters.maxRecords"></gp-input-mask>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 3 3">
			<gp-simple-input label="Largura das Imagens">
				<gp-input-mask name="imageWidth" [onlyInteger]="true" [(ngModel)]="parameters.imageWidth"></gp-input-mask>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 3 3">
			<gp-simple-input label="Altura das Imagens">
				<gp-input-mask name="imageHeight" [onlyInteger]="true" [(ngModel)]="parameters.imageHeight"></gp-input-mask>
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 3 3">
			<label>Utilizar regras de filtro do catálogo</label>
			<div>
				<gp-switch name="useCatalogRules" [(ngModel)]="parameters.useCatalogRules"></gp-switch>
			</div>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<gp-spinner-button 
				type="button" bootstrapClass="default" icon="share" [pink]="true"
				loadingText="Gerando Arquivo..." [loading]="loadingDownload" size="lg" text="Extrair Dados" pull="right"
				(click)="downloadFile()">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
</gp-card>
