import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { ApiService } from '../../../../../core/api/api.service';

@Injectable()
export class TargetAudienceService {
    constructor(private api: ApiService) { }

    create(campaignId: string, targetAudienceModel: any): Observable<string> {
        console.log(`campaignId: ${campaignId}`);
        console.log(`targetAudienceModel: ${JSON.stringify(targetAudienceModel)}`);

        return this.api.post(`/api/campaigns/${campaignId}/crmtargetaudience`, targetAudienceModel);
    }

    update(campaignId: string, targetAudienceId: string, targetAudienceModel: any): Observable<boolean> {
        return this.api.put(`/api/campaigns/${campaignId}/crmtargetaudience/${targetAudienceId}`, targetAudienceModel);
    }

    getAll(campaignId: string, targetAudienceDescription?: string, from?: number, size?: number) {
        let params: any = {};

        if (targetAudienceDescription)
            params.targetAudienceDescription = targetAudienceDescription;
        if (from)
            params.from = from;
        if (size)
            params.size = size;

        return this.api.get(`/api/campaigns/${campaignId}/crmtargetaudience/`, params);
    }

    get(campaignId: string, targetAudienceId: string) {
        return this.api.get(`/api/campaigns/${campaignId}/crmtargetaudience/${targetAudienceId}`);
    }

    getMetadaHeadersDetail(campaignId: string): Observable<any[]> {
        return this.api.get(`/api/campaigns/${campaignId}/crmtargetaudience/metadaheaders/values`);
    }
}
