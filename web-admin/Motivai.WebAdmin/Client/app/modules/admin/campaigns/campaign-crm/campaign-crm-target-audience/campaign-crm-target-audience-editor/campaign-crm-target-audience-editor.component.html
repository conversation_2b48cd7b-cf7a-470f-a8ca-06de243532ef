<gp-card [first]="true" [last]="true" title="Cadastro do Público Alvo">
  <form #targetAudienceEditForm="ngForm" (ngSubmit)="onFormSubmit()">
    <gp-card title="Identificação">
      <gp-form-row>
        <gp-form-col cols="12 12 12">
          <gp-simple-input label="Descrição do Público Alvo" errorMessage="Descrição deve ser informada." [required]="true">
            <input type="text" name="direction" required="true" class="form-control" [(ngModel)]="targetAudience.description" />
          </gp-simple-input>
        </gp-form-col>
      </gp-form-row>
    </gp-card>
    <gp-card title="Parametrização">
      <gp-form-row>
        <gp-form-col cols="12 4 4">
          <label>Selecionar grupo existente</label>
          <div>
            <gp-switch [(ngModel)]="existent" [ngModelOptions]="{ standalone: true }"></gp-switch>
          </div>
        </gp-form-col>
      </gp-form-row>
      <div *ngIf="isExistent">
        <gp-form-row>
          <gp-form-col cols="12 4 4">
            <gp-select name="existentGroupSelect" id="existentGroupSelect" [items]="items" (change)="groupPopulate($event)" [multiple]="false" [(ngModel)]="targetAudience.groupId" placeholder="Selecione um grupo"> </gp-select>
          </gp-form-col>
        </gp-form-row>
      </div>
      <gp-form-row>
        <gp-form-col cols="12 4 4">
          <label>Habilitar Serviço</label>
          <div>
            <gp-switch [(ngModel)]="targetAudience.enableService" [ngModelOptions]="{ standalone: true }"> </gp-switch>
          </div>
        </gp-form-col>
      </gp-form-row>
      <gp-form-row>
        <gp-form-col cols="12 4 4">
          <label>É um Público Master</label>
          <div>
            <gp-switch [(ngModel)]="targetAudience.isMaster" [ngModelOptions]="{ standalone: true }"> </gp-switch>
          </div>
        </gp-form-col>
      </gp-form-row>
      <gp-form-row>
        <gp-form-col cols="12 3 3">
          <gp-simple-input label="Frequência de Atualização em Dias" errorMessage="Frequência de Atualização deve ser informada." [required]="true">
            <gp-input-mask name="updateFrequency" [required]="true" [onlyDecimal]="true" [(ngModel)]="targetAudience.updateFrequency"></gp-input-mask>
          </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 3 3">
          <div grid="12 2">
            <label>Ativo</label>
            <div>
              <gp-switch [(ngModel)]="targetAudience.active" [ngModelOptions]="{ standalone: true }"> </gp-switch>
            </div>
          </div>
        </gp-form-col>
      </gp-form-row>
    </gp-card>
    <gp-card title="Dados da importação dos participantes" *ngIf="metadataValues && metadataValues.length">
      <gp-form-row>
        <div *ngFor="let metadataValue of metadataValues">
          <gp-form-col [cols]="metadataValue.cssCols">
            <label>{{ metadataValue.header | titlecase }}</label>
            <gp-select
              [name]="metadataValue.property"
              [id]="metadataValue.property"
              [items]="metadataValue.values"
              [(ngModel)]="metadataValuesModel[metadataValue.property]"
              [placeholder]="metadataValue.header | titlecase"
              [multiple]="true"
            ></gp-select>
          </gp-form-col>
        </div>
      </gp-form-row>
    </gp-card>
    <gp-card title="Dados cadastrais dos participantes">
      <gp-form-row>
        <gp-form-col cols="12 6 6">
          <gp-simple-input label="Gênero">
            <select name="genre" class="form-control" [(ngModel)]="targetAudience.targetAudienceUserParticipant.genre">
              <option value="">Selecione</option>
              <option value="Masculino">Masculino</option>
              <option value="Feminino">Feminino</option>
              <option value="Ambos">Ambos</option>
            </select>
          </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 6 6">
          <gp-simple-input label="Estado de cadastro">
            <state-select name="state" [(ngModel)]="targetAudience.targetAudienceUserParticipant.state"> </state-select>
          </gp-simple-input>
        </gp-form-col>
      </gp-form-row>
      <gp-form-row>
        <gp-form-col cols="12 3 3">
          <gp-simple-input label="Idade de">
            <gp-input-mask name="ageFrom" [onlyInteger]="true" [(ngModel)]="targetAudience.targetAudienceUserParticipant.ageOf"> </gp-input-mask>
          </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 3 3">
          <gp-simple-input label="Idade até">
            <gp-input-mask name="ageTo" [onlyInteger]="true" [(ngModel)]="targetAudience.targetAudienceUserParticipant.ageTo"> </gp-input-mask>
          </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 3 3">
          <gp-simple-input label="Saldo de">
            <gp-input-mask name="balanceOf" [onlyDecimal]="true" [(ngModel)]="targetAudience.targetAudienceUserParticipant.balanceOf"></gp-input-mask>
          </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 3 3">
          <gp-simple-input label="Saldo até">
            <gp-input-mask name="balanceTo" [onlyDecimal]="true" [(ngModel)]="targetAudience.targetAudienceUserParticipant.balanceTo"></gp-input-mask>
          </gp-simple-input>
        </gp-form-col>
      </gp-form-row>
    </gp-card>

    <gp-card title="Dados de resgate">
      <gp-form-row>
        <gp-form-col cols="12 6 6">
          <gp-simple-input label="Parceiro">
            <select name="partner" class="form-control" [(ngModel)]="targetAudience.targetAudienceRescueData.partnerId">
              <option>Selecioneee</option>
              <option *ngFor="let p of partners" [value]="p.partnerId">{{ p.partnerName }}</option>
            </select>
          </gp-simple-input>
        </gp-form-col>
      </gp-form-row>
      <gp-form-row>
        <gp-form-col cols="12 4 4">
          <gp-simple-input label="Ticket médio (de)">
            <gp-input-mask name="ticketFrom" [onlyDecimal]="true" [(ngModel)]="targetAudience.targetAudienceRescueData.averageTicketOf"></gp-input-mask>
          </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 4 4">
          <gp-simple-input label="Ticket médio (até)">
            <gp-input-mask name="ticketTo" [onlyDecimal]="true" [(ngModel)]="targetAudience.targetAudienceRescueData.averageTicketTo"></gp-input-mask>
          </gp-simple-input>
        </gp-form-col>
      </gp-form-row>
      <gp-form-row>
        <gp-form-col cols="12 4 4">
          <gp-simple-input label="Último resgate (de)">
            <gp-input-mask name="rescueFrom" [onlyDecimal]="true" [(ngModel)]="targetAudience.targetAudienceRescueData.lastRescueOf"></gp-input-mask>
          </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 4 4">
          <gp-simple-input label="Último resgate (até)">
            <gp-input-mask name="rescueTo" [onlyDecimal]="true" [(ngModel)]="targetAudience.targetAudienceRescueData.lastRescueTo"></gp-input-mask>
          </gp-simple-input>
        </gp-form-col>
      </gp-form-row>
      <gp-form-row>
        <gp-form-col cols="12 4 4">
          <gp-simple-input label="Expiração de pontos (de)">
            <gp-input-mask name="expirationOf" [onlyDecimal]="true" [(ngModel)]="targetAudience.targetAudienceRescueData.expirationOfPointsOf"></gp-input-mask>
          </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 4 4">
          <gp-simple-input label="Expiração de pontos (até)">
            <gp-input-mask name="expirationOf" [onlyDecimal]="true" [(ngModel)]="targetAudience.targetAudienceRescueData.expirationOfPointsTo"></gp-input-mask>
          </gp-simple-input>
        </gp-form-col>
      </gp-form-row>
      <gp-form-row>
        <gp-form-col cols="12 4 4">
          <gp-simple-input label="Departamento">
            <gp-categories-select #department id="department" name="department" [active]="true" [level]="1" [(ngModel)]="categoriesParameters.departmentId"> </gp-categories-select>
          </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 4 4">
          <gp-simple-input label="Categoria">
            <gp-categories-select
              #category
              id="category"
              name="category"
              [active]="true"
              [level]="2"
              [parentId]="categoriesParameters.departmentId"
              [disabled]="!categoriesParameters.departmentId"
              emptyMessage="Sem categorias"
              [(ngModel)]="categoriesParameters.categoryId"
            >
            </gp-categories-select>
          </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 4 4">
          <gp-simple-input label="Subcategoria">
            <gp-categories-select
              #subcategory
              id="subcategory"
              name="subcategory"
              [active]="true"
              [level]="3"
              [parentId]="categoriesParameters.categoryId"
              [disabled]="!categoriesParameters.categoryId"
              emptyMessage="Sem subcategorias"
              [(ngModel)]="categoriesParameters.subcategoryId"
            >
            </gp-categories-select>
          </gp-simple-input>
        </gp-form-col>
      </gp-form-row>
      <gp-form-row>
        <gp-form-col cols="12 12 12">
          <gp-spinner-button type="button" bootstrapClass="default" icon="plus" [pink]="true" loadingText="Adicionando..." [loading]="loadingDownload" size="md" text="Adicionar Regra" pull="right" (click)="addCategory()">
          </gp-spinner-button>
        </gp-form-col>
      </gp-form-row>
      <gp-form-row>
        <gp-form-col cols="12 12 12">
          <gp-grid
            #grid
            name="grid"
            [rows]="parameters.categories"
            [columns]="['Departamento', 'Categoria', 'Subcategoria']"
            [fields]="['departmentName', 'categoryName', 'subcategoryName']"
            [showActive]="false"
            [showPagination]="false"
            [showTotalPages]="false"
            [showDelete]="true"
            [showEdit]="false"
            [loading]="loading"
            (onDelete)="deleteCategory($event)"
            (onPageChanged)="onPageChanged($event)"
          >
          </gp-grid>
        </gp-form-col>
        <div class="col-xs-12">
          <gp-alert #gpGridAlert></gp-alert>
        </div>
      </gp-form-row>
    </gp-card>
    <gp-card title="Status">
      <gp-form-row>
        <gp-form-col cols="12 6 6">
          <gp-simple-input label="Status do Participante">
            <select name="status" class="form-control" [(ngModel)]="targetAudience.targetAudienceUserParticipant.status">
              <option value="">Selecione</option>
              <option>Ativo</option>
              <option>Inativo</option>
              <option>Ambos</option>
            </select>
          </gp-simple-input>
        </gp-form-col>
      </gp-form-row>
    </gp-card>

    <gp-card>
      <gp-form-row>
        <gp-form-col cols="12 2 2" *ngIf="canEditTargetAudience">
          <gp-spinner-button [pink]="true" [loading]="loading" [disabled]="!targetAudienceEditForm.valid" icon="send" type="submit" text="Salvar Público Alvo" pull="left"></gp-spinner-button>
        </gp-form-col>
        <!-- <gp-form-col cols="12 2 2">
					<gp-spinner-button [pink]="true" icon="file-excel-o" text="Extrair XLS" pull="left">
					</gp-spinner-button>
				</gp-form-col> -->
      </gp-form-row>
      <gp-form-row>
        <div class="col-md-12">
          <gp-alert #gpAlert></gp-alert>
        </div>
      </gp-form-row>
      <hr />
      <gp-form-row>
        <gp-form-col cols="12 12 12">
          <p>ATENÇÃO: A geração do público alvo é um processo assíncrono! Você poderá vincular este público a qualquer funcionalidade, porém pode levar um tempo para que o sistema termina o processamento dos dados!</p>
        </gp-form-col>
      </gp-form-row>
    </gp-card>
  </form>

  <spinner [overlay]="true" [show]="loading"></spinner>
</gp-card>
