import { FileUploadEvent } from '../../../../../../../../../../shared/components/gp-fileupload/file-upload-event';
import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { GpAlertComponent } from '../../../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpFileUploadComponent } from '../../../../../../../../../../shared/components/gp-fileupload/gp-fileupload.component';
import { GpModalComponent } from '../../../../../../../../../../shared/components/gp-modal/gp-modal.component';
import { ScheduledBillPaymentsService } from '../../services/scheduled-bill-payments-service.service';
import { EMPTY } from '../../../../../../../../../../core/constants/constants-value';

@Component({
  selector: 'scheduled-payment-external-manual-payment',
  templateUrl: './scheduled-payment-external-manual-payment.component.html',
  styles: [`
    hr {
      margin-top: 0;
      margin-bottom: 0;
    }
  `]
})
export class ScheduledPaymentExternalManualPaymentComponent implements OnInit {
  @ViewChild('alert') alert: GpAlertComponent;
  @ViewChild('gpFile') gpFile: GpFileUploadComponent;
  @ViewChild('externalManualPaymentModal') externalManualPaymentModal: GpModalComponent;

  @Input() campaignId: string;

  tomorrow: Date = new Date();
  loadingParticipant: boolean = false;
  uploading: boolean = false;
  loadingPayment: boolean = false;

  externalManualPayment: any = {
    billDetails: {}
  };

  participant: any = {
    userId: null,
    participantId: null,
    name: '',
    balance: 0
  };

  constructor(
    private scheduledBillPaymentService: ScheduledBillPaymentsService
  ) {}

  get disableButtons() {
    return this.uploading || this.loadingPayment;
  }

  get loading() {
    return this.uploading || this.loadingParticipant;
  }

  get hasParticipant() {
    return (
      this.participant.userId &&
      this.participant.participantId
    );
  }

  ngOnInit() {
    this.tomorrow.setDate(new Date().getDate() + 1);
  }

  showModal() {
    this.resetForm();
    this.externalManualPaymentModal.show();
  }

  searchParticipant() {
    if (!this.participant.document) {
      return this.alert.showWarning('CPF/CNPJ do participante deve ser informado.');
    }

    this.loadingParticipant = true;
    this.scheduledBillPaymentService
      .findParticipantInfoByDocument(this.campaignId, this.participant.document)
      .subscribe(
        response => {
          this.loadingParticipant = false;
          if (!response || !response[0]) {
            this.resetForm();
            return this.alert.showWarning('Participante não encontrado.');
          }
          this.setParticipantInfo(response[0]);
        },
        err => {
          this.loadingParticipant = false;
          this.resetForm();
          this.alert.handleAndShowError(err);
        }
      );
  }

  private uploadPaymentReceipt() {
    if (!this.gpFile.hasSelectedFile) {
      this.alert.showWarning('Selecione o comprovante de pagamento');
      return;
    }
    this.uploading = true;
    this.gpFile.path = `api/campaigns/${this.campaignId}/services/billpayments/schedulings/${EMPTY}/externalScheduledPayment/receipt`;

    this.gpFile.onComplete = (uploadEvent: FileUploadEvent) => {
      this.uploading = false;
      this.gpFile.createUploader();
      if (uploadEvent.success) {
        const fileUrl = uploadEvent.result;
        this.externalManualPayment.paymentReceiptUrl = fileUrl;
        this.savePayment();
      } else {
        return this.alert.showError(uploadEvent.errorMessage || 'Ocorreu um erro ao fazer o upload do comprovante.');
      }
    };

    this.gpFile.uploadFile();
  }

  sendExternalManualPayment() {
    if (!this.hasParticipant) {
      this.alert.showWarning('Selecione o participante.');
      return;
    }
    if (!this.externalManualPayment.billDetails.barCode) {
      this.alert.showWarning('Informe a linha digitável da conta paga.');
      return;
    }
    if (!this.externalManualPayment.billDetails.billingAmount) {
      this.alert.showWarning('Informe o valor da conta paga.');
      return;
    }

    if (this.gpFile.hasSelectedFile) {
      this.uploadPaymentReceipt();
    } else {
      this.savePayment();
    }
  }

  private savePayment() {
    this.loadingPayment = true;

    this.externalManualPayment.userId = this.participant.userId;
    this.externalManualPayment.participantId = this.participant.participantId;
    this.externalManualPayment.userDocument = this.participant.document;
    this.externalManualPayment.userName = this.participant.name;

    this.scheduledBillPaymentService.sendExternalManualPayment(this.campaignId, this.externalManualPayment)
      .subscribe(
        id => {
          this.loadingPayment = false;
          if (id) {
            this.resetForm();
            this.alert.showSuccess('Pagamento efetuado com sucesso.');
          } else {
            this.alert.showWarning('Não foi possível salvar o pagamento, por favor, tente novamente.');
          }
        },
        err => {
          this.loadingPayment = false;
          this.alert.handleAndShowError(err);
        }
      );
  }

  resetForm() {
    this.loadingParticipant = false;
    this.uploading = false;
    this.loadingPayment = false;
    this.externalManualPayment = {
      billDetails: {}
    };
    this.participant = {
      userId: null,
      participantId: null,
      name: '',
      balance: 0
    };
  }

  setParticipantInfo(participantInfo: any) {
    this.participant.userId = participantInfo.userId;
    this.participant.participantId = participantInfo.participantId;
    this.participant.name = participantInfo.name;
    this.participant.balance = participantInfo.balance;
  }
}
