<gp-card [first]="true" [last]="true" title="Importar Pedidos" *ngIf="!_batchImportId">
	<gp-form-row>
		<gp-form-col cols="12 3 3">
			<gp-simple-input label="Template do arquivo">
				<select class="form-control" name="orderFileType" [(ngModel)]="parameters.orderFileType">
					<option value="Singleline">Um pedido por linha</option>
					<option value="Multiline">Um pedido em várias linhas</option>
				</select>
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row *ngIf="parameters.orderFileType">
		<gp-form-col cols="12 12 12">
			<gp-fileupload *ngIf="canImport" name="files" [multiple]="true" [csv]="true"
				[path]="uploadPath" [data]="parameters" [disabled]="false" (oncomplete)="onItemComplete($event)" #gpFile>
			</gp-fileupload>
		</gp-form-col>
	</gp-form-row>
	<hr />
	<gp-form-row>
		<gp-form-col cols="12 12 12" *ngIf="parameters.orderFileType === 'Singleline'">
			<p>INSTRUÇÕES DE USO:</p>
			<p>1. O formato do arquivo deve ser [.csv], com as colunas separadas por [;]</p>
			<p>2. As colunas do arquivo devem seguir a seguinte ordem (campo obrigatórios marcados com *):</p>
			<ul>
				<li><strong>A: </strong>Número do pedido*</li>
				<li><strong>B: </strong>Data do pedido (Formato: DD/MM/YYYY)*</li>
				<li><strong>C: </strong>CPF ou CNPJ*</li>
				<li><strong>D: </strong>Nome do participante*</li>
				<li><strong>E: </strong>E-mail do participante*</li>
				<li><strong>F: </strong>Número de telefone (ex.: 11922224444)*</li>
				<li><strong>G: </strong>Número de celular (ex.: 11922224444)</li>
				<li><strong>H: </strong>Descrição do endereço (ex.: Casa, Trabalho...)</li>
				<li><strong>I: </strong>CEP*</li>
				<li><strong>J: </strong>Logradouro*</li>
				<li><strong>K: </strong>Número*</li>
				<li><strong>L: </strong>Complemento*</li>
				<li><strong>M: </strong>Bairro*</li>
				<li><strong>N: </strong>Cidade*</li>
				<li><strong>O: </strong>Estado*</li>
				<li><strong>P: </strong>Ponto de Referência</li>
				<li><strong>Q: </strong>Código SKU*</li>
				<li><strong>R: </strong>Quantidade*</li>
				<li><strong>S: </strong>Nome do parceiro*</li>
				<li><strong>T: </strong>Valor unitário (R$)*</li>
				<li><strong>U: </strong>Valor unitário (Pontos)</li>
				<li><strong>V: </strong>Valor de desconto (R$)</li>
				<li><strong>W: </strong>Valor de desconto (Pontos)</li>
				<li><strong>X: </strong>Valor do frete (R$)*</li>
				<li><strong>Y: </strong>Valor do frete (Pontos)</li>
			</ul>
		</gp-form-col>

		<gp-form-col cols="12 12 12" *ngIf="parameters.orderFileType === 'Multiline'">
			<p>INSTRUÇÕES DE USO:</p>
			<p>1. O formato do arquivo deve ser [.csv], com as colunas separadas por [;]</p>
			<p>2. A primeira coluna de cada linha é o agrupador. Deve ser numérico.</p>
			<p>3. A primeira linha corresponde aos dados gerais do pedido, e segue a seguinte ordem (campos obrigatórios marcardos com *):</p>
			
				<ul>
					<li><strong>A: </strong>Código da linha (deve ser um numero inteiro)*</li>
					<li><strong>B: </strong>Numero do pedido*</li>
					<li><strong>C: </strong>Data do pedido*</li>
					<li><strong>D: </strong>CPF ou CNPJ*</li>
					<li><strong>E: </strong>Nome do participante*</li>
					<li><strong>F: </strong>E-mail do participante*</li>
					<li><strong>G: </strong>Número de telefone (ex.: 11922224444)*</li>
					<li><strong>H: </strong>Número do celular (ex.: 11922224444)</li>
					<li><strong>I: </strong>Descrição do endereço (ex.: Casa, Trabalho...)</li>
					<li><strong>J: </strong>CEP*</li>
					<li><strong>K: </strong>Logradouro*</li>
					<li><strong>L: </strong>Número*</li>
					<li><strong>M: </strong>Complemento*</li>
					<li><strong>N: </strong>Bairro*</li>
					<li><strong>O: </strong>Cidade*</li>
					<li><strong>P: </strong>Estado*</li>
					<li><strong>Q: </strong>Ponto de Referência</li>
					<li><strong>R: </strong>Valor do desconto (R$)</li>
					<li><strong>S: </strong>Valor do desconto (Pontos)</li>
					<li><strong>T: </strong>Valor do frete (R$)*</li>
					<li><strong>U: </strong>Valor do frete (Pontos)</li>
				</ul>
			
			<p>4. As demais linhas correspondem aos itens do pedido, e seguem a seguinte ordem:</p>
			
				<ul>
					<li><strong>A: </strong>Código da linha (deve ser o mesmo da primeira linha do pedido)*</li>
					<li><strong>B: </strong>Código SKU*</li>
					<li><strong>C: </strong>Quantidade*</li>
					<li><strong>D: </strong>Nome do Parceiro*</li>
					<li><strong>E: </strong>Valor unitário (R$)*</li>
					<li><strong>F: </strong>Valor unitário (Pontos)</li>
					<li><strong>G: </strong>Valor de desconto (R$)</li>
					<li><strong>H: </strong>Valor de desconto (Pontos)</li>
					<li><strong>I: </strong>Valor do frete (R$)*</li>
					<li><strong>J: </strong>Valor do frete (Pontos)</li>
				</ul>
			
		</gp-form-col>
	</gp-form-row>
</gp-card>

<div *ngIf="_batchImportId">
	<gp-card [first]="true" title="Detalhes da Importação">
		<gp-form-row>
			<gp-form-col cols="12 3 3">
				<gp-simple-input label="Código do Lote">
					<input type="text" name="batchNumber" class="form-control" disabled [(ngModel)]="batchImport.batchNumber" />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 3 3">
				<gp-simple-input label="Data da Importação">
					<input type="text" name="createDate" class="form-control" disabled [(ngModel)]="batchImport.formattedCreateDate" />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 3 3">
				<gp-simple-input label="Status">
					<input type="text" name="status" class="form-control" disabled [(ngModel)]="batchImport.status" />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 3 3">
				<gp-simple-input label="Total de pedidos no arquivo">
					<input type="text" name="status" class="form-control" disabled [(ngModel)]="batchImport.totalLines" />
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-spinner-button 
					*ngIf="canApprove" type="button" icon="close" bootstrapClass="danger"
					text="REPROVAR PEDIDOS" marginLeft="10px" loadingText="Processando..." pull="right"
					[loading]="loadingApproval" (click)="refuse()">
				</gp-spinner-button>
				<gp-spinner-button 
					*ngIf="canApprove" type="button" [pink]="true" icon="check" 
					text="APROVAR PEDIDOS" loadingText="Processando..." pull="right"
					[loading]="loadingApproval" (click)="approve()">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
		<div class="row">
			<div class="col-md-12">
				<gp-alert #approveAlert></gp-alert>
			</div>
		</div>
	</gp-card>

	<gp-card title="Resumo dos pedidos temporários">
		<gp-form-row>
			<gp-form-col cols="12 4 4">
				<gp-card>
					<gp-form-row>
						<gp-form-col cols="12 12 12">
							<h3>Sucesso: <span class="text-success">{{ batchImport.totalSuccessImported }}</span></h3>
							<hr />
							<h4 *ngIf="isProcessing" style="color: #CCC">PROCESSANDO...</h4>
							<h4 *ngIf="canApprove" style="color: #CCC">PENDENTE APROVAÇÃO</h4>
							<h4 *ngIf="isCompleted" style="color: #CCC">FINALIZADO</h4>
						</gp-form-col>
					</gp-form-row>
				</gp-card>
			</gp-form-col>
			<gp-form-col cols="12 4 4">
				<gp-card>
					<gp-form-row>
						<gp-form-col cols="12 12 12">
							<h3>Avisos: <span class="text-warning">{{ batchImport.totalWarningImported }}</span></h3>
							<hr />
							<h4 *ngIf="isProcessing" style="color: #CCC">PROCESSANDO...</h4>
							<h4 *ngIf="canApprove" style="color: #CCC">PENDENTE APROVAÇÃO</h4>
							<h4 *ngIf="isCompleted" style="color: #CCC">FINALIZADO</h4>
						</gp-form-col>
					</gp-form-row>
				</gp-card>
			</gp-form-col>
			<gp-form-col cols="12 4 4">
				<gp-card>
					<gp-form-row>
						<gp-form-col cols="12 12 12">
							<h3>Erros: <span class="text-danger">{{ batchImport.totalErrorImported }}</span></h3>
							<hr />
							<h4 *ngIf="isProcessing" style="color: #CCC">PROCESSANDO...</h4>
							<h4 *ngIf="canApprove" style="color: #CCC">PENDENTE APROVAÇÃO</h4>
							<h4 *ngIf="isCompleted" style="color: #CCC">FINALIZADO</h4>
						</gp-form-col>
					</gp-form-row>
				</gp-card>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card title="Resumo dos pedidos aprovados">
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-card>
					<gp-form-row>
						<gp-form-col cols="12 12 12">
							<h3>Sucesso: <span class="text-success">{{ batchImport.totalSuccessCompleted }}</span></h3>
							<hr />
							<h4 *ngIf="isProcessing" style="color: #CCC">PROCESSANDO...</h4>
							<h4 *ngIf="canApprove" style="color: #CCC">PENDENTE APROVAÇÃO</h4>
							<h4 *ngIf="isCompleted" style="color: #CCC">FINALIZADO</h4>
						</gp-form-col>
					</gp-form-row>
				</gp-card>
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<gp-card>
					<gp-form-row>
						<gp-form-col cols="12 12 12">
							<h3>Erros: <span class="text-danger">{{ batchImport.totalErrorCompleted }}</span></h3>
							<hr />
							<h4 *ngIf="isProcessing" style="color: #CCC">PROCESSANDO...</h4>
							<h4 *ngIf="canApprove" style="color: #CCC">PENDENTE APROVAÇÃO</h4>
							<h4 *ngIf="isCompleted" style="color: #CCC">FINALIZADO</h4>
						</gp-form-col>
					</gp-form-row>
				</gp-card>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card title="Detalhes dos pedidos" *ngIf="canViewTemporary">
		<gp-form-row>
			<gp-form-col cols="12 4 4">
				<gp-simple-input label="Filtrar registros">
					<select name="statusFilter" class="form-control" [(ngModel)]="statusFilter.status" (change)="findTemporaryOrders()">
						<option value="">Todos</option>
						<option value="success">Sucesso (Importação)</option>
						<option value="warning">Avisos (Importação)</option>
						<option value="error">Erros (Importação)</option>
						<option value="processedsuccess">Sucesso (Aprovado)</option>
						<option value="processederror">Erros (Aprovado)</option>
					</select>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<hr />
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<h4>Pedidos encontrados</h4>
				<gp-grid
					name="grid"
					[rows]="temporaryOrders"
					[columns]="['Número do Pedido', 'Data', 'Status']"
					[fields]="['clientOrderNumber', 'formattedOrderDate', 'status']"
					[showActive]="true"
					activeHeaderText="Processado"
					activeField="processed"
					activeText="Sim"
					inactiveText="Não"
					[showPagination]="true"
					[showTotalPages]="false"
					[showEdit]="true"
					[showDelete]="canRemoveTemporary"
					[loading]="loadingDetails"
					[pageSize]="limit"
					(onDelete)="deleteTemporaryOrder($event)"
					(onEdit)="editTemporaryOrder($event)"
					(onPageChanged)="onPageChanged($event)">
				</gp-grid>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-alert #temporaryOrdersAlert></gp-alert>
			</gp-form-col>
		</gp-form-row>

	</gp-card>
</div>


<div class="modal fade" style="margin-top: 40px; background-color: rgba(0, 0, 0, 0.5);" bsModal #classicModal="bs-modal" (onHide)="clearModal()" [config]="{backdrop: false}" role="dialog" tabindex="-1" aria-hidden="true" aria-labelledby="smallModal">
	<div class="modal-dialog modal-md" style="margin-left: 20%;">
		<div class="modal-content" style="width: 1000px;">
			<div class="modal-header">
				<button class="close" aria-label="Close" (click)="classicModal.hide()">
					<span aria-hidden="true">&times;</span>
				</button>
				<h4 class="modal-title">Pedido: {{ generalInfo.clientOrderNumber }}</h4>
			</div>
			<div class="modal-body">
				<gp-form-row>
					<gp-form-col cols="12 12 12">
						<accordion [closeOthers]="true">

							<accordion-group panelClass="b0 mb-sm panel-default">
								<div accordion-heading>
									<label>Dados Gerais</label>
								</div>
								<form #generalForm="ngForm" (ngSubmit)="saveGeneralInfo()" novalidate>
									<gp-form-row>
										<gp-form-col cols="12 3 3">
											<gp-simple-input label="Número do pedido" [required]="true" errorMessage="Número do pedido é obrigatório">
												<input type="text" name="clientOrderNumber" class="form-control" required [(ngModel)]="generalInfo.clientOrderNumber" />
											</gp-simple-input>
										</gp-form-col>
										<gp-datepicker cols="12 3 3 3" label="Data do Pedido" name="orderDate" [(ngModel)]="generalInfo.orderDate"></gp-datepicker>
									</gp-form-row>
									<gp-form-row>
										<gp-form-col cols="12 3 3">
											<gp-simple-input label="Nome do Participante" [required]="true" errorMessage="Nome do participante é obrigatório">
												<input type="text" name="name" class="form-control" required [(ngModel)]="generalInfo.name" />
											</gp-simple-input>
										</gp-form-col>
										<gp-form-col cols="12 3 3">
											<gp-simple-input label="Documento (CPF/CNPJ)" [required]="true" errorMessage="Documento é obrigatório">
												<input type="text" name="document" class="form-control" required [(ngModel)]="generalInfo.document" />
											</gp-simple-input>
										</gp-form-col>
										<gp-form-col cols="12 6 6">
											<gp-simple-input label="E-mail do participante" [required]="true" errorMessage="E-mail do participante é obrigatório">
												<input type="text" name="email" class="form-control" required [(ngModel)]="generalInfo.email" />
											</gp-simple-input>
										</gp-form-col>
									</gp-form-row>
									<gp-form-row>
										<gp-form-col cols="12 3 3">
											<gp-simple-input label="Telefone">
												<gp-input-mask name="telephone" mask="(00) 0000-0000" [guide]="false" [(ngModel)]="generalInfo.telephone">
												</gp-input-mask>
											</gp-simple-input>
										</gp-form-col>
										<gp-form-col cols="12 3 3">
											<gp-simple-input label="Celular">
												<gp-input-mask name="cellphone" mask="(00) 0000-00000" [guide]="false" [(ngModel)]="generalInfo.cellphone">
												</gp-input-mask>
											</gp-simple-input>
										</gp-form-col>
									</gp-form-row>
									<gp-form-row>
										<gp-form-col cols="12 3 3">
											<gp-simple-input label="Valor de desconto (R$)">
												<gp-input-mask name="discountCurrency" [onlyDecimal]="true" [decimais]="2" [(ngModel)]="generalInfo.discountCurrency">
												</gp-input-mask>
											</gp-simple-input>
										</gp-form-col>
										<gp-form-col cols="12 3 3">
											<gp-simple-input label="Valor de desconto (Pontos)">
												<gp-input-mask name="discountPoints" [onlyDecimal]="true" [decimais]="2" [(ngModel)]="generalInfo.discountPoints">
												</gp-input-mask>
											</gp-simple-input>
										</gp-form-col>
										<gp-form-col cols="12 3 3">
											<gp-simple-input label="Valor do frete (R$)">
												<gp-input-mask name="shippingCostCurrency" [onlyDecimal]="true" [decimais]="2" [(ngModel)]="generalInfo.shippingCostCurrency">
												</gp-input-mask>
											</gp-simple-input>
										</gp-form-col>
										<gp-form-col cols="12 3 3">
											<gp-simple-input label="Valor do frete (Pontos)">
												<gp-input-mask name="shippingCostPoints" [onlyDecimal]="true" [decimais]="2" [(ngModel)]="generalInfo.shippingCostPoints">
												</gp-input-mask>
											</gp-simple-input>
										</gp-form-col>
									</gp-form-row>
									<gp-form-row>
										<gp-form-col cols="12">
											<gp-simple-input label="Observações" [required]="false">
												<textarea type="text" name="observations" class="form-control" [(ngModel)]="generalInfo.observations">
												</textarea>
											</gp-simple-input>
										</gp-form-col>
									</gp-form-row>
									<gp-form-row *ngIf="canEditOrderInfo">
										<gp-form-col cols="12 4 4">
											<gp-spinner-button type="submit" text="Salvar dados gerais"
												size="md" pull="left" [pink]="true" icon="send"
												loadingText="Processando" [loading]="loadingGeneralInfoForm" [disabled]="!generalForm.valid">
											</gp-spinner-button>
										</gp-form-col>
									</gp-form-row>
									<gp-form-row>
										<gp-form-col cols="12 12 12">
											<gp-alert #generalInfoAlert></gp-alert>
										</gp-form-col>
									</gp-form-row>
								</form>
							</accordion-group>

							<accordion-group panelClass="b0 mb-sm panel-default">
								<div accordion-heading>
									<label>Endereço de Entrega</label>
								</div>
								<form #shippingAddressForm="ngForm" (ngSubmit)="saveShippingAddress()" novalidate>
									<gp-form-row>
										<gp-form-col cols="12 4 4">
											<gp-simple-input label="Descrição do endereço">
												<input type="text" name="addressName" class="form-control" [(ngModel)]="shippingAddress.addressName" />
											</gp-simple-input>
										</gp-form-col>
										<gp-form-col cols="12 4 4">
											<gp-simple-input label="CEP" [required]="true" errorMessage="CEP é obrigatório">
												<gp-input-mask name="cep" mask="00000-000" [guide]="false" [(ngModel)]="shippingAddress.cep">
												</gp-input-mask>
											</gp-simple-input>
										</gp-form-col>
									</gp-form-row>
									<gp-form-row>
										<gp-form-col cols="12 4 4">
											<gp-simple-input label="Logradouro" [required]="true" errorMessage="Logradouro é obrigatório">
												<input type="text" name="street" class="form-control" required [(ngModel)]="shippingAddress.street" />
											</gp-simple-input>
										</gp-form-col>
										<gp-form-col cols="12 4 4">
											<gp-simple-input label="Número" [required]="true" errorMessage="Número é obrigatório">
												<input type="text" name="number" class="form-control" required [(ngModel)]="shippingAddress.number" />
											</gp-simple-input>
										</gp-form-col>
										<gp-form-col cols="12 4 4">
											<gp-simple-input label="Complemento">
												<input type="text" name="complement" class="form-control" required [(ngModel)]="shippingAddress.complement" />
											</gp-simple-input>
										</gp-form-col>
									</gp-form-row>
									<gp-form-row>
										<gp-form-col cols="12 4 4">
											<gp-simple-input label="Bairro" [required]="true" errorMessage="Bairro é obrigatório">
												<input type="text" name="neighborhood" class="form-control" required [(ngModel)]="shippingAddress.neighborhood" />
											</gp-simple-input>
										</gp-form-col>
										<gp-form-col cols="12 4 4">
											<gp-simple-input label="Cidade" [required]="true" errorMessage="Cidade é obrigatório">
												<input type="text" name="city" class="form-control" required [(ngModel)]="shippingAddress.city" />
											</gp-simple-input>
										</gp-form-col>
										<gp-form-col cols="12 4 4">
											<gp-simple-input label="Estado" [required]="true" errorMessage="Estado é obrigatório">
												<select class="form-control" name="uf" [(ngModel)]="shippingAddress.uf" required>
													<option>Selecione</option>
													<option *ngFor="let s of states" [value]="s.partialName">{{ s.fullName }}</option>
												</select>
											</gp-simple-input>
										</gp-form-col>
									</gp-form-row>
									<gp-form-row>
										<gp-form-col cols="12 12 12">
											<gp-simple-input label="Ponto de Referência">
												<input type="text" name="reference" class="form-control" required [(ngModel)]="shippingAddress.reference" />
											</gp-simple-input>
										</gp-form-col>
									</gp-form-row>
									<gp-form-row *ngIf="canEditShippingAddress">
										<gp-form-col cols="12 4 4">
											<gp-spinner-button type="submit" text="Salvar endereço de entrega"
												size="md" pull="left" [pink]="true" icon="send" loadingText="Processando"
												[loading]="loadingShippingAddressForm" [disabled]="!shippingAddressForm.valid">
											</gp-spinner-button>
										</gp-form-col>
									</gp-form-row>
									<gp-form-row>
										<gp-form-col cols="12 12 12">
											<gp-alert #shippingAddressAlert></gp-alert>
										</gp-form-col>
									</gp-form-row>
								</form>
							</accordion-group>

							<accordion-group panelClass="b0 mb-sm panel-default">
								<div accordion-heading>
									<label>Itens do Pedido</label>
								</div>
								<gp-form-row *ngIf="!selectedSkuCode">
									<gp-form-col cols="12 12 12">
										<gp-grid
											name="grid"
											[rows]="items"
											[columns]="['Código SKU', 'Nome do Parceiro', 'Quantidade']"
											[fields]="['skuCode', 'partnerName', 'quantity']"
											[showActive]="false"
											[showPagination]="false"
											[showTotalPages]="false"
											[showEdit]="true"
											[showDelete]="canEditItem"
											[loading]="loadingItemsForm"
											[pageSize]="100"
											(onDelete)="deleteTemporaryItem($event)"
											(onEdit)="editTemporaryItem($event)">
										</gp-grid>
									</gp-form-col>
								</gp-form-row>
								<gp-form-row>
									<gp-form-col cols="12 12 12">
										<gp-alert #itemsGridAlert></gp-alert>
									</gp-form-col>
								</gp-form-row>

								<div *ngIf="selectedSkuCode">
									<form #itemDetailForm="ngForm" (ngSubmit)="saveItem()" novalidate>
										<gp-form-row>
											<gp-form-col cols="12 4 4">
												<gp-simple-input label="Código SKU" [required]="true" errorMessage="Código SKU é obrigatório">
													<input type="text" name="skuCode" class="form-control" required [(ngModel)]="itemDetail.skuCode" />
												</gp-simple-input>
											</gp-form-col>
										</gp-form-row>
										<gp-form-row>
											<gp-form-col cols="12 6 6">
												<gp-simple-input label="Parceiro" errorMessage="Parceiro é obrigatório">
													<select name="partner" class="form-control" [(ngModel)]="itemDetail.partnerId" placeholder="Selecione o parceiro">
														<option *ngFor="let p of itemDetail.foundPartners" [value]="p.id">{{ p.name }}</option>
													</select>
												</gp-simple-input>
											</gp-form-col>
											<gp-form-col cols="12 6 6">
												<gp-simple-input label="Quantidade" [required]="true" errorMessage="Quantidade é obrigatório">
													<gp-input-mask name="quantity" [onlyInteger]="true" [required]="true" [(ngModel)]="itemDetail.quantity">
													</gp-input-mask>
												</gp-simple-input>
											</gp-form-col>
										</gp-form-row>
										<gp-form-row>
											<gp-form-col cols="12 6 6">
												<gp-simple-input label="Valor unitário importado (R$)" [required]="true" errorMessage="Valor unitário é obrigatório">
													<gp-input-mask name="unitPriceCurrency" [onlyDecimal]="true" [decimais]="2" [required]="true" [(ngModel)]="itemDetail.unitPriceCurrency">
													</gp-input-mask>
												</gp-simple-input>
											</gp-form-col>
											<gp-form-col cols="12 6 6">
												<gp-simple-input label="Valor unitário parceiro (R$)">
													<gp-input-mask name="calculatedUnitPriceCurrency" [disabled]="true" [onlyDecimal]="true" [decimais]="2" [(ngModel)]="itemDetail.calculatedUnitPriceCurrency">
													</gp-input-mask>
												</gp-simple-input>
											</gp-form-col>
										</gp-form-row>
										<gp-form-row>
											<gp-form-col cols="12 6 6">
												<gp-simple-input label="Valor de desconto (R$)">
													<gp-input-mask name="discountCurrency" [onlyDecimal]="true" [decimais]="2" [(ngModel)]="itemDetail.discountCurrency">
													</gp-input-mask>
												</gp-simple-input>
											</gp-form-col>
											<gp-form-col cols="12 6 6">
												<gp-simple-input label="Valor de desconto (Pontos)">
													<gp-input-mask name="discountPoints" [onlyDecimal]="true" [decimais]="2" [(ngModel)]="itemDetail.discountPoints">
													</gp-input-mask>
												</gp-simple-input>
											</gp-form-col>
										</gp-form-row>
										<gp-form-row>
											<gp-form-col cols="12 6 6">
												<gp-simple-input label="Valor de frete importado (R$)" [required]="true" errorMessage="Valor de frete é obrigatório">
													<gp-input-mask name="shippingCostCurrency" [onlyDecimal]="true" [decimais]="2" [required]="true" [(ngModel)]="itemDetail.shippingCostCurrency">
													</gp-input-mask>
												</gp-simple-input>
											</gp-form-col>
											<gp-form-col cols="12 6 6">
												<gp-simple-input label="Valor de frete parceiro (R$)">
													<gp-input-mask name="calculatedShippingCostCurrency" [disabled]="true" [onlyDecimal]="true" [decimais]="2" [(ngModel)]="itemDetail.calculatedShippingCostCurrency">
													</gp-input-mask>
												</gp-simple-input>
											</gp-form-col>
										</gp-form-row>
										<gp-form-row>
											<gp-form-col cols="12 6 6">
												<gp-spinner-button type="button" text="Voltar" bootstrapClass="default" size="md" pull="left" marginRight="10px"
													[disabled]="loading" (click)="hideItemForm()" [showSpinner]="false">
												</gp-spinner-button>
												<gp-spinner-button *ngIf="canEditItem" type="submit" text="Salvar item do pedido"
													size="md" pull="left" [pink]="true" icon="send"
													loadingText="Processando" [loading]="loadingItemsForm" [disabled]="!itemDetailForm.valid">
												</gp-spinner-button>
											</gp-form-col>
										</gp-form-row>
										<gp-form-row>
											<gp-form-col cols="12 12 12">
												<gp-alert #itemAlert></gp-alert>
											</gp-form-col>
										</gp-form-row>
										<gp-form-row *ngIf="itemDetail.statusMessages && itemDetail.statusMessages.length">
											<gp-form-col cols="12 12 12">
												<hr />
												<strong>Detalhes do Processamento</strong>
												<ul>
													<li *ngFor="let e of itemDetail.statusMessages">{{ e }}</li>
												</ul>
											</gp-form-col>
										</gp-form-row>
									</form>
								</div>
								
							</accordion-group>

							<accordion-group panelClass="b0 mb-sm panel-default" *ngIf="hasErrorsOrWarning">
								<div accordion-heading>
									<label>Avisos/Erros do Pedido</label>
								</div>
								<div class="row">
									<div class="col-md-12">
										<ul>
											<li *ngFor="let w of generalInfo.warningMessages">Aviso: {{ w }}</li>
											<li *ngFor="let e of generalInfo.errorMessages">Erro: {{ e }}</li>
										</ul>
									</div>
								</div>
							</accordion-group>

						</accordion>
					</gp-form-col>
				</gp-form-row>
			</div>
		</div>
	</div>
</div>
