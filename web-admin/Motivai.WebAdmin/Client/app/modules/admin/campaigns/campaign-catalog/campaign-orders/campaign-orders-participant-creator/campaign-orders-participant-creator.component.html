<spinner [show]="loading" [overlay]="true"></spinner>
<gp-alert [overlay]="true" #gpAlert></gp-alert>

<div *ngIf="!wasOrderCreated && canOrder">
	<campaign-orders-participant-search [campaignId]="campaignId"
		(onSelectedParticipant)="onSelectedParticipant($event)">
	</campaign-orders-participant-search>

	<campaign-orders-products-search [campaignId]="campaignId" [document]="selectedParticipant.document" (onMovedShoppingCart)="onMovedShoppingCart($event)"
		*ngIf="hasSelectedParticipant" #ordersProducts>
	</campaign-orders-products-search>

	<gp-card [last]="true" *ngIf="shoppingCartIsNotEmpty">
		<gp-form-row>
			<gp-form-col cols="12 12 12" [inputGroup]="false">
				<gp-spinner-button type="button" [pink]="true" icon="send" pull="right" text="Criar pedido"
					[loading]="loading" loadingText="Processando" marginTop="27px" (click)="onCreateOrders()">
				</gp-spinner-button>

				<gp-spinner-button type="button" pull="right" bootstrapClass="danger" icon="trash"
					text="Cancelar pedido" marginTop="27px" marginRight="1em" (click)="onCancelOrders()">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</gp-card>
</div>

<div *ngIf="wasOrderCreated">
	<campaign-orders-participant-view [order]="order" [selectedParticipant]="selectedParticipant" #orderParticipantView>
	</campaign-orders-participant-view>

	<gp-card [last]="true">
		<gp-spinner-button bootstrapClass="primary" text="Exportar" pull="right" icon="download" 
			[pink]="true" loadingText="Exportando..." (click)="onExport()">
		</gp-spinner-button>
		
		<gp-spinner-button bootstrapClass="primary" text="Voltar" pull="right" icon="backward" 
			marginRight="1em" (click)="onBack()">
		</gp-spinner-button>
	</gp-card>
</div>

<div class="alert alert-warning" role="alert" *ngIf="!canOrder">
	Você não tem permissão para acessar esse módulo!
</div>
