import {
  Component,
  Output,
  EventEmitter,
  ViewChild,
  Input
} from '@angular/core';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';

import { GpModalComponent } from '../../../../../../../shared/components/gp-modal/gp-modal.component';
import { CampaignOrdersHandlerService } from '../campaign-orders-handler.service';

@Component({
  selector: 'order-vouchers',
  templateUrl: 'order-vouchers.component.html'
})
export class OrderVouchersComponent {
  @ViewChild('modal') modal: GpModalComponent;
  @ViewChild('alert') alert: GpAlertComponent;

  @Input()
  campaignId: string;

  @Input()
  orderId: string;
  @Input()
  itemGrouperId: string;

  loading: boolean;
  vouchers: Array<any> = [];
  faEyeClass: string = 'fa fa-eye-slash';
  voucherLink: string = '';


  constructor(private service: CampaignOrdersHandlerService) {}

  open() {
    this.modal.show();
  }

  close() {
    this.modal.hide();
  }

  clear() {
    this.itemGrouperId = null;
    this.vouchers = [];
  }

  public showExistingVouchers(childOrder: any) {
    this.clear();
    this.open();
    this.itemGrouperId = childOrder.itemGrouperId;
    this.vouchers = childOrder.vouchers || [];
  }

  public notifyOrderVouchers() {
    this.alert.confirm('Confirma o envio do e-mail com os vales para o participante?')
      .then(result => {
        if (result && result.value) {
          this.loading = true;
          this.service.notifyOrderVouchers(this.campaignId, this.orderId, this.itemGrouperId)
            .subscribe(
              sent => {
                this.loading = false;
                if (sent) {
                  this.alert.showSuccess('Notificação dos vales efetuada com sucesso.');
                } else {
                  this.alert.showWarning('Não foi possível efetuar a notificação.');
                }
              },
              err => {
                this.loading = false;
                this.alert.showError(err);
              }
            );
        }
      });
  }
}
