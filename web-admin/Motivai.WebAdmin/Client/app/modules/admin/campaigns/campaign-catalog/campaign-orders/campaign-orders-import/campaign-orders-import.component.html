<form #variationValueForm="ngForm" (ngSubmit)="saveSettings()" novalidate>
	<gp-card title="Configuração de Variação de Valor">
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-input-radio name="variationType" label="Percentual" radioValue="Percentage" [(ngModel)]="settings.variationType"></gp-input-radio>
				<gp-input-radio name="variationType" label="Moeda (R$)" radioValue="Currency" [(ngModel)]="settings.variationType"></gp-input-radio>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 3 3">
				<gp-simple-input label="Variação máxima de preço" [required]="true" errorMessage="Valor é obrigatório">
					<gp-input-mask 
						id="priceMaxVariation" 
						name="priceMaxVariation" 
						[required]="true"
						[onlyDecimal]="true" 
						[(ngModel)]="settings.priceMaxVariation">
					</gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 3 3">
				<gp-spinner-button type="submit" [pink]="true" icon="send"
					size="md" pull="left" text="Salvar configuração" 
					loadingText="Processando" [loading]="loadingSettings" marginTop="27px" [disabled]="!variationValueForm.valid">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
		<div class="row">
			<div class="col-md-12">
				<gp-alert #settingsAlert></gp-alert>
			</div>
		</div>
	</gp-card>
</form>

<tabset #tabs class="bg-white p0 tab-no-border" [justified]="true">
	<tab (select)="refreshGrid()">
		<ng-template tabHeading>Lotes</ng-template>
		<campaign-import-batchlist #listComponent [campaignId]="campaignId" processType="Order" (edit)="editBatchImport($event)"
		 #batchList>
		</campaign-import-batchlist>
	</tab>
	<tab>
		<ng-template tabHeading>Importação</ng-template>
		<campaigns-orders-import-edit [campaignId]="campaignId" [batchImportId]="batchImportId"></campaigns-orders-import-edit>
	</tab>
</tabset>
