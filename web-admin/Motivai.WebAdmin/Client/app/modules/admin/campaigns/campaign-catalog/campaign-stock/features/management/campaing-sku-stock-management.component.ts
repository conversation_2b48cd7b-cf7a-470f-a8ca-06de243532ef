import { Compo<PERSON>, ViewChild, OnInit, OnDestroy } from '@angular/core';

import { TabsetComponent } from 'ng2-bootstrap';
import { Subscription } from 'rxjs';

import { AuthStore } from '../../../../../../../core/auth/auth.store';
import { RxjsHelpers } from '../../../../../../../shared/helpers/rxjs-helpers';
import { CampaignStore } from '../../../../campaign.store';

import { CampaignSkuStockEditorComponent } from './campaign-sku-stock-editor/campaign-sku-stock-editor.component';
import { CampaignStocksListingComponent } from './campaign-stocks-listing/campaign-stocks-listing.component';

@Component({
  selector: 'campaing-sku-stock-management',
  templateUrl: './campaing-sku-stock-management.component.html'
})
export class CampaingSkuStockManagementComponent implements OnInit, OnD<PERSON>roy {
  @ViewChild('tabs') tabs: TabsetComponent;
  @ViewChild('listing') listing: CampaignStocksListingComponent;
  @ViewChild('editor') editor: CampaignSkuStockEditorComponent;

  private _campaign$: Subscription;
  campaignId: string;
  
  constructor(private authStore: AuthStore, private _campaignStore: CampaignStore) {}

  ngOnInit() {
    this._campaign$ = this._campaignStore.asObservable
      .subscribe(id => {
        this.campaignId = id;
      });
  }

  ngOnDestroy() {
    RxjsHelpers.unsubscribe(this._campaign$);
  } 

  refreshGrid() {
    this.listing.loadStocks();
    this.editor.newStock();
  }

  editStock(stock: any) {
    if (stock && stock.id) {
      this.editor.editStock(stock.id);
      this.tabs.tabs[1].active = true;
    }
  }  

  get canEditOrUpdate() {
    return this.authStore.role.CAMPAIGNS_STOCK_EDIT;
  }
}
