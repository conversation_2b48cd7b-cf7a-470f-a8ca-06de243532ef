<gp-modal #modal title="Adicionar Boleto aos Filtros" width="900px" (onClose)="reset()">
	<spinner [overlay]="true" [show]="loading"></spinner>
	<gp-alert [overlay]="true" #alert></gp-alert>

	<gp-card title="Dados do Boleto">
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Nome do Beneficiário">
					<input type="text" class="form-control" name="receiverName"
						[ngModel]="billsPaymentConsultsError.billDetails.receiverName" disabled>
				</gp-simple-input>
			</gp-form-col>

			<gp-form-col cols="12 6 6">
				<gp-simple-input label="CPF/CNPJ do Beneficiário">
					<input type="text" class="form-control" name="receiverDocument"
						[ngModel]="billsPaymentConsultsError.billDetails.receiverDocument" disabled>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Nome do Pagador">
					<input type="text" class="form-control" name="payerName"
						[ngModel]="billsPaymentConsultsError.billDetails.payerName" disabled>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="CPF/CNPJ do Pagador">
					<input type="text" class="form-control" name="payerDocument"
						[ngModel]="billsPaymentConsultsError.billDetails.payerDocument" disabled>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Emissor">
					<input type="text" class="form-control" name="assignor"
						[ngModel]=" billsPaymentConsultsError.billDetails.assignor" disabled>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Valor Líquido do Boleto">
					<input type="text" class="form-control" name="billingAmount"
						[ngModel]="billsPaymentConsultsError.billDetails.billingAmount" disabled>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card>
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-simple-input label="Selecione quais campos do boleto serão adicionados ao filtro" [required]="true">
					<gp-form-row>
						<!-- <gp-input-checkbox grid="12 6 4 4" name="receiverName" text="Nome do Beneficiário"
							[(ngModel)]="filters.applyReceiverName">
						</gp-input-checkbox> -->
						<gp-input-checkbox grid="12 6 4 4" name="receiverDocument" text="CPF/CNPJ do Beneficiário"
							[(ngModel)]="filters.applyReceiverDocument">
						</gp-input-checkbox>
						<!-- <gp-input-checkbox grid="12 6 4 4" name="payerName" text="Nome do Pagador"
							[(ngModel)]="filters.applyPayerName">
						</gp-input-checkbox> -->
					</gp-form-row>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-form-row>
					<!-- <gp-input-checkbox grid="12 6 4 4" name="payerDocument" text="CPF/CNPJ do Pagador"
						[(ngModel)]="filters.applyPayerDocument">
					</gp-input-checkbox>
					<gp-input-checkbox grid="12 6 4 4" name="assignor" text="Emissor"
						[(ngModel)]="filters.applyAssignor">
					</gp-input-checkbox> -->
					<!--gp-input-checkbox grid="12 6 4 4" name="billingAmount" text="Valor Líquido do Boleto"
						[(ngModel)]="filters.applyBillingAmount">
					</gp-input-checkbox-->
				</gp-form-row>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card [last]="true">
		<gp-form-row>
			<gp-form-col cols="12" [inputGroup]="false">
				<gp-spinner-button type="button" [pink]="true" bootstrapClass="primary" text="Salvar" pull="right"
					icon="send" [loading]="loading" loadingText="Aguarde" [disabled]="!canAddFilters" (click)="onAddFilters()">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</gp-card>
</gp-modal>
