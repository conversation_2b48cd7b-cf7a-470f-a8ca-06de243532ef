<div>
	<gp-alert [overlay]="true" #gpAlert></gp-alert>

	<gp-card title="Pesquisa de boletos" [first]="true">
		<gp-form-row>
			<gp-form-col cols="12 4 4">
				<gp-simple-input label="Informe o tipo do boleto" [required]="true">
					<gp-select name="typeBill" [items]="typeBills" [allowClear]="false" [(ngModel)]="typeBill"></gp-select>
				</gp-simple-input>
			</gp-form-col>

			<gp-form-col cols="12 4 4">
				<gp-simple-input label="Pesquisar por" [required]="false">
					<gp-select name="searchByField" [items]="searchFields" [allowClear]="true"
						(change)="onChangeSearchField($event)" [(ngModel)]="searchByField">
					</gp-select>
				</gp-simple-input>
			</gp-form-col>

			<gp-form-col cols="12 4 4">
				<label>{{ lableSearchValue }}</label>
				<input type="text" class="form-control" name="searchValue" [(ngModel)]="searchValue" />
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-datepicker cols="12 4 4" [required]="true" label="De" name="from" [(ngModel)]="from"></gp-datepicker>
			<gp-datepicker cols="12 4 4" [required]="true" label="Até" name="to" [(ngModel)]="to"></gp-datepicker>

			<gp-form-col cols="12 4">
				<gp-simple-input label="Status do registro" [required]="true">
					<select name="status" class="form-control" [(ngModel)]="status">
						<option value="CREATED">Criado</option>
						<option value="REJECTED">Rejeitado</option>
						<option value="HANDLED">Manipulado</option>
					</select>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12">
				<gp-spinner-button type="button" pull="right" buttonClass="bg-primary-dark" icon="plus" text="Pesquisar"
					[search]="true" [loading]="loading" [disabled]="loading" (click)="onSearchBills()"
					marginRight="5px" marginTop="27px">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card title="Boletos encontrados" [last]="true">
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-grid #billsGrid name="billsGrid"
					[rows]="bills"
					[columns]="['Data Tentativa de Pagamento', 'Tipo do Boleto', 'Valor do Boleto', 'Pagador', 'Recebedor', 'Status']"
					[fields]="['formattedCreatedDate', 'typeBill', 'billDetails.billingAmount', 'billDetails.payerName', 
						'billDetails.receiverName', 'formattedStatus']"
					[showActive]="false" [showPagination]="true" [showTotalPages]="false" [pageSize]="params.limit"
					[showEdit]="true" [showDelete]="false"
					[loading]="loading"
					(onEdit)="onEditBill($event)" (onPageChanged)="onPageChanged($event)">
				</gp-grid>
			</gp-form-col>
		</gp-form-row>
	</gp-card>
</div>
