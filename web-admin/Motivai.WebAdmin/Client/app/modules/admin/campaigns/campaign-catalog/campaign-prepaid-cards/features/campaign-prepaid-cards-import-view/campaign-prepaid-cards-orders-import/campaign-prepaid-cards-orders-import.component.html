<spinner [overlay]="true" [show]="loading"></spinner>
<gp-alert [overlay]="true" #gpAlert></gp-alert>

<gp-card [first]="true" title="Importação de Lote" *ngIf="!hasBatch">
	<form>
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-fileupload name="files" [multiple]="true" [csv]="true" [path]="uploadPath"
					(oncomplete)="onUploadComplete($event)" #gpFileUploader>
				</gp-fileupload>
			</gp-form-col>
		</gp-form-row>

		<hr />
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<p>Instruções de uso:</p>
				<p>1. O formato do arquivo deve ser <u>[.csv]</u>, com as colunas separadas por [;], <u>sem cabeçalho</u>.</p>
				<p>2. Caso a identificação seja documento, informar: 11 digitos para CPF; 14 digitos para CNPJ, sem formatação. <strong>Se atente a formatar a coluna de documento como <u>texto</u>, para manter os zeros à esquerda.</strong></p>
				<p>3. Os tipos de cartões pré-pagos permitidos: <u>Sem Saque</u> e <u>Com Saque</u>.</p>
				<p>4. O valor que será creditado no cartão precisa estar em reais:</p>
				<ul>
					<li>Casas decimais: o valor precisa ser formatado como texto e com ponto <u>[.]</u> no lugar da virgula <u>[,]</u>.</li>
					<li>Exemplo: uma recarga de <u>R$ 1.152,50</u> precisar estar salvo como <u>1152.50</u>.</li>
				</ul>
			</gp-form-col>

			<gp-form-col cols="12 6 6">
				<p>5. As colunas devem seguir a seguinte ordem:</p>
				<ul>
					<li>
						<strong>A: </strong>documento
					</li>
					<li>
						<strong>B: </strong>nome do participante
					</li>
					<li>
						<strong>C: </strong>tipo do cartão
						Valores permitidos:
						<ul>
							<li>com saque</li>
							<li>sem saque</li>
						</ul>
					</li>
					<li>
						<strong>D: </strong>valor da recarga
					</li>
					<li>
						<strong>E: </strong>Emitir ou reemitir novo cartão
						Valores permitidos:
						<ul>
							<li>Sim</li>
							<li>Não</li>
						</ul>
					</li>
					<li>
						<strong>F: </strong>Forma de identificação do cartão
						Valores permitidos:
						<ul>
							<li>Proxy</li>
							<li>Número</li>
						</ul>
					</li>
					<li>
						<strong>G: </strong>Identificação do cartão (proxy ou número de acordo com a coluna F)
					</li>
					<li>
						<strong>H: </strong>CEP do endereço de entrega (se informado será consultado no Correios)
					</li>
					<li>
						<strong>I: </strong>Número do endereço de entrega
					</li>
					<li>
						<strong>J: </strong>Complemento do endereço
					</li>
					<li>
						<strong>K: </strong>Logradouro do endereço
					</li>
					<li>
						<strong>L: </strong>Bairro do endereço
					</li>
					<li>
						<strong>M: </strong>Cidade do endereço
					</li>
					<li>
						<strong>N: </strong>UF do endereço
					</li>
					<li>
						<strong>O: </strong>CPF do recebedor
					</li>
					<li>
						<strong>P: </strong>Nome do recebedor
					</li>
					<li>
						<strong>Q: </strong>Telefone do recebedor
					</li>
					<li>
						<strong>R: </strong>Celular do recebedor
					</li>
				</ul>
			</gp-form-col>

			<gp-form-col cols="12 6 6" additionalClasses="overflow-hidden">
				<img src="/assets/img/imports/modelo-planilha-pedidos-cartoes-prepagos.png" style="width: 500px;" /> <br /><br />
				<a href="/assets/files/modelo_planilha_importacao_cartoes_prepagos.csv">Clique aqui para baixar um modelo de planilha</a>
			</gp-form-col>
		</gp-form-row>
	</form>
</gp-card>

<div *ngIf="hasBatch">
	<gp-card title="Detalhes da importação" [first]="true">
		<gp-form-row>
			<gp-form-col cols="12 6">
				<gp-simple-input label="Data de Criação">
					<input type="text" class="form-control" readonly disabled [ngModel]="(batch.createDate | datetimezone)" />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6">
				<gp-simple-input label="Status">
					<input type="text" class="form-control" readonly disabled [ngModel]="batch.statusDescription" />
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 6">
				<gp-simple-input label="Qtde linhas">
					<input type="text" class="form-control" readonly disabled [ngModel]="(batch.totalLines | number:'1.0-0')" />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6">
				<gp-simple-input label="Qtde linhas em branco">
					<input type="text" class="form-control" readonly disabled [ngModel]="(batch.totalBlankLines | number:'1.0-0')" />
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<hr class="hidden-print" *ngIf="batch.errorOccurred" />
		<div class="row mb-lg" *ngIf="batch.errorOccurred">
			<div class="col-xs-12 col-sm-12">
				<div class="clearfix">
					<p class="pull-left"><strong>Ocorreu erro:</strong></p>
					<p class="pull-left mr left-p1">
						<span [class.text-danger]="batch.errorOccurred">
							<strong>{{ batch.errorOccurred ? 'Sim' : 'Não' }}</strong>
						</span>
						<span *ngIf="batch.errorMessage"> - {{ batch.errorMessage }}</span>
					</p>
				</div>
			</div>
			<div class="col-xs-12 col-sm-12" *ngIf="batch.errorDetails">
				<div class="clearfix">
					<p class="pull-left"><strong>Detalhes do erro:</strong></p>
					<p class="pull-left mr left-p1">{{ batch.errorDetails }}</p>
				</div>
			</div>
		</div>
	</gp-card>

	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<h4>Resultado da leitura do arquivo</h4>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 6 6">
			<gp-card>
				<gp-form-row>
					<gp-form-col cols="12 12 12" [inputGroup]="false">
						<h3>Sucesso: <span class="text-success">{{ (batch.totalSuccessfullyImported | number:'1.0-0') }}</span></h3>
						<hr />
						<div class="import-box-actions">
							<a class="import-link-action" title="Aprovar o lote" (click)="approve()" *ngIf="canApprove">
								<h4 class="text-success">APROVAR</h4>
							</a>
							<a class="import-link-action" title="Reprovar o lote" (click)="reject()" *ngIf="canApprove">
								<h4 class="text-danger">REPROVAR</h4>
							</a>
							<h4 *ngIf="!canApprove" style="color: #CCC">{{ isProcessing ? 'PROCESSANDO...' : '-' }}</h4>
						</div>
					</gp-form-col>
				</gp-form-row>
			</gp-card>
		</gp-form-col>
		<gp-form-col cols="12 6 6">
			<gp-card>
				<gp-form-row>
					<gp-form-col cols="12 12 12" [inputGroup]="false">
						<h3>Erros: <span class="text-danger">{{ (batch.totalErrorsOnImport | number:'1.0-0') }}</span></h3>
						<hr />
						<div class="import-box-actions">
							<h4 *ngIf="batch.totalErrorsOnImport === 0" style="color: #CCC">-</h4>
							<a class="import-link-action" *ngIf="batch.totalErrorsOnImport > 0 && batch.importErrorsFileUrl" (click)="downloadErrorsCsv()">
								<h4 class="text-danger">DETALHES</h4>
							</a>
						</div>
					</gp-form-col>
				</gp-form-row>
			</gp-card>
		</gp-form-col>
	</gp-form-row>

	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<h4>Resultado da aprovação</h4>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 6 6">
			<gp-card>
				<gp-form-row>
					<gp-form-col cols="12 12 12" [inputGroup]="false">
						<h3>Sucesso: <span class="text-success">{{ (batch.totalSuccessfullyApproved | number:'1.0-0') }}</span></h3>
						<hr />
						<div class="import-box-actions">
							<h4 style="color: #CCC">{{ isApproving ? 'PROCESSANDO...' : '-' }}</h4>
						</div>
					</gp-form-col>
				</gp-form-row>
			</gp-card>
		</gp-form-col>
		<gp-form-col cols="12 6 6">
			<gp-card>
				<gp-form-row>
					<gp-form-col cols="12 12 12" [inputGroup]="false">
						<h3>Erros: <span class="text-danger">{{ (batch.totalErrorsOnApproval | number:'1.0-0') }}</span></h3>
						<hr />
						<div class="import-box-actions">
							<h4 *ngIf="batch.totalErrorsOnApproval === 0" style="color: #CCC">-</h4>
							<a class="import-link-action" *ngIf="batch.totalErrorsOnApproval > 0" (click)="downloadCsvWithApprovalErrors()">
								<h4 class="text-danger">DETALHES</h4>
							</a>
						</div>
					</gp-form-col>
				</gp-form-row>
			</gp-card>
		</gp-form-col>
	</gp-form-row>

	<gp-card title="Pedidos do Lote">
		<gp-form-row>
			<gp-form-col cols="12 12">
				<gp-grid name="grid" [rows]="batchRegisters" [loading]="loadingRegisters"
					[columns]="['CPF/CNPJ', 'Nome', 'Tipo do Cartão', 'Tipo Pedido', 'Cartão', 'Valor Recarga (R$)', 'Total Taxas (R$)', 'Total Pedido (R$)', 'Núm. Pedido', 'Status']"
					[fields]="['userDocument', 'userName', 'cardType', 'operation', 'selectedCard.number', 'chargeAmount', 'totalFeesAmount', 'totalCost.currency', 'orderNumber', 'status']"
					[showPagination]="true" [pageSize]="20" [showTotalPages]="false" (onPageChanged)="changePage($event)"
					[showEdit]="false" [showDelete]="canApprove" (onDelete)="rejectRegister($event)">
				</gp-grid>
			</gp-form-col>
		</gp-form-row>
	</gp-card>
</div>
