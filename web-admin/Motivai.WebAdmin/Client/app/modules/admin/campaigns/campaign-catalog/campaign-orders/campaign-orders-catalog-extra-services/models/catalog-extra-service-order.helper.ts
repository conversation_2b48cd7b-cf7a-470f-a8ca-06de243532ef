import { FormatHelper } from '../../../../../../../shared/formatters/format-helper';

export class CatalogExtraServiceOrderHelper {
  static handlerOrders(orders: Array<any>): void {
    if (!orders || !orders.length) return;
    orders.forEach(order => {
      CatalogExtraServiceOrderHelper.handlerOrder(order);
    });
  }

  static handlerOrder(order: any): void {
    if (order) {
        order.formattedSystemDate = FormatHelper.formatDateWithTimezone(order.systemDate);
        order.formattedPartnerPaidBillInfo = order.partnerPaidBillInfo ? order.partnerPaidBillInfo : 'IS2B';
        order.formattedType = CatalogExtraServiceOrderHelper.handlerType(order.type);
        order.isBillPayment = order.type == 'BILLPAYMENTS' && order.billPayment;
        order.isCreditRecharge = order.type == 'CREDITSRECHARGE' && order.creditRecharge;
        order.refundable = !order.refunded;
        order.formattedRefundDate = order.refundDate ? FormatHelper.formatDateWithTimezone(order.refundDate) : undefined;
        order.formattedConfirmed = order.confirmed ? 'Sim' : 'Não';
        order.formattedIntegrated = order.integrated ? 'Sim' : 'Não';
        order.formattedRefunded = order.refunded ? 'Sim' : 'Não';
    }
  }

  static handlerType(type: any): string {
    if (!type) {
      return 'Tipo do pedido inválido';
    }

    switch (type) {
      case 'CREDITSRECHARGE':
        return 'Crédito de celular';
      case 'BILLPAYMENTS':
        return 'Pagamento de contas';
      default:
        return 'Tipo do pedido inválido';
    }
  }
}
