import { Component, Input, ViewChild, OnInit, Output, EventEmitter } from '@angular/core';
import { GpAlertComponent } from '../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { FormatHelper } from '../../../../../../../../shared/formatters/format-helper';
import { CampaignPrepaidCardsOrdersImportService } from '../../../services/campaign-prepaid-cards-orders-import.service';
import { Item } from '../../../../../../../../shared/models/item';


@Component({
  selector: 'app-campaign-prepaid-cards-batches-listing',
  templateUrl: './campaign-prepaid-cards-batches-listing.component.html'
})
export class CampaignPrepaidCardsBatchesListingComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @Output('onEdit') onEdit: EventEmitter<any> = new EventEmitter<any>();

  private _campaignId: string = null;

  prepaidCardsBatches: any[];
  loadingBatches: boolean = false;
  loading: boolean = false;
  params: any = {};

  skip: number = 0;
  limit: number = 10;
  counterBatches: number = 0;

  batchStatuses: Array<Item> = [
    Item.of(' ', 'Todos'),
    Item.of('CREATED', 'Criado'),
    Item.of('PROCESSING', 'Em Processamento'),
    Item.of('WAITING_APPROVAL', 'Aguardando Aprovação'),
    Item.of('PROCESSING_APPROVAL', 'Processando Aprovação'),
    Item.of('COMPLETED', 'Finalizado'),
    Item.of('REJECTED', 'Rejeitado'),
    Item.of('ERROR', 'Erro')
  ];


  constructor(private prepaidCardService: CampaignPrepaidCardsOrdersImportService) { }

  ngOnInit(): void {

  }


  @Input('campaignId')
  set setCampaignId(id: string) {
    if (!id || !id.length) return;

    this._campaignId = id;
    this.findAndCountBatches();
  }

  findAndCountBatches() {
    this.countBatches();
  }

  countBatches() {
    if (!this._campaignId) return;

    this.loadingBatches = true;

    this.prepaidCardService.countBatches(this._campaignId, this.params)
        .subscribe(
            counter => {
                this.loadingBatches = false;
                this.counterBatches = counter;
                if (!this.counterBatches || this.counterBatches == 0) {
                    this.counterBatches = 0;
                    this.prepaidCardsBatches = [];
                    return;
                }
                this.findBatches();
            }, err => {
                this.loadingBatches = false;
                this.gpAlert.showError(err);
            }
        );
  }

  findBatches() {
    if (!this._campaignId) return;
    this.loadingBatches = true;

    this.prepaidCardService.findBatches(this._campaignId, this.params, this.skip, this.limit)
        .subscribe(
            batches => {
                if (batches) {
                    batches.forEach(batch => {
                        batch.formattedCreateDate = FormatHelper.formatDateWithTimezone(batch.createDate);
                        batch.formattedStatus = this.handleStatus(batch.status);
                    });
                    this.prepaidCardsBatches = batches;
                }
                this.loadingBatches = false;
            }, err => {
                this.loadingBatches = false;
                this.gpAlert.showError(err);
            }
        )
  }

  showPrepaidCardsDetails(event: any) {
    this.onEdit.emit(event);
  }

  changePage(event) {
    if (event) {
      this.skip = event.skip;
      this.limit = event.limit;
      this.findBatches();
    }
  }

  handleStatus(status: any): any {
    if (!status) {
      return 'Indefinido';
    }

    switch (status) {
      case 'CREATED':
        return 'Criado';
      case 'PENDING':
        return 'Pendente';
      case 'WAITING_APPROVAL':
        return 'Aguardando aprovação';
      case 'PROCESSING_APPROVAL':
        return 'Processando aprovação';
      case 'REJECTED':
        return 'Rejeitado';
      case 'PROCESSING':
        return 'Em Processamento';
      case 'COMPLETED':
        return 'Finalizado';
      case 'ERROR':
        return 'Erro';
      default:
        return 'Indefinido';
    }
  }

  clear() {
    this.params = {};
  }
}
