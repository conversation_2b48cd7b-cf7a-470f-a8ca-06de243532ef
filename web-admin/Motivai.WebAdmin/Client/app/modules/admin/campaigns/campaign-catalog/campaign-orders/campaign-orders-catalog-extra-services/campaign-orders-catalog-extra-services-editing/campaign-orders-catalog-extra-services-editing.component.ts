import { Component, ViewChild } from '@angular/core';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CatalogExtraServiceOrderHelper } from '../models/catalog-extra-service-order.helper';
import { CatalogExtraServicesOrdersService } from '../services/catalog-extra-services-orders.service';
import { CampaignOrdersCatalogExtraServicesRefundComponent } from '../actions/campaign-orders-catalog-extra-services-refund/campaign-orders-catalog-extra-services-refund.component';
import { AuthStore } from '../../../../../../../core/auth/auth.store';

@Component({
  selector: 'campaign-orders-catalog-extra-services-editing',
  templateUrl: './campaign-orders-catalog-extra-services-editing.component.html'
})
export class CampaignOrdersCatalogExtraServicesEditingComponent {
  @ViewChild('alert') alert: GpAlertComponent;
  @ViewChild('orderRefundModal') orderRefundModal: CampaignOrdersCatalogExtraServicesRefundComponent;

  loading: boolean;
  processingRepay: boolean;

  campaignId: string;
  catalogExtraServiceOrderId: string;
  catalogExtraServiceOrder: any = {};
  participantInfo: any = {};

  constructor(private _authStore: AuthStore, private readonly catalogExtraServiceOrderService: CatalogExtraServicesOrdersService) {}

  get canRefund() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_CATALOG_ORDERS_BILL_PAYMENTS_RECHARGES_REFUND
      && this.catalogExtraServiceOrder.refundable;
  }

  findCatalogExtraServiceOrder(searchParticipant: boolean = true): void {
    this.loading = true;
    this.catalogExtraServiceOrderService
      .findCatalogExtraServiceOrder(this.campaignId, this.catalogExtraServiceOrderId)
      .subscribe(
        response => {
          if (response) {
            CatalogExtraServiceOrderHelper.handlerOrder(response);
            this.catalogExtraServiceOrder = response;
            if (searchParticipant) {
              this.findParticipant();
            }
          }
        },
        err => {
          this.loading = false;
          this.alert.showError(err);
        }
      );
  }

  findParticipant(): void {
    this.loading = true;
    this.catalogExtraServiceOrderService
      .findParticipantInfoById(this.campaignId, this.catalogExtraServiceOrder.userId)
      .subscribe(
        response => {
          if (response) {
            this.setParticipantInfo(response);
          }
          this.loading = false;
        },
        err => {
          this.loading = false;
          this.alert.handleAndShowError(err);
        }
      );
  }

  setParticipantInfo(participant: any): void {
    if (participant) {
      this.participantInfo = participant;
    }
    this.participantInfo.userDocument = participant.cpf || participant.cnpj;
  }

  refundOrder(): void {
    this.orderRefundModal.showModal();
  }

  clear(): void {
    this.catalogExtraServiceOrderId = '';
    this.catalogExtraServiceOrder = {};
    this.participantInfo = {};
    this.processingRepay = false;
  }
}
