import { Component, ViewChild } from "@angular/core";

import { TabsetComponent } from 'ng2-bootstrap';
import { CampaignCatalogCustomizeOrderResumeListComponent } from "./campaing-catalog-customize-order-resume-list/campaign-catalog-customize-order-resume-list.component";
import { CampaignCatalogCustomizeOrderResumeEditComponent } from "./campaign-catalog-customize-order-resume-edit/campaign-catalog-customize-order-resume-edit.component";

@Component({
    selector: 'campaign-catalog-customize-order-resume-view',
    templateUrl: 'campaign-catalog-customize-order-resume-view.component.html'
})
export class CampaignCatalogCustomizeOrderResumeViewComponent {

    @ViewChild('tabs') tabs: TabsetComponent;
    @ViewChild('edit') edit: CampaignCatalogCustomizeOrderResumeEditComponent;
    @ViewChild('list') list: CampaignCatalogCustomizeOrderResumeListComponent;
  


    editCustomization(customizationId: string): void {
        if (customizationId) {
          this.edit.customizationId = customizationId;
          this.edit.loadPageCustomization(customizationId);
          this.tabs.tabs[1].active = true;
        }
    }
    
      refresh(): void {
        this.edit.clear();
        this.list.loadCampaignCatalogOrderResumeCustomizations();
      }

}