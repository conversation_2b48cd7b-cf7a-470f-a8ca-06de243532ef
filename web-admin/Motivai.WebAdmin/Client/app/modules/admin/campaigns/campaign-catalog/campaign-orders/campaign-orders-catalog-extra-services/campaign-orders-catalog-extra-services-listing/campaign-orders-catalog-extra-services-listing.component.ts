import { Component, EventEmitter, OnInit, Output, ViewChild } from '@angular/core';
import { Subscription } from 'rxjs';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { RxjsHelpers } from '../../../../../../../shared/helpers/rxjs-helpers';
import { CampaignStore } from '../../../../campaign.store';
import { CatalogExtraServiceOrderHelper } from '../models/catalog-extra-service-order.helper';
import { CatalogExtraServicesOrdersService } from '../services/catalog-extra-services-orders.service';
import { PageChangeEvent } from '../../../../../../../shared/components/gp-grid/page-change-event';

@Component({
  selector: 'campaign-orders-catalog-extra-services-listing',
  templateUrl: './campaign-orders-catalog-extra-services-listing.component.html'
})
export class CampaignOrdersCatalogExtraServicesListingComponent implements OnInit {
  @ViewChild('alert') alert: GpAlertComponent;
  @Output('onEdit') onEdit: EventEmitter<any> = new EventEmitter<any>();

  campaignId;
  loading: boolean;

  private _campaign$: Subscription;

  parameters: any = {
    skip: 0,
    limit: 40
  };

  orders: Array<any>;

  constructor(
    private readonly campaignStore: CampaignStore,
    private readonly catalogExtraServiceOrderService: CatalogExtraServicesOrdersService
  ) {}

  ngOnInit() {
    this._campaign$ = this.campaignStore.asObservable.subscribe(id => {
      this.campaignId = id;
      this.searchCatalogExtraServicesOrders();
    });
  }

  ngOnDestroy(): void {
    RxjsHelpers.unsubscribe(this._campaign$);
  }

  editOrder(event: any): void {
    this.onEdit.emit(event.id);
  }

  searchCatalogExtraServicesOrders(): void {
    if (this.parameters.document) {
      this.searchParticipant();
    } else {
      this.parameters.userId = null;
      this.findCatalogExtraServiceOrders();
    }
  }

  private findCatalogExtraServiceOrders(): void {
    if (!this.campaignId) {
      return;
    }

    this.loading = true;
    this.catalogExtraServiceOrderService.findCatalogExtraServicesOrders(this.campaignId, this.parameters).subscribe(
      response => {
        CatalogExtraServiceOrderHelper.handlerOrders(response);
        this.orders = response || [];
        this.loading = false;
      },
      err => {
        this.loading = false;
        this.alert.showError(err);
      }
    );
  }

  pageChanged(event: PageChangeEvent) {
    if (event) {
      this.parameters.skip = event.skip;
      this.parameters.limit = event.limit;
      this.findCatalogExtraServiceOrders();
    }
  }

  searchParticipant(): void {
    this.loading = true;
    this.catalogExtraServiceOrderService
      .findParticipantInfoByDocument(this.campaignId, this.parameters.document)
      .subscribe(
        response => {
          this.loading = false;
          if (response || response[0]) {
            this.setParticipantInfo(response[0]);
          }
        },
        err => {
          this.loading = false;
          this.alert.handleAndShowError(err);
        }
      );
  }

  setParticipantInfo(participant: any): void {
    this.parameters.userId = participant.userId;
    this.findCatalogExtraServiceOrders();
  }
}
