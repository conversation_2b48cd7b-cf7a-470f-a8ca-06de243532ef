import { Observable } from 'rxjs/Observable';
import { Injectable } from "@angular/core";

import { ApiService } from "../../../../../../../core/api/api.service";

@Injectable()
export class CampaignOrdersProductsService {
    constructor(private _api: ApiService) {}

    getProductsByTermOrSkuCode(campaignId: string, term: string, skuCode: string): Observable<any> {
        const params = {term: term, skuCode: skuCode}
        return this._api.get(`/api/campaigns/${campaignId}/orders/products/search`, params);
    }

    createOrders(campaignId: string, cart: any): Observable<any> {
        return this._api.post(`/api/campaigns/${campaignId}/orders`, cart, 20000);
    }
}
