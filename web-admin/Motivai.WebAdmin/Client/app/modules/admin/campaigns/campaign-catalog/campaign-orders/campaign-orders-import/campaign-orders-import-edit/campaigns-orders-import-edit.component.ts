import { Component, OnInit, Input, ViewChild } from '@angular/core';

import { CampaignOrdersImportService } from '../campaigns-orders-import.service';
import { BatchImport } from '../../../../campaign-import';
import { CampaignImportService } from '../../../../campaign-import.service';
import { CampaignService } from '../../../../campaign.service';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { AuthStore } from '../../../../../../../core/auth/auth.store';
import { PERMISSION_CAMPAIGNS_IMPORT_ORDERS_APPROVE, PERMISSION_CAMPAIGNS_IMPORT_ORDERS_EDIT_ADDRESS, PERMISSION_CAMPAIGNS_IMPORT_ORDERS_EDIT_GENERAL, PERMISSION_CAMPAIGNS_IMPORT_ORDERS_EDIT_ITEMS, PERMISSION_CAMPAIGNS_IMPORT_ORDERS_REMOVETEMPORARY, PERMISSION_CAMPAIGNS_IMPORT_ORDERS_IMPORT, PERMISSION_CAMPAIGNS_IMPORT_ORDERS_VIEW } from '../../../../../../../core/auth/access-points';
import { State } from '../../../../../regions/regions';
import { RegionsService } from '../../../../../regions/regions.service';

@Component({
    selector: 'campaigns-orders-import-edit',
    templateUrl: 'campaigns-orders-import-edit.component.html'
})
export class CampaignsOrdersImportEditComponent implements OnInit {

    private _campaignId: string;
    @Input() set campaignId(v: string) {
        if (v) {
            this._campaignId = v;
            this.getLinkedPartners();
        }
    }

    private _batchImportId: string;
    @Input() set batchImportId(v: string) {
        this._batchImportId = v;

        this.getBatchImport();
        this.findTemporaryOrders();
    }

    // Page components
    @ViewChild('classicModal') classicModal: any;
    @ViewChild('generalInfoAlert') generalInfoAlert: GpAlertComponent;
    @ViewChild('shippingAddressAlert') shippingAddressAlert: GpAlertComponent;
    @ViewChild('itemAlert') itemAlert: GpAlertComponent;
    @ViewChild('itemsGridAlert') itemsGridAlert: GpAlertComponent;
    @ViewChild('temporaryOrdersAlert') temporaryOrdersAlert: GpAlertComponent;
    @ViewChild('approveAlert') approveAlert: GpAlertComponent;

    // Edit forms
    private temporaryOrderId: string = '';
    private selectedSkuCode: string = '';
    private generalInfo: any = {};
    private shippingAddress: any = {};
    private itemDetail: any = {};
    private items: any[] = [];
    private partners: any[] = [];
    public states: State[] = [];

    // Page configuration
    private batchImport: BatchImport = new BatchImport();
    private statusFilter: any = { status: '' };
    private loading: boolean = false;
    private loadingGeneralInfoForm: boolean = false;
    private loadingShippingAddressForm: boolean = false;
    private loadingItemsForm: boolean = false;
    private loadingDetails: boolean = false;
    private loadingApproval: boolean = false;

    // Grid configuration
    private temporaryOrders: any[] = [];
    private skip: number = 0;
    private limit: number = 100;

    // Upload file configuration
    private parameters: any = {};
    private countItemsQueue: number = 0;
    private countItemsProcessed: number = 0;
    get uploadPath(): string {
        return `api/campaigns/${this._campaignId}/batchimport/orders/upload`;
    }

    constructor(private _authStore: AuthStore, private _campaignService: CampaignService,
        private _campaignImportService: CampaignImportService,
        private _campaignOrdersImportService: CampaignOrdersImportService,
        private _regionsService: RegionsService) { }

    ngOnInit() {
        this.getStates();
    }

    private getLinkedPartners() {
        if (this._campaignId) {
            this._campaignService.getLinkedPartnersDataByCampaign(this._campaignId)
                .subscribe(partners => {
                    this.partners = partners.map(p => {
                        return { partnerId: p.partnerId, partnerName: p.partnerName };
                    });
                });
        }
    }

    // Get batchimport detail information
    private getBatchImport() {
        if (this._batchImportId) {
            this.loading = true;
            this._campaignImportService.getBatchById(this._campaignId, this._batchImportId)
                .subscribe(batch => {
                    this.batchImport = batch;
                    this.batchImport.status = this.setStatus(batch.status);

                    this.loading = false;
                }, err => {
                    this.loading = false;
                });
        }
    }

    private findTemporaryOrders() {
        if (this._batchImportId && this._campaignId) {
            this.loadingDetails = true;
            this._campaignOrdersImportService.findTemporaryOrders(this._campaignId, this._batchImportId, this.statusFilter.status)
                .subscribe(orders => {
                    this.temporaryOrders = orders;
                    this.loadingDetails = false;
                }, err => {
                    console.log(err);
                    this.loadingDetails = false;
                });
        }
    }

    private editTemporaryOrder($event) {
        if ($event) {
            this.loadingDetails = true;
            this.temporaryOrderId = '';
            this._campaignOrdersImportService.getTemporaryOrder(this._campaignId, this._batchImportId, $event.id)
                .subscribe(order => {
                    this.temporaryOrderId = order.id;
                    this.generalInfo = order;
                    this.shippingAddress = order.shippingAddress;
                    this.items = order.items;

                    this.loadingDetails = false;
                    this.classicModal.show();
                }, err => {
                    console.log(err);
                    this.loadingDetails = false;
                });
        }
    }

    private editTemporaryItem($event) {
        if ($event) {
            const item = this.items.find(x => x.skuCode === $event.skuCode);
            if (item) {
                this.itemDetail = item;
                this.selectedSkuCode = $event.skuCode;
            }
        }
    }

    private saveGeneralInfo() {
        this.generalInfoAlert.clear();
        if (this.temporaryOrderId) {
            this.loadingGeneralInfoForm = true;
            this._campaignOrdersImportService.updateGeneralInfo(this._campaignId, this._batchImportId, this.temporaryOrderId, this.generalInfo)
                .subscribe(order => {
                    if (order) {
                        this.generalInfoAlert.showSuccess('Dados gerais atualizados com sucesso');
                    } else {
                        this.generalInfoAlert.showError('Ocorreu um erro ao atualizar o registro');
                    }

                    this.temporaryOrderId = order.id;
                    this.generalInfo = order;
                    this.shippingAddress = order.shippingAddress;
                    this.items = order.items;
                    this.loadingGeneralInfoForm = false;
                }, err => {
                    const msg = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao atualizar os dados gerais. Entre em contato com o suporte do sistema!';
                    this.generalInfoAlert.showError(msg);
                    this.loadingGeneralInfoForm = false;
                });
        }
    }

    get canEditOrderInfo() {
        return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_IMPORT_ORDERS_EDIT_GENERAL)
            && this.isWaitingApproval;
    }

    get canEditShippingAddress() {
        return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_IMPORT_ORDERS_EDIT_ADDRESS)
            && this.isWaitingApproval;
    }

    get canEditItem() {
        return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_IMPORT_ORDERS_EDIT_ITEMS)
            && this.isWaitingApproval;
    }

    private saveShippingAddress() {
        this.shippingAddressAlert.clear();
        if (this.temporaryOrderId) {
            this.loadingShippingAddressForm = true;
            this._campaignOrdersImportService.updateShippingAddress(this._campaignId, this._batchImportId, this.temporaryOrderId, this.shippingAddress)
                .subscribe(order => {
                    if (order) {
                        this.shippingAddressAlert.showSuccess('Endereço de entrega atualizado com sucesso');
                    } else {
                        this.shippingAddressAlert.showError('Ocorreu um erro ao atualizar o endereço de entrega');
                    }

                    this.temporaryOrderId = order.id;
                    this.generalInfo = order;
                    this.shippingAddress = order.shippingAddress;
                    this.items = order.items;
                    this.loadingShippingAddressForm = false;
                }, err => {
                    const msg = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao atualizar o endereço de entrega. Entre em contato com o suporte do sistema!';
                    this.shippingAddressAlert.showError(msg);
                    this.loadingShippingAddressForm = false;
                });
        }
    }

    private saveItem() {
        this.itemAlert.clear();
        if (this.temporaryOrderId && this.selectedSkuCode) {
            this.loadingItemsForm = true;
            if (this.itemDetail.partnerId)
                this.itemDetail.partnerName = this.partners.find(x => x.partnerId === this.itemDetail.partnerId).partnerName;
            else
                this.itemDetail.partnerName = '';
            this._campaignOrdersImportService.updateOrderItem(this._campaignId, this._batchImportId, this.temporaryOrderId, this.selectedSkuCode, this.itemDetail)
                .subscribe(order => {
                    if (order) {
                        this.itemAlert.showSuccess('Item do pedido atualizado com sucesso');
                    } else {
                        this.itemAlert.showError('Ocorreu um erro ao atualizar o item do pedido');
                    }

                    this.temporaryOrderId = order.id;
                    this.generalInfo = order;
                    this.shippingAddress = order.shippingAddress;
                    this.items = order.items;
                    this.itemDetail = this.items.find(x => x.skuCode === this.selectedSkuCode);
                    this.loadingItemsForm = false;
                }, err => {
                    const msg = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao atualizar o item do pedido. Entre em contato com o suporte do sistema!';
                    this.itemAlert.showError(msg);
                    this.loadingItemsForm = false;
                });
        }
    }

    private deleteTemporaryItem($event) {
        if ($event) {
            if (this.items.length === 1) {
                if (!confirm('ATENÇÃO: Ao excluir este item o pedido também será excluído. Deseja continuar?')) {
                    return;
                }
            }

            this.loadingItemsForm = true;
            this._campaignOrdersImportService.removeOrderItem(this._campaignId, this._batchImportId, this.temporaryOrderId, $event.skuCode)
                .subscribe(response => {
                    if (!response) {
                        this.itemsGridAlert.showError('Ocorreu um erro ao remover o item do pedido');

                    } else if (this.items.length === 1) {
                        this.clearModal();

                    } else {
                        this.items.splice(this.items.indexOf($event), 1);
                    }

                    this.loadingItemsForm = false;
                }, err => {
                    const msg = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao remover o item do pedido. Entre em contato com o suporte do sistema!';
                    this.itemAlert.showError(msg);
                    this.loadingItemsForm = false;
                });
        }
    }

    private deleteTemporaryOrder($event) {
        if ($event) {
            if (confirm('ATENÇÃO: Tem certeza que deseja remover este pedido da lista de processamento?')) {
                this.loadingDetails = true;
                this._campaignOrdersImportService.removeTemporaryOrder(this._campaignId, this._batchImportId, $event.id)
                    .subscribe(response => {
                        if (!response) {
                            this.temporaryOrdersAlert.showError('Ocorreu um erro ao excluir o pedido temporário');
                            this.loadingDetails = false;

                        } else {
                            this.findTemporaryOrders();
                        }
                    }, err => {
                        const msg = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao excluir o pedido. Entre em contato com o suporte do sistema!';
                        this.temporaryOrdersAlert.showError(msg);
                        this.loadingDetails = false;
                    });
            }
        }
    }

    private approve() {
        if (this.canApprove) {
            if (this._batchImportId && this._campaignId) {
                if (confirm('ATENÇÃO: Todos os itens com status SUCESSO e AVISO serão processados. Caso não tenha revisado os pedidos, recomendamos que faça antes desta aprovação! \n\nDeseja realmente APROVAR todos os pedidos?')) {
                    this.loadingApproval = true;
                    this.approveAlert.clear();
                    this._campaignOrdersImportService.approve(this._campaignId, this._batchImportId)
                        .subscribe(response => {
                            if (!response) {
                                this.approveAlert.showError('Ocorreu um erro ao aprovar os pedidos');
                            }

                            this.getBatchImport();
                            this.findTemporaryOrders();
                            this.loadingApproval = false;
                        }, err => {
                            const msg = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao aprovar os pedidos. Entre em contato com o suporte do sistema!';
                            this.approveAlert.showError(msg);
                            this.loadingApproval = false;
                        });
                }
            }
        }
    }

    private refuse() {
        if (this.canApprove) {
            if (this._batchImportId && this._campaignId) {
                if (confirm('ATENÇÃO: Este lote será reprovado e os pedidos temporários não poderão ser aprovados. Deseja REPROVAR esta importação?')) {
                    this.loadingApproval = true;
                    this.approveAlert.clear();
                    this._campaignOrdersImportService.refuse(this._campaignId, this._batchImportId)
                        .subscribe(response => {
                            if (!response) {
                                this.approveAlert.showError('Ocorreu um erro ao reprovar os pedidos');
                            }

                            this.getBatchImport();
                            this.findTemporaryOrders();
                            this.loadingApproval = false;
                        }, err => {
                            const msg = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao reprovar os pedidos. Entre em contato com o suporte do sistema!';
                            this.approveAlert.showError(msg);
                            this.loadingApproval = false;
                        });
                }
            }
        }
    }

    private hideItemForm() {
        this.itemDetail = {};
        this.selectedSkuCode = '';
    }

    private clearModal() {
        this.generalInfo = {};
        this.shippingAddress = {};
        this.itemDetail = {};
        this.items = [];
        this.temporaryOrderId = '';
        this.selectedSkuCode = '';

        if (this.generalInfoAlert) this.generalInfoAlert.clear();
        if (this.shippingAddressAlert) this.shippingAddressAlert.clear();
        if (this.temporaryOrdersAlert) this.temporaryOrdersAlert.clear();
        if (this.itemsGridAlert) this.itemsGridAlert.clear();
        if (this.itemAlert) this.itemAlert.clear();

        this.getBatchImport();
        this.findTemporaryOrders();
    }

    private getStates() {
        this._regionsService.getStates()
            .subscribe(states => {
                if (states && states.length > 0) {
                    this.states = states;
                    this.states.sort((a, b) => {
                        if (a.fullName < b.fullName)
                            return -1;
                        if (a.fullName > b.fullName)
                            return 1;
                        return 0;
                    });
                }
            }, err => {
                console.log(err);
            });
    }

    // Helper to translate batch status
    private setStatus(status: string): string {
        switch (status) {
            case 'Waiting':
                return 'Aguardando';
            case 'Running':
                return 'Processando';
            case 'Waiting_Approval':
                return 'Aguardando Aprovação';
            case 'Running_Approval':
                return 'Processando Aprovação';
            case 'Completed':
                return 'Finalizado';
            case 'Refused':
                return 'Reprovado';
            default:
                return 'Status indefinido';
        }
    }

    get hasErrorsOrWarning() {
        return this.generalInfo.errorMessages && this.generalInfo.errorMessages.length ||
            this.generalInfo.warningMessages && this.generalInfo.warningMessages.length;
    }

    // Define fules for Processing text visibility
    get isProcessing() {
        return this.batchImport.status === 'Processando' || this.batchImport.status === 'Processando Aprovação';
    }

    // Define rules for approval link visibility
    get showApprovalLink(): boolean {
        return this.batchImport.status === 'Aguardando Aprovação';
    }

    get isWaitingApproval(): boolean {
        return this.batchImport.status === 'Aguardando Aprovação';
    }

    get isCompleted(): boolean {
        return this.batchImport.status === 'Finalizado' || this.batchImport.status === 'Reprovado';
    }

    private canImport() {
        return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_IMPORT_ORDERS_IMPORT)
    }

    private canView() {
        return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_IMPORT_ORDERS_VIEW)
    }

    get canApprove() {
        return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_IMPORT_ORDERS_APPROVE)
            && this.batchImport.status === 'Aguardando Aprovação';
    }

    private canViewTemporary() {
        return true;
    }

    get canRemoveTemporary() {
        return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_IMPORT_ORDERS_REMOVETEMPORARY)
            && this.isWaitingApproval;
    }

    // On upload complete
    onItemComplete($event: any) {
        if ($event) {
            this.countItemsProcessed += Number(1);
            if (this.countItemsProcessed === this.countItemsQueue) {
                // Todo
            }
        }
    }
}
