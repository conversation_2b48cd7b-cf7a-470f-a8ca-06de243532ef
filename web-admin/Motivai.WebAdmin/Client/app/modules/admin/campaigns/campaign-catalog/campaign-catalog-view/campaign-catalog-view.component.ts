import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';

import { RxjsHelpers } from '../../../../../shared/helpers/rxjs-helpers';
import { AuthStore } from '../../../../../core/auth/auth.store';
import { CampaignStore } from '../../campaign.store';

@Component({
    selector: 'campaign-catalog-view',
    templateUrl: 'campaign-catalog-view.component.html'
})
export class CampaignCatalogViewComponent implements OnInit, OnDestroy {
    campaignId: string;

    private _campaign$: Subscription;

    constructor(private router: Router, private _authStore: AuthStore,
        private _campaignStore: CampaignStore) { }

    ngOnInit() {
        this._campaign$ = this._campaignStore.asObservable
            .subscribe(campaignId => {
                this.campaignId = campaignId;
            });
    }

    ngOnDestroy() {
        RxjsHelpers.unsubscribe(this._campaign$);
    }

    get canViewFilters() {
        return this._authStore.role.PERMISSION_CAMPAIGNS_FILTERS;
    }

    get canViewStock() {
        return this._authStore.role.CAMPAIGNS_STOCK_VIEW
          || this._authStore.role.CAMPAIGNS_CATALOG_VOUCHER_STOCK_REPORT;
    }

    get canViewOrders() {
        return this._authStore.role.PERMISSION_CAMPAIGNS_CATALOGS_ORDERS;
    }

    get canViewPrepaidCard() {
      return this._authStore.role.PERMISSION_CAMPAIGNS_CATALOG_PREPAID_CARDS_ORDERS_IMPORT;
    }

    get canViewCustomizeOrderConfirmedPage() {
        return this._authStore.role.PERMISSION_CAMPAIGNS_CATALOGS_CUSTOMIZE_ORDER_COMFIRMED_PAGE;
    }
}
