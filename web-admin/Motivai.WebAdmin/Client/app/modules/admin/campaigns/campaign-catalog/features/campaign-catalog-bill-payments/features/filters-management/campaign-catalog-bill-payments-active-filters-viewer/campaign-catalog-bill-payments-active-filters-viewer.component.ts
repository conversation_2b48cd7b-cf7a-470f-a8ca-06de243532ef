import { Component, ViewChild } from '@angular/core';

import { GpModalComponent } from '../../../../../../../../../shared/components/gp-modal/gp-modal.component';
import { FormatHelper } from '../../../../../../../../../shared/formatters/format-helper';

@Component({
	selector: 'campaign-catalog-bill-payments-active-filters-viewer',
	templateUrl: './campaign-catalog-bill-payments-active-filters-viewer.component.html'
})
export class CampaignCatalogBillPaymentsActiveFiltersViewerComponent {
	@ViewChild('modal') modal: GpModalComponent;

	filter: any = {}

	get hasReceiverName(): boolean {
		return this.filter && this.filter.receiverName;
	}

	get hasReceiverDocument(): boolean {
		return this.filter && this.filter.receiverDocument;
	}

	get hasPayerName(): boolean {
		return this.filter && this.filter.payerName;
	}

	get hasPayerDocument(): boolean {
		return this.filter && this.filter.payerDocument;
	}

	get hasAssignor(): boolean {
		return this.filter && this.filter.assignor;
	}

	get hasBillingAmount(): boolean {
		return this.filter && this.filter.billingAmount;
	}

	setFilter(filter: any): void {
		this.filter = filter || {}
	}

	openModal(): void {
		this.modal.show();
	}

	hide(): void {
		this.modal.hide();
	}

	onBack(): void {
		this.reset();
		this.hide();
	}

	reset(): void {
		this.filter = {};
	}
}
