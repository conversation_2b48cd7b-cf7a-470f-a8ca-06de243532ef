import { Component } from '@angular/core';
import { Subscription } from 'rxjs';
import { AuthStore } from '../../../../../core/auth/auth.store';
import { RxjsHelpers } from '../../../../../shared/helpers/rxjs-helpers';
import { CampaignStore } from '../../campaign.store';

@Component({
  selector: 'campaign-stock-view',
  templateUrl: 'campaign-stock-view.component.html'
})
export class CampaignStockViewComponent {
  private _campaign$: Subscription;
  campaignId: string;

  constructor(private authStore: AuthStore, private _campaignStore: CampaignStore) {}

  ngOnInit() {
    this._campaign$ = this._campaignStore.asObservable
      .subscribe(id => {
        this.campaignId = id;
      });
  }

  ngOnDestroy() {
    RxjsHelpers.unsubscribe(this._campaign$);
  }

  get canViewStockReport() {
    return this.authStore.role.CAMPAIGNS_CATALOG_VOUCHER_STOCK_REPORT;
  }

  get canManage() {
    return this.authStore.role.CAMPAIGNS_STOCK_VIEW;
  }

  get canSearchStock() {
    return this.authStore.role.CAMPAIGNS_CATALOG_VOUCHER_STOCK_SEARCH;
  }
}
