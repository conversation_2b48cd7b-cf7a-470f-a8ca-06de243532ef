<gp-alert [overlay]="true" #gpAlert></gp-alert>
<div *ngIf="canSearchStock">
	<gp-card [first]="true">
		<gp-form-row>
			<gp-form-col cols="12 6 6" [inputGroup]="false">
				<gp-simple-input label="Código SKU">
					<input type="text" name="skuCode" class="form-control" [(ngModel)]="params.skuCode" />
				</gp-simple-input>
			</gp-form-col>

			<gp-form-col cols="12 6 6">
				<gp-spinner-button type="button" buttonClass="bg-primary-dark" icon="plus" text="Pesquisar"
					[search]="true" [loading]="loading" [disabled]="loading" (click)="onSearchVouchers()" marginTop="27px">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card [last]="true" title="Estoques encontrado">
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-grid #skuGrid name="skuGrid" [rows]="stocks"
					[columns]="['Código SKU', 'Produto', 'Parceiro']"
					[fields]="['skuCode', 'productName', 'partnerName']"
					[showActive]="false" [showTotalPages]="false" [showPagination]="true" [showEdit]="true"
					[showDelete]="false" [loading]="loading" [pageSize]="params.limit"
					(onPageChanged)="onPageChanged($event)" (onEdit)="editStock($event)">
				</gp-grid>
			</gp-form-col>
		</gp-form-row>
	</gp-card>
</div>

<div class="alert alert-warning" role="alert" *ngIf="!canSearchStock">
	Você não tem permissão para acessar esse módulo!
</div>
