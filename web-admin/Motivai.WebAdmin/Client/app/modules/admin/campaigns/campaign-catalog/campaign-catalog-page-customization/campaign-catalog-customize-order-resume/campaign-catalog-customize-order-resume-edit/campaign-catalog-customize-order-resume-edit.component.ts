import { Component, ViewChild } from "@angular/core";
import { Item } from "../../../../../../../shared/models/item";
import { CampaignCatalogPageCustomizationService } from "../../campaign-catalog-page-customization.service";
import { CampaignStore } from "../../../../campaign.store";

import { Subscription } from 'rxjs/Subscription';
import { GpAlertComponent } from "../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component";

@Component({
    selector: 'campaign-catalog-customize-order-resume-edit',
    templateUrl: 'campaign-catalog-customize-order-resume-edit.component.html'
})
export class CampaignCatalogCustomizeOrderResumeEditComponent {
    
    @ViewChild('alert') alert: GpAlertComponent;
    
    productTypes: Array<Item> = [Item.of('ValeVirtual', 'Vale Virtual'), Item.of('ValeFisico', 'Vale Físico'), Item.of('Produto', 'Produto')];
    layoutTypes: Array<Item> = [Item.of('PRODUCT', 'Produto'), Item.of('BASIC_SERVICE', 'Serviço')];

    customizationOrderConfirmedPage: any = {
        isLayoutType: false,
        isProductType: false,
        active: false,
        type: 'ORDERCONFIRMED'

    };

    customizationId: string;

    loading: boolean = false;



    constructor(private campaignStore: CampaignStore, private pageCustomizationService: CampaignCatalogPageCustomizationService) {}

    get campaignId() {
        return this.campaignStore.id;
    }
    

    private changeLayoutState(productState) {
        if (productState) {
            this.customizationOrderConfirmedPage.isLayoutType = false;
        }
    }

    private changeProductState(layoutState) {
        if (layoutState) {
            this.customizationOrderConfirmedPage.isProductType = false;
        }
    }



    public loadPageCustomization(id) {
        this.loading = true;
        this.pageCustomizationService.getCampaignCatalogPageCustomizationById(this.campaignId, id)
            .subscribe(pageCustomization => {
                this.customizationOrderConfirmedPage = pageCustomization || {
                    isLayoutType: false,
                    isProductType: false,
                    active: false,
                    type: 'ORDERCONFIRMED'
                };
                this.loading = false;
            },
            err => {
                this.loading = false;
                this.alert.showError(err);
            });
    }

    saveCustomization() {  
        this.loading = true;
        this.pageCustomizationService.saveCampaignCatalogPageCustomization(this.campaignId, this.customizationOrderConfirmedPage)
            .subscribe(response => {
                if (response) {
                    this.alert.showSuccess('Customização cadastrada com sucesso.');
                }
                this.loading = false;
            },
            err => {
                this.loading = false;
                this.alert.showError(err);
            });
        }

    public clear() {
        this.customizationOrderConfirmedPage = {
            isLayoutType: false,
            isProductType: false,
            active: false,
            type: 'ORDERCONFIRMED'
        };
    }

} 