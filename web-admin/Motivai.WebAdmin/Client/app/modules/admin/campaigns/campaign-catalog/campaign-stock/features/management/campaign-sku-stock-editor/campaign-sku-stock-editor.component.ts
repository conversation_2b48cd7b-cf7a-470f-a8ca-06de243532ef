import { Component, ViewChild, Input } from '@angular/core';
import { NgForm } from '@angular/forms';

import { GpAlertComponent } from '../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignStockService } from '../../../campaign-stock.service';

@Component({
  selector: 'campaign-sku-stock-editor',
  templateUrl: 'campaign-sku-stock-editor.component.html'
})
export class CampaignSkuStockEditorComponent {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;

  _campaignId: string;

  skuStock: any = {};
  participantGroup: any = {};
  stock: any = {};

  movementAmount: number = 0;

  loading: boolean = false;
  sending: boolean = false;
  movementing: boolean = false;

  constructor(private _stockService: CampaignStockService) {}

  get cannotMovementStock() {
    return !this.skuStock || !this.skuStock.id;
  }

  get isEditing() {
    return this.skuStock && this.skuStock.id;
  }

  @Input('campaignId')
  set campaignId(campaignId: string) {
    this._campaignId = campaignId;
  }

  public newStock() {
    this.skuStock = {
      sku: {}
    };
    this.participantGroup = {};
    this.stock = {
      minimumQuantity: 0,
      maximumQuantity: 0,
      currentQuantity: 0
    };
  }

  public editStock(stockId: string) {
    if (!stockId) return;
    this.newStock();
    this.loading = true;
    this._stockService.getStockById(this._campaignId, stockId)
      .subscribe(
        campaignStock => {
          this.loading = false;
          if (campaignStock) {
            this.skuStock = campaignStock;
            if (campaignStock.participantGroup && campaignStock.participantGroup.id) {
              this.participantGroup.id = campaignStock.participantGroup.id;
              this.participantGroup.name = campaignStock.participantGroup.name;
            }
            this.stock = campaignStock.stock;
          } else {
            this.gpAlert.showWarning('Estoque não encontrado.');
          }
        },
        err => {
          this.loading = false;
          this.gpAlert.handleAndShowError(err);
        }
      );
  }

  setSelectedGroup(group: any) {
    if (group && group.id) {
      this.participantGroup.name = group.name;
    } else {
      this.participantGroup.name = null;
    }
  }

  decreaseStock() {
    if (!this.movementAmount || !(this.movementAmount > 0)) {
      this.gpAlert.showWarning('Informe uma quantidade válida para movimentação.');
      return;
    }
    this.gpAlert.confirm(`Deseja reduzir ${this.movementAmount} item(s) do estoque?`)
      .then(result => {
        if (result.value) {
          this.movementing = true;
          this._stockService.decrementStock(this._campaignId, this.skuStock.id, this.participantGroup.id, this.movementAmount)
            .subscribe(
              result => {
                this.movementing = false;
                if (result) {
                  this.editStock(this.skuStock.id);
                  this.gpAlert.showSuccess('Movimentação de estoque realizada com sucesso.');
                } else {
                  this.gpAlert.showWarning('Não foi possível realizar a movimentação, por favor, tente novamente.');
                }
              },
              err => {
                this.movementing = false;
                this.gpAlert.handleAndShowError(err);
              }
            );
        }
      });
  }

  increaseStock() {
    if (!this.movementAmount || !(this.movementAmount > 0)) {
      this.gpAlert.showWarning('Informe uma quantidade válida para movimentação.');
      return;
    }
    this.gpAlert.confirm(`Deseja aumentar ${this.movementAmount} item(s) no estoque?`)
      .then(result => {
        if (result.value) {
          this.movementing = true;
          this._stockService.incrementStock(this._campaignId, this.skuStock.id, this.participantGroup.id, this.movementAmount)
            .subscribe(
              result => {
                this.movementing = false;
                if (result) {
                  this.editStock(this.skuStock.id);
                  this.gpAlert.showSuccess('Movimentação de estoque realizada com sucesso.');
                } else {
                  this.gpAlert.showWarning('Não foi possível realizar a movimentação, por favor, tente novamente.');
                }
              },
              err => {
                this.movementing = false;
                this.gpAlert.handleAndShowError(err);
              }
            );
        }
      });
  }

  saveStock(stockForm: NgForm) {
    if (!stockForm.valid) {
      this.gpAlert.showWarning('Preencha os campos corretamente para prosseguir.');
      return;
    }
    if (!this.skuStock.sku || !this.skuStock.sku.skuId) {
      this.gpAlert.showWarning('Selecione o SKU.');
      return;
    }

    this.skuStock.participantGroup = this.participantGroup;
    this.skuStock.stock = this.stock;
    
    this.sending = true;
    this._stockService.saveStock(this._campaignId, this.skuStock)
      .subscribe(
        result => {
          this.sending = false;
          if (result) {
            this.skuStock.id = result;
            this.gpAlert.showSuccess('Estoque salvo com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível salvar o estoque, por favor, tente novamente.');
          }
        },
        err => {
          this.sending = false;
          this.gpAlert.handleAndShowError(err);
        }
      );
  }
}
