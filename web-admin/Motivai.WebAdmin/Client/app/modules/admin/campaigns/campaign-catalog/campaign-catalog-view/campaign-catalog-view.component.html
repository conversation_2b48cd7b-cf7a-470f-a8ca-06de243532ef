<gp-card [noPaddingTop]="true" [noPaddingBottom]="true">
	<div class="row">
		<accordion [closeOthers]="true">
			<accordion-group panelClass="b0 mb-sm panel-default no-shadow" [isOpen]="true">
				<div accordion-heading>
					<label><em class="fa fa-navicon"></em> Features do Catálogo</label>
				</div>
				<div class="row menublock text-center" *ngIf="campaignId">
					<gp-menublock-item size="xs" icon="filter" color="text-success" text="Filtros"
						routerLinkActive="active" routerLink="filtros" *ngIf="canViewFilters">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="shopping-basket" color="text-success" text="Estoque"
						routerLinkActive="active" routerLink="estoques" *ngIf="canViewStock">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="shopping-cart" color="text-success" text="Pedidos"
						routerLinkActive="active" routerLink="pedidos" *ngIf="canViewOrders">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="barcode" color="text-success" text="Pagamento de Contas"
						routerLinkActive="active" routerLink="pagamentos-de-contas">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="credit-card" color="text-success" text="Cartões Pré-Pagos"
						routerLinkActive="active" routerLink="cartoes-pre-pagos" *ngIf="canViewPrepaidCard">
					</gp-menublock-item>
					
					<gp-menublock-item size="xs" icon="gears" color="text-success" text="Customização Página de Pedidos Finalizados"
						outerLinkActive="active" routerLink="customizacao-pagina-pedido-confirmado"
						*ngIf="canViewCustomizeOrderConfirmedPage"></gp-menublock-item>
				</div>
			</accordion-group>
		</accordion>
	</div>
</gp-card>
<router-outlet></router-outlet>