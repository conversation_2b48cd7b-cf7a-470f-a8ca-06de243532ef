import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from '../../../../../../../core/api/api.service';

@Injectable()
export class CatalogExtraServicesOrdersService {
  constructor(private readonly api: ApiService) {}

  findCatalogExtraServicesOrders(campaignId: string, params: any): Observable<Array<any>> {
    const parameters: any = {};

    if (params.userId) {
      parameters.userId = params.userId;
    }

    if (params.protocolNumber) {
      parameters.protocolNumber = params.protocolNumber;
    }

    if (params.barCode) {
      parameters.barCode = params.barCode;
    }

    if (params.mobilePhone) {
      parameters.mobilePhone = params.mobilePhone;
    }

    if (params.skip) {
      parameters.skip = params.skip;
    }

    if (params.limit) {
      parameters.limit = params.limit;
    }

    return this.api.get(`/api/campaigns/${campaignId}/services`, parameters);
  }

  findCatalogExtraServiceOrder(campaignId: string, catalogExtraServiceOrderId: string): Observable<any> {
    return this.api.get(`/api/campaigns/${campaignId}/services/${catalogExtraServiceOrderId}`);
  }

  findParticipantInfoByDocument(campaignId: string, document: string): Observable<any> {
    const params = { searchByField: 'document', searchValue: document };
    return this.api.get(`/api/campaigns/${campaignId}/orders/participant/info`, params);
  }

  findParticipantInfoById(campaignId: string, userId: string): Observable<any> {
    return this.api.get(`/api/users/${userId}/campaigns/${campaignId}/data`);
  }

  refundOrder(campaignId: string, extraServiceOrderId: string, refundReason: string): Observable<boolean> {
    return this.api.post(`/api/campaigns/${campaignId}/services/${extraServiceOrderId}/refunds`, {
      refundReason: refundReason
    });
  }
}
