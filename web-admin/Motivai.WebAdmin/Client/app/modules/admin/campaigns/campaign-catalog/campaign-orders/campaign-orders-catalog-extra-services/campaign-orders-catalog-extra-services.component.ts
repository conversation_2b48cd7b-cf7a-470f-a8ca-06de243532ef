import { Component, OnInit, ViewChild } from '@angular/core';
import { TabsetComponent } from 'ng2-bootstrap';
import { Subscription } from 'rxjs';
import { AuthStore } from '../../../../../../core/auth/auth.store';
import { NavigationService } from '../../../../../../core/services/navigation.service';
import { RxjsHelpers } from '../../../../../../shared/helpers/rxjs-helpers';
import { CampaignStore } from '../../../campaign.store';
import { CampaignOrdersCatalogExtraServicesEditingComponent } from './campaign-orders-catalog-extra-services-editing/campaign-orders-catalog-extra-services-editing.component';
import { CampaignOrdersCatalogExtraServicesListingComponent } from './campaign-orders-catalog-extra-services-listing/campaign-orders-catalog-extra-services-listing.component';

@Component({
  selector: 'campaign-orders-catalog-extra-services',
  templateUrl: './campaign-orders-catalog-extra-services.component.html'
})
export class CampaignOrdersCatalogExtraServicesComponent implements OnInit {
  @ViewChild('tabs') tabs: TabsetComponent;
  @ViewChild('editing') editing: CampaignOrdersCatalogExtraServicesEditingComponent;
  @ViewChild('listing') listing: CampaignOrdersCatalogExtraServicesListingComponent;

  campaignId: string = '';

  private _campaign: Subscription;

  constructor(private _authStore: AuthStore, private _navigationService: NavigationService,
    private campaignStore: CampaignStore) {}

  ngOnInit() {
    this._navigationService.goToHomeIf(a => !a.role.PERMISSION_CAMPAIGNS_CATALOG_ORDERS_BILL_PAYMENTS_RECHARGES_VIEW);

    this._campaign = this.campaignStore.asObservable.subscribe(id => {
      this.campaignId = id;
    });
  }

  ngOnDestroy(): void {
    RxjsHelpers.unsubscribe(this._campaign);
  }

  editOrder(orderId: string): void {
    if (orderId) {
      this.editing.campaignId = this.campaignId;
      this.editing.catalogExtraServiceOrderId = orderId;
      this.editing.findCatalogExtraServiceOrder();
      this.tabs.tabs[1].active = true;
    }
  }

  refresh(): void {
    this.editing.clear();
    this.listing.searchCatalogExtraServicesOrders();
  }
}
