<gp-modal #modal title="Detalhes do Filtro" width="900px" (onClose)="reset()">
	<gp-alert [overlay]="true" #alert></gp-alert>

	<gp-card title="Filtros Ativos">
		<gp-form-row>
			<gp-form-col cols="12 6 6" *ngIf="hasReceiverName">
				<gp-simple-input label="Nome do Beneficiário">
					<input type="text" class="form-control" name="receiverName" [ngModel]="filter.receiverName"
						disabled>
				</gp-simple-input>
			</gp-form-col>

			<gp-form-col cols="12 6 6" *ngIf="hasReceiverDocument">
				<gp-simple-input label="CPF/CNPJ do Beneficiário">
					<input type="text" class="form-control" name="receiverDocument" [ngModel]="(filter.receiverDocument | document)"
						disabled>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 6 6" *ngIf="hasPayerName">
				<gp-simple-input label="Nome do Pagador">
					<input type="text" class="form-control" name="payerName" [ngModel]="filter.payerName" disabled>
				</gp-simple-input>
			</gp-form-col>

			<gp-form-col cols="12 6 6" *ngIf="hasPayerDocument">
				<gp-simple-input label="CPF/CNPJ do Pagador">
					<input type="text" class="form-control" name="payerDocument" [ngModel]="(filter.payerDocument | document)"
						disabled>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 6 6" *ngIf="hasAssignor">
				<gp-simple-input label="Emissor">
					<input type="text" class="form-control" name="assignor" [ngModel]=" filter.assignor" disabled>
				</gp-simple-input>
			</gp-form-col>

			<gp-form-col cols="12 6 6" *ngIf="hasBillingAmount">
				<!-- TODO: será um intervalo de valor -->
				<gp-simple-input label="Valor Líquido do Boleto">
					<input type="text" class="form-control" name="billingAmount" [ngModel]="(filter.billingAmount | amount)"
						disabled>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card [last]="true">
		<gp-form-row>
			<gp-form-col cols="12" [inputGroup]="false">
				<gp-spinner-button type="button" bootstrapClass="default" text="Voltar" pull="right" icon="arrow-left"
					marginRight="5px" (click)="onBack()">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</gp-card>
</gp-modal>
