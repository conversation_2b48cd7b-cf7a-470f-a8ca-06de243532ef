import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from "@angular/core";
import { CampaignStore } from "../../../campaign.store";
import { CampaignFilterService } from "../campaign-filter.service";
import { GpAlertComponent } from "../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component";
import { RxjsHelpers } from '../../../../../../shared/helpers/rxjs-helpers';
import { Subscription } from "rxjs";
import { Item } from "../../../../../../shared/models/item";

@Component({
    selector: 'campaign-filters-allow-byrules',
    templateUrl: 'campaign-filters-allow-byrules.component.html'
})
export class CampaignFiltersAllowByRulesComponent implements OnInit, OnDestroy {
    @ViewChild('alert') alert: GpAlertComponent;

    private _campaign$: Subscription;
    loading: boolean = false;

    campaignPartners: Array<Item> = [];

    ruleFilterModel = {
        departmentId: '',
        categoryId: '',
        subcategoryId: ''
    };

    byAllowRules: any = [];

    params: any = {
        skip: 0,
        limit: 20
    };

    get campaignId() {
        return this._campaignStore.id;
    }

    ngOnInit() {
        this._campaign$ = this._campaignStore.asObservable
            .subscribe(id => {
                if (id)
                    this.loadFiltersByAllowRules();
            });
    }

    ngOnDestroy() {
        RxjsHelpers.unsubscribe(this._campaign$);
    }

    constructor(private _campaignStore: CampaignStore, private _filterService: CampaignFilterService) { }

    loadFiltersByAllowRules() {
        this.loading = true;
        this._filterService.getFiltersByAllowRules(this.campaignId, this.params)
            .subscribe(
                filters => {
                    this.byAllowRules = filters;
                    this.ruleFilterModel = {
                        departmentId: '',
                        categoryId: '',
                        subcategoryId: ''
                      };
                    this.loading = false;
                },
                err => {
                    this.loading = false;
                    this.handleError(err);
                }
            );
    }

    addAllowRuleFilter(event) {
        event.preventDefault();
        this.loading = true;
        this._filterService.addAllowRuleFilter(this.campaignId, this.ruleFilterModel)
            .subscribe(
                result => {
                    if (result) {
                        this.alert.showSuccess('Filtro por regra adicionado com sucesso.');
                        this.ruleFilterModel = {
                            departmentId: '',
                            categoryId: '',
                            subcategoryId: ''
                        };
                        if (!this.byAllowRules)
                            this.byAllowRules = [];
                        this.byAllowRules.push(result);
                        this.loadFiltersByAllowRules();
                    } else {
                        this.alert.showWarning('Não foi possível adicionar o filtro, por favor, tente novamente.');
                    }
                    this.loading = false;
                },
                err => {
                    this.loading = false;
                    this.handleError(err);
                }
            );
    }

    removeAllowRuleFilter(filter: any) {
        if (!confirm(`Deseja excluir o filtro por regra?`)) return;
        this.loading = true;
        this._filterService.removeAllowRuleFilter(this.campaignId, filter.id)
          .subscribe(
            result => {
              if (result) {
                if (this.byAllowRules) {
                  this.byAllowRules = this.byAllowRules.filter(r => r.id != filter.id);
                }
                this.alert.showSuccess('Filtro por regra removido com sucesso.');
              } else {
                this.alert.showWarning('Não foi possível remover o filtro, por favor, tente novamente.');
              }
              this.loading = false;
            },
            err =>  {
                this.loading = false;
                this.handleError(err);
            },
          );
      }

    handleError(err) {
        this.alert.handleAndShowError(err);
    }

    onPageChanged(event) {
        if (event) {
            this.params.skip = event.skip;
            this.params.limit = event.limit;
            this.loadFiltersByAllowRules();
        }
    }
}