import { Component, EventEmitter, Output, ViewChild } from '@angular/core';
import { AuthStore } from '../../../../../../../../core/auth/auth.store';
import { GpAlertComponent } from '../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignStockService } from '../../../campaign-stock.service';

@Component({
  selector: 'vouchers-stocks-search-listing',
  templateUrl: './campaign-vouchers-stocks-search-listing.component.html'
})
export class CampaignVouchersStocksSearchListingComponent {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @Output('edit') editorEmitter: EventEmitter<any> = new EventEmitter<any>();

  stocks: any[] = [];
  partnerId: string = null; // O valor está no arquivo appsettings.json

  params: any = {
    skuCode: '',
    skip: 0,
    limit: 10
  };

  loading: boolean = false;

  constructor(
    private authStore: AuthStore,
    private stockService: CampaignStockService
  ) {}

  get canSearchStock() {
    return this.authStore.role.CAMPAIGNS_CATALOG_VOUCHER_STOCK_SEARCH;
  }

  onSearchVouchers() {
    if (!this.params.skuCode || !this.params.skuCode.length) {
      this.gpAlert.showWarning('Informe o código SKU para pesquisa.');
      return;
    }

    this.clear();
    this.loading = true;
    this.stockService
      .findByPartnerIdAndSkuCode(null, this.params.skuCode)
      .subscribe(
        response => {
          if (response) {
            this.stocks.push(response);
          } else {
            this.stocks = [];
          }
          this.loading = false;
        },
        err => {
          this.loading = false;
          this.gpAlert.handleAndShowError(err);
        }
      );
  }

  editStock(stock: any) {
    this.editorEmitter.emit(stock);
  }

  clear() {
    this.stocks = [];
  }
}
