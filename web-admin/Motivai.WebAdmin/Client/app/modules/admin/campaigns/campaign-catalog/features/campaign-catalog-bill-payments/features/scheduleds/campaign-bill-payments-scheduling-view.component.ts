import { Component, OnInit, ViewChild } from '@angular/core';
import { TabsetComponent } from 'ng2-bootstrap';
import { CampaignBillPaymentsSchedulingListComponent } from './campaign-bill-payments-scheduling-list/campaign-bill-payments-scheduling-list.component';
import { CampaignBillPaymentsSchedulingEditComponent } from './campaign-bill-payments-scheduling-edit/campaign-bill-payments-scheduling-edit.component';

@Component({
    selector: 'campaign-bill-payments-scheduling-view',
    templateUrl: './campaign-bill-payments-scheduling-view.component.html'
})
export class CampaignBillPaymentsSchedulingViewComponent implements OnInit {
    @ViewChild('tabs') tabs: TabsetComponent;
    @ViewChild('list') list: CampaignBillPaymentsSchedulingListComponent;
    @ViewChild('edit') edit: CampaignBillPaymentsSchedulingEditComponent;

    constructor() { }

    ngOnInit(): void {
        this.tabs.tabs[1].disabled = true;
    }

    showDetails(event) {
        if (!event.id)
            return;
        this.edit.paymentScheduledId = event.id;
        this.edit.getScheduledPaymentById();
        this.tabs.tabs[1].disabled = false;
        this.tabs.tabs[1].active = true;
    }

    onListSelected() {
        this.list.find();
        this.tabs.tabs[1].disabled = true;
        this.clear();
    }

    private clear() {
        this.edit.paymentScheduled = {
            billDetails: {}
        };
    }
}
