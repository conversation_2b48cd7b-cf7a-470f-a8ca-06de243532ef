import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { TabsetComponent } from 'ng2-bootstrap/tabs';
import { Component, OnInit, ViewChild, OnDestroy } from '@angular/core';
import { CampaignService } from '../../../campaign.service';
import { ActivatedRoute } from '@angular/router';
import { AuthStore } from '../../../../../../core/auth/auth.store';
import { GpCampaignImportBatchlistComponent } from '../../../common/campaign-batches/campaign-import-batchlist.component';
import { CampaignStore } from '../../../campaign.store';
import { Subscription } from 'rxjs/Subscription';
import { RxjsHelpers } from '../../../../../../shared/helpers/rxjs-helpers';

@Component({
  selector: 'campaign-orders-import',
  templateUrl: 'campaign-orders-import.component.html'
})
export class CampaignOrdersImportComponent implements OnInit, OnDestroy {
  @ViewChild('tabs') tabs: TabsetComponent;
  @ViewChild('listComponent') listComponent: GpCampaignImportBatchlistComponent;
  @ViewChild('settingsAlert') settingsAlert: GpAlertComponent;

  settings: any = { variationType: 'Percent' };
  loadingSettings: boolean = false;
  campaignId: string;
  batchImportId: string;

  private _campaign$: Subscription;

  constructor(
    private _campaignService: CampaignService,
    private _as: AuthStore,
    private _campaignStore: CampaignStore
  ) {}

  ngOnInit() {
    this._campaign$ = this._campaignStore.asObservable.subscribe(
      id => {
        this.campaignId = id;
        this.getOrdersImportSettings();
      });
  }

  ngOnDestroy(): void {
    RxjsHelpers.unsubscribe(this._campaign$);
  }

  private editBatchImport($event) {
    if ($event) {
      this.batchImportId = $event.id;
      this.tabs.tabs[1].active = true;
    }
  }

  private refreshGrid($event) {
    this.batchImportId = '';
    this.listComponent.getBatches();
  }

  private getOrdersImportSettings() {
    if (this.campaignId) {
      this._campaignService.getOrdersImportSettings(this.campaignId).subscribe(
        settings => {
          if (settings) this.settings = settings;
        },
        err => {
          const msg = err
            ? err.message
              ? err.message
              : err.toString()
            : 'Ocorreu um erro ao carregar as configurações. Entre em contato com o suporte do sistema!';
          this.settingsAlert.showError(msg);
        }
      );
    }
  }

  private saveSettings() {
    if (this.campaignId) {
      this.loadingSettings = true;
      this._campaignService
        .saveOrdersImportSettings(this.campaignId, this.settings)
        .subscribe(
          response => {
            if (response) {
              this.settingsAlert.showSuccess(
                'Configuração de variação de valor atualizada com sucesso'
              );
            } else {
              this.settingsAlert.showError(
                'Ocorreu um erro ao salvar a configuração'
              );
            }

            this.loadingSettings = false;
          },
          err => {
            const msg = err
              ? err.message
                ? err.message
                : err.toString()
              : 'Ocorreu um erro ao salvar a configuração. Entre em contato com o suporte do sistema!';
            this.settingsAlert.showError(msg);
            this.loadingSettings = false;
          }
        );
    }
  }
}
