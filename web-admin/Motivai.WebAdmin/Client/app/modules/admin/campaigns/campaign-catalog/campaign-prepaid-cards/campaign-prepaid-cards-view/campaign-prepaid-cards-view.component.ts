import { Component } from '@angular/core';

import { AuthStore } from '../../../../../../core/auth/auth.store';

@Component({
  selector: 'app-campaign-prepaid-cards-view',
  templateUrl: './campaign-prepaid-cards-view.component.html'
})
export class CampaignPrepaidCardsViewComponent {
  constructor(private _authStore: AuthStore) {}

  get canViewPrepaidCard() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_CATALOG_PREPAID_CARDS_ORDERS_IMPORT;
  }
}
