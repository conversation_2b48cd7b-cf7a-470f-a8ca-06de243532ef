import { Component, OnInit, ViewChild, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { Subscription } from 'rxjs';

import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignService } from '../../../campaign.service';
import { CampaignStore } from '../../../campaign.store';
import { RxjsHelpers } from '../../../../../../shared/helpers/rxjs-helpers';

@Component({
  selector: 'app-name',
  templateUrl: 'campaign-orders-sla.component.html'
})
export class CampaignOrdersSlaComponent implements OnInit, OnDestroy {
  @ViewChild('alert') alert: GpAlertComponent;

  private underAnalysis: any = {
    status: 'UnderAnalysis',
    deadline: 0,
    enabled: false,
    saturday: false,
    sunday: false,
    holidays: false
  };

  private processed: any = {
    status: 'Processed',
    deadline: 0,
    enabled: false,
    saturday: false,
    sunday: false,
    holidays: false
  };

  private production: any = {
    status: 'Production',
    deadline: 0,
    enabled: false,
    saturday: false,
    sunday: false,
    holidays: false
  };

  private shipping: any = {
    status: 'Shipping',
    deadline: 0,
    enabled: false,
    saturday: false,
    sunday: false,
    holidays: false
  };

  private error: any = {
    status: 'Error',
    deadline: 0,
    enabled: false,
    saturday: false,
    sunday: false,
    holidays: false
  };

  private sla: any = {
    holidays: [],
    slaByStatus: []
  };
  private holiday: any = {};
  private holidays: any[] = [];
  private days: any[] = [];
  private months: any[] = [
    { value: 1, text: 'Janeiro' },
    { value: 2, text: 'Fevereiro' },
    { value: 3, text: 'Março' },
    { value: 4, text: 'Abril' },
    { value: 5, text: 'Maio' },
    { value: 6, text: 'Junho' },
    { value: 7, text: 'Julho' },
    { value: 8, text: 'Agosto' },
    { value: 9, text: 'Setembro' },
    { value: 10, text: 'Outubro' },
    { value: 11, text: 'Novembro' },
    { value: 12, text: 'Dezembro' }
  ];
  private loading: boolean = false;
  private _campaignId: string;

  private _campaign$: Subscription;

  constructor(
    private _campaignService: CampaignService,
    private _campaignStore: CampaignStore
  ) {}

  ngOnInit() {
    this._campaign$ = this._campaignStore.asObservable.subscribe(
      id => {
        this._campaignId = id;
        this.findSla();
      });

    let days = [];
    for (let i = 1; i <= 31; i++) {
      days.push(i);
    }
    this.days = days;
  }

  ngOnDestroy(): void {
    RxjsHelpers.unsubscribe(this._campaign$);
  }

  private findSla() {
    if (this._campaignId) {
      this.loading = true;
      this.alert.clear();
      this._campaignService.findOrdersSla(this._campaignId).subscribe(
        response => {
          if (response && response.slaByStatus) {
            const underAnalysis = response.slaByStatus.find(
              x => x.status === 'UnderAnalysis'
            );
            if (underAnalysis) this.underAnalysis = underAnalysis;

            const processed = response.slaByStatus.find(
              x => x.status === 'Processed'
            );
            if (processed) this.processed = processed;

            const production = response.slaByStatus.find(
              x => x.status === 'Production'
            );
            if (production) this.production = production;

            const shipping = response.slaByStatus.find(
              x => x.status === 'Shipping'
            );
            if (shipping) this.shipping = shipping;

            const error = response.slaByStatus.find(x => x.status === 'Error');
            if (error) this.error = error;
          }

          if (response && response.holidays) {
            this.holidays = response.holidays;
            this.holidays.forEach(x => {
              x.monthDescription = this.handleMonthDescription(x.month);
            });
          }

          this.loading = false;
        },
        err => {
          this.handleError(err);
          this.loading = false;
        }
      );
    }
  }

  private saveSla() {
    if (this._campaignId) {
      this.loading = true;
      this.alert.clear();
      if (!this.sla) this.sla = {};

      this.sla.slaByStatus = [];
      this.sla.slaByStatus.push(this.underAnalysis);
      this.sla.slaByStatus.push(this.processed);
      this.sla.slaByStatus.push(this.production);
      this.sla.slaByStatus.push(this.shipping);
      this.sla.slaByStatus.push(this.error);
      this.sla.holidays = this.holidays || null;

      this._campaignService.saveOrdersSla(this._campaignId, this.sla).subscribe(
        response => {
          if (response) {
            this.alert.showSuccess('Configurações de SLA salvas com sucesso!');
          } else {
            this.alert.showError(
              'Ocorreu um erro ao salvar as configurações de SLA'
            );
          }
          this.loading = false;
        },
        err => {
          this.handleError(err);
          this.loading = false;
        }
      );
    }
  }

  private addHoliday() {
    if (this.holiday) {
      this.holiday.monthDescription = this.handleMonthDescription(
        this.holiday.month
      );
      this.holidays.push(this.holiday);
      this.holiday = {};
    }
  }

  private removeHoliday($event: any) {
    if ($event) {
      const holiday = this.holidays.find(x => x === $event);
      if (holiday) {
        this.holidays.splice(this.holidays.indexOf(holiday), 1);
      }
    }
  }

  private handleMonthDescription(value: any) {
    return this.months.find(x => x.value.toString() === value.toString()).text;
  }

  private handleError(err: any) {
    const msg = err
      ? err.message
        ? err.message
        : err.toString()
      : 'Ocorreu um erro ao efetuar a operação';
    this.alert.showError(msg);
  }
}
