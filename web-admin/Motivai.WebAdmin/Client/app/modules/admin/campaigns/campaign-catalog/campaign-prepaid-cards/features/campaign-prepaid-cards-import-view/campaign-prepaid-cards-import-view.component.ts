import { <PERSON>mpo<PERSON>, On<PERSON>ni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { Subscription } from 'rxjs/Subscription';


import { CampaignPrepaidCardsBatchesListingComponent } from './campaign-prepaid-cards-batches-listing/campaign-prepaid-cards-batches-listing.component';
import { CampaignPrepaidCardsLinkingImportComponent } from './campaign-prepaid-cards-orders-import/campaign-prepaid-cards-orders-import.component';
import { CampaignStore } from '../../../../campaign.store';
import { RxjsHelpers } from '../../../../../../../shared/helpers/rxjs-helpers';
import { TabsetComponent } from 'ng2-bootstrap';

@Component({
  selector: 'app-campaign-prepaid-cards-import-view',
  templateUrl: './campaign-prepaid-cards-import-view.component.html'
})
export class CampaignPrepaidCardsImportViewComponent implements OnInit, On<PERSON><PERSON><PERSON> {
  @ViewChild('tabs') tabs: TabsetComponent;
  @ViewChild('batchListing') batchListing: CampaignPrepaidCardsBatchesListingComponent;
  @ViewChild('batchImport') batchImport: CampaignPrepaidCardsLinkingImportComponent;

  campaignId: string = null;
  private _campaign$: Subscription;

  constructor(private _campaignStore: CampaignStore) { }

  ngOnInit() {
    this._campaign$ = this._campaignStore.asObservable
      .subscribe(id => this.campaignId = id);
  }

  ngOnDestroy(): void {
    RxjsHelpers.unsubscribe(this._campaign$);
  }

  refreshGrid() {
    this.batchListing.clear();
    this.batchListing.findAndCountBatches();
    this.batchImport.clear();
  }

  showPrepaidCardsDetails(event: any) {
    this.batchImport.clear();
    this.batchImport.batchId = event.id;
    this.batchImport.findPrepaidCardBatch();
    this.tabs.tabs[1].active = true;
  }
}
