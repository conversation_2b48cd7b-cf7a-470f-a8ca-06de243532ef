import { DecimalPipe } from '@angular/common';
import { Component, Input, OnInit, ViewChild } from '@angular/core';
import * as moment from 'moment-timezone';
import { PERMISSION_CALLCENTER_REFUND } from '../../../../../../../core/auth/access-points';
import { AuthStore } from '../../../../../../../core/auth/auth.store';
import { isRewardsOrMarketplace } from '../../../../../../../shared/components/business/campaigns/campaign-setttings/campaign-settings';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpModalComponent } from '../../../../../../../shared/components/gp-modal/gp-modal.component';
import { CampaignSettingsService } from '../../../../campaign-settings.service';
import { CampaignStore } from '../../../../campaign.store';
import { CampaignOrdersHandlerService } from '../campaign-orders-handler.service';
import { OrderCancellationModalComponent } from '../order-cancellation-modal/order-cancellation-modal.component';
import { OrdersEventTrackingModalComponent } from '../orders-event-tracking-modal/orders-event-tracking-modal.component';
import { OrdersItemDetailsModalComponent } from '../orders-item-details-modal/orders-item-details-modal.component';
import { OrdersProductionModalComponent } from '../orders-manufacturing-modal/orders-manufacturing-modal.component';
import { OrdersPricingModalComponent } from '../orders-pricing-modal/orders-pricing-modal.component';
import { OrdersShippingModalComponent } from '../orders-shipping-modal/orders-shipping-modal.component';
import { OrderRefunderModalComponent } from './../order-refunder-modal/order-refunder-modal.component';
import { OrderVouchersComponent } from './../order-vouchers/order-vouchers.component';

@Component({
    selector: 'campaign-orders-handler-edit',
    templateUrl: 'campaign-orders-handler-edit.component.html'
})
export class CampaignOrdersHandlerEditComponent implements OnInit {
    _campaignId: string;
    @Input() set campaignId(v: string) {
        if (v) {
            this._campaignId = v;
        }
    }

    _orderId: string;
    @Input() set orderId(v: string) {
        if (v) {
            this._orderId = v;
            this.findOrderById();
        }
    }

    @ViewChild('modal') modal: GpModalComponent;

    // Manufacturing modal
    @ViewChild('productionModal') productionModal: OrdersProductionModalComponent;
    itemProduction: any = {};

    // Info modal
    @ViewChild('infoModal') infoModal: OrdersItemDetailsModalComponent;
    itemInfo: any = {};

    // Price modal
    @ViewChild('priceModal') priceModal: OrdersPricingModalComponent;
    itemPriced: any = {};

    // Tracking modal
    @ViewChild('trackingModal') trackingModal: OrdersEventTrackingModalComponent;
    itemTracking: any = {};

    // Shipping modal
    @ViewChild('shippingModal') shippingModal: OrdersShippingModalComponent;
    itemShipping: any = {};

    @ViewChild('cancellationModal') cancellationModal: OrderCancellationModalComponent;
    @ViewChild('refundedModal') refundedModal: OrderRefunderModalComponent;

    @ViewChild('vouchersModal') vouchersModal: OrderVouchersComponent;

    // Page components
    @ViewChild('alert') alert: GpAlertComponent;
    @ViewChild('childOrderAlert') childOrderAlert: GpAlertComponent;

    order: any = {};
    childrenOrders: any[] = [];
    loading: boolean = false;
    loadingScreen: boolean = false;
    pipe: DecimalPipe = new DecimalPipe('pt-BR');
    now: Date = moment().add('1', 'days').toDate();
    isManualAprove: boolean = false;
    statusDescription: string = "";
    paymentMethods: any[] = [];

    // Multiple pricing
    @ViewChild('pricingAlert') pricingAlert: GpAlertComponent;
    isMultiplePricing: boolean = false;
    multiplePricingItems: any[] = [];
    itemGrouperPricing: string = '';

    // Multiple cancellation
    @ViewChild('cancellationAlert') cancellationAlert: GpAlertComponent;
    isMultipleCancellation: boolean = false;
    multipleCancellationItems: any[] = [];
    itemGrouperCancellation: string = '';
    manualAction: string = 'MANUAL_ACTION';

    allowOnlyPartnerOrderRefund: boolean = false;

    get hasRiskAssessment() {
        return this.order && this.order.riskAssessment;
    }

    get hasReceiver() {
        return this.order && this.order.shippingAddress && this.order.shippingAddress.receiver;
    }

    get isUnderRiskAssessment() {
        return this.order && this.order.internalStatus === "UNDER_RISK_ASSESSMENT";
    }

    get isPixPaymentMethod() {
        return this.order != null && this.order.paymentMethods != null && this.order.paymentMethods.length == 1 && this.order.paymentMethods.find(p => p.type == "PIX");
    }


    constructor(private _authStore: AuthStore, private _campaignStore: CampaignStore,
        private _ordersService: CampaignOrdersHandlerService, private _campaignSettings: CampaignSettingsService) { }


    ngOnInit(): void {
        this.loadCampaignParametrization();
    }

    get isRewardsOrMarketplace() {
        return isRewardsOrMarketplace(this.order.type)
    }

    get isUserWithGpBu() {
        return this._campaignStore.isRootBu;
    }

    get hasPermissionToRefund() {
        return this._authStore.hasPermissionTo(PERMISSION_CALLCENTER_REFUND);
    }

    // Clear all components
    public clear() {
        this._orderId = '';
        this.order = {};
        this.itemPriced = {};
        this.itemShipping = {};
        this.itemInfo = {};
        this.itemTracking = {};
        this.itemProduction = {};
        this.statusDescription = '';
        this.paymentMethods = [];
        this.priceModal.clear();
        this.shippingModal.clear();
        this.trackingModal.clear();
        this.productionModal.clear();
    }

    private loadCampaignParametrization() {
        this.loading = true;
        this._campaignSettings.getCampaignParametrization(this._campaignStore.id)
            .subscribe(
                settings => {
                    this.loading = false;
                    if (settings) {
                        this.allowOnlyPartnerOrderRefund = settings.parametrizations.allowOnlyPartnerOrderRefund;
                    }
                },
                err => {
                    this.loading = false;
                    this.alert.handleAndShowError(err);
                }
            );
    }

    private findOrderById(resetCancellationItems: boolean = false) {
        if (this._campaignId && this._orderId) {
            this.loadingScreen = true;
            this._ordersService.findOrderById(this._campaignId, this._orderId).subscribe(
                order => {
                    if (order) {
                        this.order = order;
                        this.isManualAprove = false;
                        this.handleOrder(order);
                    }

                    if (resetCancellationItems) {
                        this.setMultipleCancellation(true, this.itemGrouperCancellation);
                    }
                    this.loadingScreen = false;
                },
                err => {
                    const message = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao carregar as informações do pedido';
                    this.alert.showError(message);
                    this.loadingScreen = false;
                }
            );
        }
    }

    private handleOrder(order) {
        if (this.order.participant) {
            if (this.order.participant.cpf) {
                this.order.participant.document = this.order.participant.cpf;
            } else if (this.order.participant.cnpj) {
                this.order.participant.document = this.order.participant.cnpj;
            }
        }

        if (order.childrenOrders && order.childrenOrders.length) {
            this.childrenOrders = order.childrenOrders || [];

            // Format currency
            if (this.childrenOrders.length) {
                this.childrenOrders.forEach(order => {
                    if (order.items && order.items.length) {
                        order.items.forEach(item => {
                            if (item.unitPrice && item.unitPrice.currency) {
                                item.formattedUnitPriceCurrency = this.pipe.transform(item.unitPrice.currency, '1.2-2');
                            }
                            if (item.shippingCost && item.shippingCost.currency) {
                                item.formattedShippingCostCurrency = this.pipe.transform(item.shippingCost.currency, '1.2-2');
                            }
                        });
                    }
                });
            }
        }

        this.paymentMethods = [];
        if (order.paymentMethods) {
            for (var payment of order.paymentMethods) {
                if (payment.typeDescription == 'PIX')
                {
                    this.paymentMethods.push(payment);
                }
            }
        }
    }

    private openPriceModal(itemGrouperId: string, item: any) {
        if (item) {
            this.itemPriced = this.createItemObject(itemGrouperId, item);
            if (this.itemPriced) {
                this.priceModal.clear();
                this.priceModal.open();
            }
        }
    }

    private createItemObject(itemGrouperId: string, item: any) {
        if (item) {
            return {
                itemGrouperId: itemGrouperId,
                unitPrice: item.unitPrice ? item.unitPrice.currency : 0,
                shippingCost: item.shippingCost ? item.shippingCost.currency : 0,
                quantity: item.quantity,
                productName: item.name,
                observations: item.observations,
                skuCode: item.skuCode,
                skuId: item.skuId
            };
        }

        return null;
    }

    showPriceButton(item: any): boolean {
        return item.status === 'UnderAnalysis' && item.dynamicPrice === true;
    }

    showTrackingEventsButton(item: any): boolean {
        return item.trackingEvents && item.trackingEvents.length;
    }

    showShippingButton(item: any): boolean {
        return (item.status === 'Validated' || item.status === 'Production' || item.status === 'Shipping')
            && item.processType === 'Offline'
            && !this.isB2B();
    }

    showProductionButton(item: any): boolean {
        return item.status === 'Validated' && item.processType === 'Offline' && !this.isB2B();
    }

    showMultiplePricingButton(itemGrouperId: string): boolean {
        const childOrder = this.childrenOrders.find(x => x.itemGrouperId === itemGrouperId);
        if (!childOrder) return false;
        if (!childOrder.items) return false;

        const hasDynamicPriceItems = childOrder.items.find(x => x.dynamicPrice);
        return hasDynamicPriceItems && childOrder.status === 'UnderAnalysis';
    }

    showMultipleCancellationButton(itemGrouperId: string): boolean {
        const childOrder = this.childrenOrders.find(x => x.itemGrouperId === itemGrouperId);
        if (!childOrder) return false;
        if (!childOrder.items) return false;

        const hasItensToCancel = childOrder.items.find(x => x.canBeCanceled);
        if (!hasItensToCancel) return false;
        return true;
    }

    showInvoiceButton(itemGrouperId: string): boolean {
        const childOrder = this.childrenOrders.find(x => x.itemGrouperId === itemGrouperId);
        if (!childOrder) return false;
        if (!childOrder.items) return false;

        const hasDynamicPriceItems = childOrder.items.find(x => x.dynamicPrice && !x.priced);
        return !hasDynamicPriceItems && childOrder.canBeInvoiced && (childOrder.status === 'UnderAnalysis' || childOrder.status === 'Approved');
    }

    showVouchers(childOrder: any) {
        if (!childOrder) {
            return;
        }
        this.vouchersModal.showExistingVouchers(childOrder);
    }

    showButtonVouchers(childOrder: any) {
        if (!childOrder) return false;
        if (!childOrder.items) return false;
        if (!childOrder.vouchers || !childOrder.vouchers.length) return false;

        return childOrder.items.find((x: any) => x.productType == 'ValeVirtual');
    }

    canDebtChildOrder(child: any) {
        return child && child.items && child.items.indexOf(i => i.dynamicPrice) > -1;
    }

    hasRefunds(childOrder: any, item: any) {
        return item.refunds && item.refunds.length;
    }

    isB2B(): boolean {
        if (!this.order) return false;
        if (!this.order.type) return false;
        return this.order.type === 'B2B';
    }

    private openTrackingEventsModal(itemGrouperId: string, item: any) {
        if (item) {
            this.itemTracking = {
                skuCode: item.skuCode,
                skuId: item.skuId,
                itemGrouperId: itemGrouperId,
                trackingEvents: item.trackingEvents,
            };
            this.trackingModal.open();
        }
    }

    private openShippingModal(itemGrouperId: string, item: any) {
        if (item) {
            this.itemShipping = this.createShippingObject(itemGrouperId, item);
            if (this.itemShipping) this.shippingModal.open();
        }
    }

    private createShippingObject(itemGrouperId: string, item: any) {
        if (item) {
            return {
                itemGrouperId: itemGrouperId,
                skuId: item.skuId,
                skuCode: item.skuCode,
                trackingCode: item.trackingCode,
                trackingLink: item.trackingLink,
                estimatedDeliveryDays: item.estimatedDeliveryDays
            };
        }

        return null;
    }

    openInfoModal(childOrder: any, item: any) {
        if (item) {
            if (!item.estimatedDeliveryDays) {
                item.estimatedDeliveryDays = childOrder.estimatedDeliveryDays;
            }
            this.itemInfo = item;
            this.infoModal.open();
        }
    }

    openProductionModal(itemGrouperId: string, item: any) {
        if (item) {
            this.itemProduction = {
                skuCode: item.skuCode,
                skuId: item.skuId,
                itemGrouperId
            };
            this.productionModal.open();
        }
    }

    openMasterOrderRefunderModal() {
        this.refundedModal.openModalToRefundMasterOrder(this._campaignId, this.order);
    }

    openChildOrderRefunderModal(childOrder: any) {
        this.refundedModal.openModalToRefundChildOrder(this._campaignId, this.order, childOrder);
    }

    openItemRefunderModal(childOrder: any, item: any) {
        this.refundedModal.openModalToRefundItem(this._campaignId, this.order, childOrder, item);
    }

    openRefunderModalToList(item: any) {
        if (!item) return;
        this.refundedModal.openModalToShowRefundList(this.order, item);
    }

    openCancelOrderModal() {
        this.cancellationModal.openModalToCancelMasterOrder(this._campaignId, this.order);
    }

    openCancelChildOrderModal(childOrder: any) {
        if (childOrder) {
            this.cancellationModal.openModalToCancelChildOrder(this._campaignId, this.order, childOrder);
        }
    }

    openCancelItemModal(childOrder: any, item: any) {
        if (item) {
            this.cancellationModal.openModalToCancelItem(this._campaignId, this.order, childOrder, item);
        }
    }

    conciliate(childOrder: any) {
        if (childOrder) {
            this.conciliateOrder({
                campaignId: this._campaignId,
                id: this.order.id,
                itemGrouperId: childOrder.itemGrouperId
            });
        }
    }

    setMultiplePricing(isPricing: boolean, itemGrouperId: string) {
        this.loading = true;
        if (this.pricingAlert) this.pricingAlert.clear();
        if (isPricing) {
            const childOrder = this.childrenOrders.find(x => x.itemGrouperId === itemGrouperId);
            if (childOrder && childOrder.items) {
                const itemsToPrice = childOrder.items.filter(x => x.dynamicPrice && x.status === 'UnderAnalysis') || [];
                itemsToPrice.forEach(item => {
                    if (!item.unitPrice) item.unitPrice = {};
                    if (!item.shippingCost) item.shippingCost = {};
                });

                this.multiplePricingItems = itemsToPrice;
            }
        }
        this.itemGrouperPricing = itemGrouperId;
        this.isMultiplePricing = isPricing;
        this.loading = false;
    }

    saveMultiplePricing(itemGrouperId: string) {
        this.loading = true;
        this.pricingAlert.clear();
        const items = this.multiplePricingItems.map(x => {
            return {
                skuId: x.skuId,
                skuCode: x.skuCode,
                unitPrice: x.unitPrice.currency || 0,
                shippingCost: x.shippingCost.currency || 0,
                quantity: x.quantity || 1
            };
        });
        const multiplePricing = {
            items,
            shippingCost: 0
        };

        this._ordersService.saveMultiplePrices(this._campaignId, this._orderId, itemGrouperId, multiplePricing).subscribe(
            response => {
                if (response) {
                    this.setMultiplePricing(false, '');
                    this.findOrderById();
                } else {
                    this.pricingAlert.showError('Ocorreu um erro ao precificar os produtos');
                }
                this.loading = false;
            },
            err => {
                const message = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao precificar os produtos';
                this.pricingAlert.showError(message);
                this.loading = false;
            }
        );
    }

    setMultipleCancellation(isCancellation: boolean, itemGrouperId: string) {
        this.loading = true;
        if (this.cancellationAlert) this.cancellationAlert.clear();
        if (isCancellation) {
            const childOrder = this.childrenOrders.find(x => x.itemGrouperId === itemGrouperId);
            if (childOrder && childOrder.items) {
                this.multipleCancellationItems = childOrder.items.filter(x => x.canBeCanceled);
            }
        }

        this.itemGrouperCancellation = itemGrouperId;
        this.isMultipleCancellation = isCancellation;
        this.loading = false;
    }

    showMultipleCancellationModal() {
        const items = this.multipleCancellationItems.filter(x => x.checked);
        const childOrder = this.childrenOrders.find(x => x.itemGrouperId === this.itemGrouperCancellation);
        if (!items || !items.length) return;
        this.cancellationModal.openModalToCancelMultipleItems(this.campaignId, this.order, this.itemGrouperCancellation, childOrder, items);
    }

    closeCancellationModal() {
        if (this.isMultipleCancellation) {
            this.findOrderById(true);
        } else {
            this.findOrderById();
        }
    }

    showGeneralItensGrid(itemGrouperId: string): boolean {
        if (this.isMultiplePricing && this.itemGrouperPricing === itemGrouperId) return false;
        if (this.isMultipleCancellation && this.itemGrouperCancellation === itemGrouperId) return false;
        return true;
    }

    showMultipleRefundGrid(itemGrouperId: string): boolean {
        return this.isMultipleCancellation && this.itemGrouperCancellation === itemGrouperId;
    }

    showMultiplePriceGrid(itemGrouperId: string): boolean {
        return this.isMultiplePricing && this.itemGrouperPricing === itemGrouperId;
    }

    createInvoiceForPartnerOrder(itemGrouperId: string) {
        if (!confirm('Atenção: ao prosseguir com esta ação o participante será debitado. Deseja continuar?')) return;
        this.loading = true;
        this.childOrderAlert.clear();
        this._ordersService.createInvoiceForPartnerOrder(this._campaignId, this._orderId, itemGrouperId).subscribe(
            response => {
                this.findOrderById();

                if (response === true) {
                    if (this.isB2B()) {
                        if (this.order.requiresAllChildren) {
                            this.childOrderAlert.showSuccess('Pedido atualizado com sucesso. A cobrança será gerada assim que todos os pedidos dos fornecedores forem aprovados.');
                        } else {
                            this.childOrderAlert.showSuccess('Cobrança gerada com sucesso');
                        }
                    } else {
                        this.childOrderAlert.showSuccess('Pedido faturado com sucesso');
                    }
                } else {
                    this.childOrderAlert.showError('Ocorreu um erro ao faturar o pedido');
                }
                this.loading = false;
            },
            err => {
                const message = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao faturar os produtos';
                this.childOrderAlert.showError(message);
                this.loading = false;
            }
        );
    }

    private calculateSlaStatus(percent: number): string {
        if (percent <= 50) return 'success';
        if (percent <= 80) return 'warning';
        return 'danger';
    }


    private conciliateOrder({ campaignId, id, itemGrouperId }) {
        this.alert.confirm('Conciliar pedido selecionado?')
            .then(result => {
                if (result && result.value) {
                    this.loading = true;
                    this._ordersService.conciliateOrder(campaignId, id, itemGrouperId).subscribe(
                      canceled => {
                        this.loading = false;
                        if (canceled) {
                          this.alert.showSuccess('Pedido do parceiro conciliado com sucesso');
                          this.findOrderById();
                        } else {
                          this.alert.showWarning('Não foi possível conciliar um ou mais itens do pedido. Tente novamente.');
                        }
                      },
                      err =>  {
                        this.loading = false;
                        this.alert.showError(err)
                        }
                    );
                }
            })
      }

    onHandleManualAction() {
        this.findOrderById();
    }
}
