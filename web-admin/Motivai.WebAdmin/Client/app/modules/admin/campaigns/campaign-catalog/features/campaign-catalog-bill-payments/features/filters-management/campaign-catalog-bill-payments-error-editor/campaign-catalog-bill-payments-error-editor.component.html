<spinner [overlay]="true" [show]="loading"></spinner>
<gp-alert [overlay]="true" #alert></gp-alert>

<gp-card [first]="true">
	<div class="row">
		<div class="col-lg-6 col-xs-12 br pv">
			<div class="clearfix">
				<p class="pull-left"><strong>Data Tentativa de Pagamento: </strong></p>
				<p class="pull-right mr">{{ (billsPaymentConsultsError.createDate | datetimezone) || '-'}}</p>
			</div>

			<div class="clearfix">
				<p class="pull-left"><strong>Status: </strong></p>
				<p class="pull-right mr">
					<strong>{{ billsPaymentConsultsError.formattedStatus }}</strong>
				</p>
			</div>
		</div>

		<div class="col-lg-6 col-xs-12 br pv">
			<div class="clearfix" [ngSwitch]="billsPaymentConsultsError.partners.partnerPaidBillInfo">
				<p class="pull-left"><strong>Parceiro: </strong></p>
				<div *ngSwitchCase="'IS2B'">
					<p class="pull-right mr">IS2B</p>
				</div>
				<div *ngSwitchDefault>
					<p class="pull-right mr"></p>
				</div>
			</div>
		</div>
	</div>
</gp-card>

<gp-card title="Boleto">
	<div class="row">
		<div class="col-lg-6 col-xs-12 br pv">
			<div class="clearfix">
				<p class="pull-left"><strong>Cedente: </strong></p>
				<p class="pull-right mr">{{ billsPaymentConsultsError.billDetails.assignor || '-'}}</p>
			</div>

			<div class="clearfix">
				<p class="pull-left"><strong>Tipo do Boleto: </strong></p>
				<p class="pull-right mr">{{ billsPaymentConsultsError.typeBill || '-'}}</p>
			</div>
		</div>

		<div class="col-lg-6 col-xs-12 br pv">
			<div class="clearfix">
				<p class="pull-left"><strong>Linha Digitável: </strong></p>
				<p class="pull-right mr">{{ billsPaymentConsultsError.billDetails.barCode || '-'}}</p>
			</div>

			<div class="clearfix">
				<p class="pull-left"><strong>Valor: </strong></p>
				<p class="pull-right mr">{{ (billsPaymentConsultsError.billDetails.billingAmount | amount) }}</p>
			</div>
		</div>
	</div>
	<hr />
	<div class="row">
		<div class="col-lg-6 col-xs-12 br pv">
			<h4>Pagador</h4>
			<div class="clearfix">
				<p class="pull-left"><strong>Nome: </strong></p>
				<p class="pull-right mr">{{ billsPaymentConsultsError.billDetails.payerName || '-'}}</p>
			</div>

			<div class="clearfix">
				<p class="pull-left"><strong>CPF/CNPJ: </strong></p>
				<p class="pull-right mr">{{ (billsPaymentConsultsError.billDetails.payerDocument | document)}}</p>
			</div>
		</div>

		<div class="col-lg-6 col-xs-12 br pv">
			<h4>Beneficiário</h4>
			<div class="clearfix">
				<p class="pull-left"><strong>Nome: </strong></p>
				<p class="pull-right mr">{{ billsPaymentConsultsError.billDetails.receiverName || '-'}}</p>
			</div>

			<div class="clearfix">
				<p class="pull-left"><strong>CPF/CNPJ: </strong></p>
				<p class="pull-right mr">{{ (billsPaymentConsultsError.billDetails.receiverDocument | document)}}</p>
			</div>
		</div>
	</div>
</gp-card>

<gp-card>
	<div class="col-lg-6 col-xs-12 br">
		<h4>Participante</h4>
		<div class="clearfix">
			<p class="pull-left"><strong>CPF/CNPJ: </strong></p>
			<p class="pull-right mr">{{ (billsPaymentConsultsError.userDetails.userDocument | document)}}</p>
		</div>
	</div>

	<div class="col-lg-6 col-xs-12" *ngIf="billsPaymentConsultsError.userDetails.userAccountOperatorDocument">
		<h4>Operador</h4>
		<div class="clearfix">
			<p class="pull-left"><strong>CPF/CNPJ: </strong></p>
			<p class="pull-right mr">{{ (billsPaymentConsultsError.userDetails.userAccountOperatorDocument | document)}}</p>
		</div>
	</div>
</gp-card>

<gp-card [last]="true">
	<gp-form-row>
		<gp-form-col cols="12">
			<gp-spinner-button type="button" pull="right" bootstrapClass="danger" icon="close" text="Rejeitar"
				[loading]="loading" [disabled]="!canReject" (click)="onRejectBill()" marginTop="27px">
			</gp-spinner-button>

			<gp-spinner-button [pink]="true" marginTop="27px" marginRight="5px" pull="right"
				text="Adicionar permissão de pagamento" icon="plus" [loading]="loading" [disabled]="!canAddFilters"
				(click)="onAddPermission()">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<campaign-catalog-bill-payments-adding-filters #addFilters></campaign-catalog-bill-payments-adding-filters>
