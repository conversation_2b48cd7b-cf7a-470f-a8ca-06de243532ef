import { Component, OnInit, ViewChild } from '@angular/core';
import { TabsetComponent } from 'ng2-bootstrap';

import { CampaignVouchersStocksSearchEditComponent } from './vouchers-stocks-search-edit/campaign-vouchers-stocks-search-edit.component';

@Component({
	selector: 'campaign-vouchers-stocks-search',
	templateUrl: './campaign-vouchers-stocks-search.component.html'
})
export class CampaignVouchersStocksSearchComponent implements OnInit {
	@ViewChild('tabs') tabs: TabsetComponent;
	@ViewChild('edit') edit: CampaignVouchersStocksSearchEditComponent;

	ngOnInit() {
		this.tabs.tabs[1].disabled = true;
	}
	
	editStock(event: any) {
		this.edit.clickedOnDetails(event);
		this.tabs.tabs[1].disabled = false;
		this.tabs.tabs[1].active = true;
	}

	clear() {
		this.edit.clear();
		this.tabs.tabs[1].disabled = true;
	}
}
