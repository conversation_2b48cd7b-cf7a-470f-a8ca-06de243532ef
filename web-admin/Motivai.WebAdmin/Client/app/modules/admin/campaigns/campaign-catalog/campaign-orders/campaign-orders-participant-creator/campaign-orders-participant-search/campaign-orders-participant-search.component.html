<spinner [show]="loading" [overlay]="true"></spinner>
<gp-alert [overlay]="true" #gpAlert></gp-alert>
<div>
	<gp-card title="Pesquisa de participante">
		<gp-form-row>
			<gp-form-col>
				<label>Utilizar participante padrão</label>
				<div>
					<gp-switch name="useDefaultParticipant" [(ngModel)]="userDefaultParticipant" (onchange)="onDefaultParticipantChange()">
					</gp-switch>
				</div>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 3 3">
				<label>Pesquisar por</label>
				<gp-select name="searchByField" [items]="searchFields" [allowClear]="false"
					(change)="onChangeSearchField($event)" [(ngModel)]="searchByField" [disabled]="disableParticipantSearch">
				</gp-select>
			</gp-form-col>

			<gp-form-col cols="12 3 3">
				<label>{{ lableSearchValue }}</label>
					<input type="text" class="form-control" id="searchValue" name="searchValue"
						[(ngModel)]="searchValue" [disabled]="disableParticipantSearch" />
			</gp-form-col>

			<gp-form-col cols="12 6 6" *ngIf="foundParticipants">
				<label>Selecione o participante</label>
				<gp-select name="userId" [items]="participantsList" [allowClear]="false"
					(change)="onSelectedParticipant($event)" [(ngModel)]="userId" [disabled]="disableParticipantSearch">
				</gp-select>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12">
				<gp-spinner-button [pink]="true" marginTop="27px" pull="right" text="Cadastrar Participante" 
					icon="plus" [loading]="loading" (click)="registerParticipant()">
				</gp-spinner-button>

				<gp-spinner-button type="button" pull="right" buttonClass="bg-primary-dark" icon="plus" text="Pesquisar"
					[search]="true" [loading]="loading" [disabled]="loading" (click)="onSearchParticipant()"
					marginRight="5px" marginTop="27px">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
		<campaign-participants-registration [address]="true" (onRegistered)="onRegistered($event)" #participantRegistration></campaign-participants-registration> 
	</gp-card>

	<gp-card title="Dados do participante" *ngIf="hasParticipantDetails">
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Nome">
					<input type="text" class="form-control" name="name" id="name" [ngModel]="participantDetails.name"
						disabled>
				</gp-simple-input>
			</gp-form-col>

			<gp-form-col cols="12 3 3">
				<gp-simple-input label="CPF/CNPJ">
					<input type="text" class="form-control" name="userDocument" id="userDocument"
						[ngModel]="participantDetails.document" disabled>
				</gp-simple-input>
			</gp-form-col>

			<gp-form-col cols="12 3 3" *ngIf="hasBalance">
				<gp-simple-input label="Saldo de Pontos">
					<input type="text" class="form-control" name="balance" id="balance"
						[ngModel]="participantDetails.balance | amount" disabled>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="E-mail">
					<input type="text" class="form-control" name="email" id="email"
						[ngModel]="participantDetails.mainEmail" disabled>
				</gp-simple-input>
			</gp-form-col>

			<gp-form-col cols="12 3 3">
				<gp-simple-input label="Login">
					<input type="text" class="form-control" name="login" id="login" [ngModel]="participantDetails.login"
						disabled>
				</gp-simple-input>
			</gp-form-col>

			<gp-form-col cols="12 3 3">
				<gp-simple-input label="Código">
					<input type="text" class="form-control" name="clientUserId" id="clientUserId"
						[ngModel]="participantDetails.clientUserId" disabled>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 3 3">
				<gp-simple-input label="Telefone">
					<input type="text" class="form-control" name="mainPhone" id="mainPhone"
						[ngModel]="participantDetails.mainPhone" disabled>
				</gp-simple-input>
			</gp-form-col>

			<gp-form-col cols="12 3 3">
				<gp-simple-input label="Celular">
					<input type="text" class="form-control" name="mobilePhone" id="mobilePhone"
						[ngModel]="participantDetails.mobilePhone" disabled>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<div *ngIf="hasAddress">
			<hr />
			<h4>Dados do endereço</h4>
			<gp-form-row>
				<gp-form-col cols="12 3 3">
					<gp-simple-input label="CEP">
						<gp-input-mask name="cep" mask="99999-999" [ngModel]="address.cep" disabled>
						</gp-input-mask>
					</gp-simple-input>
				</gp-form-col>
			</gp-form-row>

			<gp-form-row>
				<gp-form-col cols="12 6 6">
					<gp-simple-input label="Logradouro">
						<input type="text" class="form-control" name="address" [ngModel]="address.street" disabled/>
					</gp-simple-input>
				</gp-form-col>

				<gp-form-col cols="12 2 2">
					<gp-simple-input label="Número">
						<input type="text" class="form-control" name="number" [ngModel]="address.number" disabled/>
					</gp-simple-input>
				</gp-form-col>

				<gp-form-col cols="12 4 4">
					<gp-simple-input label="Complemento">
						<input type="text" class="form-control" name="complement" [ngModel]="address.complement" disabled/>
					</gp-simple-input>
				</gp-form-col>
			</gp-form-row>

			<gp-form-row>
				<gp-form-col cols="12 6">
					<gp-simple-input label="Bairro">
						<input type="text" class="form-control" [disabled]="addressFound" name="neighborhood"
						[ngModel]="address.neighborhood" disabled/>
					</gp-simple-input>
				</gp-form-col>

				<gp-form-col cols="12 6 6">
					<gp-simple-input label="Cidade">
						<input type="text" class="form-control" [disabled]="addressFound" name="city" required
							[ngModel]="address.city" disabled/>
					</gp-simple-input>
				</gp-form-col>
			</gp-form-row>

			<gp-form-row>
				<gp-form-col cols="12 6">
					<gp-simple-input label="Estado">
						<gp-brazil-state-selector name="state" required [disabled]="true"
							[ngModel]="address.state"></gp-brazil-state-selector>
					</gp-simple-input>
				</gp-form-col>

				<gp-form-col cols="12 6">
					<gp-simple-input label="Ponto de Referência">
						<input type="text" class="form-control" name="reference" [ngModel]="address.reference" disabled/>
					</gp-simple-input>
				</gp-form-col>
			</gp-form-row>
		</div>	
	</gp-card>
</div>
