<gp-alert [overlay]="true" #alert></gp-alert>
<spinner [overlay]="true" [show]="loadingPayment"></spinner>

<gp-modal title="Pagamento Manual Externo" width="750px" #externalManualPaymentModal>
	<form>
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="CPF/CNPJ">
					<gp-input-mask name="participantDocument" mask="00000000000000" [(ngModel)]="participant.document">
					</gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<gp-spinner-button text="Pesquisar" [loading]="loadingParticipant" loadingText="Pesquisando"
					bootstrapClass="primary" icon="search" (click)="searchParticipant()" marginTop="24px">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>

		<div *ngIf="hasParticipant">
			<gp-form-row>
				<gp-form-col cols="12 6 6">
					<gp-simple-input label="Nome do participante">
					<input class="form-control" readonly name="participantName" [ngModel]="participant.name" />
					</gp-simple-input>
				</gp-form-col>
				<gp-form-col cols="12 6 6">
					<gp-simple-input label="Saldo do participante">
					<input class="form-control" readonly name="participantBalance" [ngModel]="(participant.balance | amount)" />
					</gp-simple-input>
				</gp-form-col>
			</gp-form-row>

			<hr />
			<h4>Dados da Conta</h4>
			<gp-form-row>
				<gp-form-col cols="12 12 12">
					<gp-simple-input label="Código de barras" [required]="true">
						<gp-input-mask required name="barCode" mask="0000000000000000000000000000000000000000000000"
							[onlyInteger]="true" [thousandsSeparator]="false" [(ngModel)]="externalManualPayment.billDetails.barCode">
						</gp-input-mask>
					</gp-simple-input>
				</gp-form-col>
			</gp-form-row>

			<gp-form-row>
				<gp-form-col cols="12 6 6">
					<gp-simple-input label="Estabelecimento" [required]="true">
						<input class="form-control" required="true" name="assignor"
							[(ngModel)]="externalManualPayment.billDetails.assignor" />
					</gp-simple-input>
				</gp-form-col>
				<gp-form-col cols="12 6 6">
					<gp-simple-input label="Tipo da conta" [required]="true">
						<select class="form-control" name="serviceType" required
								[(ngModel)]="externalManualPayment.billDetails.serviceType">
							<option value="CONTACONCESSIONARIA">Conta Concessionária</option>
							<option value="FICHACOMPENSACAO">Ficha de Compensação</option>
						</select>
					</gp-simple-input>
				</gp-form-col>
			</gp-form-row>

			<gp-form-row>
				<gp-form-col cols="12 6 6">
					<gp-simple-input label="Valor da conta (R$)" [required]="true">
						<gp-input-mask required name="billingAmount" [onlyDecimal]="true" [decimais]="2" placeholder="0,00"
						[(ngModel)]="externalManualPayment.billDetails.billingAmount"></gp-input-mask>
					</gp-simple-input>
				</gp-form-col>
				<gp-datepicker cols="12 6 6" name="dueDate" label="Data de vencimento"
					[required]="true" [(ngModel)]="externalManualPayment.billDetails.dueDate">
				</gp-datepicker>
			</gp-form-row>

			<hr />
			<h4>Dados do Pagamento</h4>
			<gp-form-row>
				<gp-datepicker cols="12 6 6" name="scheduledDate" [disableSince]="tomorrow" label="Data de pagamento"
					[required]="true" [(ngModel)]="externalManualPayment.paymentDate">
				</gp-datepicker>

				<gp-form-col cols="12 6 6">
					<label>Selecione o arquivo</label>
					<gp-fileupload name="paymentReceipt" #gpFile></gp-fileupload>
				</gp-form-col>
			</gp-form-row>

			<hr />
			<h4>Dados do Solicitante</h4>
			<gp-form-row>
				<gp-form-col cols="12 6 6">
					<gp-simple-input label="Solicitado por" [required]="true">
						<input class="form-control" name="requesterName" [(ngModel)]="externalManualPayment.requesterName" />
					</gp-simple-input>
				</gp-form-col>

				<gp-datepicker cols="12 6 6" name="requesteDate" [disableSince]="tomorrow" label="Data da solicitação"
					[required]="true" [(ngModel)]="externalManualPayment.requestDate">
				</gp-datepicker>
			</gp-form-row>

			<gp-form-row>
				<gp-form-col cols="12 12 12">
					<gp-simple-input label="Motivo do pagamento" [required]="true">
						<textarea required class="form-control" rows="5" name="requestReason"
							[(ngModel)]="externalManualPayment.requestReason">
						</textarea>
					</gp-simple-input>
				</gp-form-col>
			</gp-form-row>

			<gp-form-row>
				<gp-form-col cols="12 12 12" [inputGroup]="false">
					<gp-spinner-button text="Registrar Pagamento" pull="right" [loading]="loading" [disabled]="disableButtons"
						loadingText="Salvando" [pink]="true" icon="send" (click)="sendExternalManualPayment()">
					</gp-spinner-button>
				</gp-form-col>
			</gp-form-row>
		</div>
	</form>
</gp-modal>
