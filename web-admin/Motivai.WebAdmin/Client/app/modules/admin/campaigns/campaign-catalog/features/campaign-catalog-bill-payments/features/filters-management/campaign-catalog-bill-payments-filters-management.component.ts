import { Component, OnInit, ViewChild } from '@angular/core';

import { TabsetComponent } from 'ng2-bootstrap';
import { Subscription } from 'rxjs';

import { CampaignStore } from '../../../../../campaign.store';
import { AuthStore } from '../../../../../../../../core/auth/auth.store';
import { RxjsHelpers } from '../../../../../../../../shared/helpers/rxjs-helpers';

import { CampaignCatalogBillPaymentsErrorListingComponent } from './campaign-catalog-bill-payments-error-listing/campaign-catalog-bill-payments-error-listing.component';
import { CampaignCatalogBillPaymentsErrorEditorComponent } from './campaign-catalog-bill-payments-error-editor/campaign-catalog-bill-payments-error-editor.component';
import { CampaignCatalogBillPaymentsActiveFiltersListingComponent } from './campaign-catalog-bill-payments-active-filters-listing/campaign-catalog-bill-payments-active-filters-listing.component';

@Component({
	selector: 'campaign-catalog-bill-payments-filters-management',
	templateUrl: './campaign-catalog-bill-payments-filters-management.component.html',
})
export class CampaignCatalogBillPaymentsFiltersManagementComponent implements OnInit {
	@ViewChild('tabs') tabs: TabsetComponent;
	@ViewChild('listing') listing: CampaignCatalogBillPaymentsErrorListingComponent;
	@ViewChild('editor') editor: CampaignCatalogBillPaymentsErrorEditorComponent;
	@ViewChild('activeFiltersListing') activeFiltersListing: CampaignCatalogBillPaymentsActiveFiltersListingComponent;

	private _campaign$: Subscription;
	campaignId: string;

	constructor(private authStore: AuthStore, private _campaignStore: CampaignStore) { }

	ngOnInit() {
		this._campaign$ = this._campaignStore.asObservable
			.subscribe(id => {
				this.campaignId = id;
			});
		this.disableEditTab();
	}

	ngOnDestroy() {
		RxjsHelpers.unsubscribe(this._campaign$);
	}

	clear() {
		this.listing.clear();
		this.editor.clear();
		this.activeFiltersListing.clear();
		this.disableEditTab();
	}

	editRegister(register: any) {
		if (register) {
			this.tabs.tabs[1].disabled = false;
			this.tabs.tabs[1].active = true;
			this.editor.clickedEditRegister(register);
		}
	}

	private disableEditTab() {
		this.tabs.tabs[1].disabled = true;
	}

	get canEditOrUpdate() {
		return true;
	}
}
