<gp-modal #modal [title]="'Precificar produto - ' + item.skuCode" width="1000px" marginLeft="20%" (onClose)="close()">
	<form #priceForm="ngForm" novalidate>
		<gp-form-row>
			<gp-form-col cols="12 4 4">
				<div class="form-group">
					<label>Valor Unitário (R$)*</label>
					<div class="input-group">
						<span class="input-group-addon">R$</span>
						<gp-input-mask required name="unitPrice" [onlyDecimal]="true" [decimais]="2" placeholder="0,00"
							[(ngModel)]="item.unitPrice">
						</gp-input-mask>
					</div>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 4 4" *ngIf="!isB2B">
				<div class="form-group">
					<label>Valor Frete (R$)*</label>
					<div class="input-group">
						<span class="input-group-addon">R$</span>
						<gp-input-mask [required]="!isB2B" name="shippingCost" [onlyDecimal]="true" [decimais]="2" placeholder="0,00"
							[(ngModel)]="item.shippingCost">
						</gp-input-mask>
					</div>
				</div>
			</gp-form-col>
			<gp-form-col cols="12 4 4">
				<gp-simple-input label="Quantidade" [required]="true" errorMessage="Quantidade é obrigatório">
					<gp-input-mask required name="quantity" [onlyInteger]="true" placeholder="1"
						[(ngModel)]="item.quantity">
					</gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<div class="form-group">
					<label>Observações</label>
					<textarea style="height: 100px" name="observations" class="form-control" [(ngModel)]="item.observations"></textarea>
				</div>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-spinner-button type="button" [actionSecondary]="true" text="Salvar" [loading]="loading" (click)="priceSubmit(false)" [disabled]="!priceForm.valid" 	marginRight="10px">
				</gp-spinner-button>
				<!-- <gp-spinner-button type="button" [pink]="true" icon="send" text="Salvar e Aprovar" [loading]="loading" (click)="priceSubmit(true)"></gp-spinner-button> -->
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-alert #alert></gp-alert>
			</gp-form-col>
		</gp-form-row>
	</form>
</gp-modal>
