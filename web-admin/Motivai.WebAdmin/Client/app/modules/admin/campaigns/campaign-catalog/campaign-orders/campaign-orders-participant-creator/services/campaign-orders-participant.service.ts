import { Observable } from 'rxjs/Observable';
import { Injectable } from "@angular/core";

import { ApiService } from "../../../../../../../core/api/api.service";

import { SearchField } from "../models/searchField";

@Injectable()
export class CampaignOrdersParticipantService {
    constructor(private _api: ApiService) {}

    getParticipantInfo(campaignId: string, searchByField: SearchField, searchValue: string): Observable<any> {
        const params = {searchByField: searchByField, searchValue: searchValue}
        return this._api.get(`/api/campaigns/${campaignId}/orders/participant/info`, params);
    }

    getParticipantBalance(campaignId: string, userId: string): Observable<any> {
        return this._api.get(`/api/campaigns/${campaignId}/orders/participant/${userId}/balance`);
    }
}
