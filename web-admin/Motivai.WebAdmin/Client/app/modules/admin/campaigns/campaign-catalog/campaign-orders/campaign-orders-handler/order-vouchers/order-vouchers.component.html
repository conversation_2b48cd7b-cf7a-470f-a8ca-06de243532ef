<gp-modal #modal title="Vales Virtuais Gerados" width="1000px" marginLeft="20%">
	<gp-alert #alert [overlay]="true"></gp-alert>

	<gp-form-row *ngFor="let voucher of vouchers">
		<gp-form-col cols="12 12 12">
			<p *ngIf="voucher.createDate"><strong>Data da Geração:</strong> {{ voucher.createDate | datetimezone }}</p>
			<p><strong>Nome:</strong> {{ voucher.name }}</p>
			<p><strong>Enviado:</strong> {{ voucher.notificated ? 'Sim' : 'Não' }}</p>
			<p *ngIf="voucher.notificationDate"><strong>Data de Envio:</strong> {{ voucher.notificationDate | datetimezone }}</p>
			<hr />
		</gp-form-col>
	</gp-form-row>

	<gp-form-row>
		<gp-form-col cols="12" [inputGroup]="false">
			<gp-spinner-button [actionPrimary]="true" text="Enviar Vales" pull="right" icon="send"
				[loading]="loading" loadingText="Aguarde" (click)="notifyOrderVouchers()">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>

	<spinner [overlay]="true" [show]="loading"></spinner>
</gp-modal>
