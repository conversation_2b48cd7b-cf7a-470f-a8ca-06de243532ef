import { Component, ViewChild } from '@angular/core';
import { AuthStore } from '../../../../../../../../../../core/auth/auth.store';

import { GpAlertComponent } from '../../../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpModalComponent } from '../../../../../../../../../../shared/components/gp-modal/gp-modal.component';
import { CampaignCatalogBillPaymentsFiltersManagementService } from '../../services/campaing-catalog-bill-payments-filters-management.service';

@Component({
	selector: 'campaign-catalog-bill-payments-adding-filters',
	templateUrl: './campaign-catalog-bill-payments-adding-filters.component.html'
})
export class CampaignCatalogBillPaymentsAddingFiltersComponent {
	@ViewChild('modal') modal: GpModalComponent;
	@ViewChild('alert') alert: GpAlertComponent;

	billsPaymentConsultsError: any = {
		partners: {},
		billDetails: {},
		userDetails: {}
	};

	filters: any = {};

	loading: boolean = false;
	constructor(private authStore: AuthStore, 
		private billPaymentsFiltersManagementService: CampaignCatalogBillPaymentsFiltersManagementService
	) { }

	setBillsPaymentConsultsError(bill: any) {
		this.billsPaymentConsultsError = bill || this.reset();
	}

	openModal() {
		this.modal.show();
	}

	hide() {
		this.modal.hide();
	}

	get canAddFilters() {
		return this.hasPermissionToAddFilters() && 
			(this.billsPaymentConsultsError && (this.billsPaymentConsultsError.status === "CREATED" ||
				this.billsPaymentConsultsError.status === "REJECTED"
			)
		);
	}

	private hasPermissionToAddFilters(): boolean {
		return this.authStore.role.PERMISSION_CAMPAIGNS_CATALOG_BILLS_PAYMENT_ADD_OR_REMOVE_FILTERS;
	}
	
	onAddFilters() {
		this.loading = true;
		this.buildFilters();
		this.billPaymentsFiltersManagementService.addFiltersToAllowedList(this.billsPaymentConsultsError.campaignId, this.filters)
			.subscribe(resp => {
				this.loading = false;
				if (resp) {
					this.billsPaymentConsultsError.status = "HANDLED";
					this.alert.showSuccess("Filtros adicionados com sucesso");
					this.hide();
				} else {
					this.alert.showWarning("Não foi possível adicionadar os filtros. Tente novamente");
				}
			},
			err => {
				this.loading = false;
				this.alert.handleAndShowError(err);
			}
		);
	}

	private buildFilters() {
		this.filters.billConsultErrorId = this.billsPaymentConsultsError.id;
	}

	private reset() {
		this.filters = {};
		this.billsPaymentConsultsError = {
			partners: {},
			billDetails: {},
			userDetails: {}
		};
		this.loading = false;
	}
}
