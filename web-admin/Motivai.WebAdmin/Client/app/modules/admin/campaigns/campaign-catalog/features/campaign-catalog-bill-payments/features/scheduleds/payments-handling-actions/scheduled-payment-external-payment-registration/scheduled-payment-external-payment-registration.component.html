<gp-alert [overlay]="true" #alert></gp-alert>

<gp-modal title="Pagamento manual" #externalPaymentModal>
  <div class="fixed-height">
    <gp-form-row>
      <gp-datepicker cols="12 6 6" name="scheduledDate" [disableSince]="tomorrow" label="Data de pagamento"
        [required]="true" [(ngModel)]="scheduledPaymentExternalRegistration.paymentDate"></gp-datepicker>

      <gp-form-col cols="12 6 6">
        <label>Efetuar débito no saldo</label>
        <div>
          <label class="switch switch-lg">
            <input type="checkbox" id="chargeParticipant" name="chargeParticipant"
              [(ngModel)]="scheduledPaymentExternalRegistration.chargeParticipant" />
            <span></span>
          </label>
        </div>
      </gp-form-col>
    </gp-form-row>
    <gp-form-row>
      <gp-form-col cols="12 6 6">
        <label>Selecione o arquivo</label>
        <gp-fileupload name="paymentReceipt" [path]="uploadPath" #paymentReceiptUploader></gp-fileupload>
      </gp-form-col>
    </gp-form-row>
    <gp-form-row>
      <gp-form-col cols="12 12 12">
        <gp-spinner-button type="button" [pink]="true" icon="send" text="Registrar pagamento manual"
          [loading]="loadingProcessing" pull="right" (click)="validateAndUpload()">
        </gp-spinner-button>
      </gp-form-col>
    </gp-form-row>
  </div>
</gp-modal>