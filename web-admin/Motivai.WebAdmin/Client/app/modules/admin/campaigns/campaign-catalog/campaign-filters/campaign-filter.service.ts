import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { ApiService } from '../../../../../core/api/api.service';

@Injectable()
export class CampaignFilterService {
	constructor(private _api: ApiService) { }

	getFilters(campaignId: string, params: any): Observable<any> {
		return this._api.get(`/api/campaigns/${campaignId}/filters`);
	}

	getFiltersByValue(campaignId: string): Observable<any> {

		return this._api.get(`/api/campaigns/${campaignId}/filters/byvalue`);
	}

	getFiltersByManufactures(campaignId: string, params: any): Observable<any> {
		const parameters: any = {};

		if (params.skip) {
		  parameters.skip = params.skip;
		}

		if (params.limit) {
		  parameters.limit = params.limit;
		}

		return this._api.get(`/api/campaigns/${campaignId}/filters/manufactures`, params);
	}

	getFiltersByPartners(campaignId: string, params: any): Observable<any> {
		const parameters: any = {};

		if (params.skip) {
		  parameters.skip = params.skip;
		}

		if (params.limit) {
		  parameters.limit = params.limit;
		}

		return this._api.get(`/api/campaigns/${campaignId}/filters/partners`, params);
	}

	getFiltersByBlockRules(campaignId: string, params: any): Observable<any> {
		const parameters: any = {};

		if (params.skip) {
		  parameters.skip = params.skip;
		}

		if (params.limit) {
		  parameters.limit = params.limit;
		}

		return this._api.get(`/api/campaigns/${campaignId}/filters/block/rules`, params);
	}

	getFiltersByAllowRules(campaignId: string, params: any): Observable<any> {
		const parameters: any = {};

		if (params.skip) {
		  parameters.skip = params.skip;
		}

		if (params.limit) {
		  parameters.limit = params.limit;
		}

		return this._api.get(`/api/campaigns/${campaignId}/filters/allow/rules`, params);
	}

	getFiltersByPartnerRules(campaignId: string, params: any): Observable<any> {
		const parameters: any = {};

		if (params.skip) {
		  parameters.skip = params.skip;
		}

		if (params.limit) {
		  parameters.limit = params.limit;
		}

		return this._api.get(`/api/campaigns/${campaignId}/filters/partner/rules`, params);
	}


	getFiltersBySku(campaignId: string, params: any): Observable<any> {
		const parameters: any = {};

		if (params.sku) {
		  parameters.sku = params.sku;
		}

		if (params.productName) {
		  parameters.productName = params.productName;
		}

		if (params.skip) {
		  parameters.skip = params.skip;
		}

		if (params.limit) {
		  parameters.limit = params.limit;
		}

		return this._api.get(`/api/campaigns/${campaignId}/filters/sku`, params);
	}

	getFiltersByParticipantsGroups(campaignId: string, params: any): Observable<any> {
		const parameters: any = {};

		if (params.skip) {
		  parameters.skip = params.skip;
		}

		if (params.limit) {
		  parameters.limit = params.limit;
		}

		return this._api.get(`/api/campaigns/${campaignId}/filters/participants/groups`, params);
	}


	saveValueFilter(campaignId: string, valueFilter: any): Observable<boolean> {
		return this._api.put(`/api/campaigns/${campaignId}/filters/byvalue`, valueFilter);
	}

	removeValueFilter(campaignId: string): Observable<boolean> {
		return this._api.delete(`/api/campaigns/${campaignId}/filters/byvalue`);
	}

	addManufacturerFilter(campaignId: string, filter: any): Observable<boolean> {
		return this._api.post(`/api/campaigns/${campaignId}/filters/manufacturers`, filter);
	}

	removeManufacturerFilter(campaignId: string, filterId: string): Observable<boolean> {
		return this._api.delete(`/api/campaigns/${campaignId}/filters/manufacturers/${filterId}`);
	}

	addPartnerFilter(campaignId: string, partnerId: string): Observable<boolean> {
		return this._api.post(`/api/campaigns/${campaignId}/filters/partners`, { partnerId: partnerId });
	}

	removePartnerFilter(campaignId: string, filterId: string): Observable<boolean> {
		return this._api.delete(`/api/campaigns/${campaignId}/filters/partners/${filterId}`);
	}

	addAllowRuleFilter(campaignId: string, filter: any): Observable<boolean> {
		return this._api.post(`/api/campaigns/${campaignId}/filters/allow/rules`, filter);
	}

	removeAllowRuleFilter(campaignId: string, filterId: string): Observable<boolean> {
		return this._api.delete(`/api/campaigns/${campaignId}/filters/allow/rules/${filterId}`);
	}

	addBlockRuleFilter(campaignId: string, filter: any): Observable<boolean> {
		return this._api.post(`/api/campaigns/${campaignId}/filters/block/rules`, filter);
	}

	removeBlockRuleFilter(campaignId: string, filterId: string): Observable<boolean> {
		return this._api.delete(`/api/campaigns/${campaignId}/filters/block/rules/${filterId}`);
	}

	addPartnerSkuFilter(campaignId: string, filter: any): Observable<boolean> {
		return this._api.post(`/api/campaigns/${campaignId}/filters/skus`, filter);
	}

	removePartnerSkuFilter(campaignId: string, filterId: string): Observable<boolean> {
		return this._api.delete(`/api/campaigns/${campaignId}/filters/skus/${filterId}`);
	}

	addPartnerSkuToWhitelist(campaignId: string, filter: any): Observable<boolean> {
		return this._api.post(`/api/campaigns/${campaignId}/filters/whitelist/skus`, filter);
	}

	removePartnerSkuToWhitelist(campaignId: string, filterId: string): Observable<boolean> {
		return this._api.delete(`/api/campaigns/${campaignId}/filters/whitelist/skus/${filterId}`);
	}

	saveParticipantGroupWhitelist(campaignId: string, participantGroupWhitelist: any): Observable<any> {
		return this._api.post(`/api/campaigns/${campaignId}/filters/participantsgroups/whitelist`, participantGroupWhitelist);
	}
}
