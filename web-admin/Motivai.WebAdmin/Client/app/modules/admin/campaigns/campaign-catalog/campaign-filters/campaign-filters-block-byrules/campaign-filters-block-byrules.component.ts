import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from "@angular/core";
import { CampaignStore } from "../../../campaign.store";
import { CampaignFilterService } from "../campaign-filter.service";
import { GpAlertComponent } from "../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component";
import { Subscription } from "rxjs";
import { RxjsHelpers } from '../../../../../../shared/helpers/rxjs-helpers';
import { Item } from "../../../../../../shared/models/item";

@Component({
    selector: 'campaign-filters-block-byrules',
    templateUrl: 'campaign-filters-block-byrules.component.html'
})
export class CampaignFiltersBlockByRulesComponent implements OnInit, OnDestroy {
    @ViewChild('alert') alert: GpAlertComponent;

    private _campaign$: Subscription;
    loading: boolean = false;

    campaignPartners: Array<Item> = [];

    ruleFilterModel = {
        departmentId: '',
        categoryId: '',
        subcategoryId: ''
    };

    byBlockRules: any = [];

    params: any = {
        skip: 0,
        limit: 20
    };

    get campaignId() {
        return this._campaignStore.id;
    }

    ngOnInit() {
        this._campaign$ = this._campaignStore.asObservable
            .subscribe(id => {
                if (id)
                    this.loadFiltersByBlockRules();
            });
    }


    ngOnDestroy() {
        RxjsHelpers.unsubscribe(this._campaign$);
    }

    constructor(private _campaignStore: CampaignStore, private _filterService: CampaignFilterService) { }

    loadFiltersByBlockRules() {
        this.loading = true;
        this._filterService.getFiltersByBlockRules(this.campaignId, this.params)
            .subscribe(
                filters => {
                    this.byBlockRules = filters;
                    this.ruleFilterModel = {
                        departmentId: '',
                        categoryId: '',
                        subcategoryId: ''
                      };
                    this.loading = false;
                },
                err => {
                    this.loading = false;
                    this.handleError(err);
                }
            );
    }

    addBlockRuleFilter(event) {
        event.preventDefault();
        this.loading = true;
        this._filterService.addBlockRuleFilter(this.campaignId, this.ruleFilterModel)
            .subscribe(
                result => {
                    if (result) {
                        this.alert.showSuccess('Filtro por regra adicionado com sucesso.');
                        this.ruleFilterModel = {
                            departmentId: '',
                            categoryId: '',
                            subcategoryId: ''
                        };
                        if (!this.byBlockRules)
                            this.byBlockRules = [];
                        this.byBlockRules.push(result);
                        this.loadFiltersByBlockRules();
                    } else {
                        this.alert.showWarning('Não foi possível adicionar o filtro, por favor, tente novamente.');
                    }
                    this.loading = false;
                },
                err => {
                    this.loading = false;
                    this.handleError(err);
                }
            );
    }

    removeBlockRuleFilter(filter: any) {
        if (!confirm(`Deseja excluir o filtro por regra?`)) return;
        this.loading = true;
        this._filterService.removeBlockRuleFilter(this.campaignId, filter.id)
          .subscribe(
            result => {
              if (result) {
                if (this.byBlockRules) {
                  this.byBlockRules = this.byBlockRules.filter(r => r.id != filter.id);
                }
                this.alert.showSuccess('Filtro por regra removido com sucesso.');
              } else {
                this.alert.showWarning('Não foi possível remover o filtro, por favor, tente novamente.');
              }
              this.loading = false;
            },
            err =>  {
                this.loading = false;
                this.handleError(err);
            },
          );
      }

    handleError(err) {
        this.alert.handleAndShowError(err);
    }

    onPageChanged(event) {
        if (event) {
            this.params.skip = event.skip;
            this.params.limit = event.limit;
            this.loadFiltersByBlockRules();
        }
    }
}