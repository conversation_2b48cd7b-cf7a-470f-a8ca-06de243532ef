<gp-alert [overlay]="true" #alert></gp-alert>

<gp-modal title="Estorndo do Pedido" width="750px" #orderRefundModal>
  <form>
    <div>
      <gp-form-row>
        <gp-form-col cols="12 6 6">
          <gp-simple-input label="Nome do participante">
            <input class="form-control" readonly name="participantName" [ngModel]="participantInfo.name" />
          </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 6 6">
          <gp-simple-input label="CPF/CNPJ">
            <input class="form-control" readonly name="participantDocument"
              [ngModel]="(participantInfo.userDocument)" />
          </gp-simple-input>
        </gp-form-col>
      </gp-form-row>

      <gp-form-row>
        <gp-form-col cols="12 6 6">
          <gp-simple-input label="Número do pedido">
            <input class="form-control" readonly name="protocolNumber" [ngModel]="catalogExtraServiceOrder.protocol" />
          </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 6 6" *ngIf="catalogExtraServiceOrder.deteiledCost">
          <gp-simple-input label="Valor do Pedido">
            <input class="form-control" readonly name="orderValue"
              [ngModel]="catalogExtraServiceOrder.deteiledCost.participantCost.currency | amount" />
          </gp-simple-input>
        </gp-form-col>
      </gp-form-row>

      <hr />

      <gp-form-row>
        <gp-form-col cols="12 12 12">
          <gp-simple-input label="Motivo do estorno" [required]="true">
            <textarea required class="form-control" rows="5" name="requestReason" [(ngModel)]="refundReason">
						</textarea>
          </gp-simple-input>
        </gp-form-col>
      </gp-form-row>

      <gp-form-row>
        <gp-form-col cols="12 12 12" [inputGroup]="false">
          <gp-spinner-button text="Efetuar Estorno" pull="right" [loading]="processingRefund"
            [disabled]="disableButtons" loadingText="Salvando" [pink]="true" icon="send" (click)="sendRefundOrder()">
          </gp-spinner-button>
        </gp-form-col>
      </gp-form-row>
    </div>
  </form>
</gp-modal>