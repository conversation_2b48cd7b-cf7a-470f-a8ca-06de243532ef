<form #formRule="ngForm" (ngSubmit)="addBlockRuleFilter($event)" novalidate>
    <div class="row">
        <div grid="12 6 3" [group]="true">
            <label for="department">Departamento</label>
            <gp-categories-select id="department" name="department" [active]="true" [level]="1" required
                [(ngModel)]="ruleFilterModel.departmentId">
            </gp-categories-select>
        </div>
        <div grid="12 6 3" [group]="true">
            <label for="category">Categoria</label>
            <gp-categories-select id="category" name="category" [active]="true" [level]="2" emptyMessage="Sem categorias"
                [parentId]="ruleFilterModel.departmentId" [disabled]="!ruleFilterModel.departmentId" [(ngModel)]="ruleFilterModel.categoryId">
            </gp-categories-select>
        </div>
        <div grid="12 6 3" [group]="true">
            <label for="category">Subcategoria</label>
            <gp-categories-select id="category" name="category" [active]="true" [level]="3" emptyMessage="Sem subcategorias"
                [parentId]="ruleFilterModel.categoryId" [disabled]="!ruleFilterModel.categoryId" [(ngModel)]="ruleFilterModel.subcategoryId">
            </gp-categories-select>
        </div>
        <div grid="12 6 3" class="top-p2">
            <gp-spinner-button type="submit" text="Adicionar filtro" bootstrapClass="success" [disabled]="!formRule.valid || disableButtons"
                [pink]="true" icon="plus" pull="right" [loading]="sending" loadingText="Adicionando"></gp-spinner-button>
        </div>
    </div>
</form>
<gp-form-row>
    <gp-form-col cols="12 12 12">
        <gp-grid name="byBlockRules" [loading]="loading" [rows]="byBlockRules"
            [columns]="['Departamento', 'Categoria', 'Subcategoria']" [fields]="['departmentName', 'categoryName', 'subcategoryName']"
            [showActive]="false" [showPagination]="true" [showTotalPages]="false" [showEdit]="false" pageSize="20" [showDelete]="true" (onDelete)="removeBlockRuleFilter($event)" (onPageChanged)="onPageChanged($event)">
        </gp-grid>
    </gp-form-col>
</gp-form-row>
<spinner [overlay]="true" [show]="loading"></spinner>
<gp-alert [overlay]="true" #alert></gp-alert>