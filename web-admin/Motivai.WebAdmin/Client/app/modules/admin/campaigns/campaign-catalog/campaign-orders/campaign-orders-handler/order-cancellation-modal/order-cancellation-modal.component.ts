import { Sku } from '../../../../../products/products.model';
import { Component, ViewChild, EventEmitter, Output, Input } from '@angular/core';
import { GpModalComponent } from '../../../../../../../shared/components/gp-modal/gp-modal.component';
import { CampaignOrdersHandlerService } from '../campaign-orders-handler.service';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';

@Component({
  selector: 'order-cancellation-modal',
  templateUrl: 'order-cancellation-modal.component.html'
})
export class OrderCancellationModalComponent {
  @ViewChild('modal') modal: GpModalComponent;
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @Output('onClose') onClose: EventEmitter<any> = new EventEmitter();

  private cancellationMode: string = '';
  private MASTER_ORDER = 'MASTER_ORDER';
  private PARTNER_ORDER = 'PARTNER_ORDER';
  private SINGLE_ITEM = 'SINGLE_ITEM';
  private MULTIPLE_ITEMS = 'MULTIPLE_ITEMS';
  private CONCILIATE_ORDER = 'CONCILIATE_ORDER';

  private campaignId: string;
  private itemGrouperId: string;
  private order: any;
  private childOrder: any = {};
  private item: any = {};
  private items: any[] = [];
  cancellationReason = '';

  canCancel: boolean = true;
  sending: boolean = false;

  constructor(private _orderService: CampaignOrdersHandlerService) {}

  get modalTitle() {
    if (this.isSingleItem()) {
      return 'Cancelamento do item ' + this.item.skuCode;
    } else if (this.isMultipleItem()) {
      return `Cancelamento de itens do parceiro ${this.childOrder.partnerName}`;
    } else if (this.isPartnerOrder()) {
      return 'Cancelamento do pedido do parceiro ' + this.childOrder.partnerName;
    } else if (this.isMasterOrder()) {
      return 'Cancelamento do pedido ' + this.order.internalOrderNumber;
    } else if (this.isConciliateOrder()) {
      return 'Conciliar Items do Pedido ' + this.order.internalOrderNumber;
    }
    return 'Cancelamento';
  }

  private isMasterOrder = () => this.cancellationMode === this.MASTER_ORDER;
  private isPartnerOrder = () => this.cancellationMode === this.PARTNER_ORDER;
  private isSingleItem = () => this.cancellationMode === this.SINGLE_ITEM;
  private isMultipleItem = () => this.cancellationMode === this.MULTIPLE_ITEMS;
  private isConciliateOrder = () => this.cancellationMode === this.CONCILIATE_ORDER;

  private handleError(err: any) {
    this.gpAlert.showError(err);
    this.sending = false;
  }

  private showModal() {
    this.modal.show();
  }

  resetForm() {
    if (this.gpAlert)
      this.gpAlert.clear();
    this.canCancel = true;
    this.cancellationReason = null;
  }

  openModalToCancelMasterOrder(campaignId: string, order: any) {
    this.cancellationMode = this.MASTER_ORDER;
    this.resetForm();
    this.campaignId = campaignId;
    this.order = order;
    this.childOrder = null;
    this.item = null;
    this.showModal();
  }

  openModalToCancelChildOrder(campaignId: string, order: any, childOrder: any) {
    this.cancellationMode = this.PARTNER_ORDER;
    this.resetForm();
    this.campaignId = campaignId;
    this.order = order;
    this.childOrder = childOrder;
    this.item = null;
    this.showModal();
  }

  openModalToCancelMultipleItems(campaignId: string, order: any, itemGrouperId: string, childOrder: any, items: any[]) {
    this.cancellationMode = this.MULTIPLE_ITEMS;
    this.resetForm();
    this.campaignId = campaignId;
    this.order = order;
    this.itemGrouperId = itemGrouperId;
    this.items = items;
    this.childOrder = childOrder;
    this.showModal();
  }

  close() {
    this.onClose.emit(this.item);
  }

  openModalToCancelItem(campaignId: string, order: any, childOrder: any, item: any | null) {
    this.cancellationMode = this.SINGLE_ITEM;
    this.resetForm();
    this.campaignId = campaignId;
    this.order = order;
    this.childOrder = childOrder;
    this.item = item;
    this.showModal();
  }

  openModalToConciliate(campaignId: string, order: any, childOrder: any) {
    this.cancellationMode = this.CONCILIATE_ORDER;
    this.resetForm();
    this.campaignId = campaignId;
    this.order = order;
    this.itemGrouperId = childOrder.itemGrouperId;
    this.childOrder = childOrder;
    this.item = null;
    this.showModal();
  }

  cancel() {
    this.gpAlert.clear();
    if (this.isSingleItem()) {
      this.cancelItem();
    } else if (this.isMultipleItem()) {
      this.cancelMultipleItems();
    } else if (this.isPartnerOrder()) {
      this.cancelChildOrder();
    } else if (this.isMasterOrder()) {
      this.cancelMasterOrder();
    }
  }

  private cancelMasterOrder() {
    if (!confirm(`Confirma o cancelamento do pedido ${this.order.internalOrderNumber}?`)) {
      return;
    }
    this.sending = true;
    this._orderService.cancelMasterOrder(this.campaignId, this.order.id, this.cancellationReason)
      .subscribe(
        wasCanceled => {
          this.sending = false;
          if (wasCanceled) {
            this.gpAlert.showSuccess('Pedido cancelado com sucesso.');
            this.canCancel = false;
          } else {
            this.gpAlert.showWarning('Não foi possível cancelar o pedido, por favor, tente novamente.');
          }
        },
        err => this.handleError(err)
      );
  }

  private cancelChildOrder() {
    if (!confirm(`Confirma o cancelamento do pedido do parceiro ${this.childOrder.partnerName}?`)) {
      return;
    }
    this.sending = true;
    this._orderService.cancelChildOrder(this.campaignId, this.order.id, this.childOrder.itemGrouperId, this.cancellationReason)
      .subscribe(
        wasCanceled => {
          this.sending = false;
          if (wasCanceled) {
            this.gpAlert.showSuccess('Pedido parceiro cancelado com sucesso.');
            this.canCancel = false;
          } else {
            this.gpAlert.showWarning('Não foi possível cancelar o pedido parceiro, por favor, tente novamente.');
          }
        },
        err => this.handleError(err)
      );
  }

  private cancelItem() {
    if (!confirm(`Confirma o cancelamento do item ${this.item.skuCode} - ${this.item.name}?`)) {
      return;
    }
    this.sending = true;
    this._orderService.cancelItem(this.campaignId, this.order.id, this.childOrder.itemGrouperId, this.item.skuId, this.cancellationReason)
      .subscribe(
        wasCanceled => {
          this.sending = false;
          if (wasCanceled) {
            this.gpAlert.showSuccess('Item cancelado com sucesso.');
            this.canCancel = false;
          } else {
            this.gpAlert.showWarning('Não foi possível cancelar o item, por favor, tente novamente.');
          }
        },
        err => this.handleError(err)
      );
  }

  private cancelMultipleItems() {
    if (!confirm(`Confirma o cancelamento dos itens selecionados?`)) return;
    this.sending = true;
    const items = { reason: this.cancellationReason, skusIds: this.items.map(x => x.skuId) };
    this._orderService.cancelMultipleItems(this.campaignId, this.order.id, this.itemGrouperId, items).subscribe(
      canceled => {
        if (canceled) {
          this.gpAlert.showSuccess('Itens cancelados com sucesso');
          this.canCancel = false;
        } else {
          this.gpAlert.showWarning('Não foi possível cancelar um ou mais itens do pedido. Tente novamente.');
        }
        this.sending = false;
      },
      err => this.handleError(err)
    );
  }
}
