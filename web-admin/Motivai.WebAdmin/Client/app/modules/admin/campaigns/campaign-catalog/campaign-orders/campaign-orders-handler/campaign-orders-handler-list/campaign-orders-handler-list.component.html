<gp-alert #gpAlert [overlay]="true"></gp-alert>
<gp-card title="Pesquisa" [first]="true">
	<gp-form-row>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Tipo de Processo">
				<select class="form-control" name="processType" [(ngModel)]="search.processType">
					<option value="">Todos</option>
					<option value="Online">Online</option>
					<option value="Offline">Offline</option>
				</select>
			</gp-simple-input>
		</gp-form-col>
		<gp-datepicker cols="12 4 4 4" label="Data do pedido (de)" name="orderDateFrom" [(ngModel)]="search.orderDateFrom"></gp-datepicker>
		<gp-datepicker cols="12 4 4 4" label="Data do pedido (até)" name="orderDateTo" [(ngModel)]="search.orderDateTo"></gp-datepicker>
	</gp-form-row>

	<gp-form-row>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Status do Pedido">
				<select class="form-control" name="status" [(ngModel)]="search.status">
					<option value=''>Todos</option>
					<option value="Analysis">Em análise</option>
					<option value="Approved">Aprovado</option>
					<option value="Debted">Faturado</option>
					<option value="Refused">Reprovado</option>
				</select>
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="Número do Pedido">
				<input type="text" class="form-control" name="orderNumber" [(ngModel)]="search.orderNumber" />
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 4 4">
			<gp-simple-input label="CPF/CNPJ do Participante">
				<gp-input-mask name="document" mask="00000000000000" [(ngModel)]="search.document">
				</gp-input-mask>
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 4 4">
			<gp-spinner-button type="button" [search]="true" text="Consultar pedidos"
				[loading]="loading" (click)="findOrders()">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card title="Pedidos encontrados">
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<gp-grid name="ordersGrid"
				[rows]="orders"
				[columns]="['Data/Hora do Pedido', 'Número do Pedido', 'Status', 'SLA']"
				[fields]="['formattedCreationDate', 'internalOrderNumber', 'formattedStatus', 'slaPercentDone|sla']"
				[showActive]="false" [showPagination]="true" [showTotalPages]="false" [pageSize]="limit"
				[showEdit]="true" [showDelete]="false"
				[loading]="loading"
				(onEdit)="prepareEdit($event)" (onPageChanged)="pageChanged($event)">
			</gp-grid>
		</gp-form-col>
	</gp-form-row>
</gp-card>
