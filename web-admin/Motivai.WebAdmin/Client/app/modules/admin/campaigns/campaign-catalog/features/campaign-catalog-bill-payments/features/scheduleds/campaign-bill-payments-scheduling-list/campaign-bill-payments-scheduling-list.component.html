<gp-alert [overlay]="true" #alert></gp-alert>

<gp-card [first]="true" title="Filtros">
	<form>
		<gp-form-row>
			<gp-datepicker name="from" cols="12 4" [required]="false" label="De" name="from" [(ngModel)]="params.from">
			</gp-datepicker>
			<gp-datepicker name="to" cols="12 4" [required]="false" label="Até" name="to" [(ngModel)]="params.to">
			</gp-datepicker>
			<gp-form-col cols="12 4">
				<gp-simple-input label="Status">
					<select name="status" class="form-control" [(ngModel)]="params.status">
						<option value="">Todos</option>
						<option value="SCHEDULED">Agendado</option>
						<option value="PAID">Pago</option>
						<option value="CANCELED">Cancelado</option>
						<option value="ERROR">Ocorreu um erro</option>
					</select>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 4">
				<gp-simple-input label="CPF/CNPJ">
					<gp-input-mask name="userDocument" name="document" mask="00000000000000"
						[(ngModel)]="params.userDocument"></gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 4">
				<gp-simple-input label="Código do boleto">
					<input type="text" name="barcode" class="form-control" [(ngModel)]="params.barcode" />
				</gp-simple-input>
			</gp-form-col>

			<gp-form-col cols="12 4" [inputGroup]="false" additionalClasses="top-p27">
				<gp-spinner-button pull="right" text="Pagamento Externo" bootstrapClass="danger"
					icon="money" (click)="showExternalManualPayment()">
				</gp-spinner-button>

				<gp-spinner-button marginRight="1em" pull="right" text="Pesquisar" [loading]="loading" loadingText="Aguarde"
					bootstrapClass="primary" icon="search" (click)="resetThenSearchRecords()">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</form>
</gp-card>

<gp-card>
	<gp-form-row>
		<gp-form-col cols="12">
			<gp-grid #gpGrid name="paymentsScheduled" [rows]="schedulePayments"
				[columns]="['CPF/CNPJ', 'Data de criação', 'Data agendada','Código de barras', 'Valor', 'Data de vencimento', 'Status']"
				[fields]="['userDocument', 'formattedCreateDate', 'formattedScheduledPaymentDate', 'billDetails.barCode', 'billDetails.billingAmount', 'formattedDueDate','formattedStatus']"
				[loading]="loading" [showActive]="false" [showEdit]="true" [showDelete]="false"
				(onEdit)="showDetails($event)" [showPagination]="true" [showTotalPages]="false"
				(onPageChanged)="onPageChanged($event)"></gp-grid>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card>
	<gp-form-row>
		<gp-form-col cols="12" [inputGroup]="false">
			<gp-spinner-button text="Exportar Pagamentos Agendados" pull="right" icon="download" [loading]="loading"
				[disabled]="disableAction" loadingText="Aguarde" bootstrapClass="success"
				(click)="exportScheduledPaymentsFile()"></gp-spinner-button>

			<gp-spinner-button text="Efetuar os Pagamentos Agendados" [pink]="true" icon="send" pull="right"
				marginRight="1em" [loading]="processing" [disabled]="disableAction" loadingText="Aguarde"
				(click)="processScheduledPayments()" *ngIf="canProcessPayments"></gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<div>
	<scheduled-payment-external-manual-payment #externalManualPayment [campaignId]="_campaignStore.id">
	</scheduled-payment-external-manual-payment>
</div>
