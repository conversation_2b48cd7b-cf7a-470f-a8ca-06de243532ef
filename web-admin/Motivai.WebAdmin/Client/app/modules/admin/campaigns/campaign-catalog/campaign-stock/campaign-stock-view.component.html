<gp-card [noPaddingTop]="true" [noPaddingBottom]="true">
	<div class="row">
		<accordion [closeOthers]="true">
			<accordion-group panelClass="b0 mb-sm panel-default no-shadow" [isOpen]="true">
				<div accordion-heading>
					<label><em class="fa fa-navicon"></em> Features do Estoque</label>
				</div>

				<div class="row menublock text-center" *ngIf="campaignId">
					<gp-menublock-item size="xs" icon="cogs" color="text-success" text="Gerencial"
						routerLinkActive="active" routerLink="gerenciamento" *ngIf="canManage">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="file-text-o" color="text-success" text="Relatório de Vales"
						routerLinkActive="active" routerLink="report" *ngIf="canViewStockReport">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="search" color="text-success" text="Estoque de Vales"
						routerLinkActive="active" routerLink="consulta" *ngIf="canSearchStock">
					</gp-menublock-item>
				</div>
			</accordion-group>
		</accordion>
	</div>
</gp-card>
<router-outlet></router-outlet>
