<gp-alert [overlay]="true" #gpAlert></gp-alert>
<div *ngIf="canViewStockReport">
	<gp-card>
		<gp-form-row>
			<gp-form-col cols="12 6 6" [inputGroup]="false">
				<gp-simple-input label="Código SKU">
					<input type="text" name="skuCode" class="form-control" [(ngModel)]="params.skuCode" />
				</gp-simple-input>
			</gp-form-col>

			<gp-datepicker cols="12 6 6" label="Data limite das importações" name="maximumImportDate"
				[(ngModel)]="params.maximumImportDate">
			</gp-datepicker>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 6 6" [inputGroup]="false">
				<div>
					<label>Agrupar por lote</label>
				</div>
				<gp-switch name="groupByBatch" [(ngModel)]="params.groupByBatch"></gp-switch>
			</gp-form-col>

			<gp-form-col cols="12 6 6" [inputGroup]="false">
				<div>
					<label>Agrupar por data de expiração</label>
				</div>
				<gp-switch name="groupByExpirationDate" [(ngModel)]="params.groupByExpirationDate"></gp-switch>
			</gp-form-col>

			<gp-form-col cols="12 6 6" [inputGroup]="false">
				<div>
					<label>Agrupar por cupons expirados</label>
				</div>
				<gp-switch name="groupByExpirationDate" [(ngModel)]="params.groupDateExpiration">
				</gp-switch>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-spinner-button type="button" buttonClass="bg-primary-dark" icon="plus" text="Pesquisar" pull="right"
					[search]="true" [loading]="loading" [disabled]="loading" (click)="onSearchStock()" marginTop="27px">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card title="Estoques encontrado">
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-grid #skuGrid name="skuGrid" [rows]="stocks" [pageSize]="20"
					[columns]="['Data Importação', 'Código SKU', 'Data Expiração', 'Quantidade Total', 'Quantidade Disponível', 'Quantidade Utilizada', 'Cobertura']"
					[fields]="['formattedBatchCreateDate', 'skuCode', 'formattedExpirationDate', 'totalQty', 'qtyAvailable', 'qtyUsed', 'stockDaysCoverEstimation']"
					[showActive]="false" [showTotalPages]="false" [showPagination]="true" [showEdit]="false"
					[showDelete]="false" [loading]="loading" [pageSize]="params.limit"
					(onPageChanged)="onPageChanged($event)">
				</gp-grid>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card [last]="true" *ngIf="hasRows">
		<gp-form-row>
			<gp-form-col cols="12" [inputGroup]="false">
				<gp-spinner-button text="Exportar Estoques" loadingText="Aguarde" icon="download" pull="right"
					bootstrapClass="success" (click)="onClickExport($event)">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</gp-card>
</div>