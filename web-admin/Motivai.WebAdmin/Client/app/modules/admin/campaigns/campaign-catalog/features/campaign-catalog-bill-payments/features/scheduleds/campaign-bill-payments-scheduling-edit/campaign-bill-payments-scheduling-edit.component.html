<gp-alert [overlay]="true" #alert></gp-alert>

<gp-card [first]="true">
	<div class="row">
		<div class="col-lg-6 col-xs-12 br pv">
			<div class="clearfix">
				<p class="pull-left"><strong>Data de Agendamento: </strong></p>
				<p class="pull-right mr">{{ (paymentScheduled.createDate | datetimezone) || '-'}}</p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Data Agendada do Pagamento: </strong></p>
				<p class="pull-right mr">{{paymentScheduled.formattedScheduledPaymentDate || '-'}}</p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Data da Operação: </strong></p>
				<p class="pull-right mr">{{ (paymentScheduled.operationDate | datetimezone) || '-'}}</p>
			</div>
		</div>
		<div class="col-lg-6 col-xs-12 br pv">
			<div class="clearfix" [ngSwitch]="paymentScheduled.billPaymentPartner">
				<p class="pull-left"><strong>Parceiro: </strong></p>
				<div *ngSwitchCase="'IS2B'">
					<p class="pull-right mr">IS2B</p>
				</div>
				<div *ngSwitchDefault>
					<p class="pull-right mr"></p>
				</div>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Fator de Conversão: </strong></p>
				<p class="pull-right mr">{{paymentScheduled.pointsFactor}}</p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Status: </strong></p>
				<p class="pull-right mr {{paymentScheduled.statusClass}}">
					<strong>{{ paymentScheduled.formattedStatus }}</strong>
				</p>
			</div>
		</div>
	</div>

	<div *ngIf="paymentScheduled.errorOccurred">
		<hr>
		<gp-form-row>
			<div class="col-lg-6 col-xs-12 br pv">
				<div class="clearfix">
					<p class="pull-left"><strong>Ocorreu erro: </strong></p>
					<p class="pull-right mr">{{ paymentScheduled.errorOccurred ? 'Sim' : 'Não' }}</p>
				</div>
				<div class="clearfix">
					<p class="pull-left"><strong>Mensagem do erro: </strong></p>
					<p class="pull-right mr">{{paymentScheduled.errorMessage}}</p>
				</div>
				<div class="clearfix">
					<p class="pull-left"><strong>Detalhes do erro: </strong></p>
					<p class="pull-right mr">{{paymentScheduled.errorDetails}}</p>
				</div>
			</div>
		</gp-form-row>
	</div>

	<div *ngIf="paymentScheduled.refunded || paymentScheduled.refundErrorMessage">
		<hr>
		<h4>Estorno</h4>
		<gp-form-row>
			<div class="col-lg-6 col-xs-12 br pv">
				<div class="clearfix">
					<p class="pull-left"><strong>Estorno Realizado: </strong></p>
					<p class="pull-right mr">{{ paymentScheduled.refunded ? 'Sim' : 'Não' }}</p>
				</div>
				<div class="clearfix" *ngIf="paymentScheduled.refundReceipt">
					<p class="pull-left"><strong>Data Estorno: </strong></p>
					<p class="pull-right mr">{{ (paymentScheduled.refundReceipt.finishDate | datetimezone) || '-' }}</p>
				</div>
			</div>
		</gp-form-row>
		<gp-form-row *ngIf="paymentScheduled.refundErrorMessage && paymentScheduled.refundErrorMessage.length">
			<gp-form-col cols="12 6">
				<label>Ocorreu erro durante o estorno:</label>
				<p>{{paymentScheduled.refundErrorMessage}}</p>
			</gp-form-col>
			<gp-form-col cols="12 6">
				<label>Mensagem de erro do estorno:</label>
				<p>{{paymentScheduled.refundErrorMessage}}</p>
			</gp-form-col>
		</gp-form-row>
	</div>
</gp-card>

<spinner [show]="loading" [overlay]="true"></spinner>
<gp-card title="Boleto/Conta">
	<div class="row">
		<div class="col-lg-6 col-xs-12 br pv">
			<div class="clearfix">
				<p class="pull-left"><strong>Codº Barras: </strong></p>
				<p class="pull-right mr">{{paymentScheduled.billDetails.barCode}}</p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Estabelecimento: </strong></p>
				<p class="pull-right mr">{{paymentScheduled.billDetails.assignor}}</p>
			</div>
		</div>
		<div class="col-lg-6 col-xs-12 br pv">
			<div class="clearfix">
				<p class="pull-left"><strong>Data de Vencimento: </strong></p>
				<p class="pull-right mr">{{paymentScheduled.formattedDueDate || '-'}}</p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Valor: </strong></p>
				<p class="pull-right mr">{{ paymentScheduled.billDetails.billingAmount | amount }}</p>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-lg-6 col-xs-12 br pv">
			<div class="clearfix">
				<p class="pull-left"><strong>Data de Liquidação: </strong></p>
				<p class="pull-right mr">{{ paymentScheduled.billDetails.settleDate || '-'}}</p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Liquidado no próximo dia útil: </strong></p>
				<p class="pull-right mr">{{ paymentScheduled.billDetails.formattedNextSettle | booleanToYesNo}}</p>
			</div>
		</div>
		<div class="col-lg-6 col-xs-12 br pv" *ngIf="hasRegisterData">
			<div class="clearfix">
				<p class="pull-left"><strong>Data de baixa do boleto: </strong></p>
				<p class="pull-right mr">{{paymentScheduled.billDetails.registerData.formattedPayDueDate}}</p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Data de vencimento do registro: </strong></p>
				<p class="pull-right mr">{{paymentScheduled.billDetails.registerData.formattedDueDateRegister}}</p>
			</div>
		</div>
	</div>

	<div *ngIf="hasRegisterData">
		<div class="row">
			<div class="col-lg-6 col-xs-12 br pv">
				<h4>Pagador</h4>
				<div class="clearfix">
					<p class="pull-left"><strong>Nome do pagador:</strong></p>
					<p class="pull-right mr">{{paymentScheduled.billDetails.registerData.payer}}</p>
				</div>
				<div class="clearfix">
					<p class="pull-left"><strong>Documento do Pagador: </strong></p>
					<p class="pull-right mr">{{paymentScheduled.billDetails.registerData.documentPayer}}</p>
				</div>
			</div>
			<div class="col-lg-6 col-xs-12 br pv">
				<h4>Recebedor</h4>
				<div class="clearfix">
					<p class="pull-left"><strong>Beneficiário: </strong></p>
					<p class="pull-right mr">{{paymentScheduled.billDetails.registerData.recipient}}</p>
				</div>
				<div class="clearfix">
					<p class="pull-left"><strong>Documento doBeneficiário: </strong></p>
					<p class="pull-right mr">{{paymentScheduled.billDetails.registerData.documentRecipient}}</p>
				</div>
			</div>
		</div>

		<h4>Valores</h4>
		<div class="row">
			<div class="col-lg-6 col-xs-12 br pv">
				<div class="clearfix">
					<p class="pull-left"><strong>Valor nominal do título a ser pago: </strong></p>
					<p class="pull-right mr">{{paymentScheduled.billDetails.registerData.originalValue | amount}}</p>
				</div>
				<div class="clearfix">
					<p class="pull-left"><strong>Valor total de descontos e abatimentos: </strong></p>
					<p class="pull-right mr">{{paymentScheduled.billDetails.registerData.totalWithDiscount | amount}}
					</p>
				</div>
			</div>
			<div class="col-lg-6 col-xs-12 br pv">
				<div class="clearfix">
					<p class="pull-left"><strong>Valor do desconto calculado: </strong></p>
					<p class="pull-right mr">{{paymentScheduled.billDetails.registerData.discountValue | amount}}</p>
				</div>
				<div class="clearfix">
					<p class="pull-left"><strong>Valor multa já calculado:</strong></p>
					<p class="pull-right mr">{{paymentScheduled.billDetails.registerData.fineValueCalculated | amount}}
					</p>
				</div>
			</div>
			<div class="col-lg-6 col-xs-12 br pv">
				<div class="clearfix">
					<p class="pull-left"><strong>Valor atualizado a ser pago do título:</strong></p>
					<p class="pull-right mr">{{paymentScheduled.billDetails.registerData.totalUpdated | amount}}</p>
				</div>
				<div class="clearfix">
					<p class="pull-left"><strong>Permissão alterar o valor do boleto:</strong></p>
					<p class="pull-right mr">{{ paymentScheduled.billDetails.registerData.allowChangeValue |
						booleanToYesNo}}
				</div>
			</div>
			<div class="col-lg-6 col-xs-12 br pv">
				<div class="clearfix">
					<p class="pull-left"><strong>Valor mínimo permitido para pagamento do título: </strong></p>
					<p class="pull-right mr">{{paymentScheduled.billDetails.registerData.minValue | amount}}</p>
				</div>
				<div class="clearfix">
					<p class="pull-left"><strong>Valor máximo permitido para pagamento do título:</strong></p>
					<p class="pull-right mr">{{paymentScheduled.billDetails.registerData.maxValue | amount}}</p>
				</div>
			</div>
		</div>
	</div>
</gp-card>

<gp-card title="Recibo" *ngIf="hasReceipt">
	<gp-form-row *ngIf="!paymentScheduled.manuallyPaid && paymentScheduled.paymentReceipt">
		<gp-form-col cols="12">
			<div class="col-xs-12 col-sm-4 col-payment-receipt">
				<pre class="payment-receipt">{{paymentScheduled.paymentReceipt}}</pre>
			</div>
		</gp-form-col>
	</gp-form-row>

	<gp-form-row *ngIf="!paymentScheduled.manuallyPaid && paymentScheduled.paymentReceipt">
		<gp-form-col cols="12" additionalClasses="text-center">
			<gp-spinner-button text="Imprimir recibo" [loading]="loading" loadingText="Aguarde" icon="print"
				bootstrapClass="primary" (click)="printPaymentReceipt()">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>

	<gp-form-row *ngIf="paymentScheduled.manuallyPaid">
		<div class="col-xs-12 col-lg-6 br pv">
			<div class="clearfix">
				<p class="pull-left"><strong>Data de registro: </strong></p>
				<p class="pull-right mr">{{paymentScheduled.formattedFinishDate || '-'}}</p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Usuario de registro: </strong></p>
				<p class="pull-right mr">{{ operationUserName }}</p>
			</div>
		</div>

		<div class="col-xs-12 col-lg-6 br pv" *ngIf="paymentScheduled.externalPaymentOperationDetails">
			<div class="clearfix">
				<p class="pull-left"><strong>Data solicitação:</strong></p>
				<p class="pull-right mr">{{ paymentScheduled.externalPaymentOperationDetails.requestDate |
					date:'dd/MM/yyyy' }}</p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Solicitante:</strong></p>
				<p class="pull-right mr">{{ paymentScheduled.externalPaymentOperationDetails.requesterName }}</p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Motivo solicitação:</strong></p>
				<p class="pull-right mr">{{ paymentScheduled.externalPaymentOperationDetails.reason }}</p>
			</div>
		</div>

		<div class="col-xs-12 col-lg-6 br pv" *ngIf="paymentScheduled.paymentReceiptExternalLink">
			<div class="clearfix">
				<a class="btn btn-primary" target="_blank" [href]="paymentScheduled.paymentReceiptExternalLink">
					<i class="fa fa-download fa-2x"></i>
					<span class="left-p1">Ver Comprovante</span>
				</a>
			</div>
		</div>
	</gp-form-row>
</gp-card>

<gp-card title="Participante">
	<gp-form-row>
		<gp-form-col cols="12 6">
			<label>CPF/CNPJ:</label>
			<span>{{ paymentScheduled.userDocument | document }}</span>
		</gp-form-col>
	</gp-form-row>
	<div *ngIf="paymentScheduled.accountOperator">
		<h4>Operador</h4>
		<gp-form-row>
			<gp-form-col cols="12 6">
				<label>E-mail Operador:</label>
				<span>{{ paymentScheduled.accountOperator.accountOperatorEmail }}</span>
			</gp-form-col>
			<gp-form-col cols="12 6">
				<label>CPF/CNPJ do Operador:</label>
				<span>{{ paymentScheduled.accountOperator.accountOperatorDocument | document }}</span>
			</gp-form-col>
		</gp-form-row>
	</div>
</gp-card>

<gp-card *ngIf="hasAnyAction">
	<gp-spinner-button type="button" icon="close" text="Cancelar" bootstrapClass="warning" loadingText="Processando"
		pull="right" (click)="showCancelPaymentModal()" *ngIf="canCancel">
	</gp-spinner-button>

	<gp-spinner-button type="button" icon="calendar" text="Reagendar" bootstrapClass="primary" loadingText="Processando"
		pull="right" marginRight="10px" (click)="showChangeDatePaymentModal()" *ngIf="canReschedule">
	</gp-spinner-button>

	<gp-spinner-button type="button" icon="ticket" text="Efetuar pagamento manual" [pink]="true" pull="right"
		marginRight="10px" (click)="showScheduledPaymentExternalRegistrationChangeModal()"
		*ngIf="canRegisterManualPayment">
	</gp-spinner-button>

	<gp-spinner-button type="button" icon="repeat" bootstrapClass="primary" marginRight="10px" text="Tentar novamente"
		loadingText="Processando" pull="right" (click)="retryScheduledPayment()" [loading]="loadingRetryPayment"
		*ngIf="canRetryPayment">
	</gp-spinner-button>
</gp-card>


<app-scheduled-payment-cancellation-modal [campaignId]="campaignId" [paymentScheduledId]="paymentScheduledId"
	(refreshPaymentScheduledEvent)="getScheduledPaymentById()" #scheduledPaymentCancellation>
</app-scheduled-payment-cancellation-modal>

<app-scheduled-payment-date-change-modal [campaignId]="campaignId" [paymentScheduledId]="paymentScheduledId"
	[paymentScheduled]="paymentScheduled" (refreshPaymentScheduledEvent)="getScheduledPaymentById()"
	#scheduledPaymentDateChangeModal>
</app-scheduled-payment-date-change-modal>

<app-scheduled-payment-external-payment-registration-modal [campaignId]="campaignId"
	[paymentScheduledId]="paymentScheduledId" (refreshPaymentScheduledEvent)="getScheduledPaymentById()"
	#scheduledPaymentExternalRegistrationChangeModal>
</app-scheduled-payment-external-payment-registration-modal>
