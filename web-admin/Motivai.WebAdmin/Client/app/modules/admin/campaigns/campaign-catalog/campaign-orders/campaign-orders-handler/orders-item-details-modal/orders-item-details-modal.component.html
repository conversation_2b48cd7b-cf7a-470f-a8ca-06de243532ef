<gp-modal #modal [title]="'Detalhes do item ' + item.skuCode" width="700px" (onClose)="close()">
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<h4 style="color: #333">Dados gerais</h4>
			<p><strong>Nome do produto:</strong> {{ item.name }}</p>
			<p><strong>Código SKU:</strong> {{ item.skuCode }}</p>
			<p><strong>Tipo de Produto:</strong> {{ productTypeDescription }}</p>
			<p><strong>Quantidade:</strong> {{ item.quantity }}</p>
			<p><strong>Status atual:</strong> <span class="text-primary">{{ item.statusDescription }}</span></p>
			<p><strong>Última atualização:</strong> {{ item.updateDate | datetimezone:timezone }}</p>
			<p *ngIf="item.lastIntegrationUpdate"><strong>Última integração:</strong> {{ item.lastIntegrationUpdate | datetimezone:timezone }}</p>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<hr />
			<h4 style="color: #333">Preços</h4>
		</gp-form-col>
		<div grid="12 5 5"><p><strong>Valor Unitário (R$):</strong> {{ item.unitPrice?.currency | number:'1.2-2' }}</p></div>
		<div grid="12 5 5"><p><strong>Valor Unitário (Pontos):</strong> {{ item.unitPrice?.points | number:'1.2-2' }}</p></div>
		<div grid="12 5 5"><p><strong>Valor Frete (R$):</strong> {{ item.shippingCost?.currency | number:'1.2-2' }}</p></div>
		<div grid="12 5 5"><p><strong>Valor Frete (Pontos):</strong> {{ item.shippingCost?.points | number:'1.2-2' }}</p></div>
	</gp-form-row>
	<gp-form-row>
		<div class="col-sm-12">
			<hr />
			<h4 style="color: #333">Entrega</h4>
		</div>
		<div class="col-xs-6" *ngIf="item.estimatedDeliveryDays">
			<p><strong>Prazo de entrega:</strong> {{ item.estimatedDeliveryDays + ' dias úteis' || 'Sem previsão' }}</p>
		</div>
		<div class="col-xs-6" *ngIf="item.deliveryDate">
			<p><strong>Data de entrega:</strong> {{ item.deliveryDate | date:'dd/MM/yyyy' }}</p>
		</div>
		<div class="col-xs-12" *ngIf="item.trackingLink">
			<p>
				<strong>Link de Rastreio: </strong>
				<a [href]="item.trackingLink" target="_blank">Link</a>
			</p>
		</div>
		<div class="col-xs-12" *ngIf="item.trackingCode">
			<p><strong>Código de rastreio:</strong> {{ item.trackingCode }}</p>
		</div>
		<div class="col-xs-12" *ngIf="item.deliveryCode">
			<p><strong>Código da entrega:</strong> {{ item.deliveryCode }}</p>
		</div>
		<div class="col-xs-12" *ngIf="item.invoiceNumber">
			<p><strong>Nota fiscal:</strong> {{item.invoiceNumber}} - Série: {{item.invoiceSerie}} - Chave de acesso: {{item.invoiceKeyAccess}}</p>
		</div>
	</gp-form-row>
	<gp-form-row *ngIf="item.customAttributes && item.customAttributes.length">
		<gp-form-col cols="12 12 12">
			<hr />
			<h4 style="color: #333">Atributos Customizados</h4>
			<gp-grid
				[rows]="item.customAttributes" 
				[columns]="['Nome', 'Valor']" 
				[fields]="['name', 'value']"
				[showActive]="false" [showPagination]="false" [showTotalPages]="false"
				[showEdit]="false" [showDelete]="false">
			</gp-grid>
		</gp-form-col>
	</gp-form-row>
</gp-modal>