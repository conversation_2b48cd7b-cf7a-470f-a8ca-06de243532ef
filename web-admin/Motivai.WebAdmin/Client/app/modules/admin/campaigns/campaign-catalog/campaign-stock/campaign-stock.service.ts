import { Injectable } from '@angular/core';
import moment = require('moment');
import { Observable } from 'rxjs';

import { ApiService } from '../../../../../core/api/api.service';

@Injectable()
export class CampaignStockService {
	constructor(private _api: ApiService) { }

	getStocks(campaignId: string): Observable<Array<any>> {
		return this._api.get(`/api/campaigns/${campaignId}/stocks`);
	}

	getStockById(campaignId: string, stockId: string): Observable<any> {
		return this._api.get(`/api/campaigns/${campaignId}/stocks/${stockId}`);
	}

	saveStock(campaignId: string, skuStock: any): Observable<any> {
		if (skuStock.id)
			return this._api.put(`/api/campaigns/${campaignId}/stocks/${skuStock.id}`, skuStock);
		return this._api.post(`/api/campaigns/${campaignId}/stocks`, skuStock);
	}

	incrementStock(campaignId: string, stockId: string, participantGroupId: string, quantity: number): Observable<any> {
		return this._api.put(`/api/campaigns/${campaignId}/stocks/${stockId}/movement/increment`, { quantity: quantity, participantGroupId: participantGroupId });
	}

	decrementStock(campaignId: string, stockId: string, participantGroupId: string, quantity: number): Observable<any> {
		return this._api.put(`/api/campaigns/${campaignId}/stocks/${stockId}/movement/decrement`, { quantity: quantity, participantGroupId: participantGroupId });
	}

	findBySkuCode(campaignId: string, filters: any): Observable<any> {
		const params = { ...filters };
		if (params.maximumImportDate) {
			params.maximumImportDate = moment(params.maximumImportDate).format('YYYY-MM-DD');
		}
		return this._api.get(`/api/campaigns/${campaignId}/stocks/skus/report`, params);
	}

	findBySkuCodeAndExportToCsv(campaignId: string, params: any): Observable<any> {
		return this._api.get(`/api/campaigns/${campaignId}/stocks/skus/report/csv`, params);
	}

	findByPartnerIdAndSkuCode(campaignId: string, skuCode: string): Observable<any> {
        return this._api.get(`/api/campaigns/${campaignId}/stocks/partner/sku/code/${skuCode}`);
    }
}
