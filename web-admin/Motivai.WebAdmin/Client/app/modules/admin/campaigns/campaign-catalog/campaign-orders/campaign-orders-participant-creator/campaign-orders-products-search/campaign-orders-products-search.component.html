<spinner [show]="loading" [overlay]="true"></spinner>
<gp-alert [overlay]="true" #gpAlert></gp-alert>
<div>
	<gp-card title="Carrinho de compras">
		<gp-form-row>
			<gp-form-col cols="12 4 4">
				<gp-simple-input label="Pesquise o produto">
					<input type="text" class="form-control" id="searchValue" name="searchValue"
						placeholder="SKU ou nome" [(ngModel)]="term" />
				</gp-simple-input>
			</gp-form-col>

			<gp-form-col cols="12 2 2">
				<gp-spinner-button type="button" [search]="true" text="Pesquisar" [loading]="loading" [disabled]="loading"
					(click)="onSearchProducts()" marginTop="27px">
				</gp-spinner-button>
			</gp-form-col>

			<gp-form-col cols="12 6 6">
				<gp-spinner-button type="button" text="Iniciar Acesso ao Catálogo" pull="right" [pink]="true" icon="shopping-cart"
					[loading]="loading" loadingText="Aguarde" (click)="onStartCatalogAccess()" 
					*ngIf="hasCatalog && !foundProducts" marginTop="27px">
				</gp-spinner-button>
			</gp-form-col>

			<gp-form-col cols="12 6 6" *ngIf="foundProducts">
				<label>Produtos encontrados</label>
				<gp-select name="productsComboBox" [items]="productsCombo" [allowClear]="false"
					(change)="onSelectedProduct($event)" [(ngModel)]="selectedProduct">
				</gp-select>
			</gp-form-col>
		</gp-form-row>

		<div *ngIf="hasSelectedProduct">
			<hr />
			<gp-form-row>
				<gp-form-col cols="12 3 3">
					<gp-simple-input label="Nome">
						<input type="text" class="form-control" id="name" name="name" [(ngModel)]="product.name"
							disabled />
					</gp-simple-input>
				</gp-form-col>

				<gp-form-col cols="12 3 3">
					<gp-simple-input label="Código SKU">
						<input type="text" class="form-control" id="skuCode" name="skuCode"
							[(ngModel)]="product.skuCode" disabled />
					</gp-simple-input>
				</gp-form-col>

				<gp-form-col cols="12 3 3">
					<gp-simple-input label="Parceiro">
						<input type="text" class="form-control" id="partnerName" name="partnerName"
							[(ngModel)]="product.partnerName" disabled />
					</gp-simple-input>
				</gp-form-col>

				<gp-form-col cols="12 3 3">
					<gp-simple-input label="Valor unitário">
						<input type="text" class="form-control" id="price" name="price" [(ngModel)]="product.price"
							disabled />
					</gp-simple-input>
				</gp-form-col>
			</gp-form-row>

			<gp-form-row>
				<gp-form-col cols="12 3 3">
					<gp-simple-input label="Quantidade">
						<input type="text" class="form-control" id="quantity" name="quantity"
							[(ngModel)]="quantityProducts" />
					</gp-simple-input>
				</gp-form-col>
			</gp-form-row>

			<gp-form-row>
				<gp-form-col cols="12 6">
					<gp-spinner-button type="button" [pink]="true" icon="plus" text="Adicionar ao carrinho"
						(click)="onAddProduct()" marginTop="27px">
					</gp-spinner-button>

					<gp-spinner-button type="button" bootstrapClass="danger" icon="close" text="Cancelar"
						(click)="onCancelProduct()" marginTop="27px">
					</gp-spinner-button>
				</gp-form-col>
			</gp-form-row>
		</div>

		<div *ngIf="shoppingCartIsNotEmpty">
			<hr />
			<gp-form-row>
				<gp-form-col cols="12 12 12">
					<h4>Itens do carrinho</h4>
					<gp-grid name="shoppingCart" [rows]="shoppingCart"
						[columns]="['Código', 'Produto', 'Valor Unitário', 'Quantidade', 'Total']"
						[fields]="['skuCode', 'name', 'price', 'quantity', 'total']" [showActive]="false"
						[showPagination]="false" [showTotalPages]="false" [showEdit]="false" [showDelete]="true"
						(onDelete)="onRemoveProduct($event)">
					</gp-grid>
				</gp-form-col>
			</gp-form-row>
		</div>
	</gp-card>

	<gp-card title="Acesso ao Catálogo" *ngIf="(hasCatalog && foundProducts) && !shoppingCartIsNotEmpty">
		<gp-form-col cols="12 12 12">
			<gp-spinner-button type="button" text="Iniciar Acesso ao Catálogo" pull="right" [pink]="true" icon="shopping-cart"
				[loading]="loading" loadingText="Aguarde" (click)="onStartCatalogAccess()">
			</gp-spinner-button>
		</gp-form-col>
	</gp-card>
</div>
