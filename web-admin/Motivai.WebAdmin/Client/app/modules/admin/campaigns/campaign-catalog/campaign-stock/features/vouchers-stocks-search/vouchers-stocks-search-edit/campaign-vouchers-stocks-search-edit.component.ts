import { Component, OnInit, ViewChild } from '@angular/core';

import { GpProductsStockComponent } from '../../../../../../products/products-edit/products-skus/products-stock/products-stock.component';

@Component({
  selector: 'vouchers-stocks-search-edit',
  templateUrl: './campaign-vouchers-stocks-search-edit.component.html'
})
export class CampaignVouchersStocksSearchEditComponent {
  @ViewChild('gpProductsStock') gpProductsStock: GpProductsStockComponent;

  productId: string;
  skuId: string;

  hasProduct() {
    return this.productId != null;
  }

  clickedOnDetails(event: any) {
    this.productId = event.productId || '';
    this.skuId = event.skuId || '';
    this.gpProductsStock.setProductSkuVoucher(this.productId, this.skuId);
  }

  clear() {
    this.productId = null;
    this.skuId = null;
    this.gpProductsStock.resetValues();
  }

}
