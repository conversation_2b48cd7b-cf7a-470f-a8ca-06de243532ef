import { <PERSON><PERSON>nent, On<PERSON>nit, ViewChild, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';

import { CampaignStore } from '../../../../../../campaign.store';
import { ScheduledBillPaymentsService } from '../services/scheduled-bill-payments-service.service';
import { GpAlertComponent } from '../../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { FormatHelper } from '../../../../../../../../../shared/formatters/format-helper';
import { ScheduledPaymentCancellationComponent } from '../payments-handling-actions/scheduled-payment-cancellation/scheduled-payment-cancellation.component';
import { ScheduledPaymentDateChangeComponent } from '../payments-handling-actions/scheduled-payment-date-change/scheduled-payment-date-change.component';
import { ScheduledPaymentExternalPaymentRegistrationComponent } from '../payments-handling-actions/scheduled-payment-external-payment-registration/scheduled-payment-external-payment-registration.component';
import { AuthStore } from '../../../../../../../../../core/auth/auth.store';
import { RxjsHelpers } from '../../../../../../../../../shared/helpers/rxjs-helpers';

@Component({
  selector: 'campaign-bill-payments-scheduling-edit',
  templateUrl: './campaign-bill-payments-scheduling-edit.component.html',
  styles: [
    `
      .payment-receipt {
        text-align: center;
      }
      .col-payment-receipt {
        float: none;
        margin: 0 auto;
      }
    `
  ]
})
export class CampaignBillPaymentsSchedulingEditComponent implements OnInit, OnDestroy {
  @ViewChild('alert') alert: GpAlertComponent;
  @ViewChild('scheduledPaymentCancellation') scheduledPaymentCancellation: ScheduledPaymentCancellationComponent;
  @ViewChild('scheduledPaymentDateChangeModal') scheduledPaymentDateChangeModal: ScheduledPaymentDateChangeComponent;
  @ViewChild('scheduledPaymentExternalRegistrationChangeModal') scheduledPaymentExternalRegistrationChangeModal: ScheduledPaymentExternalPaymentRegistrationComponent;

  paymentScheduled: any = {
    billDetails: {},
    participantCost: {
      currency: 0
    },
    accountOperator: {}
  };
  campaignId: string = '';
  paymentScheduledId: string = '';

  loading: boolean = false;
  loadingRetryPayment: boolean = false;

  private _campaign$: Subscription;

  constructor(private _authStore: AuthStore, private _store: CampaignStore, private _service: ScheduledBillPaymentsService) { }

  get hasRegisterData() {
    return (this.paymentScheduled &&
      this.paymentScheduled.billDetails &&
      this.paymentScheduled.billDetails.registerData)
  }

  get permissionCancelPayment() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_SCHEDULED_PAYMENTS_CANCEL_PAYMENT;
  }

  get permissionRescheduledPayment() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_SCHEDULED_PAYMENTS_RESCHEDULED_PAYMENT;
  }

  get permissionReprocessPayment() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_SCHEDULED_PAYMENTS_REPROCESS_PAYMENT;
  }

  get permissionManualPayment() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_SCHEDULED_PAYMENTS_MANUAL_PAYMENT;
  }

  get isScheduled() {
    return this.paymentScheduled.status == 'SCHEDULED';
  }

  get isError() {
    return this.paymentScheduled.status == 'ERROR';
  }

  get canRetryPayment() {
    return this.permissionReprocessPayment && this.isError;
  }

  get canRegisterManualPayment() {
    return this.permissionManualPayment && this.isError;
  }

  get canCancel() {
    return this.permissionCancelPayment && (this.isScheduled || this.isError);
  }

  get canReschedule() {
    return this.permissionRescheduledPayment && this.isScheduled;
  }

  get hasAnyAction() {
    return this.canRetryPayment || this.canRegisterManualPayment
      || this.canCancel || this.canReschedule;
  }

  get hasReceipt() {
    return this.paymentScheduled.manuallyPaid || this.paymentScheduled.paidExternally
      || this.paymentScheduled.paymentReceipt;
  }

  get operationUserName() {
    if (!this.paymentScheduled.manuallyPaid)
      return '-';
    if (this.paymentScheduled.paidExternally && this.paymentScheduled.externalPaymentOperationUser) {
      return this.paymentScheduled.externalPaymentOperationUser.name;
    }
    if (this.paymentScheduled.manualPaymentOperationUser)
      return this.paymentScheduled.manualPaymentOperationUser.name;
    return '-';
  }
  ngOnInit() {
    this._campaign$ = this._store.asObservable
      .subscribe(id => {
        this.campaignId = id;
      });
  }

  ngOnDestroy(): void {
    RxjsHelpers.unsubscribe(this._campaign$);
  }

  getScheduledPaymentById() {
    this.loading = true;
    this._service
      .getScheduledPaymentById(this.campaignId, this.paymentScheduledId)
      .subscribe(
        response => {
          if (response.status == 'ERROR') {
            response.formattedStatus = 'Erro';
            response.statusClass = 'text-danger';
          } else if (response.status == 'CANCELED') {
            response.formattedStatus = 'Cancelado';
            response.statusClass = 'text-warning';
          } else if (response.status == 'SCHEDULED') {
            response.formattedStatus = 'Agendado';
            response.statusClass = 'text-primary';
          } else if (response.status == 'PENDING') {
            response.formattedStatus = 'Em Processamento';
            response.statusClass = 'text-primary';
          } else if (response.status == 'PAID') {
            response.formattedStatus = 'Pago';
            response.statusClass = 'text-success';
          }

          response.formattedScheduledPaymentDate = FormatHelper.formatDate(
            response.scheduledPaymentDate
          );
          response.formattedDueDate = FormatHelper.formatDate(
            response.billDetails.dueDate
          );
          response.formattedFinishDate = FormatHelper.formatDateWithTimezone(
            response.finishDate
          );

          if (response.billDetails) {
            if (response.billDetails.registerData) {
              response.billDetails.registerData.formattedPayDueDate = FormatHelper.formatDateWithTimezone(
                response.billDetails.registerData.payDueDate
              );

              response.billDetails.registerData.formattedPayDueDate = FormatHelper.formatDateWithTimezone(
                response.billDetails.registerData.payDueDate
              );
              response.billDetails.registerData.formattedDueDateRegister = FormatHelper.formatDateWithTimezone(
                response.billDetails.registerData.dueDateRegister
              );
            }

            response.billDetails.formattedNextSettle = response.billDetails.nextSettle == "N" ? false : true;
          }

          this.paymentScheduled = response;
          this.loading = false;
        },
        err => {
          this.loading = false;
          this.alert.showError(err);
        }
      );
  }

  printPaymentReceipt() {
    const print = window.open('');
    print.document.title = 'Comprovante de Pagamento da Conta';
    print.document.write('<pre>');
    print.document.write(this.paymentScheduled.paymentReceipt);
    print.document.write('</pre>');
    print.document.close();
    print.focus();
    print.print();
    print.close();
  }

  retryScheduledPayment() {
    this.alert
      .confirm('Deseja tentar realizar o pagamento novamente?')
      .then(result => {
        if (result && result.value) {
          this.loadingRetryPayment = true;
          this._service.retryScheduledPaymentDate(this.campaignId, this.paymentScheduledId)
            .subscribe(
              response => {
                this.loadingRetryPayment = false;
                if (response) {
                  this.alert.showSuccess(
                    'Pagamento agendado enviado para fila de pagamento, por favor, aguarde alguns minutos.'
                  );
                }
                this.getScheduledPaymentById();
              },
              err => {
                this.loadingRetryPayment = false;
                this.alert.handleAndShowError(err);
                this.getScheduledPaymentById();
              }
            );
        }
      });
  }

  showCancelPaymentModal() {
    this.scheduledPaymentCancellation.showModal();
  }

  showChangeDatePaymentModal() {
    this.scheduledPaymentDateChangeModal.showModal();
  }

  showScheduledPaymentExternalRegistrationChangeModal() {
    this.scheduledPaymentExternalRegistrationChangeModal.showModal();
  }
}
