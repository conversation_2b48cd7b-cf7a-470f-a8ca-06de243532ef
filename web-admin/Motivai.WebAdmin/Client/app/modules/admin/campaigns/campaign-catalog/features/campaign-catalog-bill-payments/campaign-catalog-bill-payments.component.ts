import { Component, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { AuthStore } from '../../../../../../core/auth/auth.store';
import { RxjsHelpers } from '../../../../../../shared/helpers/rxjs-helpers';
import { CampaignStore } from '../../../campaign.store';

@Component({
	selector: 'campaign-catalog-bill-payments',
	templateUrl: './campaign-catalog-bill-payments.component.html'
})
export class CampaignCatalogBillPaymentsComponent implements OnInit {
	campaignId: string;

	private _campaign$: Subscription;

	constructor(private _authStore: AuthStore,
		private _campaignStore: CampaignStore) { }

	ngOnInit() {
		this._campaign$ = this._campaignStore.asObservable.subscribe(campaignId => {
			this.campaignId = campaignId;
		});
	}

	ngOnDestroy() {
		RxjsHelpers.unsubscribe(this._campaign$);
	}

	get canViewScheduledPayments() {
		return this._authStore.role.PERMISSION_CAMPAIGNS_SCHEDULED_PAYMENTS;
	}

	get canViewFiltersManagement() {
		return this._authStore.role.PERMISSION_CAMPAIGNS_CATALOG_BILLS_PAYMENT_FILTERS_MANAGEMENT;
	}
}
