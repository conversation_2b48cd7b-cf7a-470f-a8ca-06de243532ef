import { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';

import { UsersService } from '../../../../../../../core/services/users.service';
import { CampaignParticipantsRegistrationComponent } from '../../../../../../../shared/components/business/campaigns/campaign-participants-registration/campaign-participants-registration.component';

import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { FormatHelper } from '../../../../../../../shared/formatters/format-helper';
import { Item } from '../../../../../../../shared/models/item';

import { CampaignOrdersParticipantService } from '../services/campaign-orders-participant.service';

import { SearchField } from '../models/searchField';

@Component({
	selector: 'campaign-orders-participant-search',
	templateUrl: './campaign-orders-participant-search.component.html'
})
export class CampaignOrdersParticipantSearchComponent {
	@ViewChild('gpAlert') gpAlert: GpAlertComponent;
	@ViewChild('participantRegistration') participantRegistration: CampaignParticipantsRegistrationComponent;
	@Output('onSelectedParticipant') participantEmitter: EventEmitter<any> = new EventEmitter<any>();

	_campaignId: string;
	
	@Input() set campaignId(value: string) {
		this._campaignId = value;
	}
	
	userDefaultParticipant: boolean = false;
	searchFields: Item[] = [
		Item.of(SearchField.DOCUMENT, 'CPF/CNPJ'),
		Item.of(SearchField.LOGIN, 'Login'),
		Item.of(SearchField.CLIENT_USER_ID, 'Código')
	];
	
	participantInfo: any[] = [];
	participantDetails: any = {};
	
	participantsList: Item[] = [];
	userId: string;
	address: any = {};
	
	searchByField: SearchField;
	searchValue: string;
	lableSearchValue: string = 'Informe o valor a ser pesquisado';
	
	disableParticipantSearch: boolean = false;
	loading: boolean = false;

	constructor(
		private campaignOrdersParticipantService: CampaignOrdersParticipantService,
		private usersService: UsersService
	) { }

	onChangeSearchField(searchByField: SearchField) {
		this.clear();
		this.searchByField = searchByField;
		this.lableSearchValue = this.handleLableSearchValue();
	}

	private handleLableSearchValue() {
		switch (this.searchByField) {
			case SearchField.DOCUMENT: return 'Informe o CPF/CNPJ';
			case SearchField.LOGIN: return 'Informe o login';
			case SearchField.CLIENT_USER_ID: return 'Informe o código';
			default: return 'Informe o valor a ser pesquisado';
		}
	}

	onSearchParticipant() {
		if (!this.handleAndValidateSearchFields()) {
			return;
		}
		this.findParticipantInfo();
	}

	private handleAndValidateSearchFields(): boolean {
		if (!this.searchByField) {
			this.gpAlert.showWarning('Selecione por qual informação pesquisar.');
			return false;
		}
		if (this.isNullOrEmpty(this.searchValue)) {
			this.gpAlert.showWarning('Informe o valor a ser pesquisado.');
			return false;
		}
		if (this.searchByField === SearchField.DOCUMENT) {
			this.searchValue = FormatHelper.removeMask(this.searchValue);
		}
		return true;
	}

	private findParticipantInfo() {
		this.loading = true;
		this.campaignOrdersParticipantService.getParticipantInfo(this._campaignId, this.searchByField, this.searchValue)
			.subscribe(participantInfo => {
				if (participantInfo && participantInfo.length > 0) {
					participantInfo.forEach(p => {
						if (p.document) {
							this.participantsList.push(Item.of(p.userId, `${p.name} - CPF/CNPJ: ${p.document}`));
						} else if(p.login) {
							this.participantsList.push(Item.of(p.userId, `${p.name} - LOGIN: ${p.login}`));
						}
					});
					this.participantInfo = participantInfo;
					if (this.participantInfo.length == 1) {
						this.userId = participantInfo[0].userId;
						this.onSelectedParticipant(this.userId);
					}
				} else {
					this.clear();
					this.participantEmitter.emit();
					this.gpAlert.showWarning("Nenhum participante encontrado.");
				}
				this.loading = false;
			},
			err => {
				this.clear();
				this.participantEmitter.emit();
				this.gpAlert.handleAndShowError(err);
			}
		);
	}

	
	onSelectedParticipant(userId: string) {
		if (this.isNullOrEmpty(userId)) {
			return;
		}
		this.participantDetails = this.findParticipantDetails(userId);
		this.findParticipantBalance(this.participantDetails.userId);
		this.findMainAddress(userId);
		this.participantEmitter.emit(this.participantDetails);
	}
	
	private findParticipantDetails(userId: string) {
		return this.participantInfo.find(p => p.userId === userId);
	}
	
	private findParticipantBalance(userId: string) {
		this.loading = true;
		this.campaignOrdersParticipantService.getParticipantBalance(this._campaignId, userId)
			.subscribe(balance => {
				if (balance && balance > 0) {
					this.participantDetails.balance = balance;
				} else {
					this.participantDetails.balance = '0.0';
				}
				this.loading = false;
			},
			err => {
				this.loading = false;
				this.gpAlert.handleAndShowError(err);
			}
		);
	}

	private findMainAddress(userId: string) {
		this.loading = true;
		this.usersService.getMainAddress(this._campaignId, userId)
			.subscribe(address => {
				if (address) {
					this.address = address;
				} else {
					this.address = {};
				}
				this.loading = false;
			},
			err => {
				this.loading = false;
				this.address = {};
				this.gpAlert.handleAndShowError(err);
			}
		);
	}
	
	registerParticipant() {
		this.participantRegistration.openModal();
	}

	onRegistered(participantDocument: string) {
		if (this.isNullOrEmpty(participantDocument)) {
			return;
		}
		this.searchByField = SearchField.DOCUMENT;
		this.searchValue = participantDocument;
		this.disableParticipantSearch = true;
		this.findParticipantInfo();
	}

	get foundParticipants() {
		return this.participantsList.length > 0;
	}

	get hasParticipantDetails() {
		return !this.isNullOrEmpty(this.participantDetails.userId);
	}

	get hasBalance() {
		return this.participantDetails.balance != null
	}

	get hasAddress() {
		return this.address && this.address.street;
	}

	private isNullOrEmpty = function (value: string) {
		return !(typeof value === 'string' && value.trim().length > 0);
	}

	onDefaultParticipantChange() {
		if (this.userDefaultParticipant) {
			this.searchByField = SearchField.DOCUMENT;
			this.searchValue = '19734676000137';
			this.disableParticipantSearch = true;
			this.findParticipantInfo();
		} else {
			this.clear();
		}
	}

	clear() {
		this.participantInfo = [];
		this.address = {};
		this.participantDetails = {};
		this.participantsList = [];
		this.userId = '';
		this.searchValue = '';
		this.searchByField = null;
		this.disableParticipantSearch = false;
		this.loading = false;
	}
}
