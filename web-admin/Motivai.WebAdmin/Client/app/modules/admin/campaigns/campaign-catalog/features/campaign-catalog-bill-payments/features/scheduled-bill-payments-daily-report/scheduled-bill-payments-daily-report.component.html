<gp-alert [overlay]="true" #gpAlert></gp-alert>
<spinner [overlay]="true" [show]="loading"></spinner>

<gp-card title="Filtros">
	<gp-form-row>
		<gp-datepicker cols="12 4" [required]="false" label="De" name="from" [(ngModel)]="params.from">
		</gp-datepicker>
		<gp-datepicker cols="12 4" [required]="false" label="Até" name="to" [(ngModel)]="params.to">
		</gp-datepicker>
		<gp-form-col cols="12 4">
			<gp-simple-input label="CPF/CNPJ">
				<gp-input-mask name="userDocument" mask="00000000000000" [(ngModel)]="params.userDocument"></gp-input-mask>
			</gp-simple-input>
		</gp-form-col>
	</gp-form-row>

	<gp-form-row>
		<gp-form-col cols="12" [inputGroup]="false">
			<gp-spinner-button text="Pesquisar" [loading]="loading" loadingText="Aguarde"
				bootstrapClass="primary" icon="search" (click)="applyFilter()" marginTop="24px">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<gp-card [last]="true">
	<gp-form-row>
		<gp-form-col cols="12">
			<table class="table table-striped table-hover">
				<thead>
					<tr>
						<th></th>
						<th colspan="2" class="text-center">Agendados</th>
						<th colspan="2" class="text-center">Executado</th>
						<th></th>
					</tr>
					<tr>
						<th>Data</th>
						<th>Qtde.</th>
						<th>Valor Total (R$)</th>
						<th>Qtde.</th>
						<th>Valor Total (R$)</th>
						<th>Saldo (R$)</th>
					</tr>
				</thead>
				<tbody>
					<tr *ngIf="isEmpty">
						<td colspan="6">Nenhum registro encontrado</td>
					</tr>
					<tr *ngFor="let resume of dailyResume">
						<td class="text-center">{{ resume.scheduledDate | date:'dd/MM/yyyy' }}</td>
						<td>{{ resume.totalQtyPayments | number }}</td>
						<td class="text-right">{{ resume.totalAmountPayments | amount }}</td>
						<td>{{ resume.totalQtyPaymentsPaid | number }}</td>
						<td class="text-right">{{ resume.totalAmountPaymentsPaid | amount }}</td>
						<td class="text-right" [class.text-success]="resume.balance == 0" [class.text-danger]="resume.balance > 0">{{ resume.balance | amount }}</td>
					</tr>
				</tbody>
			</table>
		</gp-form-col>
	</gp-form-row>
</gp-card>
