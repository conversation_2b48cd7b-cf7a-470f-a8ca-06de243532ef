<gp-card [first]="true">
	<h3>Pedido: {{ order.internalOrderNumber }}</h3>
	<hr />
	<div class="row">
		<div class="col-lg-4 col-xs-12 br pv">
			<div class="clearfix" *ngIf="order.integrationOrderNumber">
				<p class="pull-left"><strong>Número Pedido GP Integrado</strong></p>
				<p class="pull-right mr">{{order.integrationOrderNumber || '-'}}</p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Data do Pedido</strong></p>
				<p class="pull-right mr">{{ order.creationDate | datetimezone:order.timezone }}</p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Data Local do Pedido</strong></p>
				<p class="pull-right mr">{{ order.creationDate | datetimezone }}</p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Data de Atualização</strong></p>
				<p class="pull-right mr">{{ order.updateDate | datetimezone }}</p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Situação</strong></p>
				<p class="pull-right mr" [class.text-primary]="!order.occurredError"><strong>{{ order.statusDescription
						}}</strong></p>
			</div>
			<div class="clearfix" *ngIf="isB2B()">
				<p class="pull-left"><strong>Venda Casada</strong></p>
				<p class="pull-right mr" [class.text-primary]="order.requiresAllChildren"
					[class.text-danger]="!order.requiresAllChildren"><strong>{{ order.requiresAllChildren ? 'Sim' :
						'Não' }}</strong></p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Total Pedido</strong></p>
				<p class="pull-right mr">
					<span><strong>R$ {{ order.totalAmount?.currency | number:'1.2-2' }}</strong></span>
				</p>
			</div>
		</div>
		<div class="col-lg-4 col-xs-6 br pv" *ngIf="order.shippingAddress">
			<div class="row">
				<div class="col-md-2 text-center visible-md visible-lg">
					<em class="fa fa-truck fa-3x text-muted"></em>
				</div>
				<div class="col-md-10">
					<h4>Entrega</h4>
					<address></address>{{ order.shippingAddress.addressName }}<br />
					CEP: {{ order.shippingAddress.cep | mask:'00000-000' }}<br />
					{{ order.shippingAddress.street }}, {{ order.shippingAddress.number }}<br />
					{{ order.shippingAddress.neighborhood }} - {{ order.shippingAddress.city }}/{{
					order.shippingAddress.state }}<br />
					<small>Complemento: {{ order.shippingAddress.complement }}</small><br />
					<small>Ref.: {{ order.shippingAddress.reference }}</small><br />
					<div *ngIf="hasReceiver">
						<br />
						<h4>Recebedor</h4><br />
						CPF: {{ order.shippingAddress.receiver.cpf }}<br />
						Name: {{ order.shippingAddress.receiver.name }}<br />
						Telefone: {{ order.shippingAddress.receiver.telephone }}<br />
						Celular: {{ order.shippingAddress.receiver.cellphone }}<br />
						E-mail: {{ order.shippingAddress.receiver.email }}<br />
					</div>
				</div>
			</div>
		</div>
		<div class="col-lg-4 col-xs-6 br pv">
			<div class="row">
				<div class="col-md-2 text-center visible-md visible-lg">
					<em class="fa fa-user fa-3x text-muted"></em>
				</div>
				<div class="col-md-10">
					<h4>Participante</h4>
					<address></address>{{ order.participant?.name }}<br />
					<strong>Documento:</strong> {{ order.participant?.document | document }}<br />
					<strong>Telefone:</strong> {{ (order.participant?.mainPhone | telephone) || 'Não cadastrado'
					}}<br />
					<strong>Celular:</strong> {{ (order.participant?.mobilePhone | telephone) || 'Não cadastrado'
					}}<br />
					<span *ngIf="order.participant?.clientUserId"><strong>Matricula:</strong> {{
						order.participant?.clientUserId }}<br /></span>
					<strong>Saldo:</strong> {{ order.participantBalance | number:'1.2-2' }}
				</div>
			</div>
		</div>
	</div>
	<div class="row" *ngIf="order?.observations">
		<div class="col-md-12">
			<label>Observações: </label>
			<span>{{order.observations}}</span>
		</div>
	</div>

	<div *ngIf="paymentMethods.length">
		<hr class="hidden-print"/>
		<p class="pull-left"><strong>Métodos de Pagamento</strong></p>
		<gp-grid name="paymentMethodsGrid" [rows]="paymentMethods" [columns]="['Descrição', 'Tipo', 'Total', 'Status']" [fields]="['description', 'typeDescription', 'paymentAmount.currency', 'statusDescription']"
			[loading]="loading" [showActive]="false" [showPagination]="false" [showEdit]="false" [showDelete]="false" emptyMessage="Nenhuma método de pagamento localizado.">
	   </gp-grid>
	</div>

	<hr class="hidden-print" *ngIf="order?.occurredError" />

	<div class="row mb-lg" *ngIf="order?.occurredError">
		<div class="clearfix hidden-md hidden-lg">
			<hr />
		</div>
		<div class="col-xs-12 col-sm-12 br pv">
			<div class="clearfix">
				<p class="pull-left"><strong>Ocorreu erro:</strong></p>
				<p class="pull-left mr" style="padding-left:1em">
					<span [class.text-success]="!order.occurredError"
						[class.text-danger]="order.occurredError"><strong>{{order.occurredError ? 'Sim' :
							'Não'}}</strong></span>
					<span *ngIf="order.errorDescription"> - {{order.errorDescription}}</span>
				</p>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-md-12">
			<hr />
			<!-- <gp-spinner-button type="button" [pink]="true" text="Estornar pedido completo" icon="close" pull="right"
				(click)="openMasterOrderRefunderModal()" *ngIf="order.canBeRefunded && !isUnderRiskAssessment"></gp-spinner-button>
			<gp-spinner-button type="button" marginRight="5px" bootstrapClass="warning" text="Cancelar pedido completo"
				icon="close" pull="right" (click)="openCancelOrderModal()"
				*ngIf="order.canBeCanceled"></gp-spinner-button> -->
		</div>
	</div>
	<div class="row">
		<div class="col-md-12">
			<gp-alert #alert [overlay]="true"></gp-alert>
		</div>
	</div>
	<spinner [overlay]="true" [show]="loadingScreen"></spinner>
</gp-card>

<risk-assessment [campaignId]="_campaignId" [orderId]="_orderId" [riskAssessment]="order.riskAssessment" (onManualAction)="onHandleManualAction()" *ngIf="hasRiskAssessment">
</risk-assessment>

<gp-card *ngFor="let child of order.childrenOrders" [title]="'Parceiro: ' + child.partnerName">
	<div class="row mb-lg">
		<div class="clearfix hidden-md hidden-lg">
			<hr />
		</div>
		<div class="col-lg-4 col-xs-12 br pv">
			<div class="clearfix">
				<p class="pull-left"><strong>{{ isB2B() ? 'Cobrança' : 'Número pedido no parceiro' }}</strong></p>
				<p class="pull-right mr">{{child.integratedOrderNumber || '-'}}</p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Online</strong></p>
				<p class="pull-right mr">{{child.offline ? 'Não' : 'Sim'}}</p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Data de atualização</strong></p>
				<p class="pull-right mr">{{child.updateDate | datetimezone:order.timezone}}</p>
			</div>
			<div class="clearfix" *ngIf="child.lastIntegrationUpdate">
				<p class="pull-left"><strong>Data da última integração</strong></p>
				<p class="pull-right mr">{{child.lastIntegrationUpdate | datetimezone:order.timezone}}</p>
			</div>
		</div>
		<div class="clearfix hidden-md hidden-lg">
			<hr />
		</div>
		<div class="col-lg-4 col-xs-12 br pv">
			<div class="clearfix">
				<p class="pull-left"><strong>Total itens</strong></p>
				<span class="pull-right mr">
					<div class="text-right">
						<span *ngIf="isRewardsOrMarketplace">{{child.totalAmount?.points - child.shippingCost?.points |
							number:'1.0'}} - </span>
						<span>R$ {{child.totalAmount?.currency - child.shippingCost?.currency | number:'1.2'}}</span>
					</div>
				</span>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Frete</strong></p>
				<span class="pull-right mr">
					<div class="text-right">
						<span *ngIf="isRewardsOrMarketplace">{{child.shippingCost?.points | number:'1.0'}} - </span>
						<span>R$ {{child.shippingCost?.currency | number:'1.2'}}</span>
					</div>
				</span>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Total pedido</strong></p>
				<span class="pull-right mr">
					<div class="text-right">
						<span *ngIf="isRewardsOrMarketplace"><strong>{{child.totalAmount?.points |
								number:'1.0'}}</strong> - </span>
						<span>R$ <strong>{{child.totalAmount?.currency | number:'1.2'}}</strong></span>
					</div>
				</span>
			</div>
		</div>
		<div class="clearfix hidden-md hidden-lg">
			<hr />
		</div>
		<div class="col-lg-4 col-xs-12 br pv">
			<div class="clearfix">
				<p class="pull-left"><strong>Situação</strong></p>
				<p class="pull-right mr" [class.text-primary]="!child.occurredError">
					<strong>{{child.statusDescription}}</strong></p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Debitado</strong></p>
				<p class="pull-right mr"><strong>{{ child.debted ? 'Sim' : 'Não' }}</strong></p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Integrado</strong></p>
				<p class="pull-right mr"><strong>{{ child.integrated ? 'Sim' : 'Não' }}</strong></p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Ocorreu erro</strong></p>
				<p class="pull-right mr" [class.text-success]="child.occurredError"
					[class.text-danger]="child.occurredError"><strong>{{child.occurredError ? 'Sim' : 'Não'}}</strong>
				</p>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-xs-12 col-sm-12 br pv">
			<div class="clearfix">
				<p class="pull-left">
					<strong>Mensagem de erro: </strong>
					<span [class.text-danger]="child.occurredError">{{child.errorDescription || '-'}}</span>
				</p>
			</div>
			<div class="clearfix" *ngIf="isUserWithGpBu">
				<p class="pull-left">
					<span><strong>Detalhes do erro: </strong></span>
					<span [class.text-danger]="child.occurredError">{{child.errorDetails || '-'}}</span>
				</p>
			</div>
		</div>
	</div>
	<div class="row" *ngIf="child.publicItemsUrl">
		<div class="col-md-12">
			Detalhes do pedido parceiro: <a [href]="child.publicItemsUrl" target="_blank">Clique aqui</a>
		</div>
	</div>
	<div class="row">
		<div *ngIf="showGeneralItensGrid(child.itemGrouperId)">
			<div class="col-md-12">
				<hr />
				<gp-spinner-button type="button" [pink]="true" text="Estornar pedido parceiro" icon="close" pull="right"
					(click)="openChildOrderRefunderModal(child)" *ngIf="child.canBeRefunded && !isUnderRiskAssessment">
				</gp-spinner-button>

				<gp-spinner-button type="button" marginRight="5px" bootstrapClass="warning" [loading]="loading"
					text="Conciliar Pedido" icon="refresh" pull="right" (click)="conciliate(child)"
					*ngIf="child.canBeConciliated">
				</gp-spinner-button>

				<gp-spinner-button type="button" marginRight="5px" bootstrapClass="warning"
					text="Cancelar pedido parceiro" icon="close" pull="right" (click)="openCancelChildOrderModal(child)"
					*ngIf="child.canBeCanceled">
				</gp-spinner-button>

				<gp-spinner-button type="button" [actionSecondary]="true" marginRight="5px" text="Cancelar itens"
					icon="minus-square" pull="right" (click)="setMultipleCancellation(true, child.itemGrouperId)"
					[loading]="loading" *ngIf="showMultipleCancellationButton(child.itemGrouperId)">
				</gp-spinner-button>

				<gp-spinner-button type="button" [actionSecondary]="true" marginRight="5px" text="Precificar"
					icon="dollar" pull="right" (click)="setMultiplePricing(true, child.itemGrouperId)"
					[loading]="loading" *ngIf="showMultiplePricingButton(child.itemGrouperId)">
				</gp-spinner-button>

				<gp-spinner-button type="button" [actionSecondary]="true" marginRight="5px" text="Faturar"
					icon="credit-card" pull="right" (click)="createInvoiceForPartnerOrder(child.itemGrouperId)"
					[loading]="loading" *ngIf="showInvoiceButton(child.itemGrouperId)">
				</gp-spinner-button>

				<gp-spinner-button type="button" [actionSecondary]="true" marginRight="5px" text="Visualizar Vales"
					icon="ticket" pull="right" (click)="showVouchers(child)" [loading]="loading"
					*ngIf="showButtonVouchers(child)" [disabled]="isUnderRiskAssessment">
				</gp-spinner-button>
			</div>
			<div class="col-md-12">
				<gp-alert #childOrderAlert [overlay]="true"></gp-alert>
			</div>
		</div>
		<div *ngIf="showMultiplePriceGrid(child.itemGrouperId)">
			<div class="col-md-12">
				<hr />
				<gp-spinner-button type="button" [pink]="true" text="Salvar" icon="send" pull="right"
					[loading]="loading" (click)="saveMultiplePricing(child.itemGrouperId)"></gp-spinner-button>
				<gp-spinner-button type="button" [actionSecondary]="true" marginRight="5px" text="Voltar"
					icon="arrow-left" pull="right" (click)="setMultiplePricing(false, '')"></gp-spinner-button>
			</div>
			<div class="col-md-12">
				<gp-alert #pricingAlert [overlay]="true"></gp-alert>
			</div>
		</div>
		<div *ngIf="showMultipleRefundGrid(child.itemGrouperId)">
			<div class="col-md-12">
				<hr />
				<gp-spinner-button type="button" [pink]="true" text="Cancelar itens selecionados" icon="close"
					pull="right" [loading]="loading" (click)="showMultipleCancellationModal()">
				</gp-spinner-button>

				<gp-spinner-button type="button" [actionBack]="true" marginRight="5px" text="Voltar" icon="arrow-left"
					pull="right" (click)="setMultipleCancellation(false, '')">
				</gp-spinner-button>
			</div>
			<div class="col-md-12">
				<gp-alert #cancellationAlert [overlay]="true"></gp-alert>
			</div>
		</div>
		<div class="col-md-12">
			<hr />
		</div>
	</div>
	<gp-form-row>
		<gp-form-col cols="12 12 12">

			<!-- Tabela de precificação -->
			<table class="table table-striped table-hover" *ngIf="showMultiplePriceGrid(child.itemGrouperId)">
				<thead>
					<tr>
						<th>Código SKU</th>
						<th style="min-width: 200px">Produto</th>
						<th>Quantidade</th>
						<th>Valor Unitário (R$)</th>
						<th *ngIf="!isB2B()">Valor Frete (R$)</th>
					</tr>
				</thead>
				<tbody>
					<tr *ngFor="let item of multiplePricingItems">
						<td>{{ item.skuCode }}</td>
						<td>{{ item.name }}</td>
						<td>
							<gp-input-mask name="quantity" [onlyInteger]="true" [(ngModel)]="item.quantity">
							</gp-input-mask>
						</td>
						<td>
							<gp-input-mask name="unitPrice" [onlyDecimal]="true" [decimais]="2" placeholder="0,00"
								[(ngModel)]="item.unitPrice.currency">
							</gp-input-mask>
						</td>
						<td *ngIf="!isB2B()">
							<gp-input-mask name="unitPrice" [onlyDecimal]="true" [decimais]="2" placeholder="0,00"
								[(ngModel)]="item.shippingCost.currency">
							</gp-input-mask>
						</td>
					</tr>
				</tbody>
			</table>

			<!-- Tabela de cancelamento/estorno -->
			<table class="table table-striped table-hover" *ngIf="showMultipleRefundGrid(child.itemGrouperId)">
				<thead>
					<tr>
						<th>Código SKU</th>
						<th style="min-width: 200px">Produto</th>
						<th>Quantidade</th>
						<th>Valor Unitário (R$)</th>
						<th></th>
					</tr>
				</thead>
				<tbody>
					<tr *ngFor="let item of multipleCancellationItems">
						<td>{{ item.skuCode }}</td>
						<td>{{ item.name }}</td>
						<td>{{ item.quantity }}</td>
						<td>{{ item.unitPrice?.currency }}</td>
						<td>
							<gp-input-checkbox *ngIf="!allowOnlyPartnerOrderRefund"
								[(ngModel)]="item.checked"></gp-input-checkbox>
						</td>
					</tr>
				</tbody>
			</table>

			<!-- Tabela de exibição geral dos itens -->
			<table class="table table-striped table-hover" *ngIf="showGeneralItensGrid(child.itemGrouperId)">
				<thead>
					<tr>
						<th>Código SKU</th>
						<th style="min-width: 200px">Produto</th>
						<th>Quantidade</th>
						<th>Valor Unitário (R$)</th>
						<th *ngIf="!isB2B()">Valor Frete (R$)</th>
						<th>Status</th>
						<th>SLA</th>
						<th>Ações</th>
					</tr>
				</thead>
				<tbody>
					<tr *ngFor="let item of child.items">
						<td>{{ item.skuCode }}</td>
						<td>{{ item.name }}</td>
						<td>{{ item.quantity }}</td>
						<td>{{ item.formattedUnitPriceCurrency }}</td>
						<td *ngIf="!isB2B()">{{ item.formattedShippingCostCurrency }}</td>
						<td>{{ item.statusDescription }}</td>
						<td>
							<div [class]="'label label-' + calculateSlaStatus(item.slaPercentDone)"
								style="font-size: 13px; padding: 8px 10px">{{ item.slaPercentDone }}%</div>
						</td>
						<td>
							<gp-spinner-button type="button" icon="info" [actionSecondary]="true" tooltip="Informações"
								(click)="openInfoModal(child, item)">
							</gp-spinner-button>

							<gp-spinner-button type="button" icon="list-ol" [actionSecondary]="true" tooltip="Eventos"
								(click)="openTrackingEventsModal(child.itemGrouperId, item)">
							</gp-spinner-button>

							<gp-spinner-button type="button" icon="dollar" [actionSecondary]="true" tooltip="Precificar"
								(click)="openPriceModal(child.itemGrouperId, item)" *ngIf="showPriceButton(item)">
							</gp-spinner-button>

							<gp-spinner-button type="button" icon="industry" [actionSecondary]="true" tooltip="Produção"
								(click)="openProductionModal(child.itemGrouperId, item)"
								*ngIf="showProductionButton(item)">
							</gp-spinner-button>

							<gp-spinner-button type="button" icon="truck" [actionSecondary]="true" tooltip="Entrega"
								(click)="openShippingModal(child.itemGrouperId, item)" *ngIf="showShippingButton(item)">
							</gp-spinner-button>

							<gp-spinner-button type="button" icon="close" [actionSecondary]="true" tooltip="Cancelar"
								(click)="openCancelItemModal(child, item)" *ngIf="item.canBeCanceled">
							</gp-spinner-button>

							<gp-spinner-button type="button" bootstrapClass="default" [pink]="true" icon="exchange"
								tooltip="Estornar" (click)="openItemRefunderModal(child, item)" [disabled]="isPixPaymentMethod"
								*ngIf="item.canBeRefunded && !allowOnlyPartnerOrderRefund && !isUnderRiskAssessment"></gp-spinner-button>
							<gp-spinner-button type="button" bootstrapClass="default" text="Estornos" icon="list"
								tooltip="Visualizar estornos" (click)="openRefunderModalToList(item)"
								*ngIf="hasRefunds(child, item)"></gp-spinner-button>
						</td>
					</tr>
				</tbody>
			</table>
		</gp-form-col>
	</gp-form-row>
</gp-card>

<orders-shipping-modal #shippingModal [campaignId]="_campaignId" [orderId]="_orderId" [item]="itemShipping"
	(onclose)="findOrderById()"></orders-shipping-modal>
<orders-event-tracking-modal #trackingModal [campaignId]="_campaignId" [orderId]="_orderId" [item]="itemTracking"
	(onclose)="findOrderById()"></orders-event-tracking-modal>
<orders-pricing-modal #priceModal [campaignId]="_campaignId" [orderId]="_orderId" [isB2B]="isB2B()" [item]="itemPriced"
	(onclose)="findOrderById()"></orders-pricing-modal>
<orders-item-details-modal #infoModal [item]="itemInfo" [timezone]="order?.timezone"></orders-item-details-modal>
<orders-manufacturing-modal #productionModal [campaignId]="_campaignId" [orderId]="_orderId" [item]="itemProduction"
	(onclose)="findOrderById()"></orders-manufacturing-modal>
<order-cancellation-modal #cancellationModal (onClose)="closeCancellationModal()"></order-cancellation-modal>
<order-refunder-modal #refundedModal (onClose)="findOrderById()"></order-refunder-modal>
<order-vouchers [campaignId]="_campaignId" [orderId]="_orderId" #vouchersModal></order-vouchers>
