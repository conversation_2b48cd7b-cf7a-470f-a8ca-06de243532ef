<gp-modal #modal [title]="'Tracking - ' + item.skuCode" width="1000px" marginLeft="20%" (onClose)="close()">
  <accordion [closeOthers]="true">
    <accordion-group panelClass="b0 mb-sm panel-default">
      <div accordion-heading>
        <label>Novo evento</label>
      </div>
      <form #eventForm="ngForm" (ngSubmit)="eventSubmit()" novalidate>
        <gp-form-row>
          <gp-form-col cols="12 4 4">
            <gp-simple-input label="Tipo do evento" [required]="true" errorMessage="Tipo do evento é obrigatório">
              <select class="form-control" name="type" [(ngModel)]="event.type">
                <option value="Info">Informação</option>
                <option value="Price">Preço</option>
                <option value="Shipping">Entrega</option>
              </select>
            </gp-simple-input>
          </gp-form-col>
          <gp-datepicker
            cols="12 4 4 4"
            required
            [disableSince]="now"
            label="Data do evento*"
            name="date"
            [(ngModel)]="event.date"
          ></gp-datepicker>
        </gp-form-row>
        <gp-form-row>
          <gp-form-col cols="12 12 12">
            <label>Anexar imagem</label>
            <gp-fileupload
              name="image"
              [path]="uploadPath"
              [images]="true"
              (oncomplete)="onCompleteEventFile($event)"
              #gpFile
            ></gp-fileupload>
          </gp-form-col>
        </gp-form-row>
        <gp-form-row>
          <gp-form-col cols="12 12 12">
            <div class="form-group">
              <label>Descrição*</label>
              <textarea
                style="height: 100px"
                required
                name="description"
                class="form-control"
                [(ngModel)]="event.description"
              ></textarea>
            </div>
          </gp-form-col>
        </gp-form-row>
        <gp-form-row>
          <gp-form-col cols="12 4 4">
            <gp-spinner-button
              type="submit"
              [disabled]="!eventForm.valid"
              [pink]="true"
              icon="send"
              text="Enviar evento"
              [loading]="loading"
            ></gp-spinner-button>
          </gp-form-col>
        </gp-form-row>
        <gp-form-row>
          <gp-form-col cols="12 12 12">
            <gp-alert #alert></gp-alert>
          </gp-form-col>
        </gp-form-row>
      </form>
    </accordion-group>
    <accordion-group panelClass="b0 mb-sm panel-default">
      <div accordion-heading>
        <label>Todos os eventos</label>
      </div>
      <div style="max-height: 500px; overflow-y: auto; overflow-x: hidden">
        <gp-form-row *ngIf="!item.trackingEvents || !item.trackingEvents.length">
          <gp-form-col cols="12 12 12">
            <p>Nenhum evento cadastrado para este item</p>
          </gp-form-col>
        </gp-form-row>
        <gp-form-row *ngFor="let track of item.trackingEvents">
          <gp-form-col cols="12 12 12">
            <p><strong>Data:</strong> {{ track.date | datetimezone }}</p>
            <p><strong>Tipo:</strong> {{ getEventTypeDescription(track.type) }}</p>
            <p>
              <strong>Visivel no catálogo:</strong>
              <span [class.text-danger]="!track.active" [class.text-primary]="track.active">{{
                track.active ? 'Sim' : 'Não'
              }}</span>
            </p>
            <p><strong>Descrição:</strong> {{ track.description }}</p>
            <p *ngIf="track.partnerDescription">
              <strong>Descrição do parceiro:</strong> {{ track.partnerDescription }}
            </p>
            <p *ngIf="track.imageUrl">
              <strong>Anexo:</strong>
              <a [href]="track.imageUrl + '/500'" target="_blank">Clique aqui</a>
            </p>
            <hr />
          </gp-form-col>
        </gp-form-row>
      </div>
    </accordion-group>
  </accordion>
</gp-modal>
