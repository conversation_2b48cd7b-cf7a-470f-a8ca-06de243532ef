import { Component, OnInit, ViewChild, Input, Output, EventEmitter } from '@angular/core';
import { GpModalComponent } from '../../../../../../../../../../shared/components/gp-modal/gp-modal.component';
import { ScheduledBillPaymentsService } from '../../services/scheduled-bill-payments-service.service';
import { GpAlertComponent } from '../../../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { ThrowStmt } from '@angular/compiler/src/output/output_ast';

@Component({
  selector: 'app-scheduled-payment-cancellation-modal',
  templateUrl: './scheduled-payment-cancellation.component.html'
})
export class ScheduledPaymentCancellationComponent implements OnInit {
  @ViewChild('alert') alert: GpAlertComponent;
  @ViewChild('cancelPaymentModal') cancelPaymentModal: GpModalComponent;

  @Output()
  refreshPaymentScheduledEvent: EventEmitter<any> = new EventEmitter<any>();

  @Input() campaignId: string;
  @Input() paymentScheduledId: string;

  scheduledPaymentCancel: any = {};
  loadingProcessing: boolean = false;

  constructor(private service: ScheduledBillPaymentsService) { }

  ngOnInit() { }

  showModal() {
    this.cancelPaymentModal.show();
  }

  hideModal() {
    this.cancelPaymentModal.hide();
  }

  refreshPaymentScheduled() {
    this.refreshPaymentScheduledEvent.emit();
  }

  cancelPayment() {
    this.loadingProcessing = true;

    this.service.cancelScheduledPayment(this.campaignId, this.paymentScheduledId, this.scheduledPaymentCancel).subscribe(
      response => {
        if (response) {
          this.alert.showSuccess('Agendamento cancelado com sucesso.');
        }
        this.scheduledPaymentCancel = {};
        this.loadingProcessing = false;
        this.refreshPaymentScheduled();
        this.hideModal();
      },
      err => {
        this.loadingProcessing = false;
        this.alert.showError(err);
      }
    );

  }

}
