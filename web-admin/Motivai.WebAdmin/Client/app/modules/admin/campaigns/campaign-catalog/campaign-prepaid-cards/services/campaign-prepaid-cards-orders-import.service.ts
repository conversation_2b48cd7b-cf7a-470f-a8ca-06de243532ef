import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { ApiService } from '../../../../../../core/api/api.service';

@Injectable()
export class CampaignPrepaidCardsOrdersImportService {
  constructor(private _api: ApiService) { }

  countBatches(campaignId: string, parameters: any): Observable<number> {
    const params: any = {};
    if (parameters.batchStatus) {
      params.batchStatus = parameters.batchStatus;
    }
    return this._api.get(`api/campaigns/${campaignId}/catalog/prepaidcards/ordersimport/batches/count`, params);
  }

  findBatches(campaignId: string, parameters: any, skip: number, limit: number): Observable<any[]> {
    const params: any = { skip, limit };
    if (parameters.batchStatus) {
      params.batchStatus = parameters.batchStatus;
    }
    return this._api.get(`api/campaigns/${campaignId}/catalog/prepaidcards/ordersimport/batches`, params);
  }

  getCampaignBatch(campaignId: string, batchId: string): Observable<any> {
    return this._api.get(`api/campaigns/${campaignId}/catalog/prepaidcards/ordersimport/batches/${batchId}`);
  }

  findBatchRegisters(campaignId: string, batchId: string, skip: number, limit: number) {
    return this._api.get(`api/campaigns/${campaignId}/catalog/prepaidcards/ordersimport/batches/${batchId}/registers`, {
      skip, limit
    });
  }

  approveBatch(campaignId: string, batchId: string) {
    return this._api.put(`api/campaigns/${campaignId}/catalog/prepaidcards/ordersimport/batches/${batchId}/approve`);
  }

  rejectBatch(campaignId: string, batchId: string) {
    return this._api.put(`api/campaigns/${campaignId}/catalog/prepaidcards/ordersimport/batches/${batchId}/reject`);
  }

  rejectBatchRegister(campaignId: string, batchId: string, registerId: string) {
    return this._api.put(`api/campaigns/${campaignId}/catalog/prepaidcards/ordersimport/batches/${batchId}/registers/${registerId}/reject`);
  }
}
