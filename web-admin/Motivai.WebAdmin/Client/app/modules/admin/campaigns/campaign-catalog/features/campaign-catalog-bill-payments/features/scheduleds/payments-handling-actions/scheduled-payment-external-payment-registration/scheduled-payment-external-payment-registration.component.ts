import { Component, OnInit, ViewChild, Input, Output, EventEmitter } from '@angular/core';
import { Gp<PERSON>lertComponent } from '../../../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpModalComponent } from '../../../../../../../../../../shared/components/gp-modal/gp-modal.component';
import { ScheduledBillPaymentsService } from '../../services/scheduled-bill-payments-service.service';
import { GpFileUploadComponent } from '../../../../../../../../../../shared/components/gp-fileupload/gp-fileupload.component';
import { thresholdSturges } from 'd3';

@Component({
  selector: 'app-scheduled-payment-external-payment-registration-modal',
  templateUrl: './scheduled-payment-external-payment-registration.component.html'
})
export class ScheduledPaymentExternalPaymentRegistrationComponent implements OnInit {
  @ViewChild('alert') alert: GpAlertComponent;
  @ViewChild('externalPaymentModal') externalPaymentModal: GpModalComponent;
  @ViewChild('paymentReceiptUploader') paymentReceiptUploader: GpFileUploadComponent;

  loadingProcessing: boolean = false;

  @Input() campaignId: string;
  @Input() paymentScheduledId: string;

  @Output()
  refreshPaymentScheduledEvent: EventEmitter<any> = new EventEmitter<any>();

  tomorrow = new Date();

  uploadPath: string = '';
  scheduledPaymentExternalRegistration: any = {
    chargeParticipant: Boolean
  };

  constructor(private service: ScheduledBillPaymentsService) { }

  ngOnInit() {
    this.tomorrow.setDate(new Date().getDate() + 1);
  }

  showModal() {
    this.externalPaymentModal.show();
  }

  hideModal() {
    this.externalPaymentModal.hide();
  }

  refreshPaymentScheduled() {
    this.refreshPaymentScheduledEvent.emit();
  }

  private processScheduledPayment() {
    if (!this.scheduledPaymentExternalRegistration.paymentDate) {
      return this.alert.showWarning('Selecione a data de pagamento da conta.');
    }

    this.service.externalScheduledPayment(this.campaignId, this.paymentScheduledId, this.scheduledPaymentExternalRegistration)
      .subscribe(
        result => {
          this.loadingProcessing = false;
          if (result) {
            this.alert.showSuccess('Comprovante enviado com sucesso.');
            this.scheduledPaymentExternalRegistration = {};
            this.scheduledPaymentExternalRegistration.chargeParticipant = true;
            this.refreshPaymentScheduled();
          } else {
            this.alert.showWarning('Não foi possível enviar o comprovante, por favor, tente novamente.');
          }
        },
        err => {
          this.loadingProcessing = false;
          this.alert.handleAndShowError(err);
        }
      );
  }

  uploadReceiptScheduledPayment() {
    this.loadingProcessing = true;
    this.paymentReceiptUploader.path = `api/campaigns/${this.campaignId}/services/billpayments/schedulings/${this.paymentScheduledId}/externalScheduledPayment/receipt`;

    this.paymentReceiptUploader.onComplete = uploadEvent => {
      this.loadingProcessing = false;
      let response = JSON.parse(uploadEvent.response);
      if (uploadEvent.success && response.success) {
        // this.alert.showSuccess('Comprovante de pagamento salvo com sucesso.');
        const fileUrl = this.paymentReceiptUploader.extractResultFromResponse(uploadEvent);
        this.scheduledPaymentExternalRegistration.receiptUrl = fileUrl;
        this.processScheduledPayment();
      } else {
        this.paymentReceiptUploader.createUploader();
        return this.alert.showError(uploadEvent.errorMessage || response.error || 'Ocorreu um erro ao fazer o upload do arquivo de histórico do participante.');
      }
      this.paymentReceiptUploader.createUploader();
    };

    this.paymentReceiptUploader.uploadFile();
  }

  validateAndUpload() {
    if (!this.paymentReceiptUploader || !this.paymentReceiptUploader.hasSelectedFile()) {
      return this.alert.showWarning('Selecione um arquivo para upload.');
    }

    if (!this.scheduledPaymentExternalRegistration.paymentDate) {
      return this.alert.showWarning('Selecione a data de pagamento da conta.');
    }

    if (!this.scheduledPaymentExternalRegistration.chargeParticipant) {
      this.alert
        .confirm('Não será efetuado débito no saldo do participante, deseja continuar ?')
        .then(result => {
          if (result && result.value) {
            this.uploadReceiptScheduledPayment();
          }
        });
    } else {
      this.uploadReceiptScheduledPayment();
    }
  }
}
