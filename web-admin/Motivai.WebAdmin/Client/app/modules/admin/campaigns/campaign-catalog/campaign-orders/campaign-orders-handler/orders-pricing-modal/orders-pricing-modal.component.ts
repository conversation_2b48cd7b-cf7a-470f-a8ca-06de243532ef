import { Component, OnInit, Input, Output, EventEmitter, ViewChild } from '@angular/core';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpModalComponent } from '../../../../../../../shared/components/gp-modal/gp-modal.component';
import { CampaignOrdersHandlerService } from '../campaign-orders-handler.service';

@Component({
    selector: 'orders-pricing-modal',
    templateUrl: './orders-pricing-modal.component.html'
})
export class OrdersPricingModalComponent implements OnInit {
    @Input() item: any = {};
    @Input() campaignId: string;
    @Input() orderId: string;
    @Input() isB2B: boolean = false;
    @ViewChild('alert') alert: GpAlertComponent;
    @ViewChild('modal') modal: GpModalComponent;
    @Output('onclose') onclose: EventEmitter<any> = new EventEmitter();

    loading: boolean = false;

    constructor(private _ordersService: CampaignOrdersHandlerService) { }
    ngOnInit(): void { }

    open() {
        this.modal.show();
    }

    close() {
        this.onclose.emit(this.item);
    }

    clear() {
        this.alert.clear();
    }

    private priceSubmit(process: boolean) {
        this.loading = true;
        this.alert.clear();
        this._ordersService.saveItemPrice(this.campaignId, this.orderId, this.item.itemGrouperId, this.item.skuId, this.item, process)
            .subscribe(item => {
                if (item) {
                    this.alert.showSuccess(process ? 'Produto precificado com sucesso!' : 'Produto precificado e aprovado com sucesso!');
                } else {
                    this.alert.showError('Ocorreu um erro ao salvar as informações do preço');
                }
                this.loading = false;
            }, err => {
                const msg = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao salvar as informações do preço';
                this.alert.showError(msg);
                this.loading = false;
            });
    }
}
