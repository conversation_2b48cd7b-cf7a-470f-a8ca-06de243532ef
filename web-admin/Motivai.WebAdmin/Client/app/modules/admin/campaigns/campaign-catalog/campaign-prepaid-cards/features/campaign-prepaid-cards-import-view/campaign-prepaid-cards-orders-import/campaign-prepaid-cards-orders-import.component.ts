import { PageChangeEvent } from '../../../../../../../../shared/components/gp-grid/page-change-event';
import { FormatHelper } from '../../../../../../../../shared/formatters/format-helper';
import { Component, Input, ViewChild } from '@angular/core';

import { GpAlertComponent } from '../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { FileUploadEvent } from '../../../../../../../../shared/components/gp-fileupload/file-upload-event';
import { GpFileUploadComponent } from '../../../../../../../../shared/components/gp-fileupload/gp-fileupload.component';
import { CampaignPrepaidCardsOrdersImportService } from '../../../services/campaign-prepaid-cards-orders-import.service';

@Component({
  selector: 'app-campaign-prepaid-cards-orders-import',
  templateUrl: './campaign-prepaid-cards-orders-import.component.html'
})
export class CampaignPrepaidCardsLinkingImportComponent {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @ViewChild('gpFileUploader') gpFileUploader: GpFileUploadComponent;

  loading: boolean = false;
  loadingRegisters: boolean = false;
  parameters: any = {};

  batchId: string = '';
  batch: any = {};
  batchRegisters: Array<any> = [];

  private _campaignId: string = null;
  uploadPath: string = null;

  constructor(private _ordersImportService: CampaignPrepaidCardsOrdersImportService) {}

  @Input('campaignId')
  set setCampaignId(id: string) {
    if (!id || !id.length) return;
    this._campaignId = id;
    this.uploadPath = `api/campaigns/${this._campaignId}/catalog/prepaidcards/ordersimport/batches`;
  }

  get hasBatch() {
    return this.batchId && this.batch && this.batch.id;
  }

  get isProcessing() {
    return this.batch.status == 'PROCESSING';
  }

  get isApproving() {
    return this.batch.status == 'PROCESSING_APPROVAL';
  }

  get showApprovalSection() {
    return this.batch.status == 'WAITING_APPROVAL'
			|| this.batch.status == 'PROCESSING_APPROVAL';
  }

  get canApprove() {
    return this.batch.status == 'WAITING_APPROVAL';
  }

  private handleStatus(status: any): any {
    if (!status) {
      return 'Indefinido';
    }

    switch (status) {
      case 'CREATED':
        return 'Criado';
      case 'PENDING':
        return 'Pendente';
      case 'WAITING_APPROVAL':
        return 'Aguardando aprovação';
      case 'PROCESSING_APPROVAL':
        return 'Processando aprovação';
      case 'REJECTED':
        return 'Rejeitado';
      case 'PROCESSING':
        return 'Em Processamento';
      case 'COMPLETED':
        return 'Finalizado';
      case 'ERROR':
        return 'Erro';
      default:
        return 'Indefinido';
    }
  }

  clear() {
    this.batchId = null;
    this.parameters = {};
    this.batch = {};
    this.batchRegisters = [];
    this.gpFileUploader.createUploader();
  }

  onUploadComplete(event: FileUploadEvent) {
    this.gpAlert.showSuccess('Upload realizado com sucesso.');
  }

  findPrepaidCardBatch() {
    if (!this.batchId) {
      return;
    }
    this.loading = true;
    this._ordersImportService.getCampaignBatch(this._campaignId, this.batchId)
      .subscribe(
        batch => {
          this.loading = false;
          if (batch) {
            batch.statusDescription = this.handleStatus(batch.status);
            this.batch = batch;
            if (batch.totalLines > 0) {
              this.findBatchRegisters();
            }
          } else {
            this.gpAlert.showError('Lote não encontrado');
            this.batchId = null;
          }
        },
        err => {
          this.loading = false;
          this.gpAlert.handleAndShowError(err);
        }
      );
  }

  private handleRegister(reg: any) {
    if (!reg) return;
    reg.userDocument = FormatHelper.formatDocument(reg.userDocument);
    reg.cardType = reg.cardType == 'WITHDRAWABLE' ? 'Com Saque' : 'Sem Saque';
    reg.orderNumber = reg.prepaidCardOrder ? reg.prepaidCardOrder.orderNumber : null;

    if (reg.operationType == 'ISSUE_CARD_CHARGE') {
      reg.operation = 'Emissão e Carga';
    } else if (reg.operationType == 'RECHARGE_CARD') {
      reg.operation = 'Recarga';
    } else if (reg.operationType == 'REISSUE_CARD_RECHARGE') {
      reg.operation = '2a Via e Carga';
    }

    if (reg.rejected) {
      reg.status = 'Rejeitado';
    } else if (reg.errorOccurred) {
      reg.status = 'Erro';
    } else if (reg.processed) {
      reg.status = 'Processado';
    } else {
      if (this.batch.status == 'REJECTED') {
        reg.status = 'Rejeitado';
      } else {
        reg.status = 'Pendente';
      }
    }
  }

  private findBatchRegisters(skip: number = 0, limit: number = 50) {
    if (!this.batchId) return;
    this.loadingRegisters = true;
    this._ordersImportService.findBatchRegisters(this._campaignId, this.batchId, skip, limit)
      .subscribe(
        registers => {
          if (registers && registers.length) {
            registers.forEach(reg => this.handleRegister(reg));
            this.batchRegisters = registers;
          } else {
            this.batchRegisters = [];
          }
          this.loadingRegisters = false;
        },
        err => {
          this.loadingRegisters = false;
          this.gpAlert.handleAndShowError(err);
        }
      );
  }

  changePage(event: PageChangeEvent) {
    this.findBatchRegisters(event.skip, event.limit);
  }

  downloadErrorsCsv() {
    window.open(this.batch.importErrorsFileUrl, '_blank');
  }

  downloadCsvWithApprovalErrors() {

  }

  approve() {
    this.gpAlert.confirm('Confirma a APROVAÇÃO do lote?')
      .then(result => {
        if (result && result.value) {
          this._ordersImportService.approveBatch(this._campaignId, this.batchId)
            .subscribe(
              result => {
                this.findPrepaidCardBatch();
                if (result) {
                  this.gpAlert.showSuccess('Aprovação realizada com sucesso');
                } else {
                  this.gpAlert.showWarning('Não foi possível efetuar a aprovação, por favor, tente novamente.');
                }
              },
              err => {
                this.loading = false;
                this.gpAlert.handleAndShowError(err);
              }
            );
        }
      });
  }

  reject() {
    this.gpAlert.confirm('Confirma a REJEIÇÃO do lote?')
      .then(result => {
        if (result && result.value) {
          this._ordersImportService.rejectBatch(this._campaignId, this.batchId)
            .subscribe(
              result => {
                this.findPrepaidCardBatch();
                if (result) {
                  this.gpAlert.showSuccess('Lote rejeitado com sucesso');
                } else {
                  this.gpAlert.showWarning('Não foi possível rejeitar o lote, por favor, tente novamente.');
                }
              },
              err => {
                this.loading = false;
                this.gpAlert.handleAndShowError(err);
              }
            );
        }
      });
  }

  rejectRegister(row: any) {
    if (!row || !row.id)
      return;
    if (row.rejected) {
      this.gpAlert.showWarning('Registro já foi reprovado');
      return;
    }
    this.gpAlert.confirm(`Confirma a REPROVAÇÃO do registro de R$ ${row.chargeAmount} do ${row.userDocument}?`)
      .then(result => {
        if (result && result.value) {
          this.loading = true;
          this._ordersImportService.rejectBatchRegister(this._campaignId, this.batchId, row.id)
            .subscribe(
              result => {
                this.loading = false;
                if (result) {
                  this.findPrepaidCardBatch();
                  this.gpAlert.showSuccess('Registro rejeitado com sucesso');
                } else {
                  this.gpAlert.showWarning('Não foi possível rejeitar o registro, por favor, tente novamente.');
                }
              },
              err => {
                this.loading = false;
                this.gpAlert.handleAndShowError(err);
              }
            );
        }
      });
  }
}
