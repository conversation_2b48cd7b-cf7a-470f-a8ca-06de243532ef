import { Injectable } from "@angular/core";
import { Observable } from 'rxjs/Observable';
import moment = require('moment');

import { ApiService } from "../../../../../../../../../core/api/api.service";

@Injectable()
export class CampaignCatalogBillPaymentsFiltersManagementService {

    constructor(private api: ApiService) { }

    public getBillsWithAttemptedPayment(campaignId: string, params: any): Observable<any> {
        this.validateDates(params);
        return this.api.get(`/api/campaigns/${campaignId}/catalog/billspayment/filtersmanagement/bills/errors`, params, 30000);
    }

    public addFiltersToAllowedList(campaignId: string, filtersManagement: any): Observable<any> {
        return this.api.post(`/api/campaigns/${campaignId}/catalog/billspayment/filtersmanagement/bills/filters/allowedlist`, filtersManagement, 30000);
    }

    public rejectBill(campaignId: string, billId: string): Observable<any> {
        return this.api.put(`/api/campaigns/${campaignId}/catalog/billspayment/filtersmanagement/bills/${billId}/reject`, 30000);
    }

    public findActivesFiltersBy(campaignId: string, params: any): Observable<any> {
        this.validateDates(params);
        return this.api.get(`/api/campaigns/${campaignId}/catalog/billspayment/filtersmanagement/filters/actives`, params, 30000);
    }

    public desableFilterFromAllowedList(campaignId: string, filterId: string): Observable<any> {
        return this.api.delete(`/api/campaigns/${campaignId}/catalog/billspayment/filtersmanagement/bills/filters/${filterId}`, 30000);
    }

    private validateDates(params: any): void {
        if (params.from != null) params.from = moment(params.from).format('YYYY-MM-DD');
        if (params.to != null) params.to = moment(params.to).format('YYYY-MM-DD');
    }

    public addFiltersManualToAllowedList(campaignId: string, billsPaymentManualFilterRegistration: any): Observable<any> {
        return this.api.post(`/api/campaigns/${campaignId}/catalog/billspayment/filtersmanagement/bills/filters/manual/allowedlist`, billsPaymentManualFilterRegistration, 30000);
    }
}
