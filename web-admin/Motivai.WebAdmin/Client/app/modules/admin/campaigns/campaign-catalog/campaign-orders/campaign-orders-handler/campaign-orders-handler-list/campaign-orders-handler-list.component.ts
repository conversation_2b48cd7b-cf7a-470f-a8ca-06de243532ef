import { Component, OnInit, Input, Output, EventEmitter, ViewChild } from '@angular/core';
import { CampaignOrdersHandlerService } from '../campaign-orders-handler.service';
import { FormatHelper } from '../../../../../../../shared/formatters/format-helper';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';

@Component({
    selector: 'campaign-orders-handler-list',
    templateUrl: 'campaign-orders-handler-list.component.html'
})
export class CampaignOrdersHandlerListComponent implements OnInit {
    private _campaignId: string;
    @Input() set campaignId(v: string) {
        if (v) {
            this._campaignId = v;
            this.findOrders();
        }
    }

    @Output('edit') edit: EventEmitter<any> = new EventEmitter();
    @ViewChild('gpAlert') gpAlert: GpAlertComponent;


    private search: any = {};
    private orders: any[] = [];
    private loading: boolean = false;
    private skip: number = 0;
    private limit: number = 100;

    constructor(private _ordersService: CampaignOrdersHandlerService) { }

    ngOnInit() { }

    public findOrders() {
        if (this._campaignId) {
            this.loading = true;
            this._ordersService.findOrders(this._campaignId, this.search.orderDateFrom, this.search.orderDateTo, this.search.status, this.search.orderNumber, this.search.document, this.skip, this.limit)
                .subscribe(orders => {
                    this.orders = orders;
                    if (this.orders) {
                        this.orders.forEach(order => {
                            order.formattedCreationDate = FormatHelper.formatDateWithTimezone(order.creationDate, order.timezone);
                            if (order.slaPercentDone) {
                                order.slaPercentDone = `${order.slaPercentDone}%`;
                            } else {
                                order.slaPercentDone = '';
                            }
                        });
                    }
                    this.loading = false;
                }, err => {
                    this.gpAlert.showError(err)
                    this.loading = false;
                });
        }
    }

    private prepareEdit($event) {
        if ($event) {
            this.edit.emit($event);
        }
    }

    private pageChanged($event) {
        if ($event) {
            this.skip = $event.skip;
            this.limit = $event.limit;
            this.findOrders();
        }
    }

    private calculateSlaStatus(totalDays: number, daysRemaining: number): string {
        if (daysRemaining <= 0) return 'danger';
        if (daysRemaining > totalDays / 2) return 'success';
        if (daysRemaining > totalDays / 4) return 'warning';
        return 'danger';
    }
}
