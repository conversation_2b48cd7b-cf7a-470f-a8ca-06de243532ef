<div>
	<gp-alert [overlay]="true" #gpAlert></gp-alert>

	<gp-card title="Pesquisa de filtros" [first]="true">
		<gp-form-row>
			<gp-form-col cols="12 4 4">
				<gp-simple-input label="Informe o tipo do boleto" [required]="true">
					<gp-select name="typeBill" [items]="typeBills" [allowClear]="false" [(ngModel)]="typeBill">
					</gp-select>
				</gp-simple-input>
			</gp-form-col>

			<gp-form-col cols="12 4 4">
				<gp-simple-input label="Pesquisar por" [required]="false">
					<gp-select name="searchByField" [items]="searchFields" [allowClear]="true"
						(change)="onChangeSearchField($event)" [(ngModel)]="searchByField">
					</gp-select>
				</gp-simple-input>
			</gp-form-col>

			<gp-form-col cols="12 4 4">
				<label>{{ lableSearchValue }}</label>
				<input type="text" class="form-control" id="searchValue" name="searchValue" [(ngModel)]="searchValue" />
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12">
				<gp-spinner-button [pink]="true" marginTop="27px" pull="right" text="Adicionar permissão de pagamento"
					icon="plus" [loading]="loading" (click)="onAddPermission()" [disabled]="!canAddOrRemoveFilters">
				</gp-spinner-button>

				<gp-spinner-button type="button" [actionSearch]="true" pull="right" text="Pesquisar"
					[loading]="loading" [disabled]="loading" (click)="onSearchFilters()"
					marginRight="5px" marginTop="27px">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card title="Filtros encontrados" [last]="true">
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-grid #filtersGrid name="filtersGrid" [rows]="filters"
					[columns]="['Tipo do Boleto', 'Data Inclusão do Filtro']"
					[fields]="['typeBill', 'formattedCreatedDate']"
					[showActive]="true" [showPagination]="true" [showTotalPages]="false"
					[pageSize]="params.limit" [showEdit]="false" [showDelete]="canAddOrRemoveFilters"
					[loading]="loading" [showCustom]="true" customIcon="eye"
					(onCustom)="onClickShowFilter($event)"(onDelete)="onDisableFilter($event)"
					(onPageChanged)="onPageChanged($event)">
				</gp-grid>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<campaign-catalog-bill-payments-active-filters-viewer #filterViewer></campaign-catalog-bill-payments-active-filters-viewer>
	<campaign-catalog-bill-payments-adding-permission [campaignId]="campaignId" #addPermission></campaign-catalog-bill-payments-adding-permission>
</div>
