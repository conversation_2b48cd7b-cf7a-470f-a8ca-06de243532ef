<gp-alert #gpAlert [overlay]="true"></gp-alert>
<gp-modal [title]="modalTitle" width="800px" (onClose)="close()" #modal>
	<form (ngSubmit)="sendOrderRefund()" *ngIf="isMasterOrChildOrderRefund">
		<div class="row" *ngIf="hasAvalidTransactionDetails">
			<div class="form-group col-sm-12">
				<label>Instruções de estorno para metodo de pagamento pix*:</label>
				<p>Para realizar o estorno deste pedido, anote os dados da transação abaixo e abra um chamado junto ao Banco.
					 Após receber o ID da transação informado pelo banco, volte a esta tela para registrar o ID e prosseguir com o estorno do pedido</p>
			</div>

			<div class="form-group col-sm-12">
				<h5><strong>Detalhes da transação</strong></h5>
			</div>

			<div class="form-group col-sm-12">
				<p class="pull-left"><strong>Identificador da Transação</strong></p>
				<p class="pull-right mr">{{ transactionDetails.transactionId}}</p>
			</div>

			<div class="form-group col-sm-12">
				<p class="pull-left"><strong>Data da Transação</strong></p>
				<p class="pull-right mr">{{ transactionDetails.transactionDate | datetimezone }}</p>
			</div>

			<div class="form-group col-sm-12" *ngIf="hasAvalidTransactionDetailsMetadata">
				<hr class="hidden-print"/>
				<p class="pull-left"><strong>Informações Adicionais da Transação</strong></p>
				<gp-grid name="metadataGrid" [rows]="transactionDetails.metadata" [columns]="['Descrição', 'Tipo']" [fields]="['key', 'value']"
					[loading]="sending" [showActive]="false" [showPagination]="false" [showEdit]="false" [showDelete]="false" emptyMessage="Nenhuma meta dado localizado.">
			   </gp-grid>
			</div>

			<div class="form-group col-sm-12">
				<gp-simple-input label="Identificador da Transação de Estorno" [required]="true">
					<input type="text" class="form-control" name="transactionId" [(ngModel)]="refund.externalTransactionId" />
				</gp-simple-input>
			</div>
		</div>

		<div class="row">

			<div class="form-group col-sm-12">
				<label>Motivo*:</label>
				<select class="form-control" name="refundReason" required [(ngModel)]="refund.refundReason">
					<option value="">Selecione</option>
					<option *ngFor="let reason of refundReasons" [value]="reason.key">{{reason.value}}</option>
				</select>
			</div>
		</div>
		<div class="row">
			<div grid="12" [group]="true">
				<label>Nota adicional*:</label>
				<textarea class="form-control" name="refundNote" [(ngModel)]="refund.refundNote"></textarea>
			</div>
		</div>
		<div class="row">
			<div class="col-sm-12" *ngIf="canRefund">
				<gp-spinner-button type="submit" [text]="fieldButtonText" pull="right" [pink]="true"  icon="minus-square"
					[loading]="sending" loadingText="Estornando">
				</gp-spinner-button>
			</div>
			<div class="col-md-12 div-alert">
				<gp-alert #gpAlert></gp-alert>
			</div>
		</div>
	</form>


	<div *ngIf="isShowingRefundList">
		<div class="row">
			<div class="text-center" grid="6 4">
				<div gp-knob [value]="originalAmount" [options]="optOriginalAmount"></div>
			</div>
			<div class="text-center" grid="6 4">
				<div gp-knob [value]="refundedAmount" [options]="optRefundedAmount"></div>
			</div>
			<div class="text-center" grid="6 4">
				<div gp-knob [value]="totalAmount" [options]="optTotalAmount"></div>
			</div>
		</div>
		<hr/>
		<table class="table table-bordered table-striped table-hover">
			<thead>
				<tr>
					<th>Data</th>
					<th>Descrição</th>
					<th>Valor</th>
					<th>Motivo</th>
					<th>Nota</th>
					<th>Erro</th>
				</tr>
			</thead>
			<tbody>
				<tr *ngFor="let refund of item?.refunds">
					<td>{{refund.refundDate | datetimezone:order?.timezone}}</td>
					<td>{{refund.description}}</td>
					<td class="text-right">
						<div>{{refund.amount.points | number:'1.0'}}</div>
						<div>R$ {{refund.amount.currency | number:'1.2'}}</div>
					</td>
					<td>{{getRefundReasonDescription(refund.reason)}}</td>
					<td>{{refund.note}}</td>
					<td *ngIf="refund.errorOccurred">Sim</td>
					<td *ngIf="!refund.errorOccurred">Não</td>
				</tr>
				<tr *ngIf="!item || !item.refunds || !item.refunds.length">
					<td colspan="4">Nenhum estorno efetuado.</td>
				</tr>
			</tbody>
		</table>
	</div>

	<form (ngSubmit)="sendItemRefund()" *ngIf="isItemRefund">
		<div class="row">
			<div class="text-center" grid="6 4">
				<div gp-knob [value]="originalAmount" [options]="optOriginalAmount"></div>
			</div>
			<div class="text-center" grid="6 4">
				<div gp-knob [value]="refundedAmount" [options]="optRefundedAmount"></div>
			</div>
			<div class="text-center" grid="6 4">
				<div gp-knob [value]="totalAmount" [options]="optTotalAmount"></div>
			</div>
		</div>

		<hr/>

		<div class="row" *ngIf="canRefundPartial">
			<div grid="12 6" [group]="true">
				<label>Tipo do estorno</label>
				<div>
					<gp-input-radio name="refundType" pull="left" radioValue="Full" label="Inteiro" [(ngModel)]="refund.refundType"></gp-input-radio>
					<gp-input-radio name="refundType" pull="left" radioValue="Partial" label="Parcial" [(ngModel)]="refund.refundType" *ngIf="!hasAvalidPixPaymentMethod"></gp-input-radio>
				</div>
			</div>
		</div>

		<div class="row bottom-m1">
			<div grid="12 12">
				<label>Produto:</label>
				<span>{{item?.name}} - {{item?.skuCode}}</span>
			</div>
		</div>
		<div class="row">
			<div class="col-sm-12">
				<table class="table table-bordered table-striped table-hover">
					<thead>
						<tr>
							<th>Quantidade</th>
							<th class="text-center">Valor Unit.</th>
							<th class="text-center">Frete</th>
							<th class="text-center">Total</th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td class="text-center" style="width:230px">
								<div *ngIf="!isPartial">{{item?.quantity}}</div>
								<div style="width:213px;" *ngIf="isPartial">
									<gp-input-mask name="refundQuantity" [onlyInteger]="true" [integers]="3" size="sm"
										[(ngModel)]="refund.refundQuantity"></gp-input-mask>
								</div>
							</td>
							<td>
								<div class="chkPartial" *ngIf="isPartial && hasPriceToRefund">
									<gp-input-checkbox name="itemPrice" [(ngModel)]="refund.refundPrice"></gp-input-checkbox>
								</div>
								<div class="text-right">
									<div>{{item?.unitPrice?.points | number:'1.0'}}</div>
									<div>R$ {{item?.unitPrice?.currency | number:'1.2'}}</div>
								</div>
							</td>
							<td>
								<div class="chkPartial" *ngIf="isPartial && hasShippingToRefund">
									<gp-input-checkbox name="itemShipping" [(ngModel)]="refund.refundShipping"></gp-input-checkbox>
								</div>
								<div class="text-right">
									<div>{{item?.shippingCost?.points | number:'1.0'}}</div>
									<div>R$ {{item?.shippingCost?.currency | number:'1.2'}}</div>
								</div>
							</td>
							<td class="text-right">
								<div>{{calculateSubtotalPoints(item) | number:'1.0'}}</div>
								<div>R$ {{calculateSubtotalCurrency(item) | number:'1.2'}}</div>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
		<div class="row">
			<div class="form-group col-sm-12">
				<label>Motivo*:</label>
				<select class="form-control" name="refundReason" required [(ngModel)]="refund.refundReason">
					<option value="">Selecione</option>
					<option *ngFor="let reason of refundReasons" [value]="reason.key">{{reason.value}}</option>
				</select>
			</div>
		</div>
		<div class="row">
			<div grid="12" [group]="true">
				<label>Nota adicional*:</label>
				<textarea class="form-control" name="refundNote" [(ngModel)]="refund.refundNote"></textarea>
			</div>
		</div>
		<div class="row">
			<div class="col-sm-12" *ngIf="canRefund">
				<gp-spinner-button type="submit" text="Confirmar Estorno" pull="right" [pink]="true"  icon="minus-square" marginTop="1em"
					[loading]="sending" loadingText="Estornando">
				</gp-spinner-button>
			</div>
			<!-- <div class="col-md-12 div-alert">
				<gp-alert #gpAlert></gp-alert>
			</div> -->
		</div>
	</form>
</gp-modal>
