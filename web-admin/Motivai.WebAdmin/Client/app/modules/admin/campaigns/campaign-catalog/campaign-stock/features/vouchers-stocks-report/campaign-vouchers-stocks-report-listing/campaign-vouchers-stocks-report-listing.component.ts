import { Component, OnInit, ViewChild } from '@angular/core';
import { AuthStore } from '../../../../../../../../core/auth/auth.store';

import { GpAlertComponent } from '../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpGridComponent } from '../../../../../../../../shared/components/gp-grid/gp-grid.component';
import { FormatHelper } from '../../../../../../../../shared/formatters/format-helper';

import { CampaignStockService } from '../../../campaign-stock.service';

@Component({
  selector: 'campaign-vouchers-stocks-report-listing',
  templateUrl: './campaign-vouchers-stocks-report-listing.component.html'
})
export class CampaignVouchersStocksReportListingComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @ViewChild('skuGrid') skuGrid: GpGridComponent;

  stocks: any[] = [];

  params: any = {
    skip: 0,
    limit: 20,
    skuCode: null,
    maximumImportDate: null,
    groupByBatch: true,
    groupByExpirationDate: true,
    groupDateExpiration: true,
  };

  loading: boolean = false;

  constructor(private authStore: AuthStore, private stockService: CampaignStockService) { }

  get canViewStockReport() {
    return this.authStore.role.CAMPAIGNS_CATALOG_VOUCHER_STOCK_REPORT;
  }

  get hasRows() {
    return this.stocks && this.stocks.length > 0;
  }

  ngOnInit() {
    if (!this.canViewStockReport) {
      this.gpAlert.showWarning('Você não tem permissão para acessar esse módulo.');
    }
  }

  onSearchStock() {
    this.skuGrid.resetPagination();
    this.findBySkuCode();
  }

  private findBySkuCode() {
    this.loading = true;
    this.stockService.findBySkuCode(null, this.params).subscribe(
      stocks => {
        if (stocks && stocks.length > 0) {
          stocks.forEach(s => {
            s.formattedBatchCreateDate = FormatHelper.formatDate(s.batchCreateDate);
            s.formattedExpirationDate = FormatHelper.formatDate(s.expirationDate);
            s.formattedgroupDateExpiration = FormatHelper.formatDate(s.groupDateExpiration);

          });
          this.stocks = stocks;
        } else {
          this.stocks = [];
        }
        this.loading = false;
      },
      err => {
        this.loading = false;
        this.gpAlert.handleAndShowError(err);
      }
    );
  }

  onPageChanged(event: any) {
    if (event) {
      this.params.skip = event.skip;
      this.params.limit = event.limit;
      this.findBySkuCode();
    }
  }

  onClickExport() {
    this.loading = true;
    this.stockService.findBySkuCodeAndExportToCsv(null, this.params).subscribe(
      response => {
        window.open(response, '_blank');
        this.loading = false;
      },
      err => {
        this.loading = false;
        this.gpAlert.showError(err);
      }
    );
  }

  clear() {
    this.params = {
      skip: 0,
      limit: 20,
      skuCode: '',
      maximumImportDate: '',
      groupByBatch: false,
      groupByExpirationDate: false
    };
    this.skuGrid.resetPagination();
    this.loading = false;
  }
}
