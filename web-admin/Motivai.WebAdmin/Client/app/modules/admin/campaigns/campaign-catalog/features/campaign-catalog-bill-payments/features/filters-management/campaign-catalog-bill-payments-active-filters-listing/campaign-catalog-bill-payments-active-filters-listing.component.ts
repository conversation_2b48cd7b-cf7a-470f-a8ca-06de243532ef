import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { AuthStore } from '../../../../../../../../../core/auth/auth.store';

import { Gp<PERSON>lertComponent } from '../../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpGridComponent } from '../../../../../../../../../shared/components/gp-grid/gp-grid.component';
import { FormatHelper } from '../../../../../../../../../shared/formatters/format-helper';
import { Item } from '../../../../../../../../../shared/models/item';

import { SearchField } from '../models/searchField';
import { TypeBill } from '../models/typeBills';

import { CampaignCatalogBillPaymentsFiltersManagementService } from '../services/campaing-catalog-bill-payments-filters-management.service';

import { CampaignCatalogBillPaymentsActiveFiltersViewerComponent } from '../campaign-catalog-bill-payments-active-filters-viewer/campaign-catalog-bill-payments-active-filters-viewer.component';
import { CampaignCatalogBillPaymentsAddingPermissionComponent } from './campaign-catalog-bill-payments-adding-permission/campaign-catalog-bill-payments-adding-permission.component';

@Component({
	selector: 'campaign-catalog-bill-payments-active-filters-listing',
	templateUrl: './campaign-catalog-bill-payments-active-filters-listing.component.html'
})
export class CampaignCatalogBillPaymentsActiveFiltersListingComponent implements OnInit {
	@ViewChild('gpAlert') gpAlert: GpAlertComponent;
	@ViewChild('filtersGrid') filtersGrid: GpGridComponent;
	@ViewChild('filterViewer') filterViewer: CampaignCatalogBillPaymentsActiveFiltersViewerComponent;
	@ViewChild('addPermission') addPermission: CampaignCatalogBillPaymentsAddingPermissionComponent;
	@Output('onDelete') onDelete: EventEmitter<any> = new EventEmitter();
	@Input('campaignId') campaignId: string;

	searchFields: Item[] = [
		Item.of(SearchField.DEFAULT, "Nenhum"),
		Item.of(SearchField.PAYER_DOCUMENT, 'CPF/CNPJ do Pagador'),
		Item.of(SearchField.RECEIVER_DOCUMENT, 'CPF/CNPJ do Beneficiário')
	];

	searchByField: SearchField;
	searchValue: string;
	lableSearchValue: string = 'Informe o valor a ser pesquisado';

	typeBills: Item[] = [
		Item.of(TypeBill.UTILITY_BILL, 'Conta Concessionária'),
		Item.of(TypeBill.INVOICES, 'Ficha Compensação')
	];

	typeBill: TypeBill = TypeBill.INVOICES;

	params: any = {
		skip: 0,
		limit: 10
	};

	filters: any[] = [];

	loading: boolean = false;
	constructor(
		private authStore: AuthStore,
		private billPaymentsFiltersManagementService: CampaignCatalogBillPaymentsFiltersManagementService
	) { }

	ngOnInit() {
		this.clear();
	}

	get canAddOrRemoveFilters(): boolean {
		return this.authStore.role.PERMISSION_CAMPAIGNS_CATALOG_BILLS_PAYMENT_ADD_OR_REMOVE_FILTERS;
	}

	onChangeSearchField(searchByField: SearchField): void {
		this.searchByField = searchByField;
		this.lableSearchValue = this.handleLableSearchValue();
	}

	private handleLableSearchValue(): string {
		switch (this.searchByField) {
			case SearchField.PAYER_DOCUMENT: return 'Informe o CPF/CNPJ do pagador';
			case SearchField.RECEIVER_DOCUMENT: return 'Informe o CPF/CNPJ do beneficiário';
			default: return 'Informe o valor a ser pesquisado';
		}
	}

	onSearchFilters(): void {
		if (!this.validate())
			return;
		this.searchValue = FormatHelper.removeMask(this.searchValue);
		this.filtersGrid.resetPagination();
		this.resetSkipAndLimit();
		this.findActiveFilters();
	}

	private validate(): boolean {
		if (!this.typeBill) {
			this.gpAlert.showWarning('Informe o tipo do boleto');
			return false;
		}
		if ((this.searchByField !== null && this.searchByField != SearchField.DEFAULT) && !this.searchValue) {
			this.gpAlert.showWarning('Informe o valor a ser pesquisado');
			return false;
		}
		return true;
	}

	private resetSkipAndLimit(): void {
		this.params.skip = 0;
		this.params.limit = 10;
	}

	private findActiveFilters(): void {
		this.loading = true;
		this.setParams();
		this.billPaymentsFiltersManagementService.findActivesFiltersBy(this.campaignId, this.params)
			.subscribe(response => {
				this.loading = false;
				if (response && response.length > 0) {
					this.filters = response;
					this.handleFilters();
				} else {
					this.filters = [];
				}
			},
			err => {
				this.loading = false;
				this.gpAlert.handleAndShowError(err);
			}
		);
	}

	private setParams(): void {
		this.params.searchByField = this.searchByField;
		this.params.searchValue = this.searchValue;
		this.params.typeBill = this.typeBill;
	}

	private handleFilters(): void {
		this.filters.forEach(b => {
			b.formattedCreatedDate = FormatHelper.formatDateWithTimezone(b.createDate);
			b.typeBill = this.handleTypeBill();
		});
	}

	private handleTypeBill(): string {
		if (this.typeBill === 'CONTACONCESSIONARIA') return 'Conta Concessionária';
		if (this.typeBill === 'FICHACOMPENSACAO') return 'Ficha Compensação';
		return 'Indefinido';
	}

	realodIfNeed() {
		if (this.validate())
			this.findActiveFilters();
	}

	onAddPermission(): void {
		this.addPermission.openModal();
	}

	onClickShowFilter(filter: any): void {
		this.filterViewer.setFilter(filter);
		this.filterViewer.openModal();
	}

	onDisableFilter(event: any): void {
		if (event && event.id) {
			this.gpAlert.confirm('Confirma a desativação do filtro?').then(result => {
				if (result && result.value) {
					this.desableFilterFromAllowedList(event.id);
				}
			});
		}
	}

	private desableFilterFromAllowedList(filterId: string): void {
		this.loading = true;
		this.setParams();
		this.billPaymentsFiltersManagementService.desableFilterFromAllowedList(this.campaignId, filterId)
			.subscribe(response => {
				this.loading = false;
				if (response) {
					this.gpAlert.showSuccess('Filtro desativado com sucesso');
					this.findActiveFilters();
				} else {
					this.gpAlert.showWarning('Ocorreu um erro ao desativar o filtro. Tente novamente');
				}
			},
			err => {
				this.loading = false;
				this.gpAlert.handleAndShowError(err);
			}
		);
	}

	onPageChanged(event: any): void {
		if (event) {
			this.params.skip = event.skip;
			this.params.limit = event.limit;
			this.findActiveFilters();
		}
	}

	clear(): void {
		this.params = {
			skip: 0,
			limit: 10
		}
		this.filters = [];
		this.searchByField = null;
		this.searchValue = null;
		this.typeBill = TypeBill.INVOICES;
		this.lableSearchValue = this.handleLableSearchValue();
		this.filtersGrid.resetPagination();
		this.loading = false;
	}
}
