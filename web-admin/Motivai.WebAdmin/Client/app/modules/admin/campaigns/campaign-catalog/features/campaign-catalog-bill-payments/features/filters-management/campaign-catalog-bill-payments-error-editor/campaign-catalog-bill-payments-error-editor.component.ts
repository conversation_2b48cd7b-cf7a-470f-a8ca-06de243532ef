import { Component, ViewChild } from '@angular/core';
import { AuthStore } from '../../../../../../../../../core/auth/auth.store';

import { GpAlertComponent } from '../../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { FormatHelper } from '../../../../../../../../../shared/formatters/format-helper';

import { CampaignCatalogBillPaymentsFiltersManagementService } from '../services/campaing-catalog-bill-payments-filters-management.service';

import { CampaignCatalogBillPaymentsAddingFiltersComponent } from './campaign-catalog-bill-payments-adding-filters/campaign-catalog-bill-payments-adding-filters.component';

@Component({
	selector: 'campaign-catalog-bill-payments-error-editor',
	templateUrl: './campaign-catalog-bill-payments-error-editor.component.html'
})
export class CampaignCatalogBillPaymentsErrorEditorComponent {
	@ViewChild('alert') alert: GpAlertComponent;
	@ViewChild('addFilters') addFilters: CampaignCatalogBillPaymentsAddingFiltersComponent;

	billsPaymentConsultsError: any = {
		partners: {},
		billDetails: {},
		userDetails: {}
	};

	loading: boolean = false;
	constructor(
		private authStore: AuthStore,
		private billPaymentsFiltersManagementService: CampaignCatalogBillPaymentsFiltersManagementService
	) { }

	get canAddFilters() {
		return this.billsPaymentConsultsError && (this.billsPaymentConsultsError.status === "CREATED" || this.billsPaymentConsultsError.status === "REJECTED");
	}

	get canReject() {
		return this.hasPermissionToReject() && this.billsPaymentConsultsError && this.billsPaymentConsultsError.status === "CREATED";
	}

	private hasPermissionToReject(): boolean {
		return this.authStore.role.PERMISSION_CAMPAIGNS_CATALOG_BILLS_PAYMENT_ADD_OR_REMOVE_FILTERS;
	}

	clickedEditRegister(register: any) {
		this.billsPaymentConsultsError = register || this.reset();
		this.handleBillPaymentError();
	}

	private handleBillPaymentError(): void {
		if (!this.billsPaymentConsultsError.billDetails)
			this.billsPaymentConsultsError.billDetails = {};
		if (!this.billsPaymentConsultsError.userDetails)
			this.billsPaymentConsultsError.userDetails = {};

		const payerDocument = this.billsPaymentConsultsError.billDetails.payerDocument || '';
		this.billsPaymentConsultsError.billDetails.payerDocument = FormatHelper.formatDocument(payerDocument);

		const receiverDocument = this.billsPaymentConsultsError.billDetails.receiverDocument || '';
		this.billsPaymentConsultsError.billDetails.receiverDocument = FormatHelper.formatDocument(receiverDocument);

		const userDocument = this.billsPaymentConsultsError.userDetails.userDocument || '';
		this.billsPaymentConsultsError.userDetails.userDocument = FormatHelper.formatDocument(userDocument);

		const userAccountOperatorDocument = this.billsPaymentConsultsError.userDetails.userAccountOperatorDocument || '';
		this.billsPaymentConsultsError.userDetails.userAccountOperatorDocument = FormatHelper.formatDocument(userAccountOperatorDocument);
	}

	private reset(): void {
		this.billsPaymentConsultsError = {
			partners: {},
			billDetails: {},
			userDetails: {}
		};
	}

	onAddPermission(): void {
		this.addFilters.setBillsPaymentConsultsError(this.billsPaymentConsultsError);
		this.addFilters.openModal();
	}

	onRejectBill(): void {
		this.alert.confirm('Confirma a reijeição do boleto?').then(result => {
			if (result && result.value) {
				this.loading = true;
				this.billPaymentsFiltersManagementService.rejectBill(this.billsPaymentConsultsError.campaignId, this.billsPaymentConsultsError.id)
					.subscribe(resp => {
						this.loading = false;
						if (resp) {
							this.handleStatus();
							this.alert.showSuccess("Boleto rejeitado com sucesso");
						} else {
							this.alert.showWarning("Não foi possível rejeitar o boleto. Tente novamente");
						}
					},
					err => {
						this.loading = false;
						this.alert.handleAndShowError(err);
					}
				);
			}
		});
	}

	private handleStatus(): void {
		this.billsPaymentConsultsError.status = "REJECTED";
		this.billsPaymentConsultsError.formattedStatus = "REJEITADO";
	}

	clear(): void {
		this.reset();
	}
}
