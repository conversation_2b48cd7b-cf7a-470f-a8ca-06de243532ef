<gp-card [first]="true">
    <gp-form-row>
        <gp-form-col cols="12 4">
            <label>Ativo</label>
			<div>
				<gp-switch [(ngModel)]="customizationOrderConfirmedPage.active"></gp-switch>
			</div>
        </gp-form-col>
    </gp-form-row>
    <hr/>
    <gp-form-row>
        <gp-form-col cols="12 4">
            <gp-simple-input [required]="false" label="Por tipo de produto">
                <gp-input-checkbox name="isProductType" (change)="changeLayoutState(customizationOrderConfirmedPage.isProductType)" [(ngModel)]="customizationOrderConfirmedPage.isProductType"></gp-input-checkbox>
            </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 4" *ngIf="customizationOrderConfirmedPage.isProductType && !customizationOrderConfirmedPage.isLayoutType">
			<label>Tipo do Produto</label>
            <gp-select name="layoutTypes" placeholder="Tipo de produto" [items]="productTypes" [(ngModel)]="customizationOrderConfirmedPage.productType"></gp-select>
        </gp-form-col>
    </gp-form-row>
    <hr/>
    <gp-form-row>
        <gp-form-col cols="12 4">
            <gp-simple-input [required]="false" label="Por tipo de layout">
                <gp-input-checkbox name="isLayoutType" (change)="changeProductState(customizationOrderConfirmedPage.isLayoutType)" [(ngModel)]="customizationOrderConfirmedPage.isLayoutType"></gp-input-checkbox>
            </gp-simple-input>
        </gp-form-col>
        <gp-form-col cols="12 4" *ngIf="customizationOrderConfirmedPage.isLayoutType && !customizationOrderConfirmedPage.isProductType">
			<label>Tipo do Layout</label>
            <gp-select name="layoutType" placeholder="Tipo de layout" [items]="layoutTypes" [(ngModel)]="customizationOrderConfirmedPage.layoutType"></gp-select>
        </gp-form-col>
    </gp-form-row>

    <hr/>
    <gp-form-row>
        <gp-form-col cols="12">
            <label>Customização</label>
            <gp-editor  id="content" name="content" [(ngModel)]="customizationOrderConfirmedPage.content"></gp-editor>
        </gp-form-col>
    </gp-form-row>  
</gp-card>

<spinner [overlay]="true" [show]="loading"></spinner>
<gp-alert [overlay]="true" #alert></gp-alert>
<gp-card>
    <gp-form-row>
        <gp-form-col cols="12"  [inputGroup]="false">
            <gp-spinner-button type="button" [pink]="true" text="Salvar" loadingText="Processando" pull="right" [loading]="loading"
             (click)="saveCustomization()"></gp-spinner-button>
            <gp-spinner-button type="button" text="Limpar" pull="right" [loading]="loading"
             (click)="clear()" marginRight="5px"></gp-spinner-button>
        </gp-form-col>
    </gp-form-row>
</gp-card>