import { Observable } from 'rxjs/Observable';
import { Injectable } from '@angular/core';
import { ApiService } from '../../../../../../core/api/api.service';

@Injectable()
export class CampaignOrdersImportService {
    constructor(private _api: ApiService) {}

    findTemporaryOrders(campaignId: string, batchImportId: string, status: string): Observable<any[]> {
        let params: any = {};
        if (status) params.status = status;

        return this._api.get(`/api/campaigns/${campaignId}/batchimport/${batchImportId}/temporaryorders`, params);
    }

    getTemporaryOrder(campaignId: string, batchImportId: string, temporaryOrderId: string): Observable<any> {
        return this._api.get(`/api/campaigns/${campaignId}/batchimport/${batchImportId}/temporaryorders/${temporaryOrderId}`);
    }

    updateGeneralInfo(campaignId: string, batchImportId: string, temporaryOrderId: string, info: any): Observable<any> {
        return this._api.put(`/api/campaigns/${campaignId}/batchimport/${batchImportId}/temporaryorders/${temporaryOrderId}/generalinfo`, info, 60000);
    }

    updateShippingAddress(campaignId: string, batchImportId: string, temporaryOrderId: string, address: any): Observable<any> {
        return this._api.put(`/api/campaigns/${campaignId}/batchimport/${batchImportId}/temporaryorders/${temporaryOrderId}/shippingaddress`, address, 60000);
    }

    updateOrderItem(campaignId: string, batchImportId: string, temporaryOrderId: string, skuCode: string, item: any): Observable<any> {
        return this._api.put(`/api/campaigns/${campaignId}/batchimport/${batchImportId}/temporaryorders/${temporaryOrderId}/items/${skuCode}`, item, 60000);
    }

    removeOrderItem(campaignId: string, batchImportId: string, temporaryOrderId: string, skuCode: string): Observable<boolean> {
        return this._api.delete(`/api/campaigns/${campaignId}/batchimport/${batchImportId}/temporaryorders/${temporaryOrderId}/items/${skuCode}`);
    }

    removeTemporaryOrder(campaignId: string, batchImportId: string, temporaryOrderId): Observable<boolean> {
        return this._api.delete(`/api/campaigns/${campaignId}/batchimport/${batchImportId}/temporaryorders/${temporaryOrderId}`);
    }

    approve(campaignId: string, batchImportId: string): Observable<boolean> {
        return this._api.put(`/api/campaigns/${campaignId}/batchimport/${batchImportId}/temporaryorders/approve`);
    }

    refuse(campaignId: string, batchImportId: string): Observable<boolean> {
        return this._api.put(`/api/campaigns/${campaignId}/batchimport/${batchImportId}/temporaryorders/refuse`);
    }
}
