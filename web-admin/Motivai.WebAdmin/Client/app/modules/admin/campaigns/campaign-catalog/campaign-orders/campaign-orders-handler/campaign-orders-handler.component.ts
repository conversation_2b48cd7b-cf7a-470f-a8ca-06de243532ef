import { Component, OnInit, ViewChild, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { TabsetComponent } from 'ng2-bootstrap/tabs/tabset.component';

import { CampaignOrdersHandlerListComponent } from './campaign-orders-handler-list/campaign-orders-handler-list.component';
import { CampaignOrdersHandlerEditComponent } from './campaign-orders-handler-edit/campaign-orders-handler-edit.component';
import { CampaignStore } from '../../../campaign.store';
import { RxjsHelpers } from '../../../../../../shared/helpers/rxjs-helpers';

@Component({
    selector: 'campaign-orders-handler',
    templateUrl: 'campaign-orders-handler.component.html'
})
export class CampaignOrdersHandlerComponent implements OnInit, OnDestroy {
    campaignId: string;
    orderId: string;

    private _campaign$: Subscription;

    @ViewChild('tabs') tabs: TabsetComponent;
    @ViewChild('list') list: CampaignOrdersHandlerListComponent;
    @ViewChild('edit') edit: CampaignOrdersHandlerEditComponent;

    constructor(private router: Router, private _campaignStore: CampaignStore) { }

    ngOnInit() {
        this._campaign$ = this._campaignStore.asObservable
            .subscribe(campaignId => {
                this.campaignId = campaignId;
                if (!this.campaignId) {
                    this.router.navigate(['/campanha']);
                }
            });

        this.setDetailTabDisabled(true);
    }

    ngOnDestroy() {
        RxjsHelpers.unsubscribe(this._campaign$);
    }

    onEdit($event) {
        this.orderId = $event.id;
        this.setDetailTabDisabled(false);
        this.tabs.tabs[1].active = true;
    }

    clearEditForm() {
        this.orderId = '';
        this.list.findOrders();
        this.edit.clear();
        this.setDetailTabDisabled(true);
    }

    setDetailTabDisabled(disabled: boolean) {
        this.tabs.tabs[1].disabled = disabled;
    }
}
