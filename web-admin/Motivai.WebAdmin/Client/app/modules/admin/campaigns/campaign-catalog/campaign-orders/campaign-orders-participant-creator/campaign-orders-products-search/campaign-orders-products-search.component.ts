import { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { UsersService } from '../../../../../../../core/services/users.service';

import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { FormatHelper } from '../../../../../../../shared/formatters/format-helper';
import { Item } from '../../../../../../../shared/models/item';
import { CampaignService } from '../../../../campaign.service';

import { CampaignOrdersProductsService } from '../services/campaign-orders-products.service';

@Component({
	selector: 'campaign-orders-products-search',
	templateUrl: './campaign-orders-products-search.component.html'
})
export class CampaignOrdersProductsSearchComponent {
	@ViewChild('gpAlert') gpAlert: GpAlertComponent;
	@Output('onMovedShoppingCart') productsEmitter: EventEmitter<any> = new EventEmitter<any>();

	_campaignId: string;
	_document: any;
	term: string;
	skuCode: string;

	productsCombo: Item[] = [];
	products: any[] = [];

	selectedProduct: string;

	product: any = {};
	quantityProducts: number = 0;

	shoppingCart: any[] = [];

	@Input() set campaignId(value: string) {
		this._campaignId = value
		this.loadCampaignInfo();
	}

	@Input() set document(value: string) {
		this._document = value
	}

	hasCatalog: boolean = true;
	catalogUrl: string;
	loading: boolean = false;

	constructor(
		private campaignOrdersProductsService: CampaignOrdersProductsService,
		private usersService: UsersService,
		private campaignService: CampaignService
	) { }

	private loadCampaignInfo() {
		this.loading = true;
		this.campaignService.getGeneralInfo(this._campaignId)
			.subscribe(campaignInfo => {
				this.loading = false;
				if (!campaignInfo) {
					this.gpAlert.showWarning('Não foi possível carregar os dados da campanha, por favor, tente novamente.');
					return;
				}
				this.hasCatalog = campaignInfo.modality == 'OnlyCatalog' || campaignInfo.modality == 'CatalogWithSite';
				this.catalogUrl = campaignInfo.url;
			},
			err => {
				this.loading = false;
				this.gpAlert.handleAndShowError(err);
			}
		);
	}

	onStartCatalogAccess() {
		if (this.isNullOrEmpty(this._document)) {
			this.gpAlert.showWarning('Documento do participante inválido');
			return;
		}
		this.gpAlert.confirm(`Atenção: Ao prosseguir com esta ação você será redirecionado para o catálogo em nome do participante.
		Confirma essa ação ?`)
		.then(result => {
		  	if (result && result.value) {
				this.loading = true;
				this.gpAlert.clear();
				this.usersService.authenticateByPlatformSso(this._campaignId, null, { document : this._document })
					.subscribe(token => {
						if (token) {
							this.loading = false;
							window.open(this.catalogUrl.concat(`/integration/sso?token=${token}`), '_blank');
						} else {
							this.loading = false;
							this.gpAlert.showWarning('Não foi possível autenticar o participante.');
						}
					},
					err => {
						this.loading = false;
						this.gpAlert.handleAndShowError(err);
					}
				);
			}
		});
	}

	onSearchProducts() {
		this.loading = true;
		this.selectedProduct = '';
		this.campaignOrdersProductsService.getProductsByTermOrSkuCode(this._campaignId, this.term, this.skuCode)
			.subscribe(products => {
				if (products && products.length > 0) {
					this.productsCombo = products.map(p =>
						Item.of(p.skuId, `${p.name} - Código SKU: ${p.skuCode}
						- Parceiro: ${p.partnerName} - Valor: ${p.price}`)
					);
					this.products = products;
				} else {
					this.clearProducts();
					this.gpAlert.showWarning('Nenhum produto encontrado.');
				}
				this.loading = false;
			},
			err => {
				this.clearProducts();
				this.loading = false;
				this.gpAlert.handleAndShowError(err);
			}
		);
	}

	private clearProducts(): void {
		this.products = [];
		this.productsCombo = [];
	}

	onSelectedProduct(skuId: string) {
		if (this.isNullOrEmpty(skuId)) {
			return;
		}
		this.product = this.findProductBySkuId(skuId);
		this.quantityProducts = 1;
	}

	private findProductBySkuId(skuId: string) {
		return this.products.find(p => p.skuId === skuId);
	}

	onAddProduct() {
		if (this.isNullOrEmpty(this.selectedProduct)) {
			return this.gpAlert.showWarning("Selecione um produto");
		}
		if (this.quantityProducts < 1) {
			return this.gpAlert.showWarning("Informe a quantidade");
		}
		this.addOrUpdateProductInShoppingCart(this.product);
		this.productsEmitter.emit(this.shoppingCart);
	}

	private addOrUpdateProductInShoppingCart(product: any) {
		const index = this.findProductIndexBySkuId(product.skuId);
		if (index < 0) {
			product['quantity'] = Number(this.quantityProducts);
			if (this.isCurrency()) {
				const total = this.removeCurrencyMask(product.price) * Number(this.quantityProducts);
				product['total'] = this.applyCurrencyMask(total);

			} else {
				product['total'] = product.price * Number(this.quantityProducts);
			}
			this.shoppingCart.push(product);
			return;
		}
		this.shoppingCart[index]['quantity'] += Number(this.quantityProducts);

		if (this.isCurrency()) {
			let totalFormatted = this.removeCurrencyMask(this.shoppingCart[index]['total']);
			totalFormatted += this.removeCurrencyMask(product.price) * Number(this.quantityProducts);

			this.shoppingCart[index]['total'] = this.applyCurrencyMask(totalFormatted);
		} else {
			this.shoppingCart[index]['total'] += product.price * Number(this.quantityProducts);
		}
	}

	onCancelProduct() {
		this.product = {};
		this.term = '';
		this.selectedProduct = '';
	}

	onRemoveProduct(product: any) {
		this.removeProductFromShoppingCart(product);
		this.productsEmitter.emit(this.shoppingCart);
	}

	private removeProductFromShoppingCart(product: any) {
		const index = this.findProductIndexBySkuId(product.skuId);
		if (index < 0)
			return;

		if (this.shoppingCart[index]['quantity'] === 1) {
			this.shoppingCart.splice(index, 1);
			return;
		}
		this.shoppingCart[index]['quantity']--;

		if (this.isCurrency()) {
			let totalFormatted = this.removeCurrencyMask(this.shoppingCart[index]['total']);
			totalFormatted -= this.removeCurrencyMask(product.price);

			this.shoppingCart[index]['total'] = this.applyCurrencyMask(totalFormatted);
		} else {
			this.shoppingCart[index]['total'] -= product.price;
		}
	}

	private findProductIndexBySkuId(skuId: string) {
		return this.shoppingCart.findIndex(p => p.skuId === skuId);
	}

	private isCurrency() {
		return true;
	}

	private removeCurrencyMask(value: string) {
		return Number(FormatHelper.removeCurrencyMask(value).replace('R$',''));
	}

	private applyCurrencyMask(value: number) {
		return value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });
	}

	get foundProducts() {
		return this.products.length > 0;
	}

	get hasSelectedProduct() {
		return !this.isNullOrEmpty(this.selectedProduct);
	}

	get shoppingCartIsNotEmpty() {
		return this.shoppingCart.length > 0;
	}

	private isNullOrEmpty(value: string) {
		return !(typeof value === 'string' && value.trim().length > 0);
	}

	clear() {
		this.productsCombo = [];
		this.products = [];
		this.product = {}
		this.selectedProduct = '';
		this.shoppingCart = [];
	}
}
