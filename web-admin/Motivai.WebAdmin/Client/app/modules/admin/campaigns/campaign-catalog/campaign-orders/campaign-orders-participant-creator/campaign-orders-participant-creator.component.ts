import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';

import { FormatHelper } from './../../../../../../shared/formatters/format-helper';

import { Subscription } from 'rxjs';

import { AuthStore } from '../../../../../../core/auth/auth.store';
import { GpAlertComponent } from '../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';

import { RxjsHelpers } from '../../../../../../shared/helpers/rxjs-helpers';

import { CampaignStore } from '../../../campaign.store';

import { CampaignOrdersProductsSearchComponent } from './campaign-orders-products-search/campaign-orders-products-search.component';
import { CampaignOrdersParticipantViewComponent } from './campaign-orders-participant-view/campaign-orders-participant-view.component';

import { CampaignOrdersProductsService } from './services/campaign-orders-products.service';

@Component({
	selector: 'campaing-orders-participant-creator',
	templateUrl: './campaign-orders-participant-creator.component.html'
})
export class CampaignOrdersParticipantCreatorComponent implements OnInit, OnDestroy {
	@ViewChild('gpAlert') gpAlert: GpAlertComponent;
	@ViewChild('ordersProducts') ordersProducts : CampaignOrdersProductsSearchComponent;
	@ViewChild('orderParticipantView') orderParticipantView : CampaignOrdersParticipantViewComponent;

	selectedParticipant: any = {};
	shoppingCart: any[] = [];

	private _campaign: Subscription;
	campaignId: string;

	cart: any = {};
	childCart: any = {};
	order: any = {};

	wasOrderCreated: boolean = false;
	loading: boolean = false;

	totalToDebit: any = 0;

	sessionStartDate: Date;
	sessionEndDate: Date;

	constructor(
		private authStore: AuthStore,
		private campaignStore: CampaignStore,
		private campaignOrdersProductsService: CampaignOrdersProductsService
	) {}

	get hasSelectedParticipant() {
		return this.selectedParticipant && this.selectedParticipant.userId != null;
	}

	get shoppingCartIsNotEmpty() {
		return this.shoppingCart.length > 0;
	}

	get canOrder() {
		return this.authStore.role.PERMISSION_CAMPAIGNS_CATALOG_ORDERS_PRODUCTS;
	}

	ngOnInit() {
		this.sessionStartDate = new Date();
		this._campaign = this.campaignStore.asObservable
			.subscribe(id => { this.campaignId = id; }
		);
		if (!this.canOrder) {
			this.gpAlert.showWarning('Você não tem permissão para acessar esse módulo.');
		}
	}

	ngOnDestroy(): void {
		RxjsHelpers.unsubscribe(this._campaign);
	}

	onSelectedParticipant(participant: any) {
		this.selectedParticipant = participant;
	}

	onMovedShoppingCart(products: any) {
		this.shoppingCart = products;
	}

	onCancelOrders() {
		this.clear();
	}

	onCreateOrders() {
		this.calculateTotalToDebited();
		this.gpAlert.confirm(`Ao confirmar será debitado do participante o total de: ${this.totalToDebit}
			Deseja criar o pedido ?`).then(result => {
			if (result && result.value) {
				this.loading = true;
				this.mapToCart();

				this.cart.sessionStartDate = this.sessionStartDate;
				this.cart.sessionEndDate = new Date();

				this.campaignOrdersProductsService.createOrders(this.campaignId, this.cart)
					.subscribe(order => {
						if (order && order.orderId) {
							this.order = order;
							this.wasOrderCreated = true;
							this.clear();
						} else {
							this.wasOrderCreated = false;
							this.gpAlert.showError('Ocorreu um erro ao criar o pedido');
						}
						this.loading = false;
					},
					err => {
						this.loading = false;
						this.gpAlert.handleAndShowError(err);
					}
				);
			}
		});
	}

	private calculateTotalToDebited() {
		this.totalToDebit = 0;
		this.shoppingCart.forEach(p => {
			let total = 0;
			if (this.isCurrency()) {
				total = this.removeCurrencyMask(p.total)
			} else {
				total = p.total;
			}
			this.totalToDebit += total;
		});
		if (this.isCurrency()) {
			this.totalToDebit = this.applyCurrencyMask(this.totalToDebit);
		}
	}

	// TODO: Param para definir
	private isCurrency() {
		return true;
	}

	private removeCurrencyMask(value: string) {
		return Number(FormatHelper.removeCurrencyMask(value).replace('R$',''));
	}

	private applyCurrencyMask(value: number) {
		return value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });
	}

	private mapToCart() {
		this.cart.campaignId = this.campaignId;
		this.cart.type = 'Rewards';
		this.cart.creationDate = new Date();
		this.cart.userId = this.selectedParticipant.userId;
		this.cart.participantId = this.selectedParticipant.participantId;

		this.cart.childrenCarts = [];
		this.cart.shippingAddress = this.getShippingAddress();

		this.shoppingCart.forEach(product => {
			const item = this.createCartItemFrom(product);
			const childCartIndex = this.findChildCartIndexByPartnerId(product.partnerId);

			if (childCartIndex < 0) {
				const childCart = this.createChildCart();
				childCart.itemGrouperId = product.partnerId;
				childCart.products.push(item);
				this.cart.childrenCarts.push(childCart);
			} else {
				this.cart.childrenCarts[childCartIndex].products.push(item);
			}
		});
	}

	private createCartItemFrom(product: any) {
		return {
			partnerId: product.partnerId,
			partnerName: product.partnerName,
			productId: product.productId,
			skuId: product.skuId,
			elasticId: product.id,
			offline: product.offline,
			processType: product.processType,
			productType: product.productType,
			productName: product.name,
			skuCode: product.skuCode,
			skuColor: null,
			departmentId: product.departmentId,
			categoryId: product.categoryId,
			subcategoryId: product.subcategoryId,
			available: product.available,
			quantity: product.quantity,
			unitPrices: this.getUnitPrices(product),
			detailedPrice: {
				costPrice: this.getCostPrice(product),
				gpFee: this.getGpFee(),
				clientFee: this.getClientFee(),
				clientPartnerFee: this.getClientPartnerFee(),
				salePrice: this.getSalePrice(product),
				hasFixedPrice: false // TODO VALIDAR
			},
			shippingCost: this.getShippingCost(),
			detailedShippingCost: {
				costPrice: this.geCostPrice()
			}
		}
	}

	private getUnitPrices(product: any) {
		return {
			points: product.price,
			currency: product.price
		}
	}

	private getCostPrice(product: any) {
		return {
			points: product.price,
			currency: product.price
		}
	}

	private getGpFee() {
		return {
			currency: 0,
			points: 0
		}
	}

	private getClientFee() {
		return {
			currency: 0,
			points: 0
		}
	}

	private getClientPartnerFee() {
		return {
			currency: 0,
			points: 0
		}
	}

	private getSalePrice(product: any) {
		return {
			currency: product.price,
			points: product.price
		}
	}

	private getShippingCost() {
		return {
			currency: 0,
			points: 0
		}
	}

	private geCostPrice() {
		return {
			currency: 0,
			points: 0
		}
	}

	private findChildCartIndexByPartnerId(partnerId: string) {
		return this.cart.childrenCarts.findIndex(c => c.itemGrouperId === partnerId);
	}

	private createChildCart() {
		return {
			products: [],
			itemGrouperId: ''
		}
	}

	private getShippingAddress() {
		return null;
	}

	onBack() {
		this.wasOrderCreated = false;
		this.selectedParticipant = {};
		this.orderParticipantView.clear();
	}

	onExport() {
		this.orderParticipantView.onExport();
	}

	clear() {
		this.ordersProducts.clear();
		this.shoppingCart = [];
	}
}
