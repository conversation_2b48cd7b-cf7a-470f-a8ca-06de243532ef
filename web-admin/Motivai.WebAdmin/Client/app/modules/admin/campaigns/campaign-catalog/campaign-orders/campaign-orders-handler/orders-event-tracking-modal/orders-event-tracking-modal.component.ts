import { Component, OnInit, Input, Output, ViewChild, EventEmitter } from '@angular/core';
import { GpModalComponent } from '../../../../../../../shared/components/gp-modal/gp-modal.component';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpFileUploadComponent } from '../../../../../../../shared/components/gp-fileupload/gp-fileupload.component';
import { CampaignOrdersHandlerService } from '../campaign-orders-handler.service';

@Component({
    selector: 'orders-event-tracking-modal',
    templateUrl: './orders-event-tracking-modal.component.html'
})
export class OrdersEventTrackingModalComponent implements OnInit {
    @Input() item: any = {};
    @Input() campaignId: string;
    @Input() orderId: string;
    @Output('onclose') onclose: EventEmitter<any> = new EventEmitter();

    @ViewChild('modal') modal: GpModalComponent;
    @ViewChild('alert') alert: GpAlertComponent;
    @ViewChild('gpFile') gpFile: GpFileUploadComponent;
    loading: boolean = false;
    event: any = {};
    get uploadPath(): string {
        return `api/campaigns/${this.campaignId}/orders/${this.orderId}/event/image`;
    }

    constructor(private _ordersService: CampaignOrdersHandlerService) { }
    ngOnInit(): void { }

    open() {
        this.modal.show();
    }

    close() {
        this.onclose.emit(this.item);
    }

    clear() {
        this.event = {};
        this.alert.clear();
    }

    eventSubmit() {
        if (this.event && this.item) {
            this.loading = true;
            this.alert.clear();
            if (this.gpFile.hasSelectedFile()) {
                this.gpFile.uploadFile();
            } else {
                this.saveEvent();
            }
        }
    }

    saveEvent() {
        this._ordersService.saveEvent(this.campaignId, this.orderId, this.item.itemGrouperId, this.item.skuId, this.event)
            .subscribe(response => {
                if (response) {
                    this.alert.showSuccess('Evento salvo com sucesso! Uma notificação será enviada ao participante!');
                    this.event = {};
                    this.gpFile.removeAll();
                } else {
                    this.alert.showError('Ocorreu um erro ao salvar o evento!');
                }
                this.loading = false;
            }, err => {
                const msg = err ? (err.message ? err.message.toString() : err.ToString()) : 'Ocorreu um erro ao salvar o evento';
                this.alert.showError(msg);
                this.loading = false;
            });
    }

    onCompleteEventFile($event) {
        if ($event && $event.response) {
            const response = JSON.parse($event.response);
            if (response.success && response.return) {
                this.event.imageUrl = response.return;
                this.saveEvent();
            } else {
                this.alert.showError('Ocorreu um erro ao salvar a imagem');
            }
        } else {
            this.alert.showError('Ocorreu um erro ao salvar a imagem');
        }
    }

    getEventTypeDescription(type: string): string {
        if (!type) return '';
        switch (type) {
            case 'Info': return 'Informação';
            case 'Production': return 'Produção';
            case 'Price': return 'Precificação';
            case 'Shipping': return 'Entrega';
            case 'Defective': return 'Defeito';
            case 'Collect': return 'Coleta';
            case 'ProductReturn': return 'Retorno de Produto';
            case 'Delivered': return 'Entregue';
            case 'Canceled': return 'Cancelado';
        }
        return '';
    }
}
