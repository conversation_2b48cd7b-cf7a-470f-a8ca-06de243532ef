<gp-card>
	<spinner [overlay]="true" [show]="loadingFilters || loading"></spinner>
	<gp-alert [overlay]="true" #gpAlert></gp-alert>

	<accordion [closeOthers]="true">
		<accordion-group panelClass="b0 mb-sm panel-default" (onclick)="loadFiltersByValue()">
			<div accordion-heading>
				<label>Por <PERSON> (excluir do catálogo os produtos pelo filtro de valor selecionado)</label>
			</div>
			<gp-form-validate [formGroup]="formByValue" [validationMessages]="messages" (onSubmit)="saveValueFilter($event)">
				<div class="row">
					<div grid="12 12 4" class="top-p1">
						<gp-input-radio label="Intervalo de valores" name="valueType" radioValue="Range" formControlName="valueType"></gp-input-radio>
					</div>
					<div grid="12 12 8">
						<gp-mask-validate cols="12 6 4 4" [disabled]="!isRange" [onlyDecimal]="true" label="Valor De" formControlName="fromValue"></gp-mask-validate>
						<gp-mask-validate cols="12 6 4 4" [disabled]="!isRange" [onlyDecimal]="true" label="Valor Até" formControlName="toValue"></gp-mask-validate>
					</div>
				</div>
				<div class="row">
					<div grid="12 12 4" class="top-p1">
						<gp-input-radio label="Abaixo De" name="valueType" radioValue="Ceil" formControlName="valueType"></gp-input-radio>
					</div>
					<div grid="12 12 8">
						<gp-mask-validate cols="12 6 4 4" [disabled]="!isCeil" [onlyDecimal]="true" label="Valor abaixo de" formControlName="ceilValue"></gp-mask-validate>
					</div>
				</div>
				<div class="row">
					<div grid="12 12 4" class="top-p1">
						<gp-input-radio label="Acima De" name="valueType" radioValue="Floor" formControlName="valueType"></gp-input-radio>
					</div>
					<div grid="12 12 8">
						<gp-mask-validate cols="12 6 4 4" [disabled]="!isFloor" [onlyDecimal]="true" label="Valor acima de" formControlName="floorValue"></gp-mask-validate>
					</div>
				</div>
				<div class="pull-right">
					<gp-spinner-button *ngIf="hasValueFilter" text="Remover filtro" bootstrapClass="danger" [disabled]="disableButtons"
						icon="trash" [loading]="sending" loadingText="Salvando" (click)="removeValueFilter()"></gp-spinner-button>
					<gp-spinner-button type="submit" text="Salvar" bootstrapClass="success" [disabled]="!formByValue.valid || disableButtons"
						[pink]="true" icon="send" [loading]="sending" loadingText="Salvando"></gp-spinner-button>
				</div>
			</gp-form-validate>
		</accordion-group>

		<accordion-group panelClass="b0 mb-sm panel-default" (onclick)="loadFiltersByManufactures()">
			<div accordion-heading>
				<label>Por Marca (excluir do catálogo os produtos das marcas selecionadas)</label>
			</div>
			<gp-form-validate [formGroup]="formByManufact" [validationMessages]="messagesManufact" (onSubmit)="saveManufactFilter($event)">
				<div class="row">
					<gp-select-validate cols="12 4" label="Parceiro" [searchable]="true" [items]="campaignPartners" 
						[required]="false" placeholder="Parceiro" formControlName="partnerId">
					</gp-select-validate>
					<gp-autocomplete cols="12 4 4" label="Marca" placeholder="Pesquise a marca pelo nome" formControlName="manufacturerId"
						[items]="manufacturers" (filter)="filterManufacturer($event)">
					</gp-autocomplete>
					<div class="top-p2" grid="12 4">
						<gp-spinner-button type="submit" text="Adicionar filtro" bootstrapClass="success" [disabled]="!formByManufact.valid || disableButtons"
						[pink]="true" icon="plus" pull="right" [loading]="sending" loadingText="Adicionando"></gp-spinner-button>
					</div>
				</div>
			</gp-form-validate>
			<gp-form-row>
				<gp-form-col cols="12 12 12">
					<gp-grid name="byManufact" [loading]="loading" [rows]="campaignFilters?.byManufacturers"
						[columns]="['Fabricante', 'Parceiro']" [fields]="['manufacturerName', 'partnerName']"
						[showActive]="false" [showPagination]="true" [showEdit]="false" pageSize="20" [showDelete]="true" (onDelete)="removeManufacturerFilter($event)" (onPageChanged)="onPageChanged($event)">
					</gp-grid>
				</gp-form-col>
			</gp-form-row>
		</accordion-group>

		<accordion-group panelClass="b0 mb-sm panel-default" (onclick)="loadFiltersByPartners()">
			<div accordion-heading>
				<label>Por Parceiro (excluir do catálogo os parceiros selecionados)</label>
			</div>
			<gp-form-validate [formGroup]="formByPartner" [validationMessages]="partnerMessages" (onSubmit)="savePartnerFilter($event)">
				<div class="row">
					<gp-select-validate cols="12 6" label="Parceiro" [searchable]="true" [items]="campaignPartners"  placeholder="Parceiro"
						formControlName="partnerId">
					</gp-select-validate>
					<div class="top-p2" grid="12 6">
						<gp-spinner-button type="submit" text="Adicionar filtro" bootstrapClass="success" [disabled]="disableButtons"
						[pink]="true" icon="plus" pull="right" [loading]="sending" loadingText="Adicionando"></gp-spinner-button>
					</div>
				</div>
			</gp-form-validate>
			<gp-form-row>
				<gp-form-col cols="12 12 12">
					<gp-grid name="byPartners" [loading]="loading" [rows]="campaignFilters?.byPartners" [columns]="['Parceiro']" [fields]="['name']"
						[showActive]="false" [showPagination]="true" [showEdit]="false" pageSize="20"  [showDelete]="true" (onDelete)="removePartnerFilter($event)"  (onPageChanged)="onPageChanged($event)">
					</gp-grid>
				</gp-form-col>
			</gp-form-row>
		</accordion-group>

		<accordion-group panelClass="b0 mb-sm panel-default" (onclick)="loadFiltersByBlockRules()">
			<div accordion-heading>
				<label>Por Regras (departamento/categoria/sub-categoria/sku) (excluir do catálogo as regras selecionadas)</label>
			</div>
			<campaign-filters-block-byrules #filterBlockByRules></campaign-filters-block-byrules>
		</accordion-group>

		<accordion-group panelClass="b0 mb-sm panel-default" (onclick)="loadFiltersByAllowRules()">
			<div accordion-heading>
				<label>Por Regras (departamento/categoria/sub-categoria/sku) (Habilitar no catálogo as regras selecionadas)</label>
			</div>
			<campaign-filters-allow-byrules #filterAllowByRules></campaign-filters-allow-byrules>
		</accordion-group>
		
		<accordion-group panelClass="b0 mb-sm panel-default" (onclick)="loadFiltersByPartnerRules()">
			<div accordion-heading>
				<label>Por Produtos de Parceiros (excluir do catálogo apenas os produtos selecionados de um parceiro)</label>
			</div>
			<gp-form-validate [formGroup]="formByPartnerSku" [validationMessages]="partnerSkuMessages" (onSubmit)="savePartnerSkuFilter($event)" >
				<div class="row">
					<gp-select-validate cols="12 4" label="Parceiro" [searchable]="true" [items]="campaignPartners" placeholder="Parceiro"
						formControlName="partnerId">
					</gp-select-validate>
					<gp-input-validate cols="12 4" label="SKU" formControlName="sku"></gp-input-validate>
					<div class="top-p2" grid="12 4">
						<gp-spinner-button type="submit" text="Adicionar filtro" bootstrapClass="success" [disabled]="!formByPartnerSku.valid || disableButtons"
							[pink]="true" icon="plus" pull="right" [loading]="sending" loadingText="Adicionando"></gp-spinner-button>
					</div>
				</div>
			</gp-form-validate>
			<gp-form-row>
				<gp-form-col cols="12 12 12">
					<gp-grid name="byPartnersSkus" [loading]="loading" [rows]="campaignFilters?.byPartnersSkus"
						[columns]="['Parceiro', 'Produto', 'SKU']" [fields]="['partnerName', 'productName', 'sku']"
						[showActive]="false" [showPagination]="true" [showEdit]="false" pageSize="20" [showDelete]="true" (onDelete)="removePartnerSkuFilter($event)" (onPageChanged)="onPageChanged($event)">
					</gp-grid>
				</gp-form-col>
			</gp-form-row>
		</accordion-group>

		<accordion-group panelClass="b0 mb-sm panel-default" (onclick)="loadFiltersBySku()" >
			<div accordion-heading>
				<label>Por Produtos (habilitar no catálogo apenas os produtos selecionados)</label>
			</div>
			<gp-form-row>
				<gp-form-col cols="12 4">
					<gp-simple-input label="Nome">
						<input type="text" name="productName" class="form-control" [(ngModel)]="params.productName" />
					</gp-simple-input>
				</gp-form-col>
				<gp-form-col cols="12 4">
					<gp-simple-input label="Sku">
						<input type="text" name="sku" class="form-control" [(ngModel)]="params.sku" />
					</gp-simple-input>
				</gp-form-col>
				<gp-form-col cols="12 4">
					<gp-spinner-button type="button" bootstrapClass="primary" icon="search" text="Pesquisar" loadingText="Pesquisando..."
						[loading]="loading" (click)="loadFilters()" marginTop="24px">
					</gp-spinner-button>
					<gp-spinner-button type="button" [pink]="true" icon="plus" text="Adicionar"
						[loading]="loading" marginLeft="5px" (click)="openSkuFilterCreatorModal()" marginTop="24px">
					</gp-spinner-button>
				</gp-form-col>
			</gp-form-row>
			<spinner [overlay]="true" [show]="loading"></spinner>
			<gp-form-row>
				<gp-form-col cols="12 12 12">
					<gp-grid name="whitelistBySkus" [loading]="loading" [rows]="campaignFilters?.whitelistBySkus"
						[columns]="['Parceiro', 'Produto', 'SKU']" [fields]="['partnerName', 'productName', 'sku']"
						[showActive]="false" [showPagination]="true" [showTotalPages]="false" pageSize="20" [showEdit]="false" 
						[showDelete]="true" (onDelete)="removePartnerSkuToWhitelist($event)" (onPageChanged)="onPageChanged($event)">
					</gp-grid>
				</gp-form-col>
			</gp-form-row>
		</accordion-group>

		<accordion-group panelClass="b0 mb-sm panel-default" (onclick)="loadFiltersByParticipantsGroups()">
			<div accordion-heading>
				<label>Por Produtos por Grupo (habilitar no catálogo apenas os produtos selecionados para determinados grupos)</label>
			</div>
			<gp-form-validate [formGroup]="formByGroupedSkus" [validationMessages]="groupedSkusMessages" (onSubmit)="addSkuInGroup($event)" *ngIf="editingGroupedSkus">
				<gp-form-row>
					<gp-form-col cols="12 8 8">
						<gp-simple-input label="Grupo de Participantes" [required]="true">
							<gp-select [allowClear]="true" placeholder="Parceiro" [items]="participantsGroups"
								[disabled]="!participantGroupSkus.canSelectGroup" required
								[(ngModel)]="participantGroupSkus.participantGroupId" [ngModelOptions]="{standalone:true}">
							</gp-select>
						</gp-simple-input>
					</gp-form-col>
				</gp-form-row>
				<gp-form-row>
					<gp-select-validate cols="12 4" label="Parceiro" [searchable]="true" [items]="campaignPartners" placeholder="Parceiro"
						formControlName="partnerId">
					</gp-select-validate>
					<gp-input-validate cols="12 4" label="SKU" formControlName="skuCode" (blur)="searchSku()"></gp-input-validate>
				</gp-form-row>
				<gp-form-row>
					<div [group]="true" grid="12 8 8">
						<label>Produto</label>
						<input type="text" class="form-control" placeholder="Selecione o parceiro e digite o SKU"
							[value]="product.name" readonly>
					</div>
					<div class="top-p27" grid="12 4">
						<gp-spinner-button type="submit" text="Adicionar SKU" bootstrapClass="success" loadingText="Adicionando" pull="right"
							[disabled]="!formByGroupedSkus.valid || disableButtons" [pink]="true" icon="plus" [loading]="sending"></gp-spinner-button>
					</div>
				</gp-form-row>

				<gp-form-row>
					<gp-form-col cols="12 12 12">
						<gp-grid name="groupSkus" [loading]="loading" [rows]="participantGroupSkus.skus"
							[columns]="['Parceiro', 'Código', 'SKU']" [fields]="['partnerName', 'skuName', 'skuCode']"
							[showActive]="false" [showPagination]="true" pageSize="20" [showEdit]="false" [showDelete]="true"
							(onDelete)="removeSkuFromParticipantGroupWhitelist($event)" (onPageChanged)="onPageChanged($event)">
						</gp-grid>
					</gp-form-col>
				</gp-form-row>

				<gp-form-row>
					<gp-form-col cols="12 12 12">
						<gp-spinner-button bootstrapClass="default" icon="arrow-left" (click)="editingGroupedSkus=false" text="Voltar"></gp-spinner-button>

						<gp-spinner-button type="button" text="Salvar Produtos do Grupo" bootstrapClass="success" pull="right"
							loadingText="Salvando" [pink]="true" icon="plus" [loading]="sending" (click)="saveParticipantGroupWhitelist()"></gp-spinner-button>
					</gp-form-col>
				</gp-form-row>
			</gp-form-validate>

			<div *ngIf="!editingGroupedSkus">
				<gp-form-row>
					<gp-form-col cols="12 12 12">
						<gp-spinner-button bootstrapClass="default" icon="plus" (click)="newGroup()" text="Adicionar"></gp-spinner-button>
					</gp-form-col>
				</gp-form-row>
				<gp-form-row>
					<gp-form-col cols="12 12 12">
						<gp-grid name="whitelistByGroupedSkus" [loading]="loading" [rows]="campaignFilters?.participantsGroupsWhitelists"
							[columns]="['Grupo', 'Qtde. SKUs']" [fields]="['participantGroupName', 'skusCount']"
							[showActive]="false" [showPagination]="false" [showEdit]="true" [showDelete]="false"
							(onEdit)="editParticipantGroupWhitelist($event)" (onDelete)="removeParticipantGroupWhitelist($event)">
						</gp-grid>
					</gp-form-col>
				</gp-form-row>
			</div>
		</accordion-group>
	</accordion>
</gp-card>
<gp-modal #skuFilterCreatorModal title="Adicionar Filtro">
<gp-form-validate [formGroup]="formByPartnerSkuWhitelist" [validationMessages]="partnerSkuWhitelistMessages"
	(onSubmit)="savePartnerSkuToWhitelist($event)">
	<div class="row">
		<gp-select-validate cols="12 4" label="Parceiro" [searchable]="true" [items]="campaignPartners"
			placeholder="Parceiro" formControlName="partnerId">
		</gp-select-validate>
		<gp-input-validate cols="12 4" label="SKU" formControlName="sku"></gp-input-validate>
		<div class="top-p2" grid="12 4">
			<gp-spinner-button type="submit" text="Adicionar filtro" bootstrapClass="success" loadingText="Adicionando"
				pull="right" [disabled]="!formByPartnerSkuWhitelist.valid || disableButtons" [pink]="true" icon="plus"
				[loading]="sending"></gp-spinner-button>
		</div>
	</div>
</gp-form-validate>
</gp-modal>
