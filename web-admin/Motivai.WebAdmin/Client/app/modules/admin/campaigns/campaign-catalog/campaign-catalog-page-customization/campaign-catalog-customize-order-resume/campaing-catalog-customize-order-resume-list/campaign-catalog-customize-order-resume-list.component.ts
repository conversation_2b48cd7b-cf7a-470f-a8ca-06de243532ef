import { Component, EventEmitter, OnInit, Output, ViewChild } from "@angular/core";
import { CampaignStore } from "../../../../campaign.store";
import { CampaignCatalogPageCustomizationService } from "../../campaign-catalog-page-customization.service";
import { GpAlertComponent } from "../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component";
import { Subscription } from "rxjs/Subscription";
import { DateHelper } from "../../../../../../../shared/helpers/date-helper";
import { FormatHelper } from "../../../../../../../shared/formatters/format-helper";

@Component({
    selector: 'campaign-catalog-customize-order-resume-list',
    templateUrl: 'campaign-catalog-customize-order-resume-list.component.html'
})
export class CampaignCatalogCustomizeOrderResumeListComponent implements OnInit {
    @ViewChild('alert') alert: GpAlertComponent;

    @Output('onEdit') onEdit: EventEmitter<any> = new EventEmitter<any>();
    
    loading: boolean;

    customizations: any = [];


    private _campaign$: Subscription;

    ngOnInit() {
        this._campaign$ = this.campaignStore.asObservable
          .subscribe(campaignId => {
            if (campaignId) {
                this.loadCampaignCatalogOrderResumeCustomizations();
            }
          });
    }

    
    constructor(private campaignStore: CampaignStore, private pageCustomizationService: CampaignCatalogPageCustomizationService) {}

    get campaignId() {
        return this.campaignStore.id;
    }

    editCustomization(event: any): void {
        this.onEdit.emit(event.id);
    }

    loadCampaignCatalogOrderResumeCustomizations() {
        this.loading = true;
        this.pageCustomizationService.getCampaignCatalogPageCustomizations(this.campaignId)
            .subscribe(customizations => {

                if (customizations && customizations.length) {
                    customizations.map(c => {
                        if (c.isLayoutType) {
                            c.description = `Tipo de layout - ${c.layoutType == 'PRODUCT' ? 'Produto' : 'Serviço'  }`
                        }
                        if (c.isProductType) {
                            c.description = `Tipo do produto - ${c.productType}`
                        }

                        c.handledCreateDate = FormatHelper.formatDateWithTimezone(c.createDate);
                    });
                }

                this.customizations = customizations || [];
                this.loading = false;
            },err => {
                this.loading = false;
                this.alert.showError(err);
            });
    }
}