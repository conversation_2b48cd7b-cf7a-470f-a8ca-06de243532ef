import { Component, Input, ViewChild } from '@angular/core';

import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { FormatHelper } from '../../../../../../../shared/formatters/format-helper';
import { CampaignOrdersProductsExporter } from '../services/campaign-orders-products-exporter';

@Component({
	selector: 'campaign-orders-participant-view',
	templateUrl: './campaign-orders-participant-view.component.html'
})
export class CampaignOrdersParticipantViewComponent {
	@ViewChild('gpAlert') gpAlert: GpAlertComponent;

	selectedParticipant: any = {};

	order: any = {};
	products: any[] = [];

	loading: boolean = false;

	constructor(private campaignOrdersProductsExporter: CampaignOrdersProductsExporter) {}

	@Input('order') set createdOrder(order: any) {
		this.loading = true;
		this.clear();

		this.order = order;
		this.mapToProducts();

		this.loading = false;
		if (!this.hasProducts()) {
			this.gpAlert.showError('Ocorreu um erro ao criar o pedido');
		}
	}

	@Input('selectedParticipant') set participant(participant: any) {
		this.selectedParticipant = participant;
	}

	private mapToProducts() {
		const childrenCarts = this.order.childrenCarts;
		if (!childrenCarts || childrenCarts.length < 1)
			return;

		for (const children of childrenCarts) {
			if (!children.products || children.products.length < 1)
				continue;
			for (const prod of children.products) {

				if (prod.vouchersDetails && prod.vouchersDetails.length > 0) {	
					for (const voucherDetail of prod.vouchersDetails) {
						const product = this.createProductFrom(prod, voucherDetail);
						this.products.push(product);
					}
				} else {
					const product = this.createProductFrom(prod, null);
					this.products.push(product);
				}
			}
		}
	}

	private createProductFrom(product: any, voucherDetails: any) {
		return {
			voucherCode: (voucherDetails && voucherDetails.code) ? voucherDetails.code : '-',
			skuCode: product.skuCode || '-',
			expirationDate: (voucherDetails && voucherDetails.expirationDate) ? FormatHelper.formatDate(voucherDetails.expirationDate) : '-',
			partnerName: product.partnerName || '-',
			productName: product.productName || '-',
			productType: this.handleProductType(product.productType) || '-',
			quantity: (voucherDetails) ? 1 : product.quantity,
			unitaryValue: (product.unitPrices.currency || product.unitPrices.points) || '-',
			occurredError: this.handleError(product.occurredError || false),
			errorMessage: product.errorMessage || '-'
		}
	}
	
	private handleError(error: boolean) {
		switch (error) {
			case true: return 'Sim';
			case false: return 'Não';
			default: return 'Não';
		}
	}

	private handleProductType(productType: string) {
		if (!productType)
			return '-';

		switch (productType) {
			case 'Produto': return 'Produto';
			case 'ValeFisico': return 'Vale Físico';
			case 'ValeVirtual': return 'Vale Virutal';
			default: return '-';
		}
	}

	private hasProducts() {
		return this.products.length > 0;
	}

	onExport() {
		this.campaignOrdersProductsExporter.exportJsonToExcel(this.products, 'Produtos');
	}

	clear() {
		this.order = {};
		this.products = [];
	}
}

