import { Component, OnInit, ViewChild, Input, EventEmitter, Output } from '@angular/core';
import { GpAlertComponent } from '../../../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpModalComponent } from '../../../../../../../../../../shared/components/gp-modal/gp-modal.component';
import { ScheduledBillPaymentsService } from '../../services/scheduled-bill-payments-service.service';

@Component({
  selector: 'app-scheduled-payment-date-change-modal',
  templateUrl: './scheduled-payment-date-change.component.html'
})
export class ScheduledPaymentDateChangeComponent implements OnInit {
  @ViewChild('alert') alert: GpAlertComponent;
  @ViewChild('changeDatePaymentModal') changeDatePaymentModal: GpModalComponent;

  loadingProcessing: boolean = false;

  @Input() campaignId: string;
  @Input() paymentScheduledId: string;
  @Input() paymentScheduled: any;

  @Output()
  refreshPaymentScheduledEvent: EventEmitter<any> = new EventEmitter<any>();

  today: Date = new Date();

  reschedulePaymentDate: any = {};

  constructor(private service: ScheduledBillPaymentsService) { }

  ngOnInit() {
  }

  showModal() {
    this.changeDatePaymentModal.show();
  }

  hideModal() {
    this.changeDatePaymentModal.hide();
  }

  refreshPaymentScheduled() {
    this.refreshPaymentScheduledEvent.emit();
  }


  changePaymentDate() {
    if (!this.reschedulePaymentDate.scheduledPaymentDate) {
      return this.alert.showWarning('Selecione a nova data de agendamento');
    }

    this.loadingProcessing = true;

    this.service.reschedulePaymentDate(this.campaignId, this.paymentScheduledId, this.reschedulePaymentDate).subscribe(
      response => {
        if (response) {
          this.alert.showSuccess('Reagendamento efetuado com sucesso.');
        }
        this.reschedulePaymentDate = {};
        this.loadingProcessing = false;
        this.refreshPaymentScheduled();
        this.hideModal();
      },
      err => {
        this.loadingProcessing = false;
        this.alert.showError(err);
      }
    );

  }

}
