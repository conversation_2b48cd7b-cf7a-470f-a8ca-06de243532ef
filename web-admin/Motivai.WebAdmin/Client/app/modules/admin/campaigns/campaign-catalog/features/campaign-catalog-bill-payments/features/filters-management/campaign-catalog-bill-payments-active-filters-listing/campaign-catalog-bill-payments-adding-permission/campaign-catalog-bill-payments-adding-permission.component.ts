import { Component, Input, OnInit, ViewChild } from '@angular/core';

import { GpAlertComponent } from '../../../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpModalComponent } from '../../../../../../../../../../shared/components/gp-modal/gp-modal.component';
import { Item } from '../../../../../../../../../../shared/models/item';

import { TypeBill } from '../../models/typeBills';
import { CampaignCatalogBillPaymentsFiltersManagementService } from '../../services/campaing-catalog-bill-payments-filters-management.service';

@Component({
	selector: 'campaign-catalog-bill-payments-adding-permission',
	templateUrl: './campaign-catalog-bill-payments-adding-permission.component.html'
})
export class CampaignCatalogBillPaymentsAddingPermissionComponent implements OnInit {
	@ViewChild('modal') modal: GpModalComponent;
	@ViewChild('alert') alert: GpAlertComponent;
	@Input('campaignId') campaignId: string;

	filters: any = {
		serviceType: TypeBill.INVOICES
	};

	typeBills: Item[] = [
		Item.of(TypeBill.UTILITY_BILL, 'Conta Concessionária'),
		Item.of(TypeBill.INVOICES, 'Ficha Compensação')
	];

	loading: boolean = false;
	constructor(private billPaymentsFiltersManagementService: CampaignCatalogBillPaymentsFiltersManagementService) { }

	ngOnInit() {
		this.clear();
	}

	get applyReceiverName(): boolean {
		return this.filters.applyReceiverName;
	}

	get applyReceiverDocument(): boolean {
		return this.filters.applyReceiverDocument;
	}

	get applyPayerName(): boolean {
		return this.filters.applyPayerName;
	}

	get applyPayerDocument(): boolean {
		return this.filters.applyPayerDocument;
	}

	get applyAssignor(): boolean {
		return this.filters.applyAssignor;
	}

	get applyBillingAmount(): boolean {
		return this.filters.applyBillingAmount;
	}

	get isValid(): boolean {
		return this.applyReceiverName || this.applyReceiverDocument || this.applyPayerName || this.applyPayerDocument || this.applyAssignor;
	}

	openModal(): void {
		this.modal.show();
	}

	hide(): void {
		this.modal.hide();
	}

	onClickApplyReceiverName(ckecked: boolean): void {
		if (!ckecked) {
			this.filters.receiverName = "";
		}
	}

	onClickApplyReceiverDocument(ckecked: boolean): void {
		if (!ckecked) {
			this.filters.receiverDocument = "";
		}
	}

	onClickApplyPayerName(ckecked: boolean): void {
		if (!ckecked) {
			this.filters.payerName = "";
		}
	}

	onClickApplyPayerDocument(ckecked: boolean): void {
		if (!ckecked) {
			this.filters.payerDocument = "";
		}
	}

	onClickApplyAssignor(ckecked: boolean): void {
		if (!ckecked) {
			this.filters.assignor = "";
		}
	}

	onClickApplyBillingAmount(ckecked: boolean): void {
		if (!ckecked) {
			this.filters.billingAmount = "";
		}
	}

	onAddFilters() {
		if(!this.validate())
			return;
		this.loading = true;
		this.billPaymentsFiltersManagementService.addFiltersManualToAllowedList(this.campaignId, this.filters)
			.subscribe(resp => {
				this.loading = false;
				if (resp) {
					this.alert.showSuccess("Filtros adicionados com sucesso");
					this.clear();
					this.hide();
				} else {
					this.alert.showWarning("Não foi possível adicionadar os filtros. Tente novamente");
				}
			},
			err => {
				this.loading = false;
				this.alert.handleAndShowError(err);
			}
		);
	}

	private validate(): boolean {
		if (this.applyReceiverName && !this.filters.receiverName) {
			this.alert.showWarning('Informe o nome do beneficiário');
			return false;
		}
		if (this.applyReceiverDocument && !this.filters.receiverDocument) {
			this.alert.showWarning('Informe o nome do CPF/CNPJ do beneficiário');
			return false;
		}
		if (this.applyPayerName && !this.filters.payerName) {
			this.alert.showWarning('Informe o nome do pagador');
			return false;
		}
		if (this.applyPayerDocument && !this.filters.payerDocument) {
			this.alert.showWarning('Informe o nome do CPF/CNPJ do pagador');
			return false;
		}
		if (this.applyAssignor && !this.filters.assignor) {
			this.alert.showWarning('Informe o emissor');
			return false;
		}
		if (!this.filters.serviceType) {
			this.alert.showWarning('Informe o tipo do boleto');
			return false;
		}
		return true;
	}

	onBack(): void {
		this.clear();
		this.hide();
	}

	clear(): void {
		this.filters = {
			serviceType: TypeBill.INVOICES
		};
		this.loading = false;
	}
}
