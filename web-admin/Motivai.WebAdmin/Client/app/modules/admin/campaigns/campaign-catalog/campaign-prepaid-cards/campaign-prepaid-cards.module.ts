import { NgModule, LOCALE_ID } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';

import { SharedModule } from 'app/shared/shared.module';
import { CampaignPrepaidCardsViewComponent } from './campaign-prepaid-cards-view/campaign-prepaid-cards-view.component';
import { CampaignPrepaidCardsLinkingImportComponent } from './features/campaign-prepaid-cards-import-view/campaign-prepaid-cards-orders-import/campaign-prepaid-cards-orders-import.component';
import { CampaignPrepaidCardsOrdersImportService } from './services/campaign-prepaid-cards-orders-import.service';
import { CampaignPrepaidCardsOrdersViewComponent } from './features/campaign-prepaid-cards-orders-view/campaign-prepaid-cards-orders-view.component';
import { CampaignPrepaidCardsBatchesListingComponent } from './features/campaign-prepaid-cards-import-view/campaign-prepaid-cards-batches-listing/campaign-prepaid-cards-batches-listing.component';
import { CampaignPrepaidCardsImportViewComponent } from './features/campaign-prepaid-cards-import-view/campaign-prepaid-cards-import-view.component';

const COMPONENTS = [
  CampaignPrepaidCardsViewComponent,
  CampaignPrepaidCardsImportViewComponent,
  CampaignPrepaidCardsBatchesListingComponent,
  CampaignPrepaidCardsLinkingImportComponent,
  CampaignPrepaidCardsOrdersViewComponent
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    SharedModule.forRoot(),
    RouterModule.forChild([
      { path: '', component: CampaignPrepaidCardsViewComponent,
        children: [
          { path: 'importacoes', component: CampaignPrepaidCardsImportViewComponent },
          { path: 'pedidos', component: CampaignPrepaidCardsOrdersViewComponent },
        ]
      }
    ])
  ],
  declarations: COMPONENTS,
  providers: [
    { provide: LOCALE_ID, useValue: 'pt-br' },
    CampaignPrepaidCardsOrdersImportService
  ]
})
export class CampaignPrepaidCardsModule { }
