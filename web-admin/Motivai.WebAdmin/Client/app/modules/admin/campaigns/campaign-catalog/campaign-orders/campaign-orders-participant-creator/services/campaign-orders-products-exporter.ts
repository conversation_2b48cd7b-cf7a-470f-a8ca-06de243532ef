import { Injectable } from "@angular/core";
import * as XLSX from 'xlsx';

const EXCEL_EXTENSION = '.xlsx';

@Injectable()
export class CampaignOrdersProductsExporter {

    public exportJsonToExcel(products: any[], fileName: string): void {

        const worksheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(
            [{ A: 'Parceiro', B: 'Produto', C: 'Tipo', D: 'Código SKU', E: 'Código Voucher', F: 'Data de Expiração',
                G: 'Quantidade', H: 'Valor Unitário', I: 'Ocorreu Erro', J: 'Mensagem Erro'}
            ],
            this.getOptions()
        );

        for (let i = 0, length = products.length; i < length; i++) {
            const row = this.createRow(products[i]);
            XLSX.utils.sheet_add_json(
                worksheet,
                row,
                this.getOptions()
            );
        }
        const workbook: XLSX.WorkBook = { Sheets: { Sheet1: worksheet }, SheetNames: ['Sheet1'] };

        XLSX.writeFile(workbook, `${fileName}${EXCEL_EXTENSION}`);
    }

    private createRow(product: any) {
        return [{
            A: product.partnerName,
            B: product.productName,
            C: product.productType,
            D: product.skuCode,
            E: product.voucherCode,
            F: product.expirationDate,
            G: product.quantity,
            H: product.unitaryValue,
            I: product.occurredError,
            J: product.errorMessage
        }];
    }

    private getOptions(): any {
        const options = {
            skipHeader: true,
            origin: -1,
            header: []
        };
        return options;
    }
}
