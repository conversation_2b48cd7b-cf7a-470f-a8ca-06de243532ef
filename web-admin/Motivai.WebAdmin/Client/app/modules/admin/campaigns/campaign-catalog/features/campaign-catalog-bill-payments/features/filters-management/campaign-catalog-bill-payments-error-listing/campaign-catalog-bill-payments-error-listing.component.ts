import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';

import { FormatHelper } from '../../../../../../../../../shared/formatters/format-helper';
import { GpAlertComponent } from '../../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpGridComponent } from '../../../../../../../../../shared/components/gp-grid/gp-grid.component';
import { Item } from '../../../../../../../../../shared/models/item';

import { CampaignCatalogBillPaymentsFiltersManagementService } from '../services/campaing-catalog-bill-payments-filters-management.service';

import { RegisterStatus } from '../models/registerStatus';
import { SearchField } from '../models/searchField';
import { TypeBill } from '../models/typeBills';
import { DateHelper } from '../../../../../../../../../shared/helpers/date-helper';

@Component({
	selector: 'campaign-catalog-bill-payments-error-listing',
	templateUrl: './campaign-catalog-bill-payments-error-listing.component.html'
})
export class CampaignCatalogBillPaymentsErrorListingComponent implements OnInit {
	@ViewChild('gpAlert') gpAlert: GpAlertComponent;
	@ViewChild('billsGrid') billsGrid: GpGridComponent;
	@Output('onEdit') onEdit: EventEmitter<any> = new EventEmitter();
	@Input('campaignId') campaignId: string;

	searchFields: Item[] = [
		Item.of(SearchField.DEFAULT, "Nenhum"),
		Item.of(SearchField.USER_DOCUMENT, 'CPF/CNPJ do Participante'),
		Item.of(SearchField.RECEIVER_DOCUMENT, 'CPF/CNPJ do Beneficiário'),
		Item.of(SearchField.BAR_CODE, 'Linha Digitável do Boleto')
	];

	searchByField: SearchField;
	searchValue: string;
	lableSearchValue: string = 'Informe o valor a ser pesquisado';
	status: RegisterStatus = RegisterStatus.CREATED;

	typeBills: Item[] = [
		Item.of(TypeBill.UTILITY_BILL, 'Conta Concessionária'),
		Item.of(TypeBill.INVOICES, 'Ficha Compensação')
	];

	typeBill: TypeBill = TypeBill.INVOICES;

	params: any = {
		skip: 0,
		limit: 10
	};

	from: Date;
	to: Date;

	bills: any[] = [];

	loading: boolean = false;

	constructor(private billPaymentsFiltersManagementService: CampaignCatalogBillPaymentsFiltersManagementService) { }

	ngOnInit() {
		this.clear();
	}

	onChangeSearchField(searchByField: SearchField) {
		this.searchByField = searchByField;
		this.lableSearchValue = this.handleLableSearchValue();
	}

	private handleLableSearchValue() {
		switch (this.searchByField) {
			case SearchField.USER_DOCUMENT: return 'Informe o CPF/CNPJ do participante';
			case SearchField.RECEIVER_DOCUMENT: return 'Informe o CPF/CNPJ do beneficiário';
			case SearchField.BAR_CODE: return 'Informe a linha digitável';
			default: return 'Informe o valor a ser pesquisado';
		}
	}

	onSearchBills() {
		if (!this.validate())
			return;
		this.searchValue = FormatHelper.removeMask(this.searchValue);
		this.billsGrid.resetPagination();
		this.resetSkipAndLimit();
		this.findBillsWithAttemptedPayment();
	}

	private validate(): boolean {
		if (!this.typeBill) {
			this.gpAlert.showWarning('Informe o tipo do boleto');
			return false;
		}
		if (!this.status) {
			this.gpAlert.showWarning('Informe o status do registro');
			return false;
		}
		if ((this.searchByField !== null && this.searchByField != SearchField.DEFAULT) && !this.searchValue) {
			this.gpAlert.showWarning('Informe o valor a ser pesquisado');
			return false;
		}
		if (!this.from || !this.to) {
			this.gpAlert.showWarning('Informe o período para pesquisa');
			return false;
		}
		return true;
	}

	private resetSkipAndLimit(): void {
		this.params.skip = 0;
		this.params.limit = 10;
	}

	private findBillsWithAttemptedPayment(): void {
		this.loading = true;
		this.setParams();
		this.billPaymentsFiltersManagementService.getBillsWithAttemptedPayment(this.campaignId, this.params)
			.subscribe(bills => {
				this.loading = false;
				if (bills && bills.length > 0) {
					this.bills = this.handleBills(bills);
				} else {
					this.bills = [];
				}
			},
			err => {
				this.loading = false;
				this.gpAlert.handleAndShowError(err);
			}
		);
	}

	private setParams(): void {
		this.params.searchByField = this.searchByField;
		this.params.searchValue = this.searchValue;
		this.params.typeBill = this.typeBill;
		this.params.status = this.status;
		this.params.from = this.from;
		this.params.to = this.to;
	}

	private handleBills(bills: Array<any>): Array<any> {
		bills.forEach(b => {
			b.formattedCreatedDate = FormatHelper.formatDateWithTimezone(b.createDate);
			b.formattedStatus = this.handleStatus(b.status);
			b.typeBill = this.handleTypeBill(b.serviceType);
		});
		return bills;
	}

	private handleStatus(status: string): string {
		if (status === 'CREATED') return 'CRIADO';
		if (status === 'REJECTED') return 'REJEITADO';
		if (status === 'HANDLED') return 'MANIPULADO';
		return 'INDEFINIDO';
	}

	private handleTypeBill(serviceType: string): string {
		if (serviceType === 'CONTACONCESSIONARIA') return 'Conta Concessionária';
		if (serviceType === 'FICHACOMPENSACAO') return 'Ficha Compensação';
		return 'Indefinido';
	}

	onEditBill(event: any) {
		if (event) {
			this.onEdit.emit(event);
		}
	}

	onPageChanged(event: any) {
		if (event) {
			this.params.skip = event.skip;
			this.params.limit = event.limit;
			this.findBillsWithAttemptedPayment();
		}
	}

	clear(): void {
		this.params = {
			skip: 0,
			limit: 10
		}
		this.bills = [];
		this.searchByField = null;
		this.searchValue = null;
		this.lableSearchValue = this.handleLableSearchValue();
		this.typeBill = TypeBill.INVOICES;
		this.from = DateHelper.dateBySubtractingDaysFromNow(30);
		this.to = DateHelper.tomorrowInCurrentTimezone();
		this.billsGrid.resetPagination();
		this.status = RegisterStatus.CREATED;
		this.loading = false;
	}
}
