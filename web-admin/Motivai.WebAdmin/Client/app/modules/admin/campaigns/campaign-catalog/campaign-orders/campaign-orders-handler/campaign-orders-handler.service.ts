import { Injectable } from '@angular/core';
import * as moment from 'moment';
import { Observable } from 'rxjs/Observable';
import { ApiService } from '../../../../../../core/api/api.service';

@Injectable()
export class CampaignOrdersHandlerService {
    constructor(private _api: ApiService) {}

    findOrders(campaignId: string, orderDateFrom?: Date, orderDateTo?: Date, status?: string, orderNumber?: string,
        document?: string, skip?: number, limit?: number): Observable<any[]> {

        const query: any = {};
        if (orderDateFrom) query.orderDateFrom = moment(orderDateFrom).format('YYYY-MM-DD');
        if (orderDateTo) query.orderDateTo = moment(orderDateTo).format('YYYY-MM-DD');
        if (status) query.status = status;
        if (orderNumber) query.orderNumber = orderNumber;
        if (document) query.document = document;
        if (skip != null) query.skip = skip;
        if (limit != null) query.limit = limit;
        return this._api.get(`/api/campaigns/${campaignId}/orders`, query);
    }

    findOrderById(campaignId: string, orderId: string): Observable<any> {
        return this._api.get(`/api/campaigns/${campaignId}/orders/${orderId}`, null, 20000);
    }

    aproverOrder(campaignId: string, orderId: string, statusDescription: string, status: string): Observable<any> {
        var payload:any = {
            statusDescription: statusDescription,
            status: status
        }
        return this._api.put(`/api/campaigns/${campaignId}/orders/${orderId}/aprove`, payload);
    }

    rejectOrder(campaignId: string, orderId: string, statusDescription: string, status: string): Observable<any> {
        var payload:any = {
            statusDescription: statusDescription,
            status: status
        }
        return this._api.put(`/api/campaigns/${campaignId}/orders/${orderId}/reject`, payload);
    }

    saveItemPrice(campaignId: string, orderId: string, itemGrouperId: string, skuId: string, item: any, process: boolean = false): Observable<any> {
        return this._api.put(`/api/campaigns/${campaignId}/orders/${orderId}/childrenorders/${itemGrouperId}/items/${skuId}/price?process=${process}`, item);
    }

    saveItemShipping(campaignId: string, orderId: string, itemGrouperId: string, skuId: string, item: any): Observable<any> {
        return this._api.put(`/api/campaigns/${campaignId}/orders/${orderId}/childrenorders/${itemGrouperId}/items/${skuId}/shipping`, item);
    }

    markItemAsDelivered(campaignId: string, orderId: string, itemGrouperId: string, skuId: string, item: any): Observable<any> {
        return this._api.put(`/api/campaigns/${campaignId}/orders/${orderId}/childrenorders/${itemGrouperId}/items/${skuId}/delivered`, item);
    }

    saveEvent(campaignId: string, orderId: string, itemGrouperId: string, skuId: string, event: any): Observable<any> {
        return this._api.post(`/api/campaigns/${campaignId}/orders/${orderId}/childrenorders/${itemGrouperId}/items/${skuId}/event`, event);
    }

    markItemProduction(campaignId: string, orderId: string, itemGrouperId: string, skuId: string, item: any): Observable<any> {
        return this._api.put(`/api/campaigns/${campaignId}/orders/${orderId}/childrenorders/${itemGrouperId}/items/${skuId}/production`, item);
    }


    cancelMasterOrder(campaignId: string, orderId: string, reason: string): Observable<any> {
        return this._api.put(`/api/campaigns/${campaignId}/orders/${orderId}/cancel`, { reason: reason });
    }

    cancelChildOrder(campaignId: string, orderId: string, itemGrouperId: string, reason: string): Observable<any> {
        return this._api.put(`/api/campaigns/${campaignId}/orders/${orderId}/childrenorders/${itemGrouperId}/cancel`, { reason: reason });
    }

    cancelItem(campaignId: string, orderId: string, itemGrouperId: string, skuId: string, reason: string): Observable<any> {
        return this._api.put(`/api/campaigns/${campaignId}/orders/${orderId}/childrenorders/${itemGrouperId}/items/${skuId}/cancel`, { reason: reason });
    }

	refundOrder(campaignId: string, orderId: string, refund: any): Observable<any> {
		return this._api.post(`/api/campaigns/${campaignId}/orders/${orderId}/refund`, refund, 60000);
	}

    refundOrderWithPaymentMethod(campaignId: string, orderId: string, itemGrouperId: string, paymentType: string, refund: any): Observable<any> {
		return this._api.post(`/api/campaigns/${campaignId}/orders/${orderId}/childrenorders/${itemGrouperId}/paymentmethods/${paymentType}/refunds`, refund, 60000);
	}

	refundChildOrder(campaignId: string, orderId: string, itemGrouperId: string, refund: any): Observable<any> {
		return this._api.post(`/api/campaigns/${campaignId}/orders/${orderId}/childrenorders/${itemGrouperId}/refund`, refund, 60000);
	}

	refundItem(campaignId: string, orderId: string, refund: any): Observable<any> {
		return this._api.post(`/api/campaigns/${campaignId}/orders/${orderId}/refunditem`, refund, 60000);
    }

    saveMultiplePrices(campaignId: string, orderId: string, itemGrouperId: string, items: any): Observable<any> {
        return this._api.put(`/api/campaigns/${campaignId}/orders/${orderId}/childrenorders/${itemGrouperId}/price/multiple`, items, 20000);
    }

    cancelMultipleItems(campaignId: string, orderId: string, itemGrouperId: string, items: any): Observable<any> {
        return this._api.put(`/api/campaigns/${campaignId}/orders/${orderId}/childrenorders/${itemGrouperId}/cancel/multiple`, items, 20000);
    }

    createInvoiceForPartnerOrder(campaignId: string, orderId: string, itemGrouperId: string): Observable<any> {
        return this._api.put(`/api/campaigns/${campaignId}/orders/${orderId}/childrenorders/${itemGrouperId}/invoice`, 20000);
    }

    notifyOrderVouchers(campaignId: string, orderId: string, itemGrouperId: string): Observable<any[]> {
        return this._api.post(`/api/campaigns/${campaignId}/orders/${orderId}/childrenorders/${itemGrouperId}/vouchers/notify`, {}, 20000);
    }

    conciliateOrder(campaignId: string, orderId: string, itemGrouperId: string) {
        return this._api.post(`/api/campaigns/${campaignId}/orders/${orderId}/childrenorders/${itemGrouperId}/conciliateintegration`, 60000);
    }

}
