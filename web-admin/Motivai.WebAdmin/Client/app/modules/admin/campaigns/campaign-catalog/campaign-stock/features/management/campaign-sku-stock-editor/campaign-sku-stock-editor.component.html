<gp-alert [overlay]="true" #gpAlert></gp-alert>

<form (ngSubmit)="saveStock(stockForm)" #stockForm="ngForm">
	<gp-card title="SKU" [first]="true">
		<campaign-partner-sku-selector name="partner<PERSON><PERSON>" [campaignId]="_campaignId" [disabled]="isEditing"
			[(ngModel)]="skuStock.sku" required></campaign-partner-sku-selector>
	</gp-card>

	<gp-card title="Grupo de Participantes">
		<gp-form-row>
			<campaign-participants-group-selector name="participantGroup" [(ngModel)]="participantGroup.id"
				(select)="setSelectedGroup($event)"></campaign-participants-group-selector>
		</gp-form-row>
	</gp-card>

	<gp-card title="Estoque">
		<spinner [overlay]="true" [show]="loading"></spinner>
		<gp-form-row>
			<gp-form-col cols="12 12">
				<label>Gerenciar Estoque</label>
				<div>
					<gp-switch name="active" [(ngModel)]="skuStock.active"></gp-switch>
				</div>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Estoque Mínimo">
					<gp-input-mask  id="minStock" name="minStock" [onlyInteger]="true" [(ngModel)]="stock.minimumQuantity">
					</gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Estoque Máximo">
					<gp-input-mask id="maxStock" name="maxStock" [onlyInteger]="true" [(ngModel)]="stock.maximumQuantity">
					</gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-simple-input label="E-mail para notificação de estoque mínimo">
					<input type="text" class="form-control" name="notifyEmail" [(ngModel)]="stock.notifyEmail" />
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<hr />

		<gp-form-row>
			<gp-form-col cols="12 4 3">
				<gp-simple-input label="Estoque Atual">
					<gp-input-mask name="currentStock" [onlyInteger]="true" [(ngModel)]="stock.currentQuantity" [disabled]="true">
					</gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 4 3">
				<gp-simple-input label="Movimentar do Estoque">
					<gp-input-mask name="movementAmount" [onlyInteger]="true" [(ngModel)]="movementAmount" [disabled]="cannotMovementStock">
					</gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 4 4" additionalClasses="top-p27">
				<gp-spinner-button type="button" bootstrapClass="default" icon="plus" text="Aumentar" marginRight="1em"
					[loading]="movementing" loadingText="Processando" [disabled]="cannotMovementStock" (click)="increaseStock()">
				</gp-spinner-button>
				<gp-spinner-button type="button" bootstrapClass="default" icon="minus" text="Reduzir"
					[loading]="movementing" loadingText="Processando" [disabled]="cannotMovementStock" (click)="decreaseStock()">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</gp-card>
	
	<gp-card [last]="true">
		<gp-form-row>
			<gp-form-col cols="12 12" [inputGroup]="false">
				<gp-spinner-button type="submit" icon="send" text="Salvar" [pink]="true" pull="right"
					[loading]="sending" loadingText="Salvando"></gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</gp-card>
</form>
