import { Injectable } from "@angular/core";
import { ApiService } from "../../../../../core/api/api.service";
import { Observable } from "rxjs";

@Injectable()
export class CampaignCatalogPageCustomizationService {
	constructor(private _api: ApiService) { }

    saveCampaignCatalogPageCustomization(campaignId: string, pageCustomization: any): Observable<any> {
        if (pageCustomization.id) {
            console.log(pageCustomization);

            return this._api.put(`/api/campaigns/${campaignId}/catalog/page/customization/${pageCustomization.id}`, pageCustomization);
        }

    return this._api.post(`/api/campaigns/${campaignId}/catalog/page/customization`, pageCustomization);
    }

    getCampaignCatalogPageCustomizationById(campaignId: string, id: string): Observable<any> {
        return this._api.get(`/api/campaigns/${campaignId}/catalog/page/customization/${id}`);
    }

    getCampaignCatalogPageCustomizations(campaignId: string): Observable<any[]> {
        return this._api.get(`/api/campaigns/${campaignId}/catalog/page/customization`);
    }

}
