import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs/Subscription';

import { CampaignStore } from '../../../campaign.store';
import { AuthStore } from '../../../../../../core/auth/auth.store';
import { RxjsHelpers } from '../../../../../../shared/helpers/rxjs-helpers';

@Component({
  selector: 'campaign-orders-view',
  templateUrl: 'campaign-orders-view.component.html'
})
export class CampaignOrdersViewComponent implements OnInit, OnDestroy {
  campaignId: string;

  private _campaign$: Subscription;

  constructor(
    private _authStore: AuthStore,
    private _campaignStore: CampaignStore
  ) {}

  ngOnInit() {
    this._campaign$ = this._campaignStore.asObservable.subscribe(
      id => (this.campaignId = id)
    );
  }

  ngOnDestroy(): void {
    RxjsHelpers.unsubscribe(this._campaign$);
  }

  get showMenublock() {
    return this._campaignStore.isFullCampaignOrUserWithGpBu;
  }

  get canViewOrders() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_CATALOGS_ORDERS;
  }

  get canViewExtraServicesOrders() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_CATALOG_ORDERS_BILL_PAYMENTS_RECHARGES_VIEW;
  }

  get canAccessCampaignsOrdersImport() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_IMPORT_ORDERS_VIEW;
  }

  get canOrder() {
		return this._authStore.role.PERMISSION_CAMPAIGNS_CATALOG_ORDERS_PRODUCTS;
	}
}
