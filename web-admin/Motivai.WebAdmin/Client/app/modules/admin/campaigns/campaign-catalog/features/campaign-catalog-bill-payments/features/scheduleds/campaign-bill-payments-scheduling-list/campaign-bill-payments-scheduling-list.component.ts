import { Component, OnInit, ViewChild, Output, EventEmitter, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';

import { AuthStore } from '../../../../../../../../../core/auth/auth.store';
import { FormatHelper } from '../../../../../../../../../shared/formatters/format-helper';
import { CampaignStore } from '../../../../../../campaign.store';
import { ScheduledBillPaymentsService } from '../services/scheduled-bill-payments-service.service';
import { GpAlertComponent } from '../../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpGridComponent } from '../../../../../../../../../shared/components/gp-grid/gp-grid.component';
import { RxjsHelpers } from '../../../../../../../../../shared/helpers/rxjs-helpers';
import { ScheduledPaymentExternalManualPaymentComponent } from '../payments-handling-actions/scheduled-payment-external-manual-payment/scheduled-payment-external-manual-payment.component';

@Component({
    selector: 'campaign-bill-payments-scheduling-list',
    templateUrl: './campaign-bill-payments-scheduling-list.component.html'
})
export class CampaignBillPaymentsSchedulingListComponent implements OnInit, OnDestroy {
    @ViewChild('alert') alert: GpAlertComponent;
    @ViewChild('gpGrid') gpGrid: GpGridComponent;
    @ViewChild('externalManualPayment') externalManualPayment: ScheduledPaymentExternalManualPaymentComponent;
    @Output('onDetails') onDetails: EventEmitter<any> = new EventEmitter();

    schedulePayments: any[] = [];

    params: any = {};

    private skip: number = 0;
    private limit: number = 10;

    loading: boolean = false;
    processing: boolean = false;

    private _campaign$: Subscription;

    constructor(private _service: ScheduledBillPaymentsService, private _campaignStore: CampaignStore,
      private _authStore: AuthStore) { }

    get disableAction() {
      return this.loading || this.processing;
    }

    get canProcessPayments() {
      return this._authStore.role.PERMISSION_CAMPAIGNS_SCHEDULED_PAYMENTS_PROCESS;
    }

    ngOnInit() {
      this._campaign$ = this._campaignStore.asObservable
        .subscribe(id => this.find());
    }

    ngOnDestroy(): void {
      RxjsHelpers.unsubscribe(this._campaign$);
    }

    exportScheduledPaymentsFile() {
        this._service.exportScheduledPaymentsFile(this._campaignStore.id, this.params.userDocument, this.params.barcode,
                                                  this.params.status, this.params.from, this.params.to)
          .subscribe(
            response => {
                window.open(response, '_blank');
            },
            err => {
                this.alert.showError(err);
            }
        );
    }

    showDetails(event) {
        this.onDetails.emit(event);
    }

    resetThenSearchRecords() {
        this.skip = 0;
        this.limit = 10;
        this.gpGrid.resetPagination();
        this.find();
    }

    find() {
        if (this.loading) {
            return;
        }
        this.loading = true;
        this._service.getScheduledPayments(this._campaignStore.id, this.params.userDocument, this.params.barcode,
                                           this.params.status, this.params.from, this.params.to, this.skip, this.limit)
            .subscribe(
                response => {
                    if (response != null) {
                        response.forEach(x => {
                            if (x.status == 'ERROR') {
                                x.formattedStatus = 'Erro';
                            } else if (x.status == 'CANCELED') {
                                x.formattedStatus = 'Cancelado';
                            } else if (x.status == 'SCHEDULED') {
                                x.formattedStatus = 'Agendado';
                            } else if (x.status == 'PAID') {
                                x.formattedStatus = 'Pago';
                            } else if (x.status == 'PENDING') {
                                x.formattedStatus = 'Em Processamento';
                            }

                            x.formattedErrorOccurred = x.errorOccurred ? 'Sim' : 'Não';
                            x.formattedRefunded = x.refunded ? 'Sim' : 'Não';

                            x.formattedScheduledPaymentDate = FormatHelper.formatDate(x.scheduledPaymentDate);
                            x.formattedDueDate = FormatHelper.formatDate(x.billDetails.dueDate);
                            x.formattedCreateDate = FormatHelper.formatDate(x.createDate);
                            x.formattedOperationDate = FormatHelper.formatDate(x.operationDate);
                        });
                    }
                    this.schedulePayments = response;
                    this.loading = false;
                },
                err => {
                    this.loading = false;
                    this.alert.handleAndShowError(err);
                }
            );
    }

    onPageChanged(event) {
        if (event) {
            this.skip = event.skip;
            this.limit = event.limit;
            this.find();
        }
    }

    processScheduledPayments() {
      this.alert.confirm('Confirma a execução dos pagamentos? Antes confirme se tem saldo no parceiro.')
        .then(result => {
          if (result && result.value) {
            this.processing = true;
            this._service.processScheduledPayments(this._campaignStore.id)
              .subscribe(
                  result => {
                    this.processing = false;
                    this.alert.showSuccess('Processo de pagamento iniciado com sucesso.');
                  },
                  err => {
                    this.processing = false;
                    this.alert.handleAndShowError(err);
                  }
              );
          }
        });
    }


    showExternalManualPayment() {
        this.externalManualPayment.showModal();
    }
}
