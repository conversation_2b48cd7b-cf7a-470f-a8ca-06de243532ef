<gp-card [noPaddingTop]="true" [noPaddingBottom]="true" *ngIf="showMenublock">
	<div class="row">
		<accordion [closeOthers]="true">
			<accordion-group panelClass="b0 mb-sm panel-default no-shadow" [isOpen]="true">
				<div accordion-heading>
					<label><em class="fa fa-navicon"></em> Features de Pedidos</label>
				</div>
				<div class="row menublock text-center">
					<gp-menublock-item size="xs" icon="handshake-o" color="text-danger" text="Pedidos de Produtos e Vales"
						routerLinkActive="active" routerLink="tratativa" *ngIf="canViewOrders">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="money" color="text-danger" text="Pedidos de Pague Contas e Recargas"
						routerLinkActive="active" routerLink="paguecontas-e-recargas" *ngIf="canViewExtraServicesOrders">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="shopping-cart" color="text-danger" text="Resgatar Produto"
						routerLinkActive="active" routerLink="resgates" *ngIf="canOrder">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="cloud-upload" color="text-danger" text="Importação"
						routerLinkActive="active" routerLink="importar" *ngIf="canAccessCampaignsOrdersImport">
					</gp-menublock-item>

					<gp-menublock-item size="xs" icon="clock-o" color="text-danger" text="SLA" routerLinkActive="active"
						routerLink="sla">
					</gp-menublock-item>
				</div>
			</accordion-group>
		</accordion>
	</div>
</gp-card>
<router-outlet></router-outlet>