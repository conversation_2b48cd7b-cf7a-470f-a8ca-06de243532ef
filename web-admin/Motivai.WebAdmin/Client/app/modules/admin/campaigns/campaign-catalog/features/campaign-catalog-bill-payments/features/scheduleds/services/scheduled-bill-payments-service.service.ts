import { Injectable } from '@angular/core';
import { Observable } from 'rxjs/Observable';
import { ApiService } from '../../../../../../../../../core/api/api.service';
import moment = require('moment');

@Injectable()
export class ScheduledBillPaymentsService {

    constructor(private api: ApiService) { }

    getScheduledPayments(campaignId: string, userDocument: string, barcode: string, status: string, from: string, to: string, skip: number, limit:  number): Observable<any[]> {
        const params: any = { userDocument, barcode, status };
        if (from != null) params.from = moment(from).format('YYYY-MM-DD');
        if (to != null) params.to = moment(to).format('YYYY-MM-DD');
        if (skip != null) params.skip = skip;
        if (limit != null) params.limit = limit;
        return this.api.get(`/api/campaigns/${campaignId}/services/billpayments/schedulings`, params);
    }

    exportScheduledPaymentsFile(campaignId: string, userDocument: string, barcode: string, status: string, from: string, to: string) {
        const params: any = { userDocument, barcode, status };
        if (from != null) params.from = moment(from).format('YYYY-MM-DD');
        if (to != null) params.to = moment(to).format('YYYY-MM-DD');
        return this.api.get(`/api/campaigns/${campaignId}/services/billpayments/schedulings/export/file`, params);
    }

    getScheduledPaymentById(campaignId: string, paymentScheduledId: string): Observable<any> {
        return this.api.get(`/api/campaigns/${campaignId}/services/billpayments/schedulings/${paymentScheduledId}`);
    }

    processScheduledPayments(campaignId: string): Observable<any> {
      return this.api.post(`/api/campaigns/${campaignId}/services/billpayments/schedulings/process`, {});
    }

    cancelScheduledPayment(campaignId: string, paymentScheduledId: string, data: any): Observable<any> {
        return this.api.put(`/api/campaigns/${campaignId}/services/billpayments/schedulings/${paymentScheduledId}/cancel`, data);
    }

    reschedulePaymentDate(campaignId: string, paymentScheduledId: string, data: any): Observable<any> {
        return this.api.put(`/api/campaigns/${campaignId}/services/billpayments/schedulings/${paymentScheduledId}/scheduledPaymentDate`, data);
    }

    retryScheduledPaymentDate(campaignId: string, paymentScheduledId: string): Observable<any> {
        return this.api.put(`/api/campaigns/${campaignId}/services/billpayments/schedulings/${paymentScheduledId}/retryScheduledPayment`);
    }

    externalScheduledPayment(campaignId: string, paymentScheduledId: string, data: any): Observable<any> {
        return this.api.put(`/api/campaigns/${campaignId}/services/billpayments/schedulings/${paymentScheduledId}/externalScheduledPayment`, data);
    }

    getDailyResume(campaignId: string, userDocument: string, from: Date, to: Date): Observable<Array<any>> {
      const params: any = { userDocument };
      if (from != null) params.from = moment(from).format('YYYY-MM-DD');
      if (to != null) params.to = moment(to).format('YYYY-MM-DD');
      return this.api.get(`/api/campaigns/${campaignId}/services/billpayments/schedulings/reports/dailyresume`, params);
    }

    findParticipantInfoByDocument(campaignId: string, document: string): Observable<any> {
        return this.api.get(`/api/users/campaigns/${campaignId}/participant/infos`, { document });
    }

    sendExternalManualPayment(campaignId: string, externalManualPayment: any): Observable<any> {
        return this.api.post(`/api/campaigns/${campaignId}/services/billpayments/schedulings/externalManualPayment`, externalManualPayment, 60000);
    }
}
