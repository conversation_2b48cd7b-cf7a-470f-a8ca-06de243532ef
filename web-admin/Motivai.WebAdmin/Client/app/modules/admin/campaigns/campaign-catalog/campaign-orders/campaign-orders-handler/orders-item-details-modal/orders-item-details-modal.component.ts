import { Component, Input, Output, EventEmitter, ViewChild } from '@angular/core';
import { GpModalComponent } from '../../../../../../../shared/components/gp-modal/gp-modal.component';

@Component({
    selector: 'orders-item-details-modal',
    templateUrl: './orders-item-details-modal.component.html'
})
export class OrdersItemDetailsModalComponent {
    @Input() item: any = {};
    @Input() timezone: string;
    @ViewChild('modal') modal: GpModalComponent;
    @Output('onclose') onclose: EventEmitter<any> = new EventEmitter();

    constructor() { }

    get productTypeDescription() {
        if (this.item.productType == 'ValeVirtual')
            return 'Vale Virtual';
        if (this.item.productType == 'ValeFisico')
            return 'Vale Físico';
        return 'Produto';
    }

    open() {
        this.modal.show();
    }

    close() {
        this.onclose.emit(this.item);
    }
}
