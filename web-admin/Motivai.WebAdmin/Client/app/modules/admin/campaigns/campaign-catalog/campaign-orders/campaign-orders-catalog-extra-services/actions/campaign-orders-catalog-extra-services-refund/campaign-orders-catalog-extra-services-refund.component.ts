import { Component, Input, Output, ViewChild, EventEmitter } from '@angular/core';
import { GpAlertComponent } from '../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpModalComponent } from '../../../../../../../../shared/components/gp-modal/gp-modal.component';
import { CatalogExtraServicesOrdersService } from '../../services/catalog-extra-services-orders.service';

@Component({
  selector: 'campaign-orders-catalog-extra-services-refund',
  templateUrl: './campaign-orders-catalog-extra-services-refund.component.html'
})
export class CampaignOrdersCatalogExtraServicesRefundComponent {
  @ViewChild('alert') alert: GpAlertComponent;

  @ViewChild('orderRefundModal') orderRefundModal: GpModalComponent;

  @Output() completed: EventEmitter<any> = new EventEmitter();

  @Input() campaignId: string;

  @Input() participantInfo: any = {};

  @Input() catalogExtraServiceOrder: any = {};

  processingRefund: boolean;

  refundReason: string = '';

  constructor(private readonly service: CatalogExtraServicesOrdersService) {}

  showModal(): void {
    this.orderRefundModal.show();
  }

  resetForm(): void {
    this.participantInfo = {};
  }

  sendRefundOrder(): void {
    if (!this.refundReason.trim()) {
      return this.alert.showWarning('Motivo do estorno é obrigatório');
    }

    this.processingRefund = true;
    this.alert.confirm(`Deseja efetuar o estorno do pedido?`).then(result => {
      if (result && result.value) {
        this.service.refundOrder(this.campaignId, this.catalogExtraServiceOrder.id, this.refundReason).subscribe(
          response => {
            if (response) {
              this.alert.showSuccess('O pedido foi estornado com sucesso');
              this.completed.emit();
              this.orderRefundModal.hide();
              this.clear();
            } else {
              this.alert.showError('Ocorreu um erro ao estornar o pedido, por favor, tente novamente');
            }
            this.processingRefund = false;
          },
          err => {
            this.processingRefund = false;
            this.alert.showError(err);
          }
        );
      }
    });
  }

  clear() {
    this.refundReason = '';
  }
}
