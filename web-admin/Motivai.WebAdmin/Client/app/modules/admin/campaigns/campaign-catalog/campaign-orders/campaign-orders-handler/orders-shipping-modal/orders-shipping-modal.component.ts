import { Component, OnInit, Input, Output, ViewChild, EventEmitter } from '@angular/core';
import { GpModalComponent } from '../../../../../../../shared/components/gp-modal/gp-modal.component';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignOrdersHandlerService } from '../campaign-orders-handler.service';

@Component({
    selector: 'orders-shipping-modal',
    templateUrl: './orders-shipping-modal.component.html'
})
export class OrdersShippingModalComponent implements OnInit {
    @Input() item: any = {};
    @Input() campaignId: string;
    @Input() orderId: string;
    @Output('onclose') onclose: EventEmitter<any> = new EventEmitter();

    @ViewChild('modal') modal: GpModalComponent;
    @ViewChild('shippingAlert') shippingAlert: GpAlertComponent;
    @ViewChild('deliveredAlert') deliveredAlert: GpAlertComponent;
    loading: boolean = false;

    constructor(private _ordersService: CampaignOrdersHandlerService) { }
    ngOnInit(): void { }

    open() {
        this.shippingAlert.clear();
        this.modal.show();
    }
    
    close() {
        this.onclose.emit(this.item);
    }

    clear() {
        this.shippingAlert.clear();
        this.deliveredAlert.clear();
    }

    shippingSubmit() {
        if (this.item) {
            this.loading = true;
            this.shippingAlert.clear();
            this._ordersService.saveItemShipping(this.campaignId, this.orderId, this.item.itemGrouperId, this.item.skuId, this.item)
                .subscribe(item => {
                    if (item) {
                        this.shippingAlert.showSuccess('Informações de frete atualizadas com sucesso');
                    } else {
                        this.shippingAlert.showError('Ocorreu um erro ao salvar as informações do frete');
                    }
                    this.loading = false;
                }, err => {
                    const msg = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao salvar as informações de frete!';
                    this.shippingAlert.showError(msg);
                    this.loading = false;
                });
        }
    }

    markAsDelivered() {
        if (this.item) {
            this.loading = true;
            this.deliveredAlert.clear();
            this._ordersService.markItemAsDelivered(this.campaignId, this.orderId, this.item.itemGrouperId, this.item.skuId, this.item)
                .subscribe(item => {
                    if (item) {
                        this.deliveredAlert.showSuccess('Item marcado como Entregue!');
                    } else {
                        this.deliveredAlert.showError('Ocorreu um erro ao marcar o item como entregue');
                    }
                    this.loading = false;
                }, err => {
                    const msg = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao marcar o item como entregue';
                    this.deliveredAlert.showError(msg);
                    this.loading = false;
                });
        }
    }
}
