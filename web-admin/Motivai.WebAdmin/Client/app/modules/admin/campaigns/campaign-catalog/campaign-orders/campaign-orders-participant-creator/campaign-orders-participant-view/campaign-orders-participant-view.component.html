<spinner [show]="loading" [overlay]="true"></spinner>
<gp-alert [overlay]="true" #gpAlert></gp-alert>

<div>
	<gp-card title="Dados do Pedido">
		<gp-form-row>
			<gp-form-col cols="12 4 4">
				<gp-simple-input label="Pedido">
					<input type="text" class="form-control" name="name" id="internalOrderNumber"
						[ngModel]="order.internalOrderNumber" disabled>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<hr />

		<h4>Participante</h4>
		<gp-form-row>
			<gp-form-col cols="12 3 3">
				<gp-simple-input label="Nome">
					<input type="text" class="form-control" name="name" id="name" [ngModel]="selectedParticipant.name"
						disabled>
				</gp-simple-input>
			</gp-form-col>

			<gp-form-col cols="12 3 3">
				<gp-simple-input label="CPF/CNPJ">
					<input type="text" class="form-control" name="userDocument" id="userDocument"
						[ngModel]="selectedParticipant.document" disabled>
				</gp-simple-input>
			</gp-form-col>

			<gp-form-col cols="12 3 3">
				<gp-simple-input label="Login">
					<input type="text" class="form-control" name="login" id="login"
						[ngModel]="selectedParticipant.login" disabled>
				</gp-simple-input>
			</gp-form-col>

			<gp-form-col cols="12 3 3">
				<gp-simple-input label="Código">
					<input type="text" class="form-control" name="clientUserId" id="clientUserId"
						[ngModel]="selectedParticipant.clientUserId" disabled>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card title="Produtos">
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-grid name="products" [rows]="products"
					[columns]="['Parceiro', 'Produto', 'Tipo', 'Código SKU', 'Código Voucher', 'Data de Expiração', 'Quantidade', 'Valor Unitário', 'Ocorreu Erro', 'Mensagem Erro']"
					[fields]="['partnerName', 'productName', 'productType', 'skuCode', 'voucherCode', 'expirationDate', 'quantity', 'unitaryValue', 'occurredError', 'errorMessage']"
					[showActive]="false" [showPagination]="false" [showTotalPages]="false" [showEdit]="false" [showDelete]="false">
				</gp-grid>
			</gp-form-col>
		</gp-form-row>
	</gp-card>
</div>
