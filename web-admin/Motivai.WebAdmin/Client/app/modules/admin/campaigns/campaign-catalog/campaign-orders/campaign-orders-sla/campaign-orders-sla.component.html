<gp-card title="Configuração de SLA de pedidos">
	<gp-form-row>
		<div grid="12 12 12">
			<p>Habilitar SLA na tratativa de pedidos para os seguintes status nos ITENS do pedido!</p>
		</div>
	</gp-form-row>
	<gp-form-row>
		<div grid="12 12 12">
			<h4>Em análise</h4>
		</div>
		<div grid="12 3 3">
			<gp-simple-input label="Prazo (dias)">
				<gp-input-mask required [onlyInteger]="true" [(ngModel)]="underAnalysis.deadline"></gp-input-mask>
			</gp-simple-input>
		</div>
		<div grid="12 2 2">
			<label>Habilitar SLA</label>
			<div>
				<gp-switch [(ngModel)]="underAnalysis.enabled"></gp-switch>
			</div>
		</div>
		<div grid="12 2 2">
			<label>Considerar Sábado</label>
			<div>
				<gp-switch [(ngModel)]="underAnalysis.saturday"></gp-switch>
			</div>
		</div>
		<div grid="12 2 2">
			<label>Considerar Domingo</label>
			<div>
				<gp-switch [(ngModel)]="underAnalysis.sunday"></gp-switch>
			</div>
		</div>
		<div grid="12 2 2">
			<label>Considerar Feriados</label>
			<div>
				<gp-switch [(ngModel)]="underAnalysis.holidays"></gp-switch>
			</div>
		</div>
	</gp-form-row>
	<br />
	<gp-form-row>
		<div grid="12 12 12">
			<h4>Processado</h4>
		</div>
		<div grid="12 3 3">
			<gp-simple-input label="Prazo (dias)">
				<gp-input-mask required [onlyInteger]="true" [(ngModel)]="processed.deadline"></gp-input-mask>
			</gp-simple-input>
		</div>
		<div grid="12 2 2">
			<label>Habilitar SLA</label>
			<div>
				<gp-switch [(ngModel)]="processed.enabled"></gp-switch>
			</div>
		</div>
		<div grid="12 2 2">
			<label>Considerar Sábado</label>
			<div>
				<gp-switch [(ngModel)]="processed.saturday"></gp-switch>
			</div>
		</div>
		<div grid="12 2 2">
			<label>Considerar Domingo</label>
			<div>
				<gp-switch [(ngModel)]="processed.sunday"></gp-switch>
			</div>
		</div>
		<div grid="12 2 2">
			<label>Considerar Feriados</label>
			<div>
				<gp-switch [(ngModel)]="processed.holidays"></gp-switch>
			</div>
		</div>
	</gp-form-row>
	<br />
	<gp-form-row>
		<div grid="12 12 12">
			<h4>Em produção</h4>
		</div>
		<div grid="12 3 3">
			<gp-simple-input label="Prazo (dias)">
				<gp-input-mask required [onlyInteger]="true" [(ngModel)]="production.deadline"></gp-input-mask>
			</gp-simple-input>
		</div>
		<div grid="12 2 2">
			<label>Habilitar SLA</label>
			<div>
				<gp-switch [(ngModel)]="production.enabled"></gp-switch>
			</div>
		</div>
		<div grid="12 2 2">
			<label>Considerar Sábado</label>
			<div>
				<gp-switch [(ngModel)]="production.saturday"></gp-switch>
			</div>
		</div>
		<div grid="12 2 2">
			<label>Considerar Domingo</label>
			<div>
				<gp-switch [(ngModel)]="production.sunday"></gp-switch>
			</div>
		</div>
		<div grid="12 2 2">
			<label>Considerar Feriados</label>
			<div>
				<gp-switch [(ngModel)]="production.holidays"></gp-switch>
			</div>
		</div>
	</gp-form-row>
	<br />
	<gp-form-row>
		<div grid="12 12 12">
			<h4>Em Rota de Entrega</h4>
		</div>
		<div grid="12 3 3">
			<gp-simple-input label="Prazo (dias)">
				<gp-input-mask required [onlyInteger]="true" [(ngModel)]="shipping.deadline"></gp-input-mask>
			</gp-simple-input>
		</div>
		<div grid="12 2 2">
			<label>Habilitar SLA</label>
			<div>
				<gp-switch [(ngModel)]="shipping.enabled"></gp-switch>
			</div>
		</div>
		<div grid="12 2 2">
			<label>Considerar Sábado</label>
			<div>
				<gp-switch [(ngModel)]="shipping.saturday"></gp-switch>
			</div>
		</div>
		<div grid="12 2 2">
			<label>Considerar Domingo</label>
			<div>
				<gp-switch [(ngModel)]="shipping.sunday"></gp-switch>
			</div>
		</div>
		<div grid="12 2 2">
			<label>Considerar Feriados</label>
			<div>
				<gp-switch [(ngModel)]="shipping.holidays"></gp-switch>
			</div>
		</div>
	</gp-form-row>
	<br />
	<gp-form-row>
		<div grid="12 12 12">
			<h4>Erro</h4>
		</div>
		<div grid="12 3 3">
			<gp-simple-input label="Prazo (dias)">
				<gp-input-mask required [onlyInteger]="true" [(ngModel)]="error.deadline"></gp-input-mask>
			</gp-simple-input>
		</div>
		<div grid="12 2 2">
			<label>Habilitar SLA</label>
			<div>
				<gp-switch [(ngModel)]="error.enabled"></gp-switch>
			</div>
		</div>
		<div grid="12 2 2">
			<label>Considerar Sábado</label>
			<div>
				<gp-switch [(ngModel)]="error.saturday"></gp-switch>
			</div>
		</div>
		<div grid="12 2 2">
			<label>Considerar Domingo</label>
			<div>
				<gp-switch [(ngModel)]="error.sunday"></gp-switch>
			</div>
		</div>
		<div grid="12 2 2">
			<label>Considerar Feriados</label>
			<div>
				<gp-switch [(ngModel)]="error.holidays"></gp-switch>
			</div>
		</div>
	</gp-form-row>
</gp-card>
<gp-card title="Configurações de Feriados">
	<gp-form-row>
		<div grid="12 12 12">
			<p>Configure os feriados e paralisações no atendimento da campanha!</p>
		</div>
	</gp-form-row>
	<gp-form-row>
		<div grid="12 4 4">
			<gp-simple-input label="Descrição">
				<input type="text" class="form-control" name="description" [(ngModel)]="holiday.description" />
			</gp-simple-input>
		</div>
		<div grid="12 3 3">
			<gp-simple-input label="Dia">
				<select class="form-control" [(ngModel)]="holiday.day">
					<option *ngFor="let day of days" [value]="day">{{ day }}</option>
				</select>
			</gp-simple-input>
		</div>
		<div grid="12 3 3">
			<gp-simple-input label="Mês">
				<select class="form-control" [(ngModel)]="holiday.month">
					<option *ngFor="let month of months" [value]="month.value">{{ month.text }}</option>
				</select>
			</gp-simple-input>
		</div>
		<div grid="12 2 2">
			<gp-spinner-button pull="right" type="button" [pink]="true" icon="plus" text="Adicionar" marginTop="26px" (click)="addHoliday()"></gp-spinner-button>
		</div>
	</gp-form-row>
	<gp-form-row>
		<div grid="12 12 12">
			<gp-grid name="ordersGrid"
				[rows]="holidays" 
				[columns]="['Descrição', 'Dia', 'Mês']" 
				[fields]="['description', 'day', 'monthDescription']"
				[showActive]="false" [showPagination]="false" [showTotalPages]="false" [pageSize]="1000"
				[showEdit]="false" [showDelete]="true"
				[loading]="loading"
				(onDelete)="removeHoliday($event)">
			</gp-grid>
		</div>
	</gp-form-row>
</gp-card>
<gp-card>
	<gp-form-row>
		<div grid="12 12 12">
			<gp-spinner-button pull="right" [loading]="loading" [pink]="true" icon="send" text="Salvar configurações do SLA" (click)="saveSla()"></gp-spinner-button>
		</div>
		<div grid="12 12 12">
			<gp-alert #alert></gp-alert>
		</div>
	</gp-form-row>
</gp-card>
