import { Component, ViewChild, OnInit, OnDestroy, Output, EventEmitter } from '@angular/core';
import { FormGroup, FormBuilder, Validators, FormControl } from '@angular/forms';
import { CustomValidators } from 'ng2-validation';
import { Subscription } from 'rxjs';

import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignService } from '../../campaign.service';
import { ManufacturerService } from '../../../manufacturers/manufacturer.service';
import { CampaignStore } from '../../campaign.store';
import { RxjsHelpers } from '../../../../../shared/helpers/rxjs-helpers';
import { Item } from '../../../../../shared/models/item';
import { ParticipantsGroupService } from '../../campaign-participants/campaign-participants-groups/participants-groups.service';
import { CompanyService } from '../../../companies/company/company.service';
import { CampaignFilterService } from './campaign-filter.service';
import { GpModalComponent } from '../../../../../shared/components/gp-modal/gp-modal.component';
import { CampaignFiltersAllowByRulesComponent } from './campaign-filters-allow-byrules/campaign-filters-allow-byrules.component';
import { CampaignFiltersBlockByRulesComponent } from './campaign-filters-block-byrules/campaign-filters-block-byrules.component';

@Component({
  selector: 'campaign-filters',
  templateUrl: 'campaign-filters.component.html'
})
export class CampaignFiltersComponent implements OnInit, OnDestroy {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @ViewChild('skuFilterCreatorModal') skuFilterCreatorModal: GpModalComponent;

  @ViewChild('filterBlockByRules') filterBlockByRules: CampaignFiltersBlockByRulesComponent;
  @ViewChild('filterAllowByRules') filterAllowByRules: CampaignFiltersAllowByRulesComponent;


  @Output("filterBlockByRulesEvent") filterBlockByRulesEvent = new EventEmitter();
  @Output("filterAllowByRulesEvent") filterAllowByRulesEvent = new EventEmitter();
  campaignId: string;
  campaignFilters: any = {};

  formByValue: FormGroup;
  messages = {
    valueType: {
      required: 'Tipo do valor é obrigatório'
    },
    fromValue: {
      required: 'Valor é obrigatório',
      number: 'Valor inválido'
    },
    toValue: {
      required: 'Valor é obrigatório',
      number: 'Valor inválido'
    },
    floorValue: {
      required: 'Valor é obrigatório',
      number: 'Valor inválido'
    },
    ceilValue: {
      required: 'Valor é obrigatório',
      number: 'Valor inválido'
    }
  };

  formByManufact: FormGroup;
  messagesManufact = { manufacturerId: { required: 'Marca é obrigatória' } };

  formByPartner: FormGroup;
  partnerMessages = { partnerId: { required: 'Parceiro é obrigatório' } };

  formByPartnerSku: FormGroup;
  partnerSkuMessages = { partnerId: { required: 'Parceiro é obrigatório' }, sku: { required: 'Código do SKU é obrigatório' } };

  formByPartnerSkuWhitelist: FormGroup;
  partnerSkuWhitelistMessages = { partnerId: { required: 'Parceiro é obrigatório' }, sku: { required: 'Código do SKU é obrigatório' } };

  formByGroupedSkus: FormGroup;
  groupedSkusMessages = { groupId: { required: 'Grupo é obrigatório' }, partnerId: { required: 'Parceiro é obrigatório' }, sku: { required: 'Código do SKU é obrigatório' } };

  ruleFilterModel = {
    departmentId: '',
    categoryId: '',
    subcategoryId: ''
  };

  hasValueFilter: boolean = false;
  isRange: boolean = false;
  isCeil: boolean = false;
  isFloor: boolean = false;

  loadingFilters: boolean = false;
  loading: boolean = false;
  sending: boolean = false;

  campaignPartners: Array<Item> = [];
  manufacturers: Array<Item> = [];

  participantsGroups: Array<Item> = [];
  product: any = { name: null };
  editingGroupedSkus: boolean = false;
  participantGroupSkus: any = {
    canSelectGroup: true,
    participantGroupId: null,
    skus: []
  };

  params: any = {
    skip: 0,
    limit: 20
  };

  private _campaign$: Subscription;

  constructor(private _campaignStore: CampaignStore, private _campaignService: CampaignService,
      private _filterService: CampaignFilterService, private _companyService: CompanyService,
      private _manufacturerService: ManufacturerService, private _groupsService: ParticipantsGroupService,
      fb: FormBuilder) {
    this.createForms(fb);
  }

  ngOnInit() {
    this._campaign$ = this._campaignStore.asObservable
      .subscribe(id => {
        this.campaignId = id;
        this.loadingFilters = true;
        this.loadFilters();
        this.loadCombos();
      });
  }

  ngOnDestroy() {
    RxjsHelpers.unsubscribe(this._campaign$);
  }

  get disableButtons() {
    return this.loading || this.sending;
  }

  openSkuFilterCreatorModal() {
    this.skuFilterCreatorModal.show();
  }

  private createForms(fb: FormBuilder) {
    this.createFormByValue(fb);
    this.formByManufact = fb.group({
      partnerId: [''],
      manufacturerId: ['', Validators.required]
    });
    this.formByPartner = fb.group({ partnerId: [''] });
    this.formByPartnerSku = fb.group({
      partnerId: ['', Validators.required],
      sku: ['', Validators.required]
    });
    this.formByPartnerSkuWhitelist = fb.group({
      partnerId: ['', Validators.required],
      sku: ['', Validators.required]
    });
    this.formByGroupedSkus = fb.group({
      partnerId: ['', Validators.required],
      skuCode: ['', Validators.required]
    });
  }

  private createFormByValue(fb: FormBuilder) {
    const valueTypeControl = new FormControl('', Validators.required);
      const fromValueControl = new FormControl({value: '', disabled: true}, Validators.compose([Validators.required, CustomValidators.number]));
      const toValueControl = new FormControl({value: '', disabled: true}, Validators.compose([Validators.required, CustomValidators.number]));
      const floorValueControl = new FormControl({value: '', disabled: true}, Validators.compose([Validators.required, CustomValidators.number]));
      const ceilValueControl = new FormControl({value: '', disabled: true}, Validators.compose([Validators.required, CustomValidators.number]));

      this.formByValue = fb.group({
        valueType: valueTypeControl,
        fromValue: fromValueControl,
        toValue: toValueControl,
        floorValue: floorValueControl,
        ceilValue: ceilValueControl
      });

      valueTypeControl.valueChanges
        .subscribe(value => {
          fromValueControl.disable();
          toValueControl.disable();
          floorValueControl.disable();
          ceilValueControl.disable();
          this.isRange = value === 'Range';
          this.isCeil = value === 'Ceil';
          this.isFloor = value === 'Floor';

          if (this.isRange) {
            fromValueControl.enable();
            toValueControl.enable();
          } else if (this.isCeil) {
            ceilValueControl.enable();
          } else if (this.isFloor) {
            floorValueControl.enable();
          }
        });
  }

  private loadCombos() {
    this.loading = true;
    this._campaignService.getLinkedPartnersDataByCampaign(this.campaignId)
      .subscribe(
        linkedPartners => {
          this.campaignPartners = linkedPartners.map(part => Item.of(part.partnerId, part.partnerName));
        },
        err => {
          this.loading = false;
          this.handleError(err)
        }
      );

    this._groupsService.search(this.campaignId)
      .subscribe(
        groups => {
          if (groups) {
            this.participantsGroups = groups.map(g => Item.of(g.id, g.name));
          }
        },
        err => {
          this.loading = false;
          this.handleError(err)
        }
      );
  }

  handleError(err) {
    this.gpAlert.handleAndShowError(err);
    this.loading = false;
    this.sending = false;
  }

  loadFilters() {
    this.loadFiltersByValue();
    this.loadFiltersByManufactures();
    this.loadFiltersByPartners();
    this.loadFiltersByBlockRules();
    this.loadFiltersByAllowRules();
    this.loadFiltersByPartnerRules();
    this.loadFiltersBySku();
    this.loadFiltersByParticipantsGroups();
  }

  loadFiltersByValue() {
    this.loading = true;
    this._filterService.getFiltersByValue(this.campaignId)
      .subscribe(
        filters => {
          this.campaignFilters.byValue = filters;
          this.hasValueFilter = filters && filters.id;
          this.setFormValues();
          this.loading = false;
          this.loadingFilters = false;
        },
        err => {
          this.loading = false;
          this.loadingFilters = false;
          this.handleError(err)
        }
      );
  }

  loadFiltersByManufactures() {
    this.loading = true;
    this._filterService.getFiltersByManufactures(this.campaignId, this.params)
      .subscribe(
        filters => {
          this.campaignFilters.byManufacturers = filters;
          this.setFormValues();
          this.loading = false;
          this.loadingFilters = false;
        },
        err => {
          this.loading = false;
          this.loadingFilters = false;
          this.handleError(err)
        }
      );
  }

  loadFiltersByPartners() {
    this.loading = true;
    this._filterService.getFiltersByPartners(this.campaignId, this.params)
      .subscribe(
        filters => {
          this.campaignFilters.byPartners = filters;
          this.setFormValues();
          this.loading = false;
          this.loadingFilters = false;
        },
        err => {
          this.loading = false;
          this.loadingFilters = false;
          this.handleError(err)
        }
      );
  }

  loadFiltersByBlockRules() {
    this.filterBlockByRules.loadFiltersByBlockRules();
  }

  loadFiltersByAllowRules() {
    this.filterAllowByRules.loadFiltersByAllowRules();
  }

  loadFiltersByPartnerRules() {
    this.loading = true;
    this._filterService.getFiltersByPartnerRules(this.campaignId, this.params)
      .subscribe(
        filters => {
          this.campaignFilters.byPartnersSkus = filters;
          this.setFormValues();
          this.loading = false;
          this.loadingFilters = false;
        },
        err => {
          this.loading = false;
          this.loadingFilters = false;
          this.handleError(err)
        }
      );
  }

  loadFiltersBySku() {
    this.loading = true;
    this._filterService.getFiltersBySku(this.campaignId, this.params)
      .subscribe(
        filters => {
          this.campaignFilters.whitelistBySkus = filters;
          this.setFormValues();
          this.loading = false;
          this.loadingFilters = false;
        },
        err => {
          this.loading = false;
          this.loadingFilters = false;
          this.handleError(err)
        }
      );
  }

  loadFiltersByParticipantsGroups() {
    this.loading = true;
    this._filterService.getFiltersByParticipantsGroups(this.campaignId, this.params)
      .subscribe(
        filters => {
          this.participantGroupSkus = filters;
          this.setFormValues();
          this.loading = false;
          this.loadingFilters = false;
        },
        err => {
          this.loading = false;
          this.loadingFilters = false;
          this.handleError(err)
        }
      );
  }

  onPageChanged(event) { 
    if (event) {
      this.params.skip = event.skip;
      this.params.limit = event.limit;
      this.loadFiltersBySku();
    }
  }

  private setFormValues() {
    this.formByValue.reset();
    this.formByManufact.reset();
    this.formByPartner.reset();
    this.formByPartnerSku.reset();
    this.formByPartnerSkuWhitelist.reset();
    this.formByGroupedSkus.reset();
    if (this.campaignFilters.byValue) {
      this.formByValue.patchValue({
        valueType: this.campaignFilters.byValue.valueType,
        fromValue: this.campaignFilters.byValue.fromValue,
        toValue: this.campaignFilters.byValue.toValue,
        floorValue: this.campaignFilters.byValue.floorValue,
        ceilValue: this.campaignFilters.byValue.ceilValue
      });
    }
    this.ruleFilterModel = {
      departmentId: '',
      categoryId: '',
      subcategoryId: ''
    };
  }

  filterManufacturer(name: string) {
    if(!name || name.length == 0) {
      this.manufacturers = [];
      return;
    }
    this._manufacturerService.getByName(name)
      .subscribe(
        manufacturers => {
          if (manufacturers && manufacturers.length) {
            this.manufacturers = manufacturers.map(m => Item.of(m.id, m.name));
          } else {
            this.manufacturers = [];
          }
        },
        err => this.handleError(err)
      );
  }

  private setSending() {
    this.sending = true;
    this.gpAlert.clear();
  }

  saveValueFilter(event) {
    if (!event.valid) {
      this.gpAlert.showWarning('Preencha os campos para continuar.');
      return;
    }
    this.setSending();
    let valueFilter = event.model;
    this._filterService.saveValueFilter(this.campaignId, valueFilter)
      .subscribe(
        result => {
          if (result) {
            this.gpAlert.showSuccess('Filtro por valor salvo com sucesso.');
            this.campaignFilters.byValue = valueFilter;
            this.hasValueFilter = true;
          } else {
            this.gpAlert.showWarning('Não foi possível salvar filtro por valor, por favor, tente novamente.');
          }
        },
        err => this.handleError(err),
        () => this.sending = false
      );
  }

  removeValueFilter() {
    if (!confirm('Deseja remover o filtro por valor?')) return;
    this.setSending();
    this._filterService.removeValueFilter(this.campaignId)
      .subscribe(
        result => {
          if (result) {
            this.gpAlert.showSuccess('Filtro por valor removido com sucesso.');
            this.formByValue.reset();
            this.campaignFilters.byValue = null;
            this.hasValueFilter = false;
          } else {
            this.gpAlert.showWarning('Não foi possível remover o filtro, por favor, tente novamente.');
          }
        },
        err => this.handleError(err),
        () => this.sending = false
      );
  }

  saveManufactFilter(event) {
    if (!event.valid) {
      this.gpAlert.showWarning('Preencha os campos para continuar.');
      return;
    }
    this.setSending();
    this._filterService.addManufacturerFilter(this.campaignId, event.model)
      .subscribe(
        result => {
          if (result) {
            this.gpAlert.showSuccess('Filtro de fabricante adicionado com sucesso.');
            this.formByManufact.reset();
            if (!this.campaignFilters.byManufacturers)
              this.campaignFilters.byManufacturers = [];
            this.campaignFilters.byManufacturers.push(result);
          } else {
            this.gpAlert.showWarning('Não foi possível adicionar o filtro, por favor, tente novamente.');
          }
        },
        err => this.handleError(err),
        () => this.sending = false
      );
  }

  removeManufacturerFilter(filter: any) {
    if (!confirm(`Deseja excluir o filtro do fabricante '${filter.manufacturerName}'?`)) return;
    this.setSending();
    this._filterService.removeManufacturerFilter(this.campaignId, filter.id)
      .subscribe(
        result => {
          if (result) {
            if (this.campaignFilters.byManufacturers) {
              this.campaignFilters.byManufacturers = this.campaignFilters.byManufacturers.filter(m => m.id != filter.id);
            }
            this.gpAlert.showSuccess('Filtro de fabricante removido com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível remover o filtro, por favor, tente novamente.');
          }
        },
        err => this.handleError(err),
        () => this.sending = false
      );
  }

  savePartnerFilter(event) {
    if (!event.valid) {
      this.gpAlert.showWarning('Preencha os campos para continuar.');
      return;
    }
    this.setSending();
    this._filterService.addPartnerFilter(this.campaignId, event.model.partnerId)
      .subscribe(
        result => {
          if (result) {
            if (this.campaignPartners) {
              let addedPartner = this.campaignPartners.find(p => p.id == event.model.partnerId);
              if (addedPartner) {
                if (!this.campaignFilters.byPartners) {
                  this.campaignFilters.byPartners = [];
                }
                this.campaignFilters.byPartners.push({ id: addedPartner.id, name: addedPartner.text });
              }
            }
            this.formByPartner.reset();
            this.gpAlert.showSuccess('Filtro de parceiro adicionado com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível adicionar o filtro, por favor, tente novamente.');
          }
        },
        err => this.handleError(err),
        () => this.sending = false
      );
  }

  removePartnerFilter(filter: any) {
    if (!confirm(`Deseja excluir o filtro do parceiro '${filter.name}'?`)) return;
    this.setSending();
    this._filterService.removePartnerFilter(this.campaignId, filter.id)
      .subscribe(
        result => {
          if (result) {
            if (this.campaignFilters.byPartners) {
              this.campaignFilters.byPartners = this.campaignFilters.byPartners.filter(p => p.id != filter.id);
            }
            this.gpAlert.showSuccess('Filtro de parceiro removido com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível remover o filtro, por favor, tente novamente.');
          }
        },
        err => this.handleError(err),
        () => this.sending = false
      );
  }

  savePartnerSkuFilter(event) {
    if (!event.valid) {
      this.gpAlert.showWarning('Preencha os campos para continuar.');
      return;
    }
    this.setSending();
    this._filterService.addPartnerSkuFilter(this.campaignId, event.model)
      .subscribe(
        result => {
          if (result) {
            this.gpAlert.showSuccess('Filtro de produto do parceiro adicionado com sucesso.');
            this.formByPartnerSku.reset();
            if (!this.campaignFilters.byPartnersSkus)
              this.campaignFilters.byPartnersSkus = [];
            this.campaignFilters.byPartnersSkus.push(result);
          } else {
            this.gpAlert.showWarning('Não foi possível adicionar o filtro, por favor, tente novamente.');
          }
        },
        err => this.handleError(err),
        () => this.sending = false
      );
  }

  removePartnerSkuFilter(filter: any) {
    if (!confirm(`Deseja excluir o filtro do produto '${filter.productName}' do parceiro '${filter.partnerName}'?`)) return;
    this.setSending();
    this._filterService.removePartnerSkuFilter(this.campaignId, filter.id)
      .subscribe(
        result => {
          if (result) {
            if (this.campaignFilters.byPartnersSkus) {
              this.campaignFilters.byPartnersSkus = this.campaignFilters.byPartnersSkus.filter(s => s.id != filter.id && s.sku != filter.sku);
            }
            this.gpAlert.showSuccess('Filtro de produto do parceiro removido com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível remover o filtro, por favor, tente novamente.');
          }
        },
        err => this.handleError(err),
        () => this.sending = false
      );
  }

  savePartnerSkuToWhitelist(event) {
    if (!event.valid) {
      this.gpAlert.showWarning('Preencha os campos para continuar.');
      return;
    }
    this.setSending();
    this._filterService.addPartnerSkuToWhitelist(this.campaignId, event.model)
      .subscribe(
        result => {
          if (result) {
            this.gpAlert.showSuccess('Filtro de produto do parceiro adicionado com sucesso.');
            this.formByPartnerSkuWhitelist.reset();
            if (!this.campaignFilters.whitelistBySkus)
              this.campaignFilters.whitelistBySkus = [];
            this.campaignFilters.whitelistBySkus.push(result);
            this.skuFilterCreatorModal.hide()
          } else {
            this.gpAlert.showWarning('Não foi possível adicionar o filtro, por favor, tente novamente.');
          }
        },
        err => this.handleError(err),
        () => this.sending = false
      );
  }

  removePartnerSkuToWhitelist(filter: any) {
    if (!confirm(`Deseja excluir o filtro do produto '${filter.productName}' do parceiro '${filter.partnerName}'?`)) return;
    this.setSending();
    this._filterService.removePartnerSkuToWhitelist(this.campaignId, filter.id)
      .subscribe(
        result => {
          if (result) {
            if (this.campaignFilters && this.campaignFilters.whitelistBySkus) {
              this.campaignFilters.whitelistBySkus = this.campaignFilters.whitelistBySkus.filter(p => p.id != filter.id && p.sku != filter.sku);
            }
            this.gpAlert.showSuccess('Filtro de produto do parceiro removido com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível remover o filtro, por favor, tente novamente.');
          }
        },
        err => this.handleError(err),
        () => this.sending = false
      );
  }

  private cleanGroup() {
    this.participantGroupSkus = {
      canSelectGroup: true,
      participantGroupId: null,
      skus: []
    };
    this.product = { name: null };
  }

  newGroup() {
    this.formByGroupedSkus.reset();
    this.editingGroupedSkus = true;
    this.cleanGroup();
  }

  cancelGroupEdit() {
    this.formByGroupedSkus.reset();
    this.editingGroupedSkus = false;
    this.cleanGroup();
  }

  addSkuInGroup(event) {
    if (!event.valid) {
      this.gpAlert.showWarning('Preencha os campos para continuar.');
      return;
    }
    if (!this.product.skuId) {
      this.gpAlert.showWarning('Selecione um SKU válido.');
      return;
    }

    let partnerId = '', skuCode = '';

    const partnerControl = this.formByGroupedSkus.get('partnerId');
    if (partnerControl)
      partnerId = partnerControl.value;

    const skuControl = this.formByGroupedSkus.get('skuCode');
    if (skuControl)
      skuCode = skuControl.value;

    if (this.participantGroupSkus.skus) {
      if (this.participantGroupSkus.skus.findIndex(s => s.partnerId == partnerId && s.skuId == this.product.skuId) > -1) {
        this.gpAlert.showWarning('SKU já está na lista do grupo.');
        return;
      }
    } else {
      this.participantGroupSkus.skus = [];
    }

    this.participantGroupSkus.skus.push({
      partnerId: partnerId,
      partnerName: this.product.partnerName,
      productId: this.product.id,
      skuId: this.product.skuId,
      skuCode: skuCode,
      skuName: this.product.name
    });

    this.formByGroupedSkus.reset();
    this.product = { name: '' };
  }

  editParticipantGroupWhitelist(event) {
    this.newGroup();
    this.participantGroupSkus = event;
  }

  removeSkuFromParticipantGroupWhitelist(event) {
    if (!event) return;
    this.participantGroupSkus.skus = this.participantGroupSkus.skus.filter(g => g.partnerId !== event.partnerId || g.skuId !== event.skuId);
  }

  removeParticipantGroupWhitelist(group: any) {
    if (!group || !group.id) return;
  }

  saveParticipantGroupWhitelist() {
    if (this.participantGroupSkus.canSelectGroup) {
      // Valida se selecionou grupo
      if(!this.participantGroupSkus.participantGroupId) {
        this.gpAlert.showWarning('Selecione o grupo de participante.');
        return;
      }

      // Valiad se o grupo selecionado já não tem uma config
      if (this.campaignFilters && this.campaignFilters.participantsGroupsWhitelists) {
        const existGroup = this.campaignFilters.participantsGroupsWhitelists
          .find(g => g.participantGroupId == this.participantGroupSkus.participantGroupId && g != this.participantGroupSkus);
        if (existGroup) {
          this.gpAlert.showWarning('Já existe uma lista de SKUs para o grupo de participante selecionado.');
          return;
        }
      }
    }

    const group = this.participantsGroups.find(g => g.id == this.participantGroupSkus.participantGroupId);
    if (group) {
      this.participantGroupSkus.participantGroupName = group.text;
    }

    this.participantGroupSkus.skusCount = this.participantGroupSkus.skus ? this.participantGroupSkus.skus.length : 0;

    this.setSending();
    this._filterService.saveParticipantGroupWhitelist(this.campaignId, this.participantGroupSkus)
      .subscribe(
        result => {
          this.sending = false;
          if (result) {
            this.gpAlert.showSuccess('Filtro salvo com sucesso.');
            this.cancelGroupEdit();
            this.loadFilters();
          } else {
            this.gpAlert.showWarning('Não foi possível salvar o filtro, por favor, tente novamente.');
          }
        },
        err => this.handleError(err)
      );
  }

  searchSku() {
    this.gpAlert.clear();
    this.product = null;
    let partnerId = '', skuCode = '';

    const partnerControl = this.formByGroupedSkus.get('partnerId');
    if (partnerControl)
      partnerId = partnerControl.value;

    const skuControl = this.formByGroupedSkus.get('skuCode');
    if (skuControl)
      skuCode = skuControl.value;

    if (!partnerId || !skuCode) {
      return;
    }

    this.loading = true;
    this.product = { name: 'Pesquisando...' };
    this._companyService.findPartnerProductBySkuCode(partnerId, skuCode)
      .subscribe(
        product => {
          this.loading = false;
          if (product) {
            this.product = product;
          } else {
            this.gpAlert.showWarning('Produto não encontrado pelo SKU informado.');
            this.product = { name: 'Produto não encontrado pelo SKU informado' };
          }
        },
        err => this.handleError(err)
      );
  }
}
