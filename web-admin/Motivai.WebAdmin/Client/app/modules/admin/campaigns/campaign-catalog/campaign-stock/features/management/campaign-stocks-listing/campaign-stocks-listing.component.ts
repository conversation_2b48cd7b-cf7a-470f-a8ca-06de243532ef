import { CampaignStockService } from '../../../campaign-stock.service';
import { GpAlertComponent } from '../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import {
  Component,
  ViewChild,
  Input,
  Output,
  EventEmitter
} from '@angular/core';
import { AuthStore } from '../../../../../../../../core/auth/auth.store';

@Component({
  selector: 'campaign-stocks-listing',
  templateUrl: 'campaign-stocks-listing.component.html'
})
export class CampaignStocksListingComponent {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;

  @Output('edit') editorEmitter: EventEmitter<any> = new EventEmitter<any>();

  loading: boolean = false;
  stocks: Array<any> = [];

  private _campaignId: string;

  constructor(private authStore: AuthStore, private _stockService: CampaignStockService) {}

  @Input('campaignId')
  set campaignId(campaignId: string) {
    this._campaignId = campaignId;
    if (this._campaignId) {
      this.loadStocks();
    }
  }

  public loadStocks() {
    this.loading = true;
    this._stockService.getStocks(this._campaignId).subscribe(
      stocks => {
        this.loading = false;
        this.stocks = stocks || [];
      },
      err => {
        this.loading = false;
        this.gpAlert.handleAndShowError(err);
      }
    );
  }

  editStock(stock: any) {
    this.editorEmitter.emit(stock);
  }

  get canEditOrUpdate() {
    return this.authStore.role.CAMPAIGNS_STOCK_EDIT;
  }
}
