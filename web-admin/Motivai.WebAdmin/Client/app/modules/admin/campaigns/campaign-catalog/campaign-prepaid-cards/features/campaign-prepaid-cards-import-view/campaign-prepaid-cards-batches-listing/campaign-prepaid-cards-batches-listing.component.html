<spinner [overlay]="true" [show]="loading"></spinner>
<gp-alert #gpAlert [overlay]="true"></gp-alert>

<gp-card [first]="true">
  <gp-form-row>
    <gp-form-col cols="12 5">
      <gp-simple-input label="Status do Lote">
        <gp-select name="batchStatus" [allowClear]="false" [items]="batchStatuses" [(ngModel)]="params.batchStatus">
        </gp-select>
      </gp-simple-input>
    </gp-form-col>

    <gp-form-col cols="12 2">
      <gp-spinner-button [search]="true" pull="left" text="Pesquisar" marginLeft="5px" marginTop="27px" [loading]="loading"
        loadingText="Processando" (click)="findAndCountBatches()"></gp-spinner-button>
    </gp-form-col>
  </gp-form-row>
</gp-card>

<gp-card title="Registros encontrados" [last]="true">
  <gp-form-row>
    <gp-form-col cols="12">
      <gp-grid name="grid" [rows]="prepaidCardsBatches" [loading]="loadingBatches"
        [columns]="['Data da Importação', 'Nome do Arquivo', 'Status']"
        [fields]="['formattedCreateDate', 'originalFilename', 'formattedStatus']"
        [showPagination]="true" [pageSize]="limit" [showTotalPages]="true" [totalRecords]="counterBatches" (onPageChanged)="changePage($event)"
        [showDelete]="false" [showEdit]="true" (onEdit)="showPrepaidCardsDetails($event)">
      </gp-grid>
    </gp-form-col>
  </gp-form-row>
</gp-card>
