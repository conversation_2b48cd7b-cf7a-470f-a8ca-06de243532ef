<gp-alert [overlay]="true" #alert></gp-alert>

<gp-modal title="Reagendar pagamento" #changeDatePaymentModal>
	<div class="row">
		<div class="col-xs-12">
			<gp-form-row>
				<gp-form-col cols="12 6 6">
					<label>Data de vencimento: </label>
					<strong>{{ paymentScheduled.formattedDueDate }} </strong>
				</gp-form-col>

				<gp-form-col cols="12 6 6">
					<label>Data anterior do agendamento: </label>
					<strong>{{ paymentScheduled.formattedScheduledPaymentDate }} </strong>
				</gp-form-col>
			</gp-form-row>

			<gp-form-row>
				<gp-datepicker cols="12 6 6" name="scheduledDate" [disableUntil]="today" [disableWeekends]="true"
					label="Nova data de agendamento" [required]="true"
					[(ngModel)]="reschedulePaymentDate.scheduledPaymentDate"></gp-datepicker>
			</gp-form-row>

			<gp-form-col cols="12">
				<gp-spinner-button text="Reagendar" [loading]="loadingProcessing" bootstrapClass="primary" pull="right"
					[disabled]="loading" (click)="changePaymentDate()"></gp-spinner-button>
			</gp-form-col>
		</div>
	</div>
</gp-modal>