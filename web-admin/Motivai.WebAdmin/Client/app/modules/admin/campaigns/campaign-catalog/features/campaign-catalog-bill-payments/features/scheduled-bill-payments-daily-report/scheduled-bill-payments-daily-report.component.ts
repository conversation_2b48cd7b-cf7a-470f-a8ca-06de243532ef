import { Component, ViewChild } from '@angular/core';

import { CampaignStore } from '../../../../../campaign.store';
import { ScheduledBillPaymentsService } from '../scheduleds/services/scheduled-bill-payments-service.service';
import { GpAlertComponent } from '../../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';

@Component({
  selector: 'app-scheduled-bill-payments-daily-report',
  templateUrl: './scheduled-bill-payments-daily-report.component.html',
  styles: [`
    table thead tr th {
      text-align: center;
      border-left: 1px solid #eee;
      border-right: 1px solid #eee;
    }
    table tbody tr td {
      padding-left: 0.5em;
      padding-right: 0.5em;
    }
  `]
})
export class ScheduledBillPaymentsDailyReportComponent {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;

  params: any = {};

  loading: boolean = false;
  dailyResume: Array<any> = [];

  constructor(private _campaignStore: CampaignStore, private _paymentsService: ScheduledBillPaymentsService) { }

  get isEmpty() {
    return !this.dailyResume || !this.dailyResume.length;
  }

  applyFilter() {
    if (!this.params.from || !this.params.to) {
      this.gpAlert.showWarning('Selecione o período de consulta.');
      return;
    }
    this.loading = true;
    this.dailyResume = [];
    this._paymentsService.getDailyResume(this._campaignStore.id, this.params.userDocument, this.params.from, this.params.to)
      .subscribe(
        resume => {
          this.loading = false;
          if (resume && resume.length) {
            this.dailyResume = resume;
          }
        },
        error => {
          this.loading = false;
          this.gpAlert.handleAndShowError(error);
        }
      );
  }
}
