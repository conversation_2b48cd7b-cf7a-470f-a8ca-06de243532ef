<gp-modal #modal title="Adicionar Filtros de Pagamento" width="900px" (onClose)="clear()">
	<spinner [overlay]="true" [show]="loading"></spinner>
	<gp-alert [overlay]="true" #alert></gp-alert>

	<gp-card>
		<gp-form-row>
			<gp-form-col cols="12 4 4">
				<gp-simple-input label="Informe o tipo do boleto" [required]="true">
					<gp-select name="typeBill" [items]="typeBills" [allowClear]="false" [(ngModel)]="filters.serviceType">
					</gp-select>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<hr />

		<h4>Selecione quais campos do boleto serão adicionados ao filtro</h4>
		<!-- <gp-form-row>
			<br />
			<gp-form-col cols="12 12 12">
				<gp-form-row>
					<gp-input-checkbox style="margin-top: 25px;" grid="12 4 4" name="receiverName" text="Nome do Beneficiário"
						[(ngModel)]="filters.applyReceiverName" (onclick)="onClickApplyReceiverName($event)">
					</gp-input-checkbox>

					<gp-form-col cols="12 8 8">
						<gp-simple-input label="Nome do Beneficiário">
							<input type="text" class="form-control" name="receiverName"
								[(ngModel)]="filters.receiverName" [disabled]="!applyReceiverName">
						</gp-simple-input>
					</gp-form-col>
				</gp-form-row>
			</gp-form-col>
		</gp-form-row> -->

		<gp-form-row>
			<gp-input-checkbox style="margin-top: 25px;" grid="12 4 4" name="receiverDocument" text="CPF/CNPJ do Beneficiário"
				[(ngModel)]="filters.applyReceiverDocument" (onclick)="onClickApplyReceiverDocument($event)">
			</gp-input-checkbox>

			<gp-form-col cols="12 8 8">
				<gp-simple-input label="CPF/CNPJ do Beneficiário">
					<input type="text" class="form-control" name="receiverDocument"
						[(ngModel)]="filters.receiverDocument" [disabled]="!applyReceiverDocument">
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<!-- <gp-form-row>
			<gp-input-checkbox style="margin-top: 25px;" grid="12 4 4" name="payerName" text="Nome do Pagador"
				[(ngModel)]="filters.applyPayerName" (onclick)="onClickApplyPayerName($event)">
			</gp-input-checkbox>

			<gp-form-col cols="12 8 8">
				<gp-simple-input label="Nome do Pagador">
					<input type="text" class="form-control" name="payerName"
						[(ngModel)]="filters.payerName" [disabled]="!applyPayerName">
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row> -->

		<!-- <gp-form-row>
			<gp-input-checkbox style="margin-top: 25px;" grid="12 4 4" name="payerDocument" text="CPF/CNPJ do Pagador"
				[(ngModel)]="filters.applyPayerDocument" (onclick)="onClickApplyPayerDocument($event)">
			</gp-input-checkbox>

			<gp-form-col style="margin-top: 25px;" cols="12 8 8">
				<gp-simple-input label="CPF/CNPJ do Pagador">
					<input type="text" class="form-control" name="payerDocument"
						[(ngModel)]="filters.payerDocument" [disabled]="!applyPayerDocument">
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row> -->

		<!-- <gp-form-row>
			<gp-input-checkbox style="margin-top: 25px;" grid="12 4 4" name="assignor" text="Emissor"
				[(ngModel)]="filters.applyAssignor" (onclick)="onClickApplyAssignor($event)">
			</gp-input-checkbox>

			<gp-form-col cols="12 8 8">
				<gp-simple-input label="Emissor">
					<input type="text" class="form-control" name="assignor"
						[(ngModel)]="filters.assignor" [disabled]="!applyAssignor">
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row> -->

		<!--gp-form-row>
			<gp-input-checkbox style="margin-top: 25px;" grid="12 4 4" name="billingAmount" text="Valor Líquido do Boleto"
				[(ngModel)]="filters.applyBillingAmount" (onclick)="onClickApplyBillingAmount($event)">
			</gp-input-checkbox>

			<gp-form-col cols="12 8 8">
				<gp-simple-input label="Valor Líquido do Boleto">
					<input type="text" class="form-control" name="billingAmount"
						[(ngModel)]="filters.billingAmount" [disabled]="!applyBillingAmount">
				</gp-simple-input>
			</gp-form-col>
		</!--gp-form-row-->
	</gp-card>

	<gp-card [last]="true">
		<gp-form-row>
			<gp-form-col cols="12" [inputGroup]="false">
				<gp-spinner-button type="button" [pink]="true" text="Salvar" pull="right"
					icon="send" [loading]="loading" loadingText="Aguarde" [disabled]="!isValid" (click)="onAddFilters()">
				</gp-spinner-button>

				<gp-spinner-button type="button" bootstrapClass="default" text="Voltar" pull="right"
					icon="arrow-left" marginRight="5px" [loading]="loading" (click)="onBack()">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</gp-card>
</gp-modal>
