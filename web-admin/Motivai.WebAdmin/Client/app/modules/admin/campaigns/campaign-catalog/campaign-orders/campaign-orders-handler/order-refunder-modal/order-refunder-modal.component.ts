import { Component, ViewChild, Output, EventEmitter } from '@angular/core';
import { v4 as uuid } from 'uuid';

import { GpModalComponent } from '../../../../../../../shared/components/gp-modal/gp-modal.component';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignOrdersHandlerService } from '../campaign-orders-handler.service';
import { AuthStore } from '../../../../../../../core/auth/auth.store';
import { CompanyService } from '../../../../../companies/company/company.service';
import { PartnerSettings } from '../../../../../companies/company/company';

@Component({
  selector: 'order-refunder-modal',
  templateUrl: 'order-refunder-modal.component.html'
})
export class OrderRefunderModalComponent {
  @ViewChild('modal') modal: GpModalComponent;
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @Output('onClose') onClose: EventEmitter<any> = new EventEmitter();

  private campaignId: string;
  order: any = {};
  childOrder: any;
  item: any;
  refund: any = {};

  optOriginalAmount: any = {};
  optRefundedAmount: any = {};
  optTotalAmount: any = {};
  originalAmount = 0.01;
  refundedAmount = 0.01;
  totalAmount = 0.01;
  transactionId: string;
  transactionDetails: any = {};

  refundReasons = [
    { key: 'Desistencia', value: 'Desistência' },
    { key: 'Arrependimento', value: 'Arrependimento' },
    { key: 'ProblemaPedido', value: 'Problema com o pedido' },
    { key: 'ProblemaItem', value: 'Problema com o item' },
    { key: 'ProblemaEntrega', value: 'Problema na entrega' },
    { key: 'Insatisfacao', value: 'Insatisfação' },
    { key: 'Outros', value: 'Outros' },
  ];

  canRefund: boolean = true;
  sending: boolean = false;

  constructor(private _authStore: AuthStore, private _orderService: CampaignOrdersHandlerService) {}

  get canRefundPartial() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_CATALOGS_ORDERS_PARTIAL_REFUND;
  }

  get modalTitle() {
    if (this.isShowingRefundList)
      return `Estornos do Item ${this.item.skuCode}`;
    if (this.isItemRefund)
      return `Estorno do Item ${this.item.skuCode}`;
    if (this.isChildOrderRefund)
      return `Estorno do Pedido Parceiro ${this.childOrder.partnerName}`;
    if (this.isMasterOrderRefund)
      return `Estorno do Pedido ${this.order.internalOrderNumber}`;
    return 'Estorno';
  }

  get isMasterOrChildOrderRefund() {
    return this.isMasterOrderRefund || this.isChildOrderRefund;
  }

  get isMasterOrderRefund() {
    return !this.item && !this.childOrder && this.order && this.order.id;
  }

  get isChildOrderRefund() {
    return !this.item && this.childOrder && this.childOrder.itemGrouperId && this.order && this.order.id;
  }

  get isItemRefund() {
    return this.item && this.item.skuId && this.childOrder && this.childOrder.itemGrouperId && this.order && this.order.id;
  }

  get isShowingRefundList() {
    return this.order && !this.childOrder && this.item && this.item.skuId;
  }

  get isPartial() {
    return this.refund && this.refund.refundType === 'Partial';
  }

  get hasPriceToRefund() {
    return this.item && this.item.unitPrice && this.item.unitPrice.points > 0;
  }

  get hasShippingToRefund() {
    return this.item && this.item.shippingCost && this.item.shippingCost.points > 0;
  }

  get hasAvalidPixPaymentMethod() {
    return this.hasPaymentMethod && this.order.paymentMethods.find(p => p.type == "PIX");
  }

  get hasAvalidTransactionDetails() {
    return this.hasAvalidPaymentMethod && this.order.paymentMethods.find(p => p.type == "PIX").transactionDetails != null;
  }

  get hasAvalidPaymentMethod() {
    return this.hasPaymentMethod && this.order.paymentMethods.find(p => p.type == "PIX");
  }

  get fieldButtonText() {
    if (this.hasPaymentMethod) return 'Registrar Estorno';
    return 'Confirmar Estorno';
}

  get hasPaymentMethod() {
    return this.order != null && this.order.paymentMethods != null && this.order.paymentMethods.length;
  }

  get hasAvalidTransactionDetailsMetadata() {
    return this.transactionDetails != null && this.transactionDetails.metadata != null && this.transactionDetails.metadata.length;
  }


  private handleError(err: any) {
    this.gpAlert.showError(err);
    this.sending = false;
  }

  private fetchKnob(subText, total, textValue, barColor): any {
    return {
      readOnly: true,
      inputFormatter: val => textValue,
      size: 180,
      textColor: '#666666',
      fontSize: '20',
      fontWeigth: '600',
      fontFamily: 'Verdana',
      valueformat: 'percent',
      min: 0,
      max: total || 100,
      trackWidth: 13,
      barWidth: 13,
      trackColor: '#EEEEEE',
      barColor: barColor,
      subText: {
        enabled: true,
        fontFamily: 'Verdana',
        font: '14',
        fontWeight: 'bold',
        text: subText,
        color: '#666666',
        offset: 7
      },
    };
  }

  private prepareCharts() {
    // calcula os totais
    this.refundedAmount = this.item.refunds ? this.item.refunds.filter(r => !r.errorOccurred).reduce((acc, r) => acc + (r.amount && r.amount.points ? r.amount.points : 0), 0) : 0;
    this.originalAmount = this.calculateSubtotalPoints(this.item) + this.refundedAmount;
    this.totalAmount = this.originalAmount - this.refundedAmount;

    const originalTotalFixed = this.originalAmount.toFixed(2);
    this.optOriginalAmount = this.fetchKnob('Total Original', originalTotalFixed, originalTotalFixed, '#2882E0');
    this.optRefundedAmount = this.fetchKnob('Estornado', originalTotalFixed, this.refundedAmount.toFixed(2), '#E90124');
    this.optTotalAmount = this.fetchKnob('Total Atual', originalTotalFixed, this.totalAmount.toFixed(2), '#35C64D');
  }

  private prepareItemRefund() {
    this.refund = {
      refundType: 'Full',
      orderId: this.order.id,
      itemGrouperId: this.childOrder.itemGrouperId,
      productId: this.item.productId,
      skuId: this.item.skuId,
      skuCode: this.item.skuCode,
      refundQuantity: this.item.quantity
    };
    this.prepareCharts();
  }

  private showModal() {
    this.modal.show();
  }

  close() {
    this.onClose.emit(this.item);
  }

  resetForm() {
    if (this.gpAlert)
      this.gpAlert.clear();
    this.canRefund = true;
    this.refund = {};
  }

  calculateSubtotalPoints(item: any): number {
    return item.unitPrice.points * item.quantity
      + (item.shippingCost && item.shippingCost.points ? item.shippingCost.points : 0);
  }

  calculateSubtotalCurrency(item: any): number {
    return item.unitPrice.currency * item.quantity
      + (item.shippingCost && item.shippingCost.currency ? item.shippingCost.currency : 0);
  }

  getRefundReasonDescription(refundReason: string): string {
    const reason = this.refundReasons.find(r => r.key == refundReason);
    return reason ? reason.value : '';
  }

  openModalToRefundMasterOrder(campaignId: string, order: any) {
    this.resetForm();
    this.setRefundTraceId();
    this.campaignId = campaignId;
    this.order = order;
    this.childOrder = null;
    this.item = null;
    this.showModal();
  }

  openModalToRefundChildOrder(campaignId: string, order: any, childOrder: any) {
    this.resetForm();
    this.setRefundTraceId();
    this.campaignId = campaignId;
    this.order = order;
    this.handleTransactionDetails();
    this.childOrder = childOrder;
    this.item = null;
    this.showModal();
  }

  openModalToRefundItem(campaignId: string, order: any, childOrder: any, item: any) {
    this.resetForm();
    this.campaignId = campaignId;
    this.order = order;
    this.childOrder = childOrder;
    this.item = item;
    if (this.item && this.item.skuId) {
      this.prepareItemRefund();
    }
    this.setRefundTraceId();
    this.showModal();
  }

  openModalToShowRefundList(order: any, item: any) {
    this.resetForm();
    this.order = order;
    this.childOrder = null;
    this.item = item;
    this.prepareCharts();
    this.showModal();
  }

  private setRefundTraceId(): void {
    this.refund.refundTraceId = uuid();
  }

  sendOrderRefund() {
    if (this.checkOrderIsUberPartner(this.order)) {
      return;
    }

    //TODO: chamar path para trazer o parceiro
    //validar com base na flag se o estorno integrado esta ativo
    if (this.isItemRefund) {
      this.sendItemRefund();
    } else if (this.isChildOrderRefund) {
      this.sendChildOrderRefund();
    } else if (this.isMasterOrderRefund) {
      this.sendMasterOrderRefund();
    }
  }

  private sendMasterOrderRefund() {
    this.gpAlert.clear();

    if (!this.refund.refundReason) {
      this.gpAlert.showWarning('Selecione o motivo o estorno.');
      return;
    } else if (!this.refund.refundNote) {
      this.gpAlert.showWarning('Nota adicional é obrigatória.');
      return;
    }
    if (!confirm(`Confirma o estorno do pedido ${this.order.internalOrderNumber}?`)) {
      return;
    }
    this.sending = true;

    this._orderService.refundOrder(this.campaignId, this.order.id, this.refund)
      .subscribe(
        wasRefunded => {
          this.sending = false;
          if (wasRefunded) {
            this.gpAlert.showSuccess('Estorno efetuado com sucesso.');
            this.canRefund = false;
          }
        },
        err => {
          this.sending = false;
          this.gpAlert.showError(err);
        }
      );
  }

  private refundOrderWithPaymentMethod() {
    this._orderService.refundOrderWithPaymentMethod(this.campaignId, this.order.id, this.childOrder.itemGrouperId, 'PIX', this.refund)
      .subscribe(
        wasRefunded => {
          this.sending = false;
          if (wasRefunded) {
            this.gpAlert.showSuccess('Estorno efetuado com sucesso.');
            this.canRefund = false;
          }
        },
        err => {
          this.sending = false;
          this.gpAlert.showError(err);
        }
      );
  }

  private sendChildOrderRefund() {
    this.gpAlert.clear();
    if (this.hasAvalidPaymentMethod && !this.refund.externalTransactionId) {
      this.gpAlert.showWarning('Identificador da transação de estorno é obrigatório.');
      return;
    }

    if (!this.refund.refundReason) {
      this.gpAlert.showWarning('Selecione o motivo o estorno.');
      return;
    } else if (!this.refund.refundNote) {
      this.gpAlert.showWarning('Nota adicional é obrigatória.');
      return;
    }
    if (!confirm(`Confirma o estorno do pedido do parceiro ${this.childOrder.partnerName}?`)) {
      return;
    }
    this.sending = true;
    if (this.hasAvalidPixPaymentMethod) {
      this.refundOrderWithPaymentMethod();
    } else {
      this.refundChildOrder();
    }
  }

  private refundChildOrder() {
    this._orderService.refundChildOrder(this.campaignId, this.order.id, this.childOrder.itemGrouperId, this.refund)
      .subscribe(
        wasRefunded => {
          this.sending = false;
          if (wasRefunded) {
            this.gpAlert.showSuccess('Estorno efetuado com sucesso.');
            this.canRefund = false;
          }
        },
        err => {
          this.sending = false;
          this.gpAlert.showError(err);
        }
      );
  }

  sendItemRefund() {
    this.gpAlert.clear();
    if (!this.refund) {
      return;
    }

    // desativado estorno parcial até finalização do processo financeiro
    if (this.isPartial && !this.canRefundPartial) {
      this.gpAlert.showWarning('Não é permitido o estorno parcial de item.');
      return;
    }

    if (!this.refund.refundReason) {
      this.gpAlert.showWarning('Selecione o motivo o estorno.');
      return;
    } else if (!this.refund.refundNote) {
      this.gpAlert.showWarning('Nota adicional é obrigatória.');
      return;
    }
    if (this.isPartial && this.refund.refundQuantity > this.item.quantity) {
      this.gpAlert.showWarning('Quantidade para estorno não pode ser maior que a quantidade do item.');
      return;
    }
    if (!confirm(`Confirma o estorno do item ${this.item.skuCode}?`)) {
      return;
    }

    this.sending = true;
    this._orderService.refundItem(this.campaignId, this.order.id, this.refund)
      .subscribe(
        wasRefunded => {
          this.sending = false;
          if (wasRefunded) {
            this.gpAlert.showSuccess('Estorno efetuado com sucesso.');
            this.canRefund = false;
          }
        },
        err =>  {
          this.sending = false;
          this.gpAlert.showError(err)
        }
      );
  }

  checkOrderIsUberPartner(order: any): boolean {
    if (!order) return false;
    const { childrenOrders } = order;

    if (!childrenOrders || childrenOrders.length == 0) return false;
    const hasUber = childrenOrders.find(x => x.partnerName === "UBER");

    if (hasUber) return true;
    return false;
  }

  handleTransactionDetails() {
    if (this.hasPaymentMethod) {
      this.transactionDetails = this.hasAvalidTransactionDetails ? this.order.paymentMethods.find(p => p.type === "PIX").transactionDetails : {};
    } else {
      this.transactionDetails = {};
    }
  }
}
