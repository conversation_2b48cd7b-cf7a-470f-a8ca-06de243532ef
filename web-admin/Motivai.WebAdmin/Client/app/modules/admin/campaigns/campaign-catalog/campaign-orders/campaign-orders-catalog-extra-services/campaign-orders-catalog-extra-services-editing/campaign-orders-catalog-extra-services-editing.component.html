<spinner [show]="loading" [overlay]="true"></spinner>
<gp-alert [overlay]="true" #alert></gp-alert>

<gp-card title="Detalhes do Pedido" first="true">
  <div class="row">
    <div class="col-lg-6 col-xs-12 br pv">
      <div class="clearfix">
        <p class="pull-left"><strong>Número do Pedido: </strong></p>
        <p class="pull-right mr">{{ (catalogExtraServiceOrder.protocol) || '-'}}</p>
      </div>
      <div class="clearfix">
        <p class="pull-left"><strong>Data do Pedido: </strong></p>
        <p class="pull-right mr">{{catalogExtraServiceOrder.formattedSystemDate || '-'}}</p>
      </div>
      <div class="clearfix">
        <p class="pull-left"><strong>Tipo do Pedido: </strong></p>
        <p class="pull-right mr">{{catalogExtraServiceOrder.formattedType || '-'}}</p>
      </div>
      <div class="clearfix" *ngIf="catalogExtraServiceOrder.deteiledCost">
        <p class="pull-left"><strong>Valor do Pedido: </strong></p>
        <p class="pull-right mr">R$ {{catalogExtraServiceOrder.deteiledCost.participantCost.currency | amount}}</p>
      </div>
    </div>
    <div class="col-lg-6 col-xs-12 br pv">
      <div class="clearfix">
        <p class="pull-left"><strong>Parceiro: </strong></p>
        <p class="pull-right mr">{{catalogExtraServiceOrder.formattedPartnerPaidBillInfo || '-' }}</p>
      </div>
      <div class="clearfix">
        <p class="pull-left"><strong>Pedido integrado: </strong></p>
        <p class="pull-right mr">{{catalogExtraServiceOrder.integrated | booleanToYesNo }}</p>
      </div>
      <div class="clearfix">
        <p class="pull-left"><strong>Pedido confirmado: </strong></p>
        <p class="pull-right mr">{{catalogExtraServiceOrder.confirmed | booleanToYesNo }}</p>
      </div>

      <div *ngIf="catalogExtraServiceOrder.refunded">
        <div class="clearfix">
          <p class="pull-left"><strong>Pedido estornado: </strong></p>
          <p class="pull-right mr">{{catalogExtraServiceOrder.refunded | booleanToYesNo }}</p>
        </div>

        <div class="clearfix">
          <p class="pull-left"><strong>Data do estorno: </strong></p>
          <p class="pull-right mr">{{catalogExtraServiceOrder.formattedRefundDate || '-' }}</p>
        </div>
      </div>
    </div>
  </div>

</gp-card>

<gp-card title="Boleto/Conta" *ngIf="catalogExtraServiceOrder.isBillPayment">
  <div class="row">
    <div class="col-lg-4 col-xs-12 br pv">
      <div class="clearfix">
        <p class="pull-left"><strong>Codº Barras: </strong></p>
        <p class="pull-right mr">{{catalogExtraServiceOrder.billPayment.barcode || '-'}}</p>
      </div>
      <div class="clearfix">
        <p class="pull-left"><strong>Estabelecimento: </strong></p>
        <p class="pull-right mr">{{catalogExtraServiceOrder.billPayment.assignor || '-'}}</p>
      </div>
      <div class="clearfix" *ngIf="catalogExtraServiceOrder.billPayment.registerData">
        <p class="pull-left"><strong>Valor: </strong></p>
        <p class="pull-right mr">R$ {{ catalogExtraServiceOrder.billPayment.registerData.originalValue | amount }}</p>
      </div>
    </div>
    <div class="col-lg-4 col-xs-12 br pv" *ngIf="catalogExtraServiceOrder.billPayment.registerData">
      <div class="clearfix">
        <p class="pull-left"><strong>Documento do recebedor: </strong></p>
        <p class="pull-right mr">{{catalogExtraServiceOrder.billPayment.registerData.documentRecipient || '-' }}</p>
      </div>
      <div class="clearfix">
        <p class="pull-left"><strong>Nome do recebedor: </strong></p>
        <p class="pull-right mr">{{catalogExtraServiceOrder.billPayment.registerData.recipient || '-' }}</p>
      </div>
    </div>
    <div class="col-lg-4 col-xs-12 br pv" *ngIf="catalogExtraServiceOrder.billPayment.registerData">
      <div class="clearfix">
        <p class="pull-left"><strong>Documento do pagador: </strong></p>
        <p class="pull-right mr">{{catalogExtraServiceOrder.billPayment.registerData.documentPayer || '-' }}</p>
      </div>
      <div class="clearfix">
        <p class="pull-left"><strong>Nome do pagador: </strong></p>
        <p class="pull-right mr">{{catalogExtraServiceOrder.billPayment.registerData.payer || '-' }}</p>
      </div>
    </div>
  </div>
</gp-card>

<gp-card title="Dados da Recarga" *ngIf="catalogExtraServiceOrder.isCreditRecharge">
  <div class="row">
    <div class="col-lg-6 col-xs-12 br pv">
      <div class="clearfix">
        <p class="pull-left"><strong>Operadora: </strong></p>
        <p class="pull-right mr">{{catalogExtraServiceOrder.creditRecharge.partnerProviderName || '-'}}</p>
      </div>
      <div class="clearfix">
        <p class="pull-left"><strong>Número de telefone: </strong></p>
        <p class="pull-right mr">
          ({{catalogExtraServiceOrder.creditRecharge.ddd}}) {{catalogExtraServiceOrder.creditRecharge.phoneNumber ||
          '-'}}
        </p>
      </div>
    </div>
  </div>
</gp-card>

<gp-card title="Participante" *ngIf="participantInfo.userDocument">
  <gp-form-row>
    <gp-form-col cols="12 6">
      <label>CPF/CNPJ:</label>
      <span>{{ participantInfo.userDocument | document }}</span>
    </gp-form-col>

    <gp-form-col cols="12 6">
      <label>Nome:</label>
      <span>{{ participantInfo.name || '-'}}</span>
    </gp-form-col>
  </gp-form-row>
</gp-card>

<gp-card *ngIf="canRefund">
  <gp-form-row>
    <gp-form-col cols="12 12 12" [inputGroup]="false">
      <gp-spinner-button [actionPrimary]="true" text="Efetuar estorno" pull="right" [disabled]="disableButtons"
        (click)="refundOrder()">
      </gp-spinner-button>
    </gp-form-col>
  </gp-form-row>
</gp-card>

<campaign-orders-catalog-extra-services-refund #orderRefundModal [campaignId]="campaignId"
  [catalogExtraServiceOrder]="catalogExtraServiceOrder" [participantInfo]="participantInfo"
  (completed)="findCatalogExtraServiceOrder()">
</campaign-orders-catalog-extra-services-refund>
