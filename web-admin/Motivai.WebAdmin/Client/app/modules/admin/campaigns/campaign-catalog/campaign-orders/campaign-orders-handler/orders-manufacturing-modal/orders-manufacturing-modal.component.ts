import { Component, OnInit, Input, Output, EventEmitter, ViewChild } from '@angular/core';
import { GpModalComponent } from '../../../../../../../shared/components/gp-modal/gp-modal.component';
import { GpAlertComponent } from '../../../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignOrdersHandlerService } from '../campaign-orders-handler.service';

@Component({
    selector: 'orders-manufacturing-modal',
    templateUrl: './orders-manufacturing-modal.component.html'
})
export class OrdersProductionModalComponent implements OnInit {
    @Input() item: any = {};
    @Input() campaignId: string;
    @Input() orderId: string;
    @ViewChild('modal') modal: GpModalComponent;
    @ViewChild('alert') alert: GpAlertComponent;
    @Output('onclose') onclose: EventEmitter<any> = new EventEmitter();

    loading: boolean = true;

    constructor(private _ordersService: CampaignOrdersHandlerService) { }

    ngOnInit(): void { }

    open() {
        this.modal.show();
    }

    close() {
        this.onclose.emit(this.item);
    }

    clear() {
        this.alert.clear();
    }

    markItemProduction() {
        if (this.item) {
            this.loading = true;
            this.alert.clear();
            this._ordersService.markItemProduction(this.campaignId, this.orderId, this.item.itemGrouperId, this.item.skuId, this.item).subscribe(
                item => {
                    if (item) {
                        this.alert.showSuccess('Produto marcado como "Em produção"! Uma notificação foi enviada ao participante!');
                    } else {
                        this.alert.showError('Ocorreu um erro ao atualizar o registro!');
                    }
                    this.loading = false;
                }, 
                err => {
                    const msg = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao atualizar o registro!';
                    this.alert.showError(msg);
                    this.loading = false;
                });
        }
    }
}
