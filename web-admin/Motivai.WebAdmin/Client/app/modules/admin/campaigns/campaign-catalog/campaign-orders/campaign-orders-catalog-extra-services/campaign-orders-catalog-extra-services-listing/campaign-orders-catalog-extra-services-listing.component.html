<spinner [show]="loading" [overlay]="true"></spinner>
<gp-alert [overlay]="true" #alert></gp-alert>

<gp-card title="Filtros" first="true">
  <gp-form-row>
    <gp-form-col cols="12 4 4">
      <gp-simple-input label="CPF/CNPJ">
        <gp-input-mask name="document" mask="00000000000000" [(ngModel)]="parameters.document"></gp-input-mask>
      </gp-simple-input>
    </gp-form-col>
    <gp-form-col cols="12 4 4">
      <label>Número do Pedido</label>
      <gp-input-mask name="protocolNumber" [(ngModel)]="parameters.protocolNumber" [onlyInteger]="true"
        [thousandsSeparator]="false" placeholder="Número do Pedido">
      </gp-input-mask>
    </gp-form-col>
  </gp-form-row>

  <gp-form-row>
    <gp-form-col cols="12 4 4">
      <gp-simple-input label="Código de Barras">
        <gp-input-mask name="barCode" mask="00000000000000000000000000000000000000000000000"
          [(ngModel)]="parameters.barCode"></gp-input-mask>
      </gp-simple-input>
    </gp-form-col>
    <gp-form-col cols="12 4 4">
      <label>Número de Celular</label>
      <gp-input-mask name="mobilePhone" [(ngModel)]="parameters.mobilePhone" mask="(00) 00000-0000"
        placeholder="Número do Celular">
      </gp-input-mask>
    </gp-form-col>

    <gp-form-col cols="12 2 2" additionalClasses="top-p2">
      <gp-spinner-button [actionSearch]="true" text="Pesquisar" (click)="searchCatalogExtraServicesOrders()">
      </gp-spinner-button>
    </gp-form-col>
  </gp-form-row>
</gp-card>

<gp-card title="Registros">
  <gp-form-row>
    <gp-form-col cols="12 12 12">
      <gp-grid name="orders" [rows]="orders"
        [columns]="['Número do pedido', 'Tipo do Pedido', 'Parceiro', 'Data do pedido', 'Confirmado', 'Integrado', 'Estornado']"
        [fields]="['protocol','formattedType','formattedPartnerPaidBillInfo','formattedSystemDate', 'formattedConfirmed', 'formattedIntegrated', 'formattedRefunded']"
        [showActive]="false" [showDelete]="false" [showEdit]="true"
        [showPagination]="true" [pageSize]="40" [showTotalPages]="false" (onPageChanged)="pageChanged($event)"
        (onEdit)="editOrder($event)" emptyMessage="Nenhum pedido encontrado">
      </gp-grid>
    </gp-form-col>
  </gp-form-row>
</gp-card>
