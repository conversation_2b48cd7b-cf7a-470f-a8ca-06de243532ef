import { Component, ViewChild, OnInit } from '@angular/core';
import { DatePipe } from '@angular/common';
import { Router } from '@angular/router';

import {
  PERMISSION_CAMPAIGNS_VIEW,
  PERMISSION_CAMPAIGNS_EDIT} from '../../../../core/auth/access-points';
import { GpAlertComponent } from '../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignService } from '../campaign.service';
import { CompanyService } from '../../companies/company/company.service';
import { Campaign } from '../campaign';
import { AuthStore } from '../../../../core/auth/auth.store';
import { Item } from '../../../../shared/models/item';

@Component({
  selector: 'campaign-listing',
  templateUrl: 'campaign-listing.component.html'
})
export class CampaignListingComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  filterParams = {
    name: '',
    client: '',
    status: 'ACTIVE'
  };
  clients: Array<Item> = [];
  campaigns: Array<Campaign>;
  loading: boolean = false;

  status: Array<Item> = [
    Item.of('ALL', 'Todos'),
    Item.of('ACTIVE', 'Ativo'),
    Item.of('INACTIVE', 'Inativo')
  ];

  constructor(
    private _authStore: AuthStore,
    private _campaignService: CampaignService,
    private _clientService: CompanyService,
    private _datePipe: DatePipe,
    private _route: Router
  ) {}

  ngOnInit() {
    this._clientService.getClientsToListing().subscribe(clients => {
      if (clients) {
        this.clients = clients.map(c => Item.of(c.id, c.name));
        this.setClients();
      }
    });
    this.loadCampaigns();
  }

  get canOnlyViewCampaign() {
    return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_VIEW) && !this.canEditCampaign;
  }

  get canCreateCampaign() {
    return this._authStore.role.PERMISSION_CAMPAIGNS_INS;
  }

  get canEditCampaign() {
    return this._authStore.hasPermissionTo(PERMISSION_CAMPAIGNS_EDIT);
  }

  private setClients() {
    if (this.clients && this.clients.length && this.campaigns && this.campaigns.length) {
      this.campaigns.forEach(campaign => {
        let client = this.clients.find(c => c.id == campaign.clientId);
        if (client) {
          campaign.clientName = client.text;
        }
      });
    }
  }

  public handleError(err) {
    let errorMessage = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao efetuar a operação.';
    this.gpAlert.showError(errorMessage);
    this.loading = false;
  }

  private formatDate(date) {
    return this._datePipe.transform(date, 'dd/MM/yyyy');
  }

  loadCampaigns() {
    this.loading = true;
    this.gpAlert.clear();
    this._campaignService.getAll(this.filterParams.name, this.filterParams.client, null, null, null, this.filterParams.status)
      .subscribe(
        campaigns => {
          if (campaigns) {
            this.campaigns = campaigns;
            if (campaigns && campaigns.length) {
              campaigns.forEach((c: any) => {
                c.typeDesc = c.type == 'Rewards' ? 'Incentivo e Fidelidade' : c.type;
                c.period = `${this.formatDate(c.startDate)} à ${this.formatDate(c.endDate)}`;
              });
            }
            this.setClients();
          } else {
            this.gpAlert.showWarning('Não foi possível carregar as campanhas.');
          }
        },
        err => this.handleError(err),
        () => (this.loading = false)
      );
  }

  onClientChange(item) {
    if (item) {
      this.filterParams.client = item.id;
    } else {
      this.filterParams.client = '';
    }
  }

  navigateToView(campaign: any) {
    this._route.navigate(['/campanha', campaign.id, 'configuracoes']);
  }
}
