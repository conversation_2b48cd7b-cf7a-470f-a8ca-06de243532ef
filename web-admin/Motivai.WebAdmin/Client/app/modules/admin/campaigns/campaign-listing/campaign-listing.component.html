<div class="content-heading">
  Campanhas
  <small>Gerenciamento de campanhas cadastradas</small>
</div>

<gp-card title="Filtrar Campanhas">
  <gp-form-row>
    <gp-form-col cols="12 4">
      <input
        type="text"
        class="form-control"
        name="name"
        [(ngModel)]="filterParams.name"
        placeholder="Por nome da campanha"
      />
    </gp-form-col>
    <gp-form-col cols="12 4">
      <ng-select
        [allowClear]="false"
        [items]="clients"
        placeholder="Por cliente"
        (data)="onClientChange($event)"
      ></ng-select>
    </gp-form-col>
    <gp-form-col cols="12 4">
      <gp-select [items]="status" [(ngModel)]="filterParams.status"> </gp-select>
    </gp-form-col>
  </gp-form-row>

  <gp-form-row>
    <gp-form-col [inputGroup]="false" additionalClasses="text-right" cols="12">
      <gp-spinner-button
        text="Pesquisar"
        [search]="true"
        (click)="loadCampaigns()"
        [loading]="loading"
        loadingText="Pesquisando"
      >
      </gp-spinner-button>

      <button
        class="btn btn-md btn-responsive btn-main left-m1"
        [routerLink]="['/campanha', 'nova']"
        *ngIf="canCreateCampaign"
      >
        Nova campanha <i class="fa fa-plus" style="margin-left: 5px"></i>
      </button>
    </gp-form-col>
  </gp-form-row>
</gp-card>

<gp-card title="Campanhas Cadastradas">
  <div class="row">
    <div class="col-xs-12">
      <gp-grid
        name="partnersGrid"
        [loading]="loading"
        [rows]="campaigns"
        [columns]="['Nome', 'Tipo', 'Cliente', 'Período']"
        [fields]="['name', 'typeDesc', 'clientName', 'period']"
        [showActive]="false"
        [showPagination]="false"
        [showEdit]="true"
        [showDelete]="false"
        (onEdit)="navigateToView($event)"
      >
      </gp-grid>
    </div>
  </div>
  <div class="row">
    <div class="col-md-12" style="padding-top:5px;">
      <div class="div-alert">
        <gp-alert #gpAlert></gp-alert>
      </div>
    </div>
  </div>
</gp-card>
