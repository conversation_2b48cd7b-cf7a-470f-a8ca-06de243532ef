<gp-card [first]="true" title="Pesquisa">
	<gp-alert [overlay]="true" #alert></gp-alert>
	<gp-form-row>
		<gp-form-col cols="12 3 3">
			<gp-simple-input label="Número do Pedido">
				<input type="text" name="orderNumber" class="form-control" placeholder="Número do Pedido" [(ngModel)]="searchParameters.orderNumber" />
			</gp-simple-input>
		</gp-form-col>
		<gp-datepicker cols="12 3 3" name="transactionDate" label="Data da Transação" [required]="false" [(ngModel)]="searchParameters.transactionDate"></gp-datepicker>
		<gp-form-col cols="12 3 3">
			<gp-spinner-button 
				text="Pesquisar" 
				buttonClass="bg-primary-dark" 
				icon="search" 
				(click)="loadTransactions()"
				[loading]="loading" 
				marginTop="26px"
				loadingText="Pesquisando">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
</gp-card>
<gp-card title="Transações Encontradas">
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<gp-grid
				name="grid"
				[rows]="transactions"
				[columns]="[ 'Descrição do Transação', 'Data de Processamento', 'Valor em Pontos', 'Valor em R$' ]"
				[fields]="[ 'description', 'processingDate', 'totalPoints', 'currencyAmount' ]"
				[showActive]="false"
				[showPagination]="false"
				[showTotalPages]="false"
				[showDelete]="false"
				[showEdit]="true"
				[loading]="loading"
				(onEdit)="editTransaction($event)">
			</gp-grid>
		</gp-form-col>
	</gp-form-row>
</gp-card>