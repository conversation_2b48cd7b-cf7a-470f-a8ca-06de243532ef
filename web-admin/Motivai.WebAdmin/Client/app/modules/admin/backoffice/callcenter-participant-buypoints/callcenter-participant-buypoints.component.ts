import { CallCenterParticipantBuyPointsDetailComponent } from './callcenter-participant-buypoints-detail/callcenter-participant-buypoints-detail.component';
import { CallCenterParticipantBuyPointsListComponent } from './callcenter-participant-buypoints-list/callcenter-participant-buypoints-list.component';
import { TabsetComponent } from 'ng2-bootstrap/tabs';
import { Component, OnInit, ViewChild } from '@angular/core';

@Component({
    selector: 'callcenter-participant-buypoints',
    templateUrl: 'callcenter-participant-buypoints.component.html'
})
export class CallCenterParticipantBuyPointsComponent implements OnInit {

    private _transactionId: string;
    @ViewChild('tabs') tabs: TabsetComponent;
    @ViewChild('listComponent') listComponent: CallCenterParticipantBuyPointsListComponent;
    @ViewChild('editComponent') editComponent: CallCenterParticipantBuyPointsDetailComponent;

    constructor() { }

    ngOnInit() { }

    private clearEditForm() {
        this._transactionId = '';
        this.editComponent.reset();
        this.listComponent.loadTransactions();
    }

    private onEdit($event) {
        this._transactionId = $event;
        this.tabs.tabs[1].active = true;
    }
}
