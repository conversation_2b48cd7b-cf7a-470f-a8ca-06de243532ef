import { Component, OnInit, Output, EventEmitter, ViewChild } from '@angular/core';

import { TransactionService } from './../../transaction.service';
import { Router } from '@angular/router';
import { DataStore } from './../../../../../core/store/data.store';
import { CALLCENTER_STORE_KEY, CallcenterSession } from './../../callcenter-session';
import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';

@Component({
    selector: 'callcenter-participant-buypoints-list',
    templateUrl: 'callcenter-participant-buypoints-list.component.html'
})
export class CallCenterParticipantBuyPointsListComponent implements OnInit {
    @ViewChild('alert') alert: GpAlertComponent;

    private callcenterSession: CallcenterSession;
    private searchParameters: any = {};
    private loading: boolean = false;
    private transactions: any[] = [];
    @Output('edit') edit: EventEmitter<any> = new EventEmitter();

    constructor(private _ds: DataStore, private _route: Router, private _transactionService: TransactionService) { }

    ngOnInit() {
        this.callcenterSession = this._ds.get(CALLCENTER_STORE_KEY);
        if (!this.callcenterSession) {
            this._route.navigate(['/callcenter/pesquisa']);
        }
    }

    public loadTransactions() {
        this.loading = true;
        this.transactions = [];
        this._transactionService.getParticipantTransactions(this.callcenterSession.participantId, this.searchParameters.orderNumber, 'BuyPoints')
            .subscribe(transactions => {
                this.transactions = transactions;
                this.loading = false;
            }, err => {
                this.loading = false;
                this.alert.showError(err);
            })
    }

    private editTransaction($event: any) {
        if ($event) {
            this.edit.emit($event.id);
        }
    }
}
