import { GpAlertComponent } from './../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { TransactionService } from './../../transaction.service';
import { Component, OnInit, Input, ViewChild } from '@angular/core';
import * as bootstrap from 'ng2-bootstrap';

@Component({
    selector: 'callcenter-participant-buypoints-detail',
    templateUrl: 'callcenter-participant-buypoints-detail.component.html'
})
export class CallCenterParticipantBuyPointsDetailComponent implements OnInit {

    private _transactionId: string;
    @Input() set transactionId(v: string) {
        this._transactionId = v;
        if (v) {
            this.getTransactionDetail();
        }
    }

    private knOptionsTotal: any = this.fetchKnob('R$0,00', 'Total', '#35C64D');
    private knOptionsDebts: any = this.fetchKnob('R$0,00', 'Utilizado', '#FC752C');
    private knOptionsCanceled: any = this.fetchKnob('R$0,00', 'Cancelado', '#E90124');
    private knOptionsBalance: any = this.fetchKnob('R$0,00', 'Disponível', '#2882E0');
    private loadingScreen: boolean = false;
    private loadingRefund: boolean = false;
    private refund: any = {};
    private transaction: any = {};
    private percentTotal: number = 0;
    private percentDebts: number = 0;
    private percentCanceled: number = 0;
    private percentBalance: number = 0;
    @ViewChild('alertByValue') alertByValue: GpAlertComponent;

    constructor(private _transactionService: TransactionService) { }

    ngOnInit() { }

    public reset() {
        if (this.percentTotal > 0) this.knOptionsTotal = this.fetchKnob('R$0,00', 'Total', '#35C64D');
        if (this.percentDebts > 0) this.knOptionsDebts = this.fetchKnob('R$0,00', 'Utilizado', '#FC752C');
        if (this.percentCanceled > 0) this.knOptionsCanceled = this.fetchKnob('R$0,00', 'Cancelado', '#E90124');
        if (this.percentBalance > 0) this.knOptionsBalance = this.fetchKnob('R$0,00', 'Disponível', '#2882E0');
        this.loadingScreen = false;
        this.refund = {};
        this.transaction = {};
        this.percentTotal = 0;
        this.percentDebts = 0;
        this.percentCanceled = 0;
        this.percentBalance = 0;
    }

    private getTransactionDetail() {
        if (this._transactionId) {
            this.loadingScreen = true;
            this.transaction = {};
            this._transactionService.getTransactionDetail(this._transactionId)
                .subscribe(transaction => {
                    this.transaction = transaction;

                    // calculates knobs size
                    this.percentTotal = 100;
                    this.percentBalance = transaction.balance * 100 / transaction.total;
                    this.percentCanceled = transaction.totalCanceled * 100 / transaction.total;
                    this.percentDebts = transaction.totalDebts * 100 / transaction.total;

                    // set knobs configuration
                    this.knOptionsTotal = this.fetchKnob(`R$${transaction.formattedTotal}`, 'Total', '#35C64D');
                    if (this.percentDebts > 0)
                        this.knOptionsDebts = this.fetchKnob(`R$${transaction.formattedTotalDebts}`, 'Utilizado', '#FC752C');

                    if (this.percentCanceled > 0)
                        this.knOptionsCanceled = this.fetchKnob(`R$${transaction.formattedTotalCanceled}`, 'Cancelado', '#E90124');

                    if (this.percentBalance > 0)
                        this.knOptionsBalance = this.fetchKnob(`R$${transaction.formattedBalance}`, 'Disponível', '#2882E0');

                    this.loadingScreen = false;
                }, err => {
                    console.log(err);
                    this.loadingScreen = false;
                });
        }
    }

    private refundByValue() {
        if (this._transactionId) {
            this.loadingRefund = true;
            this.alertByValue.clear();
            this._transactionService.refundBuyPoints(this._transactionId, this.refund)
                .subscribe(response => {
                    if (response === true) {
                        this.getTransactionDetail();
                        this.alertByValue.showSuccess('Estorno realizado com sucesso!');
                    } else {
                        this.alertByValue.showError('Não foi possível criar o estorno');
                    }

                    this.loadingRefund = false;
                }, err => {
                    const msg = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao criar o estorno';
                    this.alertByValue.showError(msg);
                    this.loadingRefund = false;
                });
        }
    }

    private get validateRefundForm(): boolean {
        if (this.refund.currencyAmount) {
            if (Number(this.refund.currencyAmount) > this.transaction.balance) {
                return true;
            }
        }

        return !this.refund.reason || !this.refund.notes || !this.refund.currencyAmount;
    }

    private fetchKnob(text, subText, barColor): any {
        return {
            readOnly: true,
            inputFormatter: function(value) {
                return text;
            },
            size: 180,
            textColor: '#666666',
            fontSize: '20',
            fontWeigth: '600',
            fontFamily: 'Verdana',
            valueformat: 'percent',
            value: 0,
            min: 0,
            max: 100,
            trackWidth: 13,
            barWidth: 13,
            trackColor: '#EEEEEE',
            barColor: barColor,
            dynamicOptions: true,
            subText: {
                enabled: true,
                fontFamily: 'Verdana',
                font: '14',
                fontWeight: 'bold',
                text: subText,
                color: '#666666',
                offset: 7
            },
        }
    }
}
