<gp-card [first]="true" *ngIf="!_transactionId">
	<div class="row">
		<div class="col-md-12">
			<gp-alert type="warning" message="Selecione uma transação para visualizar os detalhes!"></gp-alert>
		</div>
	</div>
</gp-card>
<div *ngIf="_transactionId">
	<gp-card [first]="true" title="Visão Geral">
		<gp-form-row>
			<gp-form-col cols="12 3 3">
				<div gp-knob [value]="percentTotal" [options]="knOptionsTotal"></div>
			</gp-form-col>
			<gp-form-col cols="12 3 3">
				<div gp-knob [value]="percentDebts" [options]="knOptionsDebts"></div>
			</gp-form-col>
			<gp-form-col cols="12 3 3">
				<div gp-knob [value]="percentCanceled" [options]="knOptionsCanceled"></div>
			</gp-form-col>
			<gp-form-col cols="12 3 3">
				<div gp-knob [value]="percentBalance" [options]="knOptionsBalance"></div>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card title="Detalhes da Transação">
		<gp-form-row>
			<gp-form-col cols="12 5 5">
				<p><strong>Gateway:</strong> {{ transaction.gatewaySettings?.gateway }}</p>
				<p><strong>Pedido no Gateway:</strong> {{ transaction.gatewaySettings?.orderCode }}</p>
				<p><strong>Transação no Gateway:</strong> {{ transaction.gatewaySettings?.chargeId }}</p>
				<p><strong>Data da Transação:</strong>{{ transaction.transactionDate }}</p>
			</gp-form-col>
			<gp-form-col cols="12 5 5">
				<p><strong>Total (R$):</strong> {{ transaction.formattedTotal }}</p>
				<p><strong>Total (Pontos):</strong> {{ transaction.formattedPointsAmount }}</p>
				<p><strong>Status:</strong> {{ transaction.gatewaySettings?.status }}</p>
				<p><strong>Data de Expiração:</strong> {{ transaction.expirationDate }}</p>
			</gp-form-col>
			<gp-form-col cols="12 2 2">
				<gp-spinner-button 
					type="submit" 
					text="Estornar Valor (R$)"
					size="md" 
					pull="right" 
					[pink]="true" 
					icon="minus-square"
					(click)="classicModal.show()">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<legend>Histórico de Transações</legend>
	<gp-card *ngFor="let t of transaction.history" [title]="t.description">
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<p><strong>Data de Transação:</strong> {{ t.transactionDate }}</p>
				<p><strong>Tipo:</strong> {{ t.type }}</p>
				<p><strong>Origem:</strong> {{ t.origin }}</p>
				<p *ngIf="t.orderNumber"><strong>Pedido:</strong> {{ t.orderNumber }}</p>
				<p><strong>Total (R$):</strong> {{ t.formattedCurrencyAmount }}</p>
				<p><strong>Total (Pontos):</strong> {{ t.formattedPointsAmount }}</p>
				<p *ngIf="t.gatewayRefund?.reason"><strong>Motivo:</strong> {{ t.gatewayRefund?.reason }}</p>
				<p *ngIf="t.gatewayRefund?.notes"><strong>Observações:</strong> {{ t.gatewayRefund?.notes }}</p>
			</gp-form-col>
		</gp-form-row>
	</gp-card>
</div>

<spinner [overlay]="true" [show]="loadingScreen"></spinner>

<div class="modal fade" style="margin-top: 40px; background-color: rgba(0, 0, 0, 0.5);" bsModal #classicModal="bs-modal" [config]="{backdrop: false}" role="dialog" tabindex="-1" aria-hidden="true" aria-labelledby="smallModal">
	<div class="modal-dialog modal-md">
		<div class="modal-content">
			<div class="modal-header">
				<button class="close" aria-label="Close" (click)="classicModal.hide()">
					<span aria-hidden="true">&times;</span>
				</button>
				<h4 class="modal-title">Estornar Valor</h4>
			</div>
			<div class="modal-body">
				<gp-form-row>
					<gp-form-col cols="12 12 12">
						<p>Informe abaixo o motivo e o valor que deseja estornar! Para estornar um valor maior que o disponível, é necessário antes estornar um pedido atrelado a esta compra de pontos!</p>
						<p>Esta ação não terá mais volta!</p>
					</gp-form-col>
				</gp-form-row>
				<gp-form-row>
					<gp-form-col cols="12 12 12">
						<h4>Disponível para estorno: R${{ transaction.formattedBalance }}</h4>
					</gp-form-col>
				</gp-form-row>
				<hr />
				<gp-form-row>
					<gp-form-col cols="12 12 12">
						<gp-simple-input label="Motivo">
							<select class="form-control" [(ngModel)]="refund.reason">
								<option value="">Selecione</option>
								<option value="Desistencia">Desistência</option>
								<option value="Arrependimento">Arrependimento</option>
								<option value="ProblemaPedido">Problema com o pedido</option>
								<option value="Insatisfacao">Insatisfação</option>
							</select>
						</gp-simple-input>
					</gp-form-col>
				</gp-form-row>
				<gp-form-row>
					<gp-form-col cols="12 12 12">
						<div class="form-group">
							<label>Observações</label>
							<input type="text" class="form-control" name="notes" [(ngModel)]="refund.notes" />
						</div>
					</gp-form-col>
				</gp-form-row>
				<gp-form-row>
					<gp-form-col cols="12 4 4">
						<div class="form-group">
							<div class="input-group">
								<span class="input-group-addon">R$</span>
								<input type="text" class="form-control" name="currencyAmount" placeholder="50,00" [(ngModel)]="refund.currencyAmount" />
							</div>
						</div>
					</gp-form-col>
					<gp-form-col cols="12 4 4">
						<gp-spinner-button 
							type="submit" 
							text="Confirmar Estorno"
							size="md" 
							pull="right" 
							[pink]="true" 
							icon="minus-square"
							[disabled]="validateRefundForm"
							(click)="refundByValue()"
							[loading]="loadingRefund"
							marginTop="1px">
						</gp-spinner-button>
					</gp-form-col>
				</gp-form-row>
				<div class="row">
					<div class="col-md-12">
						<gp-alert #alertByValue></gp-alert>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
