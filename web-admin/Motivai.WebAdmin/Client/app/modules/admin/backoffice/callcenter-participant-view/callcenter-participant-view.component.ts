import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

import { AuthStore } from './../../../../core/auth/auth.store';
import { DataStore } from './../../../../core/store/data.store';
import { PERMISSION_CALLCENTER } from './../../../../core/auth/access-points';
import { CALLCENTER_STORE_KEY } from './../callcenter-session';

@Component({
  selector: 'callcenter-participant-view',
  templateUrl: 'callcenter-participant-view.component.html'
})
export class CallcenterParticipantViewComponent implements OnInit {

  constructor(private _authStore: AuthStore, private _gpDataStore: DataStore,
    private _route: Router) {}

  ngOnInit() {
    if (!this._authStore.role.PERMISSION_CALLCENTER) {
      this._route.navigate(['/']);
    }
    if (!this._gpDataStore.get(CALLCENTER_STORE_KEY)) {
      this._route.navigate(['/callcenter/pesquisa']);
    }
  }

  get canAccessParticipant() {
    return true;
  }

  get canAccessRedeem() {
    return true;
  }

  get canAccessAddress() {
    return true;
  }

  get canAccessPassword() {
    return true;
  }

  get canAccessTeleRedeemAccess() {
    return this._authStore.role.PERMISSION_CALLCENTER_TELEREDEEM_ACCESS;
  }

  get canAccessManualTransaction() {
    // return this._authStore.role.PERMISSION_CALLCENTER_MANUAL_TRANSACTION;
    return false;
  }

  finalizeSession() {
    this._gpDataStore.remove(CALLCENTER_STORE_KEY);
    this._route.navigate(['/callcenter/pesquisa']);
  }
}
