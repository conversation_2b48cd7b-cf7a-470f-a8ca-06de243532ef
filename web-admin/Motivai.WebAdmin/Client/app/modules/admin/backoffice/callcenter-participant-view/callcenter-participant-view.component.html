<div class="content-heading">Call Center</div>
<gp-card [noPaddingTop]="true" [noPaddingBottom]="true">
	<div class="row hidden-print">
		<accordion [closeOthers]="true">
			<accordion-group panelClass="b0 mb-sm panel-default no-shadow" [isOpen]="true">
				<div accordion-heading>
					<label><em class="fa fa-navicon"></em> Features do Call Center</label>
					<span class="pull-right">
						<span title="Encerrar sessão" (click)="finalizeSession()"><span
								class="fa fa-2x fa-sign-out mb-xs text-danger"></span></span>
					</span>
				</div>
				<div class="row menublock" style="margin-top:-1em;">
					<gp-menublock-item size="xs" icon="user" color="text-primary" text="Participante"
						routerLinkActive="active" [routerLink]="['/callcenter/participante']"></gp-menublock-item>

					<gp-menublock-item size="xs" icon="map-marker" color="text-primary" text="Endereços"
						routerLinkActive="active" [routerLink]="['/callcenter/enderecos']"></gp-menublock-item>

					<gp-menublock-item size="xs" icon="lock" color="text-primary" text="Alterar Senha"
						routerLinkActive="active" [routerLink]="['/callcenter/alterar-senha']"></gp-menublock-item>

					<gp-menublock-item size="xs" icon="list" color="text-warning" text="Extrato"
						routerLinkActive="active" [routerLink]="['/callcenter/extrato']"></gp-menublock-item>

					<gp-menublock-item size="xs" icon="shopping-cart" color="text-warning" text="Resgates"
						routerLinkActive="active" [routerLink]="['/callcenter/resgates']"></gp-menublock-item>

					<gp-menublock-item size="xs" icon="credit-card" color="text-warning" text="Compras de Pontos"
						routerLinkActive="active" [routerLink]="['/callcenter/compras-de-pontos']"></gp-menublock-item>

					<gp-menublock-item size="xs" icon="volume-control-phone" color="text-success" text="Teleresgate"
						routerLinkActive="active" [routerLink]="['/callcenter/teleresgate']" *ngIf="canAccessTeleRedeemAccess">
					</gp-menublock-item>
				</div>
			</accordion-group>
		</accordion>
	</div>
</gp-card>
<div>
	<router-outlet></router-outlet>
</div>
