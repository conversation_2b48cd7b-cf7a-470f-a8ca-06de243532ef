<gp-card title="Código de Verificação">
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<p>ATENÇÃO: O código de verificação será enviado para o número: <strong>{{ cellphone }}</strong></p>
			<p>Caso a sessão do callcenter seja encerrada, o código se tornará inválido e será necessário gerar outro!</p>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<gp-spinner-button 
				type="button" 
				text="Enviar código de verificação"
				size="lg" 
				[pink]="true" 
				icon="send"
				loadingText="Enviando..."  
				[loading]="loadingCode"
				(click)="sendVerificationCode()">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
</gp-card>
<gp-card title="Redefinir Senha">
	<gp-form-row>
		<gp-form-col cols="12 3 3">
			<gp-simple-input label="Código de Verificação">
				<input type="text" class="form-control" name="confirmVerificationCode" [(ngModel)]="confirmVerificationCode" />
			</gp-simple-input>
		</gp-form-col>
		<gp-form-col cols="12 6 6">
			<gp-spinner-button 
				type="button" 
				text="Redefinir Senha"
				size="lg" 
				[pink]="true" 
				icon="send"
				loadingText="Processando..."  
				[loading]="loadingPassword"
				[disabled]="!confirmVerificationCode"
				marginTop="24px"
				(click)="resetPassword()">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 12 12">
			<gp-alert #alert></gp-alert>
		</gp-form-col>
	</gp-form-row>
</gp-card>
