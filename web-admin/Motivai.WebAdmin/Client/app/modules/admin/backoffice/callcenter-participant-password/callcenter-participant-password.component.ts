import { Router } from '@angular/router';
import { Component, OnInit, ViewChild } from '@angular/core';

import { DataStore } from './../../../../core/store/data.store';
import { ParticipantService } from './../participant.service';
import { CALLCENTER_STORE_KEY, CallcenterSession } from './../callcenter-session';
import { GpAlertComponent } from './../../../../shared/components/gp-alert/gp-alert/gp-alert.component';

@Component({
    selector: 'callcenter-participant-password',
    templateUrl: 'callcenter-participant-password.component.html'
})
export class CallCenterParticipantPasswordComponent implements OnInit {

    private loading: boolean = false;
    private loadingCode: boolean = false;
    private loadingPassword: boolean = false;
    private verificationCode: string;
    private confirmVerificationCode: string;
    private cellphone: string;
    private callcenterSession: any;
    @ViewChild('alert') alert: GpAlertComponent;

    constructor(private _participantService: ParticipantService, private _ds: DataStore, private _route: Router) { }

    ngOnInit() {
        this.callcenterSession = this._ds.get(CALLCENTER_STORE_KEY);
        if (!this.callcenterSession) {
            this._route.navigate(['/callcenter/pesquisa']);
        } else {
            this.verificationCode = this.callcenterSession.verificationCode;
            this.loadUser();
        }
    }

    private loadUser() {
        this.loading = true;
        this._participantService.getParticipantInfo(this.callcenterSession.userId, this.callcenterSession.campaignId)
            .subscribe(info => {
                this.cellphone = info.cellphone;
                this.loading = false;
            }, err => {
                const msg = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao buscar informações do participante';
                this.alert.showError(msg);
                this.loading = false;
            });
    }

    private sendVerificationCode() {
        this.loadingCode = true;
        this._participantService.sendVerificationCode(this.callcenterSession.userId, this.callcenterSession.campaignId)
            .subscribe(code => {
                if (code) {
                    this.alert.showSuccess('Código de verificação enviado ao participante');
                } else {
                    this.alert.showError('Não foi possível enviar o código de verificação');
                }

                this.verificationCode = code;
                this._ds.set(CALLCENTER_STORE_KEY, { ...this.callcenterSession, verificationCode: code });
                this.loadingCode = false;
            }, err => {
                const msg = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao enviar código de verificação';
                this.alert.showError(msg);
                this.loadingCode = false;
            });
    }

    private resetPassword() {
        this.loadingPassword = true;
        this._participantService.resetPassword(this.callcenterSession.userId, this.callcenterSession.campaignId, {
            verificationCode: this.verificationCode,
            confirmVerificationCode: this.confirmVerificationCode
        }).subscribe(updated => {
            if (updated) {
                this.alert.showSuccess('Senha redefinida com sucesso');
            } else {
                this.alert.showError('Não foi possível redefinir a senha. Se o erro persistir, entre em contato com o suporte técnico!');
            }

            this.loadingPassword = false;
        }, err => {
            const msg = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao resetar a senha';
            this.alert.showError(msg);
            this.loadingPassword = false;
        })
    }
}
