<gp-card>
	<gp-alert [overlay]="true" #gpAlert></gp-alert>

	<div class="row">
		<div class="col-sm-12">
			<p>Atenção: Ao prosseguir com esta ação você será redirecionado para o site campanha ou catálogo em nome do participante.</p>
			<p>Para continuar, resolve o recaptcha e clique no botão do ambiente desejado.</p>
		</div>
	</div>

	<gp-form-row>
		<gp-form-col cols="12 6">
			<div style="padding-bottom: 1em;">
				<re-captcha #reCaptcha (resolved)="resolvedCaptcha($event)" size="normal">
				</re-captcha>
			</div>
		</gp-form-col>
	</gp-form-row>

	<div class="row">
		<div class="col-xs-12 col-sm-6">
			<gp-spinner-button type="button" text="Iniciar Acesso ao Site" [pink]="true" icon="desktop"
				[loading]="accessingSite" loadingText="Aguarde" (click)="startCampaignSiteAccess()" *ngIf="hasCampaignSite">
			</gp-spinner-button>
		</div>

		<!--DESCOMENTAR APOS ECORODOVIAS VALIDAR-->
		<!-- <div class="col-xs-12 col-sm-6">
			<gp-spinner-button type="button" text="Iniciar Acesso ao Catálogo" [pink]="true" icon="shopping-cart"
				[loading]="accessingCatalog" loadingText="Aguarde" (click)="startCatalogAccess()" *ngIf="hasCatalog">
			</gp-spinner-button>
		</div> -->
	</div>

	<div class="row">
		<div class="col-md-12 div-alert">
			<gp-alert #gpAlert></gp-alert>
		</div>
	</div>
</gp-card>

<form rel="nofollow noreferrer noopener" style="display:none" target="_blank" method="post" #loginForm>
	<input type="hidden" name="token" [value]="catalogAccessToken"/>
	<input type="hidden" name="campaign" [value]="callcenterSession.campaignId"/>
	<input type="hidden" name="user" [value]="callcenterSession.userId"/>
	<input type="hidden" name="participant" [value]="callcenterSession.participantId"/>
	<input type="hidden" name="callcenter" [value]="callcenterSession.callcenterId"/>
</form>
