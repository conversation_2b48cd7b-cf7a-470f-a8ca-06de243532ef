import { NgForm } from '@angular/forms';
import { Router } from '@angular/router';
import { Component, ViewChild, OnInit, ElementRef } from '@angular/core';

import { GpAlertComponent } from './../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { DataStore } from './../../../../core/store/data.store';
import { CampaignService } from './../../campaigns/campaign.service';
import { CallcenterSession, CALLCENTER_STORE_KEY } from '../callcenter-session';
import { ParticipantService } from '../participant.service';
import { TimezoneHelper } from '../../../../shared/helpers/timezone-helper';
import { RecaptchaComponent } from 'ng-recaptcha';

@Component({
  selector: 'callcenter-participant-telesale',
  templateUrl: 'callcenter-participant-telesale.component.html'
})
export class CallcenterParticipantTelesaleComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @ViewChild('loginForm') loginForm: ElementRef;
  @ViewChild('reCaptcha') reCaptcha: RecaptchaComponent;
  recaptchaToken: any = null;

  loading: boolean = false;
  accessingCatalog: boolean = false;
  accessingSite: boolean = false;

  callcenterSession: CallcenterSession;
  catalogAccessToken: string;

  hasCampaignSite: boolean = false;
  campaignSiteUrl: string;
  hasCatalog: boolean = true;
  catalogUrl: string;

  constructor(private _route: Router, private _gpDataStore: DataStore,
    private _campaignService: CampaignService, private _participantService: ParticipantService) { }

  ngOnInit() {
    this.callcenterSession = this._gpDataStore.get(CALLCENTER_STORE_KEY);
    if (!this.callcenterSession) {
      this._route.navigate(['/callcenter/pesquisa']);
    }
    this.loadCampaignInfo();
  }

  private loadCampaignInfo() {
    this.loading = true;
    this._campaignService.getGeneralInfo(this.callcenterSession.campaignId)
      .subscribe(
        campaignInfo => {
          this.loading = false;
          if (!campaignInfo) {
            this.gpAlert.showWarning('Não foi possível carregar os dados da campanha, por favor, tente novamente.');
            return;
          }
          this.hasCampaignSite = campaignInfo.modality == 'OnlySite' || campaignInfo.modality == 'CatalogWithSite';
          this.hasCatalog = campaignInfo.modality == 'OnlyCatalog' || campaignInfo.modality == 'CatalogWithSite';
          this.campaignSiteUrl = campaignInfo.campaignSiteUrl;
          this.catalogUrl = campaignInfo.url;
        },
        err => {
          this.loading = false;
          this.gpAlert.handleAndShowError(err);
        }
      );
  }

  clearRecaptcha() {
    this.recaptchaToken = null;
    if (this.reCaptcha) {
      this.reCaptcha.reset();
    }
  }

  resolvedCaptcha(response: any) {
    this.recaptchaToken = response;
  }

  // REMOVER APOS ECORODOVIAS VALIDAR
  startCatalogAccess() {
    if (!this.recaptchaToken) {
      this.gpAlert.handleAndShowError('Preencha o reCaptcha.');
      return;
    }

    this.accessingCatalog = true;
    const timezone = TimezoneHelper.getTimezone();
    this._participantService.generateCatalogAccessToken(this.callcenterSession.userId,
        this.callcenterSession.campaignId, timezone, this.recaptchaToken)
      .subscribe(
        response => {
          this.clearRecaptcha();
          if (response) {
            this.catalogAccessToken = response.token;
            if (!this.loginForm.nativeElement) return;
            this.loginForm.nativeElement.action = response.url + '/integration/authenticatecallcenter';
            this.loginForm.nativeElement.submit();
            this.accessingCatalog = false;
          } else {
            this.gpAlert.showWarning('Não foi possível carregar a URL da campanha.');
            this.accessingCatalog = false;
          }
        },
        err => {
          this.accessingCatalog = false;
          this.gpAlert.handleAndShowError(err);
          this.clearRecaptcha();
        }
      );
  }

   // REMOVER APOS ECORODOVIAS VALIDAR
  startCampaignSiteAccess() {
    if (!this.recaptchaToken) {
      this.gpAlert.handleAndShowError('Preencha o reCaptcha.');
      return;
    }

    this.accessingSite = true;
    const timezone = TimezoneHelper.getTimezone();
    this._participantService.generateCampaignSiteAccessToken(this.callcenterSession.userId, this.callcenterSession.campaignId,
        timezone, this.recaptchaToken)
      .subscribe(
        params => {
          this.clearRecaptcha();
          this.accessingSite = false;
          if (params && params.length) {
            window.open(`${this.campaignSiteUrl}/integration/sso?${params}`, '_blank');
          } else {
            this.gpAlert.showWarning('Não foi possível gerar o token de acesso, por favor, tente novamente.');
          }
        },
        err => {
          this.accessingSite = false;
          this.gpAlert.handleAndShowError(err);
          this.clearRecaptcha();
        }
      );
  }
}
