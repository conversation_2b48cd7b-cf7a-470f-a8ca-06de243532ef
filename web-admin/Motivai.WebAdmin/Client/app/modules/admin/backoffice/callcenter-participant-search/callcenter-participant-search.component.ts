import { Component, ViewChild, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { RecaptchaComponent } from 'ng-recaptcha';

import { FormatHelper } from './../../../../shared/formatters/format-helper';
import { AuthStore } from './../../../../core/auth/auth.store';
import { DataStore } from './../../../../core/store/data.store';
import { GpAlertComponent } from './../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CallcenterSession, CALLCENTER_STORE_KEY } from './../callcenter-session';
import { ParticipantService } from './../participant.service';
import { Item } from '../../../../shared/models/item';
import { compareStrings } from '../../../../shared/helpers/comparators';

@Component({
  selector: 'callcenter-participant-search',
  templateUrl: 'callcenter-participant-search.component.html'
})
export class CallcenterParticipantSearchComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @ViewChild('reCaptcha') reCaptcha: RecaptchaComponent;
  recaptchaToken: any = null;

  filterType: string = 'document';
  filterValue: string = '';

  searching: boolean = false;
  sending: boolean = false;
  participant: any = null;
  campaigns: Array<Item> = [];
  selectedCampaign: string = '';

  constructor(private _authStore: AuthStore, private _gpDataStore: DataStore,
    private _participantService: ParticipantService, private _route: Router) {}

  ngOnInit() {
    this._gpDataStore.remove(CALLCENTER_STORE_KEY);
    this._gpDataStore.forceStore();
    if (!this._authStore.role.PERMISSION_CALLCENTER) {
      this._route.navigate(['/']);
    }
  }

  get disableButtons(): boolean {
    return this.searching || this.sending;
  }

  get filterLabel() {
    if (this.filterType == 'document')
      return 'CPF/CNPJ do participante';
    return 'Login do participante';
  }

  get participantFound(): boolean {
    return this.participant && this.participant.userId;
  }

  private handleError(err) {
    this.gpAlert.handleAndShowError(err);
    this.searching = false;
    if (this.reCaptcha) {
      this.reCaptcha.reset();
    }
  }

  resolvedCaptcha(response: any) {
    this.recaptchaToken = response;
  }

  searchParticipant() {
    this.searching = true;
    this.clear();
    let filterValue = this.filterValue;

    if (!filterValue || !filterValue.length) {
      this.gpAlert.showWarning('Preencha o ' + this.filterLabel);
      this.searching = false;
      return;
    }
    if (!this.recaptchaToken) {
      this.gpAlert.showWarning('Preencha o recaptcha.');
      this.searching = false;
      return;
    }

    if (this.filterType == 'document') {
      filterValue = FormatHelper.removeMask(this.filterValue);
    }

    this._participantService.findParticipant(this.filterType, filterValue, this.recaptchaToken)
      .subscribe(
        participant => {
          if (participant && participant.userId) {
            this.participant = participant;

            if (this.participant.campaigns && this.participant.campaigns.length) {
              this.participant.campaigns.sort((c1, c2) => compareStrings(c1.name, c2.name));
              this.campaigns = this.participant.campaigns.map(c => Item.of(c.id, c.name));
            }
          } else {
            this.handleError('Nenhum participante encontrado.');
          }
          this.searching = false;
        },
        err => this.handleError(err)
      );
  }

  private clear() {
    this.participant = null;
    this.campaigns = [];
  }

  selectCampaign() {
    if (!this.participantFound) {
      this.gpAlert.showWarning('Pesquisa por um participante para prosseguir.');
      return;
    } else if (!this.selectedCampaign) {
      this.gpAlert.showWarning('Selecione uma campanha para prosseguir.');
      return;
    }

    this.sending = true;
    const campaign = this.participant.campaigns.find(c => c.id == this.selectedCampaign);
    const callcenterId = this._authStore.loggedUser.id;
    const hasBlocked = this.participant.usersParticipantCampaign.find(c => c.campaignId == this.selectedCampaign);
    const session = new CallcenterSession(callcenterId, campaign.id, campaign.name, this.participant.userId, this.participant.name, campaign.id, hasBlocked.blocked, hasBlocked.blockingDetails);
    this._gpDataStore.set(CALLCENTER_STORE_KEY, session);
    this._gpDataStore.forceStore();
    this._route.navigate(['/callcenter/participante']);
  }
}
