<div class="content-heading">
	Call Center <small>Pesquisar participante</small>
</div>

<form (ngSubmit)="selectCampaign()">
	<gp-alert [overlay]="true" #gpAlert></gp-alert>

	<gp-card title="Pesquisa de Participantes">
		<gp-form-row>
			<div grid="6 6 3 2" class="top-p05">
				<p>Consultar por:</p>
			</div>
			<div grid="6">
				<gp-input-radio name="filterType" pull="left" radioValue="document"
					label="CPF/CNPJ" [(ngModel)]="filterType"></gp-input-radio>
				<gp-input-radio name="filterType" pull="left" radioValue="login"
					label="Login" [(ngModel)]="filterType"></gp-input-radio>
			</div>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 6">
				<gp-simple-input [label]="filterLabel" [required]="true">
					<input type="text" class="form-control" name="filterValue" [(ngModel)]="filterValue" />
				</gp-simple-input>
			</gp-form-col>

			<gp-form-col cols="12 6" additionalClasses="top-p27">
				<gp-spinner-button type="button" text="Pesquisar" [pink]="true" icon="search" [disabled]="disableButtons"
					[loading]="searching" loadingText="Pesquisando" (click)="searchParticipant()">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 6">
				<div style="padding: 1em 0 1em 0;">
					<re-captcha #reCaptcha (resolved)="resolvedCaptcha($event)" size="normal">
					</re-captcha>
				</div>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card *ngIf="participantFound" title="Participante encontrado">
		<div class="row">
			<div grid="6" [group]="true">
				<label>Nome</label>
				<input type="text" class="form-control" readonly disabled="true" [value]="participant.name" />
			</div>
			<div grid="3" [group]="true">
				<label>CPF/CNPJ</label>
				<input type="text" class="form-control" readonly disabled="true" [value]="participant.document" />
			</div>
			<div grid="3" [group]="true">
				<label>RG</label>
				<input type="text" class="form-control" readonly disabled="true" [value]="participant.rg" />
			</div>
		</div>
		<div class="row">
			<gp-form-col cols="12 6">
				<gp-simple-input label="Selecione a campanha" [required]="true">
					<gp-select name="campaign2" [required]="true" [items]="campaigns" [(ngModel)]="selectedCampaign">
					</gp-select>
				</gp-simple-input>
			</gp-form-col>

			<div grid="12 6" class="top-p2">
				<gp-spinner-button type="submit" text="Selecionar" [pink]="true" icon="send"
					[loading]="sending" loadingText="Aguarde" [disabled]="disableButtons">
				</gp-spinner-button>
			</div>
		</div>
	</gp-card>
</form>
