import { Injectable } from '@angular/core';
import * as moment from 'moment';
import { Observable } from 'rxjs/Observable';

import { ApiService } from './../../../core/api/api.service';

@Injectable()
export class TransactionService {
  constructor(private _api: ApiService) { }

  getTransactions(userId: string, campaignId: string, transactionOrigin?: string, startDate?: Date, endDate?: Date): Observable<any[]> {
    let params: any = {};
    if (transactionOrigin) params.transactionOrigin = transactionOrigin;
    if (startDate) params.startDate = moment(startDate).format('YYYY-MM-DD');
    if (endDate) params.endDate = moment(endDate).format('YYYY-MM-DD');

    return this._api.get(`/api/callcenter/users/${userId}/campaigns/${campaignId}/transactions`, params);
  }

  getParticipantTransactions(participantId: string, orderNumber?: string, transactionOrigin?: string): Observable<any[]> {
    let params: any = {};
    if (transactionOrigin) params.transactionOrigin = transactionOrigin;
    if (orderNumber) params.orderNumber = orderNumber;

    return this._api.get(`/api/callcenter/transactions/participants/${participantId}`, params);
  }

  getTransactionDetail(transactionId: string): Observable<any> {
    return this._api.get(`/api/callcenter/transactions/${transactionId}`);
  }

  refundBuyPoints(transactionId: string, refund: any): Observable<boolean> {
    return this._api.post(`/api/callcenter/transactions/${transactionId}/refund/buypoints`, refund);
  }
}
