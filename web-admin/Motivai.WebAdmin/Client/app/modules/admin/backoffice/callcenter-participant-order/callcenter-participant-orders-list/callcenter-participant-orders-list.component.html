<gp-card title="Filtro de resgates">
	<div class="row">
		<div grid="12 4 3" [group]="true">
			<label>Número</label>
			<input type="text" class="form-control" name="orderNumber" [(ngModel)]="filter.orderNumber" />
		</div>
		<gp-datepicker cols="12 4 3" label="Data" name="orderDate" [(ngModel)]="filter.orderDate" [required]="false">
		</gp-datepicker>
		<div grid="12 4 3" [group]="true" class="top-p2">
			<gp-spinner-button text="Pesquisar" [search]="true" (click)="searchOrders()"
				[loading]="loading" loadingText="Pesquisando"></gp-spinner-button>
		</div>
	</div>
	<div class="row">
		<div class="col-md-12 div-alert">
			<gp-alert #gpAlert></gp-alert>
		</div>
	</div>
</gp-card>
<gp-card title="Resgates efetuados pelo participante">
	<gp-form-row>
		<gp-form-col cols="12 12">
			<gp-grid name="ordersGrid" [rows]="orders" [loading]="loading" (onEdit)="viewDetails($event)"
				[columns]="['Número','Data','Total Pontos','Total Reais','Total Estornado Pontos','Total Estornado Reais','Situação']" 
				[fields]="['internalOrderNumber','creationDate','totalAmountPoints','totalAmountCurrency','totalRefundPoints','totalRefundCurrency','status']"
				[showActive]="false" [showPagination]="false" [showTotalPages]="false"
				[showEdit]="true" [showDelete]="false">
			</gp-grid>
		</gp-form-col>
	</gp-form-row>
</gp-card>