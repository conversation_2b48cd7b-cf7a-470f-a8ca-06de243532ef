<gp-card>
	<spinner [overlay]="true" [show]="loading"></spinner>
	<h3 class="mt0">Pedido {{orderNumber}} - {{orderTypeDesc}}</h3>
	<hr/>
	<div class="row mb-lg">
		<div class="clearfix hidden-md hidden-lg"><hr/></div>
		<div class="col-lg-4 col-xs-12 br pv">
			<div class="clearfix">
				<p class="pull-left"><strong>Data do Pedido</strong></p>
				<p class="pull-right mr">{{order.creationDate | datetimezone:order.timezone}}</p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Data de Atualização</strong></p>
				<p class="pull-right mr">{{order.updateDate | datetimezone:order.timezone}}</p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Situação</strong></p>
				<p class="pull-right mr" [class.text-primary]="!order.occurredError"><strong>{{order.statusDescription}}</strong></p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Total Itens</strong></p>
				<p class="pull-right mr">
					<span>{{order.totalAmount?.points - order.shippingCost?.points | number:'1.0'}}</span> -
					<span>R$ {{order.totalAmount?.currency - order.shippingCost?.currency | number:'1.0'}}</span>
				</p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Frete</strong></p>
				<p class="pull-right mr">
					<span>{{order.shippingCost?.points | number:'1.0'}}</span> -
					<span>R$ {{order.shippingCost?.currency | number:'1.0'}}</span>
				</p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Total Pedido</strong></p>
				<p class="pull-right mr">
					<span><strong>{{order.totalAmount?.points | number:'1.0'}}</strong></span> -
					<span><strong>R$ {{order.totalAmount?.currency | number:'1.0'}}</strong></span>
				</p>
			</div>
		</div>

		<div class="col-lg-4 col-xs-6 br pv">
			<div class="row">
				<div class="col-md-2 text-center visible-md visible-lg">
					<em class="fa fa-truck fa-4x text-muted"></em>
				</div>
				<div class="col-md-10" *ngIf="hasShippingAddress">
					<h4>Endereço de Entrega</h4>
					<address></address>{{shippingAddress?.addressName}}<br/>
					CEP: {{shippingAddress?.cep | mask:'00000-000'}}<br/>
					{{shippingAddress?.street}}, {{shippingAddress?.number}}<br/>
					{{shippingAddress?.neighborhood}} - {{shippingAddress?.city}}/{{shippingAddress?.state}}
				</div>
			</div>
		</div>
		<div class="col-lg-4 col-xs-6 br pv">
			<div class="row">
				<div class="col-md-2 text-center visible-md visible-lg">
					<em class="fa fa-user fa-4x text-muted"></em>
				</div>
				<div class="col-md-10" *ngIf="hasShippingAddress">
					<h4>Destinatário</h4>
					<address></address>{{shippingAddress?.receiver?.name}}<br/>
					CPF: {{shippingAddress?.receiver?.cpf | mask:'000.000.000-00'}}<br/>
					Telefone: {{shippingAddress?.receiver?.telephone | mask:'(00) 0000-00000'}}<br/>
					Celular: {{shippingAddress?.receiver?.cellphone | mask:'(00) 0000-00000'}}
				</div>
			</div>
		</div>
	</div>
	<hr class="hidden-print" />
	<div class="row mb-lg">
		<div class="clearfix hidden-md hidden-lg"><hr/></div>
		<div class="col-xs-12 col-sm-12 br pv">
			<div class="clearfix">
				<p class="pull-left">Observações: <span>{{order.observations}}</span></p>
			</div>
		</div>
	</div>
	<hr class="hidden-print" />
	<div class="row mb-lg">
		<div class="clearfix hidden-md hidden-lg"><hr/></div>
		<div class="col-xs-12 col-sm-4 br pv">
			<div class="clearfix">
				<p class="pull-left"><strong>Ocorreu erro</strong></p>
				<p class="pull-right mr" [class.text-success]="!order.occurredError" [class.text-danger]="order.occurredError"><strong>{{order.occurredError ? 'Sim' : 'Não'}}</strong></p>
			</div>
		</div>
		<div class="col-xs-12 col-sm-8 br pv">
			<div class="clearfix">
				<p class="pull-left right-m1"><strong>Mensagem de erro:</strong></p>
				<p class="pull-left mr" [class.text-danger]="order.occurredError">{{order.errorDescription || '-'}}</p>
			</div>
		</div>
	</div>
	<div class="clearfix">
		<button class="btn btn-default pull-left" type="button" onclick="window.print();">Imprimir</button>
		<gp-spinner-button type="submit" text="Estornar pedido" pull="right" [pink]="true" icon="minus-square"
			[loading]="refundingItem" loadingText="Estornando" (click)="openMasterOrderRefunderModal()" *ngIf="canRefundOrder(order)"></gp-spinner-button>
	</div>
	<div class="row">
		<div class="col-md-12 div-alert">
			<gp-alert #gpAlert></gp-alert>
		</div>
		<spinner [overlay]="true" [show]="loading"></spinner>
	</div>
</gp-card>

<risk-assessment [campaignId]="callcenterSession.campaignId" [orderId]="orderId" [riskAssessment]="order.riskAssessment" (onManualAction)="onHandleManualAction()" *ngIf="hasRiskAssessment">
</risk-assessment>

<gp-card *ngFor="let childOrder of order.childrenOrders" [title]="itemGrouperDesc + childOrder.partnerName">
	<div class="row mb-lg">
		<div class="clearfix hidden-md hidden-lg"><hr/></div>
		<div class="col-lg-4 col-xs-12 br pv">
			<div class="clearfix">
				<p class="pull-left"><strong>Número Pedido no Parceiro</strong></p>
				<p class="pull-right mr">{{childOrder.integratedOrderNumber || '-'}}</p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Online</strong></p>
				<p class="pull-right mr">{{childOrder.offline ? 'Não' : 'Sim'}}</p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Data de Atualização</strong></p>
				<p class="pull-right mr">{{childOrder.updateDate | datetimezone:order.timezone}}</p>
			</div>
			<div class="clearfix" *ngIf="childOrder.lastIntegrationUpdate">
				<p class="pull-left"><strong>Data da Última Integração</strong></p>
				<p class="pull-right mr">{{childOrder.lastIntegrationUpdate | datetimezone:order.timezone}}</p>
			</div>
		</div>
		<div class="clearfix hidden-md hidden-lg"><hr/></div>
		<div class="col-lg-4 col-xs-12 br pv">
			<div class="clearfix">
				<p class="pull-left"><strong>Total Itens</strong></p>
				<span class="pull-right mr">
					<div class="text-right">
						<span *ngIf="isRewardsOrMarketplace">{{childOrder.totalAmount?.points - childOrder.shippingCost?.points | number:'1.0'}} - </span>
						<span>R$ {{childOrder.totalAmount?.currency - childOrder.shippingCost?.currency | number:'1.2'}}</span>
					</div>
				</span>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Frete</strong></p>
				<span class="pull-right mr">
					<div class="text-right">
						<span *ngIf="isRewardsOrMarketplace">{{childOrder.shippingCost?.points | number:'1.0'}} - </span>
						<span>R$ {{childOrder.shippingCost?.currency | number:'1.2'}}</span>
					</div>
				</span>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Total Pedido</strong></p>
				<span class="pull-right mr">
					<div class="text-right">
						<span *ngIf="isRewardsOrMarketplace"><strong>{{childOrder.totalAmount?.points | number:'1.0'}}</strong> - </span>
						<span>R$ <strong>{{childOrder.totalAmount?.currency | number:'1.2'}}</strong></span>
					</div>
				</span>
			</div>
		</div>
		<div class="clearfix hidden-md hidden-lg"><hr/></div>
		<div class="col-lg-4 col-xs-12 br pv">
			<div class="clearfix">
				<p class="pull-left"><strong>Situação</strong></p>
				<p class="pull-right mr" [class.text-primary]="!childOrder.occurredError"><strong>{{childOrder.statusDescription}}</strong></p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Ocorreu erro</strong></p>
				<p class="pull-right mr" [class.text-success]="!(order.occurredError || childOrder.occurredError)" [class.text-danger]="order.occurredError || childOrder.occurredError"><strong>{{order.occurredError || childOrder.occurredError ? 'Sim' : 'Não'}}</strong></p>
			</div>
			<div class="clearfix">
				<p class="pull-left"><strong>Mensagem de erro</strong></p>
				<p class="pull-right mr" [class.text-danger]="childOrder.occurredError">{{childOrder.errorDescription || '-'}}</p>
			</div>
		</div>
	</div>
	<div class="table-responsive table-bordered mb-lg">
		<table class="table table-bordered table-striped table-hover">
			<thead>
				<tr>
					<th>Situação</th>
					<th>SKU</th>
					<th>Produto</th>
					<!-- <th>Rastreamento</th> -->
					<th>Prazo de entrega (dias)</th>
					<th>Quantidade</th>
					<th class="text-center">Valor Unit.</th>
					<th class="text-center">Frete</th>
					<th class="text-center">Total</th>
					<th class="text-center">Ações</th>
				</tr>
			</thead>
			<tbody>
				<tr *ngFor="let item of childOrder.items">
					<td [class.text-primary]="!item.occurredError" [class.text-danger]="item.occurredError"><strong>{{item.statusDescription}}</strong></td>
					<td>{{item.skuCode}}</td>
					<td>{{item.name}}</td>
					<!--<td><a *ngIf="item.trackingLink" [href]="item.trackingLink" target="_blank">Link</a></td>-->
					<td>{{item.estimatedDeliveryDays || childOrder.estimatedDeliveryDays || 'Sem previsão'}}</td>
					<td>{{item.quantity}}</td>
					<td class="text-right">
						<div *ngIf="isRewardsOrMarketplace">{{item.unitPrice.points | number:'1.0'}}</div>
						<div>R$ {{item.unitPrice.currency | number:'1.2'}}</div>
					</td>
					<td class="text-right">
						<div *ngIf="isRewardsOrMarketplace">{{item.shippingCost?.points | number:'1.0'}}</div>
						<div>R$ {{item.shippingCost?.currency | number:'1.2'}}</div>
					</td>
					<td class="text-right">
						<div *ngIf="isRewardsOrMarketplace">{{calculateSubtotalPoints(item) | number:'1.0'}}</div>
						<div>R$ {{calculateSubtotalCurrency(item) | number:'1.2'}}</div>
					</td>
					<td class="text-center">
						<gp-spinner-button type="button" bootstrapClass="default" icon="truck" [disabled]="disableButtons"
							tooltip="Visualizar Tracking" (click)="showTrackingModal(item)" *ngIf="hasTracking(item)"></gp-spinner-button>

						<gp-spinner-button type="button" bootstrapClass="default" icon="list" [disabled]="disableButtons"
							tooltip="Visualizar estornos" (click)="openRefunderModalToList(item)" *ngIf="hasRefunds(childOrder, item)"></gp-spinner-button>
						<gp-spinner-button type="button" bootstrapClass="default" icon="recycle" [disabled]="disableButtons"
							[loading]="refundingItem" tooltip="Estornar item completo ou parcial"
							*ngIf="canRefundItem(childOrder, item) && !allowOnlyPartnerOrderRefund" (click)="openItemRefunderModal(childOrder, item)"></gp-spinner-button>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="row">
		<div class="col-sm-offset-8 col-sm-4 pv">
			<div class="clearfix">
				<p class="pull-left top-m1">Total Itens</p>
				<span class="pull-right mr">
					<div class="text-right" *ngIf="isRewardsOrMarketplace">{{childOrder.totalAmount?.points - childOrder.shippingCost?.points | number:'1.0'}}</div>
					<div class="text-right">R$ {{childOrder.totalAmount?.currency - childOrder.shippingCost?.currency | number:'1.2'}}</div>
				</span>
			</div>
			<hr/>
			<div class="clearfix">
				<p class="pull-left top-m1">Frete</p>
				<span class="pull-right mr">
					<div class="text-right" *ngIf="isRewardsOrMarketplace">{{childOrder.shippingCost?.points | number:'1.0'}}</div>
					<div class="text-right">R$ {{childOrder.shippingCost?.currency | number:'1.2'}}</div>
				</span>
			</div>
			<hr/>
			<div class="clearfix">
				<p class="pull-left top-m1"><strong>Total Pedido</strong></p>
				<span class="pull-right mr">
					<div class="text-right" *ngIf="isRewardsOrMarketplace"><strong>{{childOrder.totalAmount?.points | number:'1.0'}}</strong></div>
					<div class="text-right">R$ <strong>{{childOrder.totalAmount?.currency | number:'1.2'}}</strong></div>
				</span>
			</div>
		</div>
		<div class="clearfix right-m1">
			<gp-spinner-button type="submit" text="Estornar pedido parceiro" pull="right" [pink]="true"  icon="minus-square"
				[loading]="refundingPartner" loadingText="Estornando" (click)="openChildOrderRefunderModal(childOrder)"
				*ngIf="canRefundPartnerOrder(childOrder)"></gp-spinner-button>
		</div>
	</div>
</gp-card>

<gp-modal title="Tracking do item" width="1000px" marginLeft="20%" #gpTrackingModal>
	<div class="row">
		<div class="col-xs-12 col-sm-6 br pv">
			<div class="clearfix">
				<p class="pull-left right-m1"><label>Link para Rastreio:</label></p>
				<p class="pull-left mr"><a *ngIf="itemWithTracking?.trackingLink" [href]="itemWithTracking?.trackingLink" target="_blank">Link</a></p>
			</div>
			<div class="clearfix">
				<p class="pull-left right-m1"><label>Código da Rastreio:</label></p>
				<p class="pull-left mr">{{itemWithTracking?.trackingCode}}</p>
			</div>
		</div>
		<div class="col-xs-12 col-sm-6 br pv">
			<div class="clearfix">
				<p class="pull-left right-m1"><label>Código da Entrega:</label></p>
				<p class="pull-left mr">{{itemWithTracking?.deliveryCode}}</p>
			</div>
		</div>
	</div>
	<div class="row" *ngIf="itemWithTracking?.invoiceNumber">
		<div class="col-xs-12 col-sm-12 br pv">
			<div class="clearfix">
				<p class="pull-left right-m1"><label>Nota Fiscal:</label></p>
				<p class="pull-left mr">
					<span>
						{{itemWithTracking?.invoiceNumber}} - Série: {{itemWithTracking?.invoiceSerie}} - Chave de acesso: {{itemWithTracking?.invoiceKeyAccess}}
					</span>
				</p>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-xs-12">
			<gp-grid name="trackings" [rows]="itemWithTracking?.trackingEvents"
				[columns]="['Data','Descrição', 'Descrição do Parceiro']" [fields]="['dateText','description', 'partnerDescription']"
				[showActive]="false" [showEdit]="false" [showDelete]="false">
			</gp-grid>
		</div>
	</div>
</gp-modal>

<order-refunder-modal #refundedModal (onClose)="loadOrder()"></order-refunder-modal>
