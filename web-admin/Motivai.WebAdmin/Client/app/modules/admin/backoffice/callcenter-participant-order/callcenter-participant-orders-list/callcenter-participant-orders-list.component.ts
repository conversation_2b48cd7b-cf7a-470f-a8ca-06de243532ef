import { Router } from '@angular/router';
import { Component, ViewChild } from '@angular/core';

import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CALLCENTER_STORE_KEY, CallcenterSession } from '../../callcenter-session';
import { DataStore } from '../../../../../core/store/data.store';
import { OrderService } from '../../order.service';

@Component({
  selector: 'callcenter-participant-orders-list',
  templateUrl: 'callcenter-participant-orders-list.component.html'
})
export class CallcenterParticipantOrdersListComponent {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  callcenterSession: CallcenterSession;
  filter: any = {};
  loading: boolean = false;

  orders: Array<any> = [];

  constructor(private _gpDataStore: DataStore, private _route: Router, private _orderService: OrderService) { }

  ngOnInit() {
    this.callcenterSession = this._gpDataStore.get(CALLCENTER_STORE_KEY);
    if (!this.callcenterSession) {
      this._route.navigate(['/callcenter/pesquisa']);
    }
    this.searchOrders();
  }

  private handleError(err) {
    let errorMessage = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao efetuar a operação.';
    this.gpAlert.showError(errorMessage);
    this.loading = false;
  }

  searchOrders() {
    this.loading = true;
    this.gpAlert.clear();
    this._orderService.getParticipantOrders(this.callcenterSession.userId, this.callcenterSession.campaignId,
          this.filter.orderNumber, this.filter.orderDate)
      .subscribe(
        orders => {
          this.orders = orders;
          this.loading = false;
        },
        err => this.handleError(err)
      );
  }

  public viewDetails(event) {
    this.gpAlert.clear();
    if (!event || !event.id) {
      this.gpAlert.showWarning('Pedido inválido.');
      return;
    }
    this._route.navigate(['/callcenter/resgates', event.id, 'detalhes', event.internalOrderNumber]);
  }
}
