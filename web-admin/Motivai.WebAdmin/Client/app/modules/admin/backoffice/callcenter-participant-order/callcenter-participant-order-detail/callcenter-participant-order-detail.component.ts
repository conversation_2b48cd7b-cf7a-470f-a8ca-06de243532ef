import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

import { PERMISSION_CALLCENTER_REFUND } from '../../../../../core/auth/access-points';
import { DataStore } from '../../../../../core/store/data.store';
import { getTypeDescription, isRewardsOrMarketplace } from '../../../../../shared/components/business/campaigns/campaign-setttings/campaign-settings';
import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpModalComponent } from '../../../../../shared/components/gp-modal/gp-modal.component';
import { OrderRefunderModalComponent } from '../../../campaigns/campaign-catalog/campaign-orders/campaign-orders-handler/order-refunder-modal/order-refunder-modal.component';
import { CampaignSettingsService } from '../../../campaigns/campaign-settings.service';
import { CALLCENTER_STORE_KEY, CallcenterSession } from '../../callcenter-session';
import { OrderService } from '../../order.service';
import { AuthStore } from './../../../../../core/auth/auth.store';

@Component({
  selector: 'callcenter-participant-order-detail',
  templateUrl: 'callcenter-participant-order-detail.component.html',
  styleUrls: [`
    table th { text-align: center; }
    hr { margin: 0.5em 0; }
    .chkPartial { float:left; padding:0.8em 0 0 1em; }
  `]
})
export class CallcenterParticipantOrderDetailComponent implements OnInit {
  @ViewChild('gpTrackingModal') gpTrackingModal: GpModalComponent;
  @ViewChild('refundedModal') refundedModal: OrderRefunderModalComponent;
  itemWithTracking: any;

  // Modal Pedido
  @ViewChild('orderRefundModal') orderRefundModal: any;
  @ViewChild('gpOrderAlert') gpOrderAlert: GpAlertComponent;
  refundingOrder: boolean = false;
  // Modal Pedido Parceiro
  @ViewChild('gpRefundPartnerOrder') gpPartnerAlert: GpAlertComponent;
  @ViewChild('partnerRefundModal') partnerRefundModal: any;
  refundingPartner: boolean = false;
  // Modal Item
  @ViewChild('itemRefundModal') itemRefundModal: any;
  @ViewChild('itemRefundsListModal') itemRefundsListModal: any;
  @ViewChild('gpRefundItem') gpItemAlert: GpAlertComponent;
  refundingItem: boolean = false;

  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  callcenterSession: CallcenterSession;
  loading: boolean = false;
  
  orderId: string;
  orderNumber: string;
  order: any = {};
  shippingAddress: any = {};

  // vars da modal
  optOriginalAmount: any = {};
  optRefunded: any = {};
  optTotalAmount: any = {};
  originalAmount = 0.01;
  refundedAmount = 0.01;
  totalAmount = 0.01;

  childOrder: any;
  item: any;
  refund: any = {};

  refundReasons = [
    { key: 'Desistencia', value: 'Desistência' },
    { key: 'Arrependimento', value: 'Arrependimento' },
    { key: 'ProblemaPedido', value: 'Problema com o pedido' },
    { key: 'ProblemaItem', value: 'Problema com o item' },
    { key: 'ProblemaEntrega', value: 'Problema na entrega' },
    { key: 'Insatisfacao', value: 'Insatisfação' },
    { key: 'Outros', value: 'Outros' },
  ];

  allowOnlyPartnerOrderRefund: boolean = false;

  constructor(private _authStore: AuthStore, private _gpDataStore: DataStore,
    private _orderService: OrderService, private _router: Router, private _route: ActivatedRoute, private _campaignSettings: CampaignSettingsService) {}

  ngOnInit() {
    this.callcenterSession = this._gpDataStore.get(CALLCENTER_STORE_KEY);
    if (!this.callcenterSession) {
      this._router.navigate(['/callcenter/pesquisa']);
    }
    this.loading = true;
    this.loadCampaignParametrization();
    if (this._route != null) {
      this._route.params.subscribe((params: any) => {
        this.orderId = params['orderId'];
        this.orderNumber = params['orderNumber'];
        if (this.orderId) {
          this.loadOrder();
        } else {
          this._router.navigate(['']);
        }
      });
    } else {
      this.loading = false;
    }
  }

  private loadCampaignParametrization() {
    this.loading = true;
    this._campaignSettings.getCampaignParametrization(this.callcenterSession.campaignId)
        .subscribe(
            settings => {
                if (settings) {
                  this.allowOnlyPartnerOrderRefund = settings.parametrizations.allowOnlyPartnerOrderRefund;
                }
            },
            err => {
              this.handleError(err);
            }
        );
  }

  get isRewardsOrMarketplace() {
    return isRewardsOrMarketplace(this.order.type);
  }

  get orderTypeDesc() {
    return getTypeDescription(this.order.type);
  }

  get disableButtons() {
    return this.loading || this.refundingItem || this.refundingPartner;
  }

  get itemGrouperDesc() {
    if (!this.order || !this.order.type) return 'Parceiro ';
    return isRewardsOrMarketplace(this.order.type) ? 'Parceiro ' : 'Fábrica/Distribuidora ';
  }

  get isPartial() {
    return this.refund && this.refund.refundType === 'Parcial';
  }

  get hasPriceToRefund() {
    return this.item && this.item.unitPrice.points > 0;
  }

  get hasShippingToRefund() {
    return this.item && this.item.shippingCost.points > 0;
  }

  get hasPermissionToRefund() {
    return this._authStore.hasPermissionTo(PERMISSION_CALLCENTER_REFUND);
  }

  get hasShippingAddress() {
    return this.shippingAddress != null;
  }

  get hasRiskAssessment() {
    return this.order && this.order.riskAssessment;
  }

  private extractErrorMessage(err) {
    return err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao efetuar a operação.';
  }

  private handleError(err) {
    this.gpAlert.showError(this.extractErrorMessage(err));
    this.loading = false;
  }

  loadOrder() {
    this.loading = true;
    this.order = {};
    this.shippingAddress = {};
    this._orderService.getOrderDetail(this.orderId)
      .subscribe(
        order => {
          this.order = order;
          if (this.order) {
            this.shippingAddress = this.order.shippingAddress;
          }
          this.loading = false;
        },
        err => {
          this.handleError(err);
          this.order = {};
        }
      );
  }

  private fetchKnob(subText, textValue, barColor): any {
    return {
      readOnly: true,
      inputFormatter: val => textValue,
      size: 180,
      textColor: '#666666',
      fontSize: '20',
      fontWeigth: '600',
      fontFamily: 'Verdana',
      valueformat: 'percent',
      min: 0,
      max: 100,
      trackWidth: 13,
      barWidth: 13,
      trackColor: '#EEEEEE',
      barColor: barColor,
      subText: {
        enabled: true,
        fontFamily: 'Verdana',
        font: '14',
        fontWeight: 'bold',
        text: subText,
        color: '#666666',
        offset: 7
      },
    };
  }

  calculateSubtotalPoints(item: any): number {
    return item.unitPrice.points * item.quantity
      + (item.shippingCost && item.shippingCost.points ? item.shippingCost.points : 0);
  }

  calculateSubtotalCurrency(item: any): number {
    return item.unitPrice.currency * item.quantity
      + (item.shippingCost && item.shippingCost.currency ? item.shippingCost.currency : 0);
  }

  hasTracking(item: any) {
    return item && item.trackingEvents && item.trackingEvents.length;
  }

  showTrackingModal(item: any) {
    if (!this.hasTracking(item)) {
      return;
    }
    this.itemWithTracking = item;
    this.gpTrackingModal.show();
  }

  canRefundOrder() {
    return this.hasPermissionToRefund && this.order && this.order.id &&  isRewardsOrMarketplace(this.order.type)
      && !this.order.occurredError && this.order.externalStatus !== 'Refunded' && this.order.internalStatus !== "UNDER_RISK_ASSESSMENT"
  }

  canRefundPartnerOrder(partnerOrder: any) {
    return this.canRefundOrder() && partnerOrder && !partnerOrder.occurredError && partnerOrder.status !== 'Refunded';
  }

  canRefundItem(partnerOrder: any, item: any) {
    return this.hasPermissionToRefund && this.canRefundPartnerOrder(partnerOrder) && (item.status !== 'Refunded' && item.status !== 'Canceled')
      && (!item.dynamicPrice || item.dynamicPrice && item.priced);
  }

  isRefund(partnerOrder: any, item: any) {
    return !partnerOrder.occurredError && item.status === 'Refunded';
  }

  hasRefunds(partnerOrder: any, item: any) {
    return !partnerOrder.occurredError && item.refunds && item.refunds.length;
  }

  openMasterOrderRefunderModal() {
    this.refundedModal.openModalToRefundMasterOrder(this.order.campaignId, this.order);
  }

  openChildOrderRefunderModal(childOrder: any) {
    this.refundedModal.openModalToRefundChildOrder(this.order.campaignId, this.order, childOrder);
  }

  openItemRefunderModal(childOrder: any, item: any) {
    this.refundedModal.openModalToRefundItem(this.order.campaignId, this.order, childOrder, item);
  }

  openRefunderModalToList(item: any) {
    if (!item) return;
    this.refundedModal.openModalToShowRefundList(this.order, item);
  }

  onHandleManualAction() {
    this.loadOrder();
  }
}
