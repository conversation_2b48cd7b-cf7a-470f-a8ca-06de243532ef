import { Component, OnInit, ViewChild } from '@angular/core';
import { CampaignCreditDebitManualViewComponent } from '../../campaigns/common/campaign-points/campaign-credit-debit-manual/campaign-credit-debit-manual-view/campaign-credit-debit-manual-view.component';
import { DataStore } from '../../../../core/store/data.store';
import { Router } from '@angular/router';
import { CALLCENTER_STORE_KEY, CallcenterSession } from '../callcenter-session';

@Component({
    selector: 'callcenter-campaign-manual-transaction',
    templateUrl: './callcenter-campaign-manual-transaction.component.html'
})
export class CallCenterCampaignManualTransactionComponent implements OnInit {
    @ViewChild('view') view: CampaignCreditDebitManualViewComponent;


    private callcenterSession: CallcenterSession;

    campaignId: string;
    userId: string;

    constructor(private _ds: DataStore, private _route: Router) { }

    ngOnInit(): void {
        this.callcenterSession = this._ds.get(CALLCENTER_STORE_KEY);
        if (!this.callcenterSession)
            this._route.navigate(['/callcenter/pesquisa']);

        if (this.callcenterSession.campaignId != null && this.callcenterSession.campaignId.length > 0)
            this.campaignId = this.callcenterSession.campaignId;
        if (this.callcenterSession.userId != null && this.callcenterSession.userId.length > 0)
            this.userId = this.callcenterSession.userId;
            
            this.view.isCallCenter = true;
    }
}
