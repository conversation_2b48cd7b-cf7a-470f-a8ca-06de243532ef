<gp-alert [overlay]="true" #gpAlert></gp-alert>
<gp-card title="Filtro de resgates">
	<div class="row">
		<gp-datepicker cols="12 4 3" label="Início do Período" name="startDate" [(ngModel)]="filters.startDate" [required]="false">
		</gp-datepicker>
		<gp-datepicker cols="12 4 3" label="Fim do Período" name="endDate" [(ngModel)]="filters.endDate" [required]="false">
		</gp-datepicker>
		<div grid="12 4 3" [group]="true">
			<label>Origem da transação</label>
			<select name="transactionOrigin" class="form-control" [(ngModel)]="filters.transactionOrigin">
				<option value="">Todas</option>
				<option value="Import">Importação</option>
				<option value="BuyPoints">Compra de Pontos</option>
				<option value="Order">Pedidos</option>
				<option value="MobileRecharge">Recarga de Celular</option>
				<option value="BillPayment">Pague de Contas</option>
				<option value="Distribution">Distribuição de Pontos</option>
				<option value="PrepaidCard">Cartão Pré-Pago</option>
				<option value="BankTransfer">Transferência Bancária</option>
				<option value="EvaluationBonus">Bônus de Avaliação</option>
				<option value="PointsEngine">Motor de Pontos</option>
				<option value="AdminManual">Transação Manual</option>
				<option value="PointsExpiration">Expiração de Pontos</option>
				<option value="TransactionRefund">Estorno de Transação</option>
				<option value="OrderRefund">Estorno de Pedido</option>
				<option value="BuyPointsRefund">Estorno de Compra de Pontos</option>
				<!-- <option value="CallCenter"></option> -->
				<!-- <option value="AppTransferP2P"></option>
				<option value="AppPaymentP2P"></option>
				<option value="AppPayment"></option> -->
			</select>
		</div>
		<div grid="12 4 3" [group]="true" class="top-p2">
			<gp-spinner-button text="Pesquisar" [search]="true" (click)="getTransactions()"
				[loading]="loading" loadingText="Pesquisando"></gp-spinner-button>
		</div>
	</div>
</gp-card>

<gp-card title="Extrato">
	<gp-form-row *ngIf="showBalance">
		<gp-form-col cols="12 12">
			<p>
				<label>Saldo: </label>
				<span>{{ participantBalance | number:'1.2' }}</span>
			</p>
		</gp-form-col>
	</gp-form-row>
	<gp-form-row>
		<gp-form-col cols="12 12">
			<gp-datatable [columnsName]="['Data', 'Tipo', 'Descrição', 'Valor']">
				<tbody>
					<tr *ngFor="let transaction of transactions">
						<td>{{ transaction.processingDate | datetimezone }}</td>
						<td>{{ transaction.transactionType == 'Credit' ? 'Crédito' : 'Débito' }}</td>
						<td>{{ transaction.description }}</td>
						<td class="text-right">{{ transaction.totalPoints | number:'1.2' }}</td>
					</tr>
					<tr *ngIf="isEmpty">
						<td colspan="4">Nenhum registro encontrado.</td>
					</tr>
				</tbody>
			</gp-datatable>
			<spinner [overlay]="true" [show]="loading"></spinner>
		</gp-form-col>
	</gp-form-row>
</gp-card>
