import { Observable } from 'rxjs/Observable';
import { Injectable } from '@angular/core';
import * as moment from 'moment';
import { ApiService } from '../../../core/api/api.service';

@Injectable()
export class OrderService {
	constructor(private _api: ApiService) { }

	getParticipantOrders(userId: string, campaignId: string, orderNumber: string, orderDate: any): Observable<Array<any>> {
		if (orderDate) {
			orderDate = moment(orderDate).format('YYYY-MM-DD');
		}
		return this._api.get(`/api/callcenter/users/${userId}/campaigns/${campaignId}/orders`, { orderNumber, orderDate });
	}

	getOrderDetail(orderId: string): Observable<any> {
		return this._api.get(`/api/callcenter/orders/${orderId}`, null, 10000);
	}

	refundOrder(orderId: string, refund: any): Observable<any> {
		return this._api.post(`/api/callcenter/orders/${orderId}/refund`, refund, 20000);
	}

	refundPartnerOrder(orderId: string, partnerId: string, refund: any): Observable<any> {
		return this._api.post(`/api/callcenter/orders/${orderId}/partnersorders/${partnerId}/refund`, refund, 20000);
	}

	refundItem(orderId: string, refund: any): Observable<any> {
		return this._api.post(`/api/callcenter/orders/${orderId}/refunditem`, refund, 20000);
	}
}
