<gp-card title="Dados do participante">
	<div class="row">
		<div grid="12 6" [group]="true">
			<label>Nome:</label>
			<input type="text" class="form-control" readonly disabled="true" [value]="participantInfo?.name" />
		</div>
		<div grid="12 3" [group]="true" *ngIf="participantInfo?.cnpj">
			<label>CNPJ:</label>
			<input type="text" class="form-control" readonly disabled="true"
				[value]="participantInfo?.cnpj | mask:'00.000.000/0000-00'" />
		</div>
		<div grid="12 3" [group]="true" *ngIf="participantInfo?.cpf">
			<label>CPF:</label>
			<input type="text" class="form-control" readonly disabled="true"
				[value]="participantInfo?.cpf | mask:'000.000.000-00'" />
		</div>
		<div grid="12 3" *ngIf="showBalance">
			<label><PERSON><PERSON> de Pontos:</label>
			<input type="text" class="form-control" readonly dislabled [value]="participantBalance | amount" />
		</div>
	</div>
	<div class="row">
		<div grid="12 6" [group]="true">
			<gp-simple-input label="Email:">
				<input type="text" name="email" readonly disabled="true" class="form-control"
					[(ngModel)]="participantInfo.email" />
			</gp-simple-input>
		</div>
		<div grid="12 3" [group]="true">
			<label>Login:</label>
			<input type="text" class="form-control" readonly disabled="true" [value]="participantInfo?.login" />
		</div>
		<div grid="12 3" [group]="true">
			<label>Bloqueado:</label>
			<input type="text" class="form-control" readonly disabled="true" [value]="callcenterSession?.isBlocked | booleanToYesNo" />
		</div>
	</div>
	<div class="row">
		<div grid="12 3" [group]="true">
			<label>Telefone:</label>
			<input type="text" class="form-control" readonly disabled="true"
				[value]="participantInfo?.telephone | mask:'(00) 0000-0000'" />
		</div>
		<div grid="12 3" [group]="true">
			<gp-simple-input label="Celular:">
				<gp-input-mask name="cellphone" readonly disabled="true" mask="(00) 00000-0000"
					[(ngModel)]="participantInfo.cellphone"></gp-input-mask>
			</gp-simple-input>
		</div>

		<div grid="12 6" [group]="true" *ngIf="callcenterSession?.isBlocked">
			<label>Motivo do Bloqueio:</label>
			<input type="text" class="form-control" readonly disabled="true"
			[value]="callcenterSession?.blockingDatails.reason" />
		</div>
	</div>

	<hr *ngIf="participantInfo.authenticationMfaSettings">
	<div *ngIf="participantInfo.authenticationMfaSettings">
		<h4>Dados de autenticação MFA</h4>
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Formato de envio do Token">
					<input type="text" class="form-control" readonly
						[(ngModel)]="participantInfo.authenticationMfaSettings.authenticationMfaFormatDescription" />
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 6 6"
				*ngIf="participantInfo.authenticationMfaSettings.authenticationMfaFormat != 'SMS_ONLY'">
				<gp-simple-input label="E-mail">
					<input type="text" class="form-control" readonly
						[(ngModel)]="participantInfo.authenticationMfaSettings.email" />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 6"
				*ngIf="participantInfo.authenticationMfaSettings.authenticationMfaFormat != 'EMAIL_ONLY'">
				<gp-simple-input label="Celular">
					<gp-input-mask name="cellphone" readonly disabled="true" mask="(00) 00000-0000"
					[(ngModel)]="participantInfo.authenticationMfaSettings.mobilePhone"></gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
	</div>

	<div class="row">
		<div class="col-md-12 div-alert">
			<gp-alert #gpAlert></gp-alert>
		</div>
		<spinner [overlay]="true" [show]="loading"></spinner>
	</div>
</gp-card>
<gp-card>
	<gp-form-row>
		<gp-form-col [inputGroup]="false" cols="12">
			<gp-spinner-button type="submit" pull="right" [loading]="loading" [pink]="true"
				(click)="openParticipantContactModal()" loadingText="Processando..."
				text="Atualizar informações de contato"></gp-spinner-button>
		</gp-form-col>
	</gp-form-row>
</gp-card>
<callcenter-participant-contact-modal (onClose)="loadParticipant()" [userId]="this.callcenterSession.userId"
	[campaignId]="callcenterSession.campaignId" #participantContactModal></callcenter-participant-contact-modal>
