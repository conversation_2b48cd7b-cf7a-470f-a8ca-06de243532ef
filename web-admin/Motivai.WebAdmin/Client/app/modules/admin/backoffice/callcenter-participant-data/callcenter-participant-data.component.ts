import { Router } from '@angular/router';
import { ParticipantService } from './../participant.service';
import { Component, OnInit, ViewChild } from '@angular/core';

import { DataStore } from './../../../../core/store/data.store';
import { CALLCENTER_STORE_KEY, CallcenterSession } from './../callcenter-session';
import { GpAlertComponent } from './../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CallcenterParticipantContactModalComponent } from './callcenter-pariticipant-contact-modal/callcenter-participant-contact-modal.component';

@Component({
  selector: 'callcenter-participant-data',
  templateUrl: 'callcenter-participant-data.component.html'
})
export class CallcenterParticipantDataComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @ViewChild('participantContactModal') participantContactModal: CallcenterParticipantContactModalComponent;

  loading: boolean = false;
  callcenterSession: CallcenterSession;
  participantInfo: any = {};

  showBalance: boolean = true;
  participantBalance: number = 0;

  constructor(private _gpDataStore: DataStore, private _route: Router, private _participantService: ParticipantService) { }

  ngOnInit() {
    this.callcenterSession = this._gpDataStore.get(CALLCENTER_STORE_KEY);
    if (!this.callcenterSession) {
      this._route.navigate(['/callcenter/pesquisa']);
    }
    if (!this.callcenterSession.blockingDatails) {
        this.callcenterSession.blockingDatails = {};
    }
    if (this.callcenterSession.isBlocked && !this.callcenterSession.blockingDatails.reason) {
      this.callcenterSession.blockingDatails.reason = "Bloqueado manualmente.";
    }
    this.loadParticipant();
    this.loadParticipantBalance();
  }

  private handleError(err) {
    let errorMessage = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao efetuar a operação.';
    this.gpAlert.showError(errorMessage);
    this.loading = false;
  }

  private updateCallcenterSession() {
    this._gpDataStore.set(CALLCENTER_STORE_KEY, this.callcenterSession);
    this._gpDataStore.forceStore();
  }

  private loadParticipant() {
    this.loading = true;
    this._participantService.getParticipantInfo(this.callcenterSession.userId, this.callcenterSession.campaignId)
      .subscribe(
        info => {
          if (info) {
            this.participantInfo = info;
            if (this.participantInfo.authenticationMfaSettings) {
              this.participantInfo.authenticationMfaSettings.authenticationMfaFormatDescription = this.handleAuthenticationMfaFormatDescription(this.participantInfo.authenticationMfaSettings.authenticationMfaFormat);
            }
            if (!this.callcenterSession.participantId) {
              this.callcenterSession.participantId = info.participantId;
              this.updateCallcenterSession();
            }
          } else {
            this.gpAlert.showWarning('Participante não encontrado.');
          }
          this.loading = false;
        },
        err => this.handleError(err)
      );
  }

  private loadParticipantBalance() {
    this._participantService.getParticipantBalance(this.callcenterSession.userId, this.callcenterSession.campaignId)
      .subscribe(
        balance => {
          this.showBalance = true;
          this.participantBalance = balance;
        },
        err => {
          this.showBalance = false;
          this.handleError(err);
        }
      );
  }

  openParticipantContactModal() {
    this.participantContactModal.participantContact = {
      mainEmail: this.participantInfo.email,
      mobilePhone: this.participantInfo.cellphone
    };
    this.participantContactModal.oldParticipantContact = {
      mainEmail: this.participantInfo.email,
      mobilePhone: this.participantInfo.cellphone
    };
    this.participantContactModal.open();
  }

  private handleAuthenticationMfaFormatDescription(authenticationMfaFormat: string): string {
    if (!authenticationMfaFormat) {
      return 'Formato inválido';
    }

    switch (authenticationMfaFormat) {
      case 'SMS_ONLY':
        return 'Somente SMS';
      case 'EMAIL_ONLY':
        return 'Somente E-mail';
      case 'BOTH':
        return 'SMS e E-mail';
      default:
        return 'Formato inválido';
    }
  }
}
