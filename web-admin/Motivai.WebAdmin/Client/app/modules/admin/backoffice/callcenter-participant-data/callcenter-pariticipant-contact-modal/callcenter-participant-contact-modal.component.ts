import { GpAlertComponent } from './../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpModalComponent } from './../../../../../shared/components/gp-modal/gp-modal.component';
import { Component, OnInit, ViewChild, Input, Output, EventEmitter } from '@angular/core';
import { ParticipantService } from '../../participant.service';

@Component({
    selector: 'callcenter-participant-contact-modal',
    templateUrl: './callcenter-participant-contact-modal.component.html'
})
export class CallcenterParticipantContactModalComponent implements OnInit {
    @ViewChild('modal') modal: GpModalComponent;
    @ViewChild('gpAlert') gpAlert: GpAlertComponent;
    @Output() onClose: EventEmitter<any> = new EventEmitter<any>();

    @Input() campaignId: string = '';
    @Input() userId: string = '';

    participantContact: any = {
        mainEmail: '',
        mobilePhone: ''
    };

    oldParticipantContact: any = {
        mainEmail: '',
        mobilePhone: ''
    };

    reason: string = '';

    loading: boolean = false;

    constructor(private _participantService: ParticipantService) { }

    ngOnInit(): void { }

    upadateParticipantContactInformation() {
        this.loading = true;

        const contactChangeOperation = {
            reason: this.reason,
            after: this.participantContact,
            before: this.oldParticipantContact
        };

        this._participantService.upadateParticipantContactInformation(this.userId, this.campaignId, contactChangeOperation)
            .subscribe(
                response => {
                    if (response) {
                        this.gpAlert.showSuccess('Informações de contato atualizadas.');
                        this.close();
                    }
                    this.loading = false;
                }, err => {
                    this.loading = false;
                    this.gpAlert.showError(err);
                }
            );
    }

    clear() {
        this.participantContact = {
            mainEmail: '',
            mobilePhone: ''
        };
        this.oldParticipantContact = {
            mainEmail: '',
            mobilePhone: ''
        };
        this.reason = '';
        this.onClose.emit();
    }


    open() {
        this.modal.show();
    }

    close() {
        this.modal.hide();
        this.onClose.emit();
    }
}
