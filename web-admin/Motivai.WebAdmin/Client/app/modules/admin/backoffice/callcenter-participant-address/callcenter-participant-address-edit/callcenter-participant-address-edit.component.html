<form #addressForm="ngForm" (ngSubmit)="addressSubmit()" novalidate>		
	<gp-card [first]="true">
		<gp-form-row>
			<gp-form-col cols="6 6 6">
				<gp-simple-input [required]="true" label="Descrição do Endereço" errorMessage="Descrição do Endereço é obrigatório">
					<input type="text" class="form-control" name="addressName" required [(ngModel)]="address.addressName" />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="6 6 6">
				<gp-simple-input [required]="false" label="Endereço Principal">
					<!--gp-switch [(ngModel)]="address.mainAddress"></gp-switch-->
					<gp-input-checkbox name="mainAddress" [(ngModel)]="address.mainAddress"></gp-input-checkbox>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card title="Dados do Endereço">
		<gp-form-row>
			<gp-form-col cols="12 2 2">
				<gp-simple-input [required]="true" label="CEP" errorMessage="CEP é obrigatório">
					<gp-input-mask name="cep" mask="99999999" [(ngModel)]="address.cep" (onblur)="searchAddress($event)"></gp-input-mask>
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 10 10">
				<p style="margin-top: 32px">{{ renderCepStatus }}</p>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input [required]="true" label="Logradouro" errorMessage="Logradouro é obrigatório">
					<input type="text" class="form-control" [disabled]="addressFound" name="address" required [(ngModel)]="address.street" />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 2 2">
				<gp-simple-input [required]="true" label="Número" errorMessage="Número é obrigatório">
					<input type="text" class="form-control" name="number" required [(ngModel)]="address.number" />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 4 4">
				<gp-simple-input [required]="true" label="Complemento" errorMessage="Complemento é obrigatório">
					<input type="text" class="form-control" name="complement" required [(ngModel)]="address.complement" />
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input [required]="true" label="Cidade" errorMessage="Cidade é obrigatório">
					<input type="text" class="form-control" [disabled]="addressFound" name="city" required [(ngModel)]="address.city" />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 2 2">
				<gp-simple-input [required]="true" label="Estado" errorMessage="Estado é obrigatório">
					<input type="text" class="form-control" [disabled]="addressFound" name="state" required [(ngModel)]="address.state" />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 4 4">
				<gp-simple-input [required]="true" label="Bairro" errorMessage="Bairro é obrigatório">
					<input type="text" class="form-control" [disabled]="addressFound" name="neighborhood" [(ngModel)]="address.neighborhood" />
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-simple-input [required]="true" label="Ponto de Referência" errorMessage="Ponto de Referência é obrigatório">
					<input type="text" class="form-control" name="reference" required [(ngModel)]="address.reference" />
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card title="Dados do Recebedor">
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Nome do Recebedor">
					<input type="text" class="form-control" name="receiverName" [(ngModel)]="address.receiverName" />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="E-mail do Recebedor">
					<input type="text" class="form-control" name="receiverEmail" [(ngModel)]="address.receiverEmail" />
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
		<gp-form-row>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="CPF do Recebedor">
					<input type="text" class="form-control" name="receiverCpf" [(ngModel)]="address.receiverCpf" />
				</gp-simple-input>
			</gp-form-col>
			<gp-form-col cols="12 6 6">
				<gp-simple-input label="Celular do Recebedor">
					<input type="text" class="form-control" name="receiverMobilePhone" [(ngModel)]="address.receiverMobilePhone" />
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>
	</gp-card>

	<gp-card>
		<div class="row">
			<div class="col-md-12">
				<gp-spinner-button type="submit" text="Salvar Endereço"
					size="lg" pull="right" [pink]="true" icon="send"
					loadingText="Processando"  [loading]="loading" [disabled]="!addressForm.valid">
				</gp-spinner-button>

				<gp-spinner-button text="Novo" bootstrapClass="default" icon="plus" size="lg" pull="right" marginRight="10px"
					[disabled]="loading" (click)="reset()" [showSpinner]="false">
				</gp-spinner-button>
			</div>
		</div>
		<div class="row">
			<div class="col-md-12">
				<gp-alert #alert></gp-alert>
			</div>
		</div>
	</gp-card>
</form>
