import { CallCenterParticipantAddressListComponent } from './callcenter-participant-address-list/callcenter-participant-address-list.component';
import { TabsetComponent } from 'ng2-bootstrap/tabs';
import { CallCenterParticipantAddressEditComponent } from './callcenter-participant-address-edit/callcenter-participant-address-edit.component';
import { Component, OnInit, ViewChild } from '@angular/core';

@Component({
    selector: 'callcenter-address',
    templateUrl: 'callcenter-participant-address.component.html'
})

export class CallCenterParticipantAddressComponent implements OnInit {

    private addressId: string;
    @ViewChild('tabs') tabs: TabsetComponent;
    @ViewChild('editComponent') editComponent: CallCenterParticipantAddressEditComponent;
    @ViewChild('listComponent') listComponent: CallCenterParticipantAddressListComponent;

    constructor() { }

    ngOnInit() { }

    private editAddress($event: any) {
        if ($event) {
            this.addressId = $event;
            this.tabs.tabs[1].active = true;
        }
    }

    private clearEditForm() {
        this.listComponent.loadAddresses();
        this.editComponent.reset();
        this.addressId = '';
    }
}
