import { Router } from '@angular/router';
import { NgForm } from '@angular/forms';
import { Component, OnInit, ViewChild, Input } from '@angular/core';

import { CorreiosService } from './../../../../../core/services/correios.service';
import { DataStore } from './../../../../../core/store/data.store';
import { ParticipantService } from './../../participant.service';
import { GpAlertComponent } from './../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CALLCENTER_STORE_KEY, CallcenterSession } from './../../callcenter-session';

@Component({
    selector: 'callcenter-participant-address-edit',
    templateUrl: 'callcenter-participant-address-edit.component.html'
})
export class CallCenterParticipantAddressEditComponent implements OnInit {

    private _addressId: string;
    @Input() set addressId(v: string) {
        if (v) {
            this._addressId = v;
            this.findAddress();
        }
    }

    private address: any = {};
    private loading: boolean = false;
    private loadingCep: boolean = false;
    private addressFound: boolean = true;
    private callcenterSession: CallcenterSession;
    @ViewChild('addressForm') addressForm: NgForm;
    @ViewChild('alert') alert: GpAlertComponent;

    constructor(private _correiosService: CorreiosService, private _participantService: ParticipantService, private _ds: DataStore, private _route: Router) { }

    ngOnInit() {
        this.callcenterSession = this._ds.get(CALLCENTER_STORE_KEY);
        if (!this.callcenterSession) {
            this._route.navigate(['/callcenter/pesquisa']);
        }
    }

    public reset() {
        this.address = {};
        this._addressId = '';
        this.alert.clear();
        this.addressForm.reset();

        Object.keys(this.addressForm.controls).forEach(key => {
            this.addressForm.controls[key].reset();
        });
    }

    private findAddress() {
        if (this._addressId) {
            this.loading = true;
            this._participantService.findAddress(this.callcenterSession.userId, this.callcenterSession.campaignId, this._addressId)
                .subscribe(address => {
                    this.address = address;
                    this.loading = false;
                }, err => {
                    const msg = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao buscar o endereço';
                    this.alert.showError(msg);
                    this.loading = false;
                });
        }
    }

    private searchAddress($event: any) {
        if (this.address.cep) {
            this.alert.clear();
            this.loadingCep = true;
            this._correiosService.searchAddressByCep(this.address.cep)
                .subscribe(address => {
                    if (address.address) {
                        if (!this.address)
                            this.address = {};

                        this.address.street = address.address;
                        this.address.city = address.city;
                        this.address.state = address.uf;
                        this.address.neighborhood = address.neighborhood;
                        this.addressFound = true;
                    } else {
                        this.addressFound = false;
                    }
                    this.loadingCep = false;

                }, err => {
                    const msg = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao buscar o CEP';
                    this.alert.showError(msg);
                    this.loadingCep = false;
                    this.addressFound = false;
                });
        }
    }

    private addressSubmit() {
        this.loading = true;
        this.alert.clear();
        this._participantService.saveAddress(this.callcenterSession.userId, this.callcenterSession.campaignId, this.address)
            .subscribe(address => {
                if (address) {
                    this.address = address;
                    this.alert.showSuccess('Endereço salvo com sucesso');
                } else {
                    this.alert.showError('Ocorreu um erro ao gravar o endereço');
                }

                this.loading = false;
            }, err => {
                const msg = err ? (err.message ? err.message : err.toString()) : 'Ocorreu um erro ao salvar o endereço';
                this.alert.showError(msg);
                this.loading = false;
            });
    }

    private get renderCepStatus() {
        if (this.loadingCep)
            return 'Pesquisando CEP...';
        if (!this.addressFound)
            return 'CEP não encontrado. Termine de preencher o endereço!';

        return '';
    }
}
