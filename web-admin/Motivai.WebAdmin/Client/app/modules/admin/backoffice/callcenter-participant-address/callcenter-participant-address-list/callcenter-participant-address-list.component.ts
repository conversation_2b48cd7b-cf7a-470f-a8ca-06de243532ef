import { EventEmitter } from '@angular/core';
import { Router } from '@angular/router';
import { Component, OnInit, Output } from '@angular/core';

import { CALLCENTER_STORE_KEY, CallcenterSession } from './../../callcenter-session';
import { DataStore } from './../../../../../core/store/data.store';
import { ParticipantService } from './../../participant.service';

@Component({
  selector: 'callcenter-participant-address-list',
  templateUrl: 'callcenter-participant-address-list.component.html'
})

export class CallCenterParticipantAddressListComponent implements OnInit {

  private callcenterSession: CallcenterSession;
  private loading: boolean = false;
  private addresses: any[] = [];
  @Output('edit') edit: EventEmitter<any> = new EventEmitter();

  constructor(private _participantService: ParticipantService, private _ds: DataStore, private _route: Router) { }

  ngOnInit() {
    this.callcenterSession = this._ds.get(CALLCENTER_STORE_KEY);
    if (!this.callcenterSession) {
      this._route.navigate(['/callcenter/pesquisa']);
    }

    this.loadAddresses();
  }

  loadAddresses() {
    this.loading = true;
    this._participantService.getAddresses(this.callcenterSession.userId, this.callcenterSession.campaignId)
      .subscribe(addresses => {
        this.addresses = addresses;
        if (addresses) {
          addresses.forEach(a => {
            a.main = a.mainAddress ? 'Sim' : 'Não';
          });
        }
        this.loading = false;
      }, err => {
        console.log(err);
        this.loading = false;
      });
  }

  private removeAddress($event: any) {
    if ($event.id) {
      if (confirm(`Deseja remover este endereço: ${$event.addressName}`)) {
        this.loading = true;
        this._participantService.removeAddress(this.callcenterSession.userId, this.callcenterSession.campaignId, $event.id)
          .subscribe(removed => {
            this.loadAddresses();
          }, err => {
            console.log(err);
            this.loading = false;
          });
      }
    }
  }

  private editAddress($event: any) {
    if ($event.id) {
      this.edit.emit($event.id);
    }
  }
}
