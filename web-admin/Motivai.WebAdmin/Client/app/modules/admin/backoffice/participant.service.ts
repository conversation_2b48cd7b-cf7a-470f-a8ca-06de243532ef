import { Injectable } from '@angular/core';
import { Observable } from 'rxjs/Observable';

import { ApiService } from '../../../core/api/api.service';

@Injectable()
export class ParticipantService {
  constructor(private _api: ApiService) { }

  findParticipant(filterType: string, filterValue: string, recaptchaToken: string): Observable<any> {
    return this._api.get('/api/callcenter/participants', { [filterType]: filterValue, recaptchaToken });
  }

  getParticipantInfo(userId: string, campaignId: string): Observable<any> {
    return this._api.get(`/api/callcenter/participants/${userId}/campaigns/${campaignId}/info`);
  }

  getParticipantBalance(userId: string, campaignId: string): Observable<number> {
    return this._api.get(`/api/callcenter/participants/${userId}/campaigns/${campaignId}/balance`);
  }

  getAddresses(userId: string, campaignId: string): Observable<any[]> {
    return this._api.get(`/api/callcenter/participants/${userId}/campaigns/${campaignId}/addresses`);
  }

  removeAddress(userId: string, campaignId: string, addressId: string): Observable<boolean> {
    return this._api.delete(`/api/callcenter/participants/${userId}/campaigns/${campaignId}/addresses/${addressId}`);
  }

  findAddress(userId: string, campaignId: string, addressId: string): Observable<any> {
    return this._api.get(`/api/callcenter/participants/${userId}/campaigns/${campaignId}/addresses/${addressId}`);
  }

  saveAddress(userId: string, campaignId: string, address: any): Observable<any> {
    if (address.id)
      return this._api.put(`/api/callcenter/participants/${userId}/campaigns/${campaignId}/addresses/${address.id}`, address);
    return this._api.post(`/api/callcenter/participants/${userId}/campaigns/${campaignId}/addresses`, address);
  }

  // Reset password
  sendVerificationCode(userId: string, campaignId: string): Observable<string> {
    return this._api.post(`/api/callcenter/participants/${userId}/campaigns/${campaignId}/verificationcode/sms`, null);
  }

  resetPassword(userId: string, campaignId: string, verificationCode: any): Observable<boolean> {
    return this._api.put(`/api/callcenter/participants/${userId}/campaigns/${campaignId}/password/sms`, verificationCode);
  }

  upadateParticipantContactInformation(userId: string, campaignId: string, contact: any): Observable<boolean> {
    return this._api.put(`/api/callcenter/participants/${userId}/campaigns/${campaignId}/contact`, contact);
  }

  //REMOVER APOS ECORODOVIAS VALIDAR
  generateCampaignSiteAccessToken(userId: string, campaignId: string, timezone: string, recaptchaToken: string): Observable<string> {
    return this._api.post(`/api/callcenter/participants/${userId}/campaigns/${campaignId}/campaignsite/ssotoken`, { timezone, recaptchaToken });
  }

  //REMOVER APOS ECORODOVIAS VALIDAR
  generateCatalogAccessToken(userId: string, campaignId: string, timezone: string, recaptchaToken: string): Observable<any> {
    return this._api.post(`/api/callcenter/participants/${userId}/campaigns/${campaignId}/catalog/ssotoken`, { timezone, recaptchaToken });
  }

  unblockParticipant(campaignId: string, userId: string, reason: string): Observable<boolean> {
    return this._api.put(`/api/campaigns/${campaignId}/participant/manager/users/${userId}/unblock`, { reason });
  }
}
