import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, LOCALE_ID, NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { SelectModule } from 'ng2-select';

import { SharedModule } from '../../../shared/shared.module';
import { BankTransfersService } from './bank-transfers.service';
import { BankTransferOrderDetailsComponent } from './bank-transfers-order-details/bank-transfer-order-details.component';
import { BankTransfersOrdersListComponent } from './bank-transfers-orders-list/bank-transfers-orders-list.component';
import { BankTransfersOrdersViewComponent } from './bank-transfers-orders-view/bank-transfers-orders-view.component';
import { BankTransferOrderRefundModalComponent } from './bank-transfers-order-details/bank-transfer-order-refund-modal/bank-transfer-order-refund-modal.component';

const COMPONENTS = [
    BankTransfersOrdersViewComponent,
    BankTransfersOrdersListComponent,
    BankTransferOrderDetailsComponent,
    BankTransferOrderRefundModalComponent
];

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        SelectModule,
        SharedModule.forRoot(),
        RouterModule.forChild([
            { path: '', component: BankTransfersOrdersViewComponent, pathMatch: 'full' }
        ])
    ],
    declarations: COMPONENTS,
    exports: [
        RouterModule
    ],
    providers: [
        [{ provide: LOCALE_ID, useValue: 'pt-br' }],
        BankTransfersService
    ],
    schemas: [CUSTOM_ELEMENTS_SCHEMA]
 })

export class BankTransfersModule {}
