<gp-card [first]="true">
    <gp-row>
        <gp-form-col cols="12" *ngIf="!isRootBu">
            <label>Selecione a Campanha para Extrair</label>
        </gp-form-col>
        <gp-form-col cols="12">
            <gp-spinner-button [loading]="loading" [actionDownload]="true" text="Extrair para XLS" [disabled]="disableActions"
                (click)="exportCashbackOrders()">
            </gp-spinner-button>
        </gp-form-col>
    </gp-row>
</gp-card>
<gp-card [first]="false" title="Pesquisar">
    <gp-form-row>
        <gp-form-col cols="12">
            <label *ngIf="!isRootBu">Selecione a Campanha para pesquisar</label>
        </gp-form-col>
    </gp-form-row>
        <gp-form-row>
            <gp-form-col cols="12 3 3">
				<gp-simple-input label="Campanha" [required]="!isRootBu">
                    <gp-select name="campaign" [allowClear]="false" [items]="campaigns" placeholder="Por campanha" [(ngModel)]="params.campaignId"  [required]="!isRootBu"></gp-select>
                </gp-simple-input>
            </gp-form-col>
            <gp-form-col cols="12 3 3">
                <gp-simple-input label="CPF/CNPJ">
                    <gp-input-mask name="document" mask="00000000000000" [(ngModel)]="params.userDocument">
                    </gp-input-mask>
                </gp-simple-input>
            </gp-form-col>
            <gp-form-col cols="12 3 3">
                <gp-simple-input label="Nº do Pedido">
                    <input type="text" name="orderNumber" class="form-control" [(ngModel)]="params.orderNumber" />
                </gp-simple-input>
            </gp-form-col>
            <gp-form-col cols="12 3 3">
                <gp-simple-input label="Status dos Pedidos">
                    <select name="status" class="form-control" [(ngModel)]="params.status">
                        <option value="">Selecione</option>
                        <option value="RECEIVED">Recebido</option>
                        <option value="AWAITING_APPROVAL">Aguardando Aprovação</option>
                        <option value="TRANSFERRED">Transferência Efetuada</option>
                        <option value="REFUNDED">Estornado</option>
                        <option value="CANCELED">Cancelado</option>
                        <option value="ERROR">Erro</option>
                    </select>
                </gp-simple-input>
            </gp-form-col>
        </gp-form-row>

        <gp-form-row>
            <gp-form-col cols="12 12 12">
                <gp-spinner-button pull="right" [search]="true" text="Pesquisar" [loading]="loading" [disabled]="disableActions"
                    (click)="loadCashbackOrders()" marginTop="26px">
                </gp-spinner-button>
            </gp-form-col>
        </gp-form-row>
</gp-card>

<gp-card>
    <gp-form-row>
        <gp-form-col cols="12 12 12">

            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Nº Pedido</th>
                        <th>Data/Hora </th>
                        <th>CPF/CNPJ</th>
                        <th>Nome</th>
                        <th>Valor da Transferencia</th>
                        <th>Status</th>
                        <th>Ocorreu um Erro</th>
                        <th class="text-center">Ações</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let c of cashback">
                        <td>{{ c.orderNumber }}</td>
                        <td>{{ c.createDate | datetimezone:c.timezone }}</td>
                        <td>{{ c.document | document }}</td>
                        <td>{{ c.name }}</td>
                        <td>{{ c.transferAmount | currency:'BRL':true:'1.2-2' }}</td>
                        <td>{{ c.statusDescription }}</td>
                        <td>
                            <span class="label" [class.label-danger]="c.occurredError" [class.label-success]="!c.occurredError">
                                {{ c.occurredError | booleanToYesNo }}
                            </span>
                        </td>
                        <td class="text-center">
                            <button type="button" title="Editar" class="btn btn-default btn-sm" tooltip="Editar"
                                    (click)="detailedCashbackOrder(c)">
                                <i title="Editar" class="fa fa-pencil-square-o grid-edit"></i>
                            </button>
                        </td>
                    </tr>
                    <tr *ngIf="!cashback || !cashback.length">
                        <td colspan="!cashback">Nenhum registro encontrado.</td>
                    </tr>
                </tbody>
                <br/>
            </table>
        </gp-form-col>
    </gp-form-row>
    <gp-form-row>
        <gp-form-col cols="12">
            <div class="pull-right">
                <button (click)="onPrevious()" class="btn btn-default" [disabled]="currentPage === 1" style="margin-right: 20px;">< Anterior</button>
                <span>{{ renderPaginationText }}</span>
                <button (click)="onNext()" class="btn btn-default" [disabled]="cashback && cashback.length < pageSize" style="margin-left: 20px;">Próximo ></button>
            </div>
        </gp-form-col>
    </gp-form-row>
</gp-card>
<spinner [show]="loading || loadingCampaigns" [overlay]="true"></spinner>
<gp-alert #alert [overlay]="true"></gp-alert>
