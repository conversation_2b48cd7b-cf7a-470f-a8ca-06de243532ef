import { BankTransfersService } from '../bank-transfers.service';
import { Component, OnInit, ViewChild, Output, EventEmitter } from '@angular/core';
import { GpAlertComponent } from '../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CampaignService } from '../../campaigns/campaign.service';
import { FormatHelper } from '../../../../shared/formatters/format-helper';
import { CampaignStore } from '../../campaigns/campaign.store';
import { Principal } from '../../../../core/auth/profile';
import { AuthStore } from '../../../../core/auth/auth.store';

@Component({
    selector: 'bank-transfers-orders-list',
    templateUrl: './bank-transfers-orders-list.component.html'
})
export class BankTransfersOrdersListComponent implements OnInit {
    @ViewChild('alert') alert: GpAlertComponent;
    @Output('select') onSelect: EventEmitter<any> = new EventEmitter<any>();
    cashback: any[] = [];
    params: any = {
        campaignId: '',
        orderNumber: '',
        userDocument: '',
        status,
        skip: 0,
        limit: 40
    };
    campaigns: any[] = [];
    loading: boolean = false;
    loadingCampaigns: boolean = false;

    currentPage: number = 1;
    pageSize: number = 40;

    constructor(private _service: BankTransfersService, private _campaignService: CampaignService, private _authStore: AuthStore) { }

    get renderPaginationText() {
        return `Página ${this.currentPage}`;
    }

    get isRootBu() {
        return this._authStore.loggedUser.isRootBu;
    }

    get disableActions() {
        return !this.isRootBu && !this.params.campaignId;
    }


    ngOnInit(): void {
        this.loadCampaigns();
        if (this.isRootBu) {
            this.loadCashbackOrders();
        }
    }

    loadCampaigns() {
        this.loadingCampaigns = true;
        this._campaignService.getAll(null, null, null, 0, 500).subscribe(
            campaigns => {
                this.campaigns = campaigns.map(campaign => ({ id: campaign.id, text: campaign.name })) || [];
                this.loadingCampaigns = false;
            },
            err => {
                this.alert.showError(err);
                this.loadingCampaigns = false;
            });
    }

    private validateFilters(): boolean {
        if (this.isRootBu) {
            return true;
        }
        if (!this.params.campaignId) {
            this.alert.showWarning('Selecione a campanha.');
            return false;
        }
        return true;
    }

    exportCashbackOrders() {
        if (!this.validateFilters()) {
            return;
        }
        this.loading = true;
        this._service.exportOrders(this.params.campaignId, this.params.userDocument, this.params.orderNumber, this.params.status).subscribe(
            response => {
                window.open(response, '_blank');
                this.loading = false;
            },
            err => {
                this.loading = false;
                this.alert.showError(err);
            }
        );
    }

    loadCashbackOrders() {
        if (!this.validateFilters()) {
            return;
        }
        this.loading = true;
        this._service.getOrders(this.params.campaignId, this.params.userDocument, this.params.orderNumber, this.params.status, this.params.skip, this.params.limit)
            .subscribe(
                cashback => {
                    this.cashback = cashback;
                    console.log(cashback);
                    this.loading = false;
                },
                err => {
                    this.loading = false;
                    this.alert.showError(err);
                }
            );
    }


    detailedCashbackOrder(order: any) {
        if (!order || !order.id || !order.id.length) return;
        this.onSelect.emit(order.id);
    }

    onPrevious() {
        if (this.currentPage > 1) this.currentPage -= 1;

        this.params.skip = (this.currentPage - 1) * this.pageSize;
        this.params.limit = this.pageSize;
        this.loadCashbackOrders();
    }

    onNext() {
        this.currentPage += 1;
        this.params.skip = (this.currentPage - 1) * this.pageSize;
        this.params.limit = this.pageSize;

        this.loadCashbackOrders();

    }
}
