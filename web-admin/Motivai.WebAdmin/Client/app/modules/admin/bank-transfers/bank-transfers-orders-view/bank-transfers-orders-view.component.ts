import { Component, ViewChild } from '@angular/core';
import { TabsetComponent } from 'ng2-bootstrap';
import { BankTransfersOrdersListComponent } from '../bank-transfers-orders-list/bank-transfers-orders-list.component';
import { BankTransferOrderDetailsComponent } from '../bank-transfers-order-details/bank-transfer-order-details.component';

@Component({
    selector: 'bank-transfers-orders-view',
    templateUrl: './bank-transfers-orders-view.component.html'
})
export class BankTransfersOrdersViewComponent {
    @ViewChild('tabs') tabs: TabsetComponent;
    @ViewChild('listComponent') listComponent: BankTransfersOrdersListComponent;
    @ViewChild('detailComponent') detailComponent: BankTransferOrderDetailsComponent;

    orderId: any = '';
    constructor() { }

    get disableOrderDetail() {
        return !this.orderId || !this.orderId.length;
    }

    onListingClick() {
        this.detailComponent.clear();
        this.tabs.tabs[1].disabled = true;
        if (this.listComponent)
          this.listComponent.loadCashbackOrders();
    }

    onSelect(orderId: string) {
        if (!orderId || !orderId.length) {
            this.orderId = null;
            return;
        }
        this.orderId = orderId;
        this.tabs.tabs[1].disabled = false;
        this.tabs.tabs[1].active = true;
        this.detailComponent.orderId = this.orderId;
    }
}
