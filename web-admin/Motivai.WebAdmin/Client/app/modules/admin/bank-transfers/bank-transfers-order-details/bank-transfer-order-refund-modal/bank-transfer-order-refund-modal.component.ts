import { Component, ViewChild, Input, Output, EventEmitter } from '@angular/core';

import { AuthStore } from '../../../../../core/auth/auth.store';
import { GpModalComponent } from '../../../../../shared/components/gp-modal/gp-modal.component';
import { GpAlertComponent } from '../../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { BankTransfersService } from '../../bank-transfers.service';

@Component({
  selector: 'bank-transfer-order-refund-modal',
  templateUrl: 'bank-transfer-order-refund-modal.component.html',
})
export class BankTransferOrderRefundModalComponent {
  @Output('close') closeEmitter: EventEmitter<any> = new EventEmitter<any>();

  @ViewChild('modal') modal: GpModalComponent;
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;

  loading: boolean = false;
  sending: boolean = false;

  order: any;
  refunded: boolean = false;
  refundReason: string;

  constructor(private _authStore: AuthStore, private _bankTransferService: BankTransfersService) { }

  public showModal(order: any) {
    if (!this._authStore.role.PERMISSION_CASHBACK_ORDERS_REFUND) {
      return;
    }
    this.clear();
    this.order = order;
    this.modal.show();
  }

  private clear() {
    this.order = null;
    this.refunded = false;
    this.refundReason = null;
  }

  public onClose() {
    this.clear();
    this.closeEmitter.emit({})
  }

  public closeModal() {
    this.modal.hide();
  }

  refundOrder() {
    if (!this.order) {
      return;
    }
    if (!this.refundReason) {
      this.gpAlert.showWarning('Informe o motivo do estorno.');
      return;
    }

    this.sending = true;
    this._bankTransferService.refundOrder(this.order.campaignId, this.order.orderNumber, this.refundReason)
      .subscribe(
        result => {
          this.sending = false;
          if (result) {
            this.refunded = true;
            this.gpAlert.showSuccess('Pedido estornado com sucesso.');
          } else {
            this.gpAlert.showWarning('Não foi possível estornar o pedido, por favor, tente novamente.');
          }
        },
        err => {
          this.sending = false;
          this.gpAlert.handleAndShowError(err);
        }
      );
  }

}
