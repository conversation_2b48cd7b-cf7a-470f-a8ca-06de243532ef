<gp-modal title="Estorno de Pedido" width="800px" (onClose)="onClose()" #modal>
	<gp-alert #gpAlert [overlay]="true"></gp-alert>

	<form>
		<gp-form-row>
			<gp-form-col cols="12 12">
				<p>Antes de estornar o pedido, confirme com a equipe do financeiro se o pedido já não foi transferido.</p>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 12">
				<gp-simple-input label="Motivo do estorno" [required]="true">
					<textarea required class="form-control" rows="5" name="refundReason" [(ngModel)]="refundReason">
					</textarea>
				</gp-simple-input>
			</gp-form-col>
		</gp-form-row>

		<gp-form-row>
			<gp-form-col cols="12 12 12">
				<gp-spinner-button type="button" [actionPrimary]="true" pull="right" text="Estornar" [loading]="sending"
					[disabled]="!refundReason" (click)="refundOrder()" *ngIf="!refunded">
				</gp-spinner-button>

				<gp-spinner-button type="button" [actionBack]="true" pull="right" marginRight="1em"
					text="Voltar" (click)="closeModal()">
				</gp-spinner-button>
			</gp-form-col>
		</gp-form-row>
	</form>
</gp-modal>
