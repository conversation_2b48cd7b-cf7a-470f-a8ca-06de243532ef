import { Component, ViewChild, Input } from '@angular/core';
import { AuthStore } from '../../../../core/auth/auth.store';

import { GpAlertComponent } from '../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { GpFileUploadComponent } from '../../../../shared/components/gp-fileupload/gp-fileupload.component';
import { BankTransfersService } from '../bank-transfers.service';
import { BankTransferOrderRefundModalComponent } from './bank-transfer-order-refund-modal/bank-transfer-order-refund-modal.component';

@Component({
  selector: 'bank-transfer-order-details',
  templateUrl: 'bank-transfer-order-details.component.html',
  styles: [`
        .fixed-height {
            height: 140px;
        }
    `]
})
export class BankTransferOrderDetailsComponent {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;
  @ViewChild('transferReceiptUploader') transferReceiptUploader: GpFileUploadComponent;
  @ViewChild('bankTransferRefundModal') bankTransferRefundModal: BankTransferOrderRefundModalComponent;

  now: Date;

  _orderId: string;
  order: any = {};
  loading: boolean = false;
  sending: boolean = false;

  transfer: any = {};

  constructor(private _authStore: AuthStore, private _bankTransferService: BankTransfersService) {
    this.now = new Date();
    this.now.setDate(this.now.getDate() + 1);
  }

  @Input()
  set orderId(id: string) {
    if (id && id.length) {
      this._orderId = id;
      this.loadOrder();
    } else {
      this._orderId = null;
    }
  }

  get isReceived() {
    return this.order && this.order.status == 'RECEIVED';
  }

  get isCanceled() {
    return this.order && this.order.status == 'CANCELED';
  }

  get canBeApproved() {
    return this.order && this.order.status == 'AWAITING_APPROVAL';
  }

  get canBeRefund() {
    return this._authStore.role.PERMISSION_CASHBACK_ORDERS_REFUND
      && (this.isReceived || this.isCanceled || this.canBeApproved);
  }

  get canBeRefundedOrApproved() {
    return this.canBeApproved || this.canBeRefund;
  }

  get canRegisterTransfer() {
    return this.order && (
      !!this.order.status
      && this.order.status != 'AWAITING_APPROVAL'
      && this.order.status != 'REFUNDED'
      && this.order.status != 'CANCELED'
    );
  }

  get hasTransfersHistoric() {
    return this.order && this.order.transfersHistoric && this.order.transfersHistoric.length > 1;
  }

  get hasApprovingInfo() {
    return this.order && this.order.approverUser && !!this.order.approverUser.name
      && !!this.order.approvalDate;
  }

  get isReceivingByPix() {
    return this.order.bankTransferMethod == 'PIX';
  }

  get isReceivingByBankTransfer() {
    return this.order.bankTransferMethod != 'PIX';
  }

  private mapAccountTypeDescription(type: string) {
    if (type == 'CheckingAccount') {
      return 'Conta Corrente';
    } else if (type == 'SavingsAccount') {
      return 'Conta Poupança';
    }
    return 'Não Informada';
  }

  loadOrder() {
    if (!this._orderId || !this._orderId.length) {
      return;
    }

    this.loading = true;
    this._bankTransferService.getOrderById(this._orderId)
      .subscribe(
        order => {
          this.loading = false;
          if (order) {
            order.bankTransferMethodDescription = order.bankTransferMethod == 'PIX' ? 'PIX' : 'TED';
            order.bankAccount.typeDescription = this.mapAccountTypeDescription(order.bankAccount.type);
            this.order = order;
          } else {
            this.gpAlert.showWarning('Pedido não encontrado.');
            this.clear();
          }
        },
        err => {
          this.loading = false;
          this.gpAlert.handleAndShowError(err);
        }
      );
  }

  registerTransfer() {
    if (!this.transferReceiptUploader.hasSelectedFile()) {
      this.gpAlert.showWarning('Selecione o comprovante de transferência.');
      return;
    }
    this.transferReceiptUploader.path = `api/cashback/orders/${this._orderId}/receipt`;
    this.transferReceiptUploader.uploadFile();
    this.sending = true;
  }

  onTransferReceiptComplete(event) {
    if (event.success) {
      this.transferReceiptUploader.removeAll();
      this.transferReceiptUploader.createUploader();

      const apiReturn = JSON.parse(event.response);
      if (apiReturn) {
        this.transfer.receiptUrl = apiReturn['return'];
      }
      this.maskAsTransferred();
    } else {
      this.gpAlert.showError(event.errorMessage);
      this.sending = false;
    }
  }

  private maskAsTransferred() {
    // registra a tentativa atual
    this.transfer.bankTransferMethod = this.order.bankTransferMethod || 'BANK_TRANSFER';
    this._bankTransferService.maskAsTransferred(this._orderId, this.transfer)
      .subscribe(
        result => {
          this.sending = false;
          if (result) {
            this.gpAlert.showSuccess('Pedido atualizado com sucesso.');
            this.transfer = {};
            this.loadOrder();
          } else {
            this.gpAlert.showWarning('Não foi possível atualizar o pedido, por favor, tente novamente.');
          }
        },
        err => {
          this.sending = false;
          this.gpAlert.handleAndShowError(err);
        }
      );
  }

  clear() {
    this._orderId = null;
    this.order = {};
    this.transfer = {};
  }

  approve() {
    this.gpAlert.confirm('Confirma a aprovação do pedido?')
      .then(result => {
        if (result && result.value) {
          this.sendToApproval();
        }
      });
  }

  private sendToApproval() {
    this.sending = true;
    this._bankTransferService.markAsApproved(this._orderId)
      .subscribe(
        result => {
          this.sending = false;
          if (result) {
            this.gpAlert.showSuccess('Pedido aprovado com sucesso.');
            this.loadOrder();
          } else {
            this.gpAlert.showWarning('Não foi possível aprovar o pedido, por favor, tente novamente.');
          }
        },
        err => {
          this.sending = false;
          this.gpAlert.handleAndShowError(err);
        }
      );
  }

  showRefundModal() {
    if (!this.canBeRefund) {
      return;
    }
    this.bankTransferRefundModal.showModal(this.order);
  }

  onModalClose() {
    this.loadOrder();
  }

}
