<spinner [show]="loading" [overlay]="true"></spinner>
<gp-alert #gpAlert [overlay]="true"></gp-alert>

<gp-card [first]="true">
  <h3>Pedido: {{ order.orderNumber }}</h3>
  <h4>Campanha: {{ order.campaignName }}</h4>
  <hr />
  <gp-form-row>
    <div class="col-lg-4 col-xs-12 br pv">
      <div class="clearfix">
        <p class="pull-left"><strong>Data do Pedido:</strong></p>
        <p class="pull-right mr">{{ order.createDate | datetimezone: order.timezone }}</p>
      </div>
      <div class="clearfix">
        <p class="pull-left"><strong>Pontos a Transferir:</strong></p>
        <p class="pull-right mr">
          <strong>{{ order.pointsAmount || 0 | coin }}</strong>
        </p>
      </div>
      <div class="clearfix">
        <p class="pull-left"><strong>Total das Taxas:</strong></p>
        <p class="pull-right mr">
          <strong>R$ {{ order.totalFeesAmount || 0 | coin }}</strong>
        </p>
      </div>
      <div class="clearfix">
        <p class="pull-left"><strong>Total para Transferir:</strong></p>
        <p class="pull-right mr">R$ {{ order.transferAmount || 0 | coin }}</p>
      </div>
      <div class="clearfix">
        <p class="pull-left"><strong>Total do Pedido em Pontos:</strong></p>
        <p class="pull-right mr">
          <strong>{{ order.totalCost?.points || 0 | coin }}</strong>
        </p>
      </div>
      <div class="clearfix">
        <p class="pull-left"><strong>Total do Pedido em Reais:</strong></p>
        <p class="pull-right mr">
          <strong>R$ {{ order.totalCost?.currency || 0 | coin }}</strong>
        </p>
      </div>
    </div>
    <div class="col-lg-4 col-xs-6 br pv">
      <div class="row">
        <div class="col-md-2 text-center visible-md visible-lg">
          <em class="fa fa-user fa-3x text-muted"></em>
        </div>
        <div class="col-md-10">
          <h4>Dados do Comprador</h4>
          <strong>Name:</strong> {{ order.name }}<br />
          <strong>CPF/CNPJ:</strong> {{ order.document | document }}<br />
          <span *ngIf="order.telephone">
            <strong>Telefone:</strong> {{ (order.telephone | telephone) || 'Não cadastrado' }}<br />
          </span>
          <span *ngIf="order.cellphone">
            <strong>Celular:</strong> {{ (order.cellphone | telephone) || 'Não cadastrado' }}<br />
          </span>
        </div>
      </div>
      <div class="row top-p2" *ngIf="hasApprovingInfo">
        <div class="col-md-2 text-center visible-md visible-lg">
          <em class="fa fa-user fa-3x text-muted"></em>
        </div>
        <div class="col-md-10">
          <h4>Dados do Aprovador</h4>
          <strong>Name:</strong> {{ order.approverUser.name }}<br />
          <strong>Data de Aprovação:</strong> {{ order.approvalDate | datetimezone: order.timezone }}<br />
        </div>
      </div>
    </div>
    <div class="col-lg-4 col-xs-6 br pv">
      <div class="row">
        <div class="col-md-2 text-center visible-md visible-lg">
          <em class="fa fa-bank fa-3x text-muted"></em>
        </div>
        <div class="col-md-10" *ngIf="isReceivingByBankTransfer">
          <h4>Dados da Conta Bancária</h4>
          <strong>Banco:</strong> {{ order.bankAccount?.bankName || order.bankAccount?.bankCode }}<br />
          <strong>Tipo:</strong> {{ order.bankAccount?.typeDescription }}<br />
          <strong>Agência:</strong> {{ order.bankAccount?.agencyNumber }}<br />
          <strong>Conta:</strong> {{ order.bankAccount?.accountNumber }}-{{ order.bankAccount?.accountDigit }}<br />
          <strong>Forma de Transferência:</strong> {{ order.bankTransferMethodDescription }}
        </div>
        <div class="col-md-10" *ngIf="isReceivingByPix">
          <h4>Dados da Conta Bancária</h4>
          <strong>Chave PIX:</strong> {{ order.bankAccountPixKey }}<br />
          <strong>Forma de Transferência:</strong> {{ order.bankTransferMethodDescription }}
        </div>
      </div>
    </div>
  </gp-form-row>

  <hr class="hidden-print" />
  <div class="row mb-lg">
    <div class="col-xs-12 col-sm-12">
      <p class="pull-left"><strong>Situação:</strong></p>
      <p class="pull-left mr left-p1">
        <span class="text-success" [class.text-warning]="isCanceled" [class.text-danger]="order.occurredError">
          {{ order.statusDescription }}
        </span>
      </p>
    </div>
    <div class="col-xs-12 col-sm-12" *ngIf="isCanceled">
      <p class="pull-left"><strong>Motivo do Cancelamento:</strong></p>
      <p class="pull-left mr left-p1">{{ order.cancellationReason }}</p>
    </div>
    <div class="col-xs-12 col-sm-12" *ngIf="isCanceled">
      <p class="pull-left"><strong>Data do Cancelamento:</strong></p>
      <p class="pull-left mr left-p1">{{ order.cancellationDate | datetimezone: order.timezone }}</p>
    </div>
    <div class="col-xs-12 col-sm-12" *ngIf="order.occurredError">
      <div class="clearfix">
        <p class="pull-left"><strong>Ocorreu erro:</strong></p>
        <p class="pull-left mr left-p1">
          <span [class.text-danger]="order.occurredError">
            <strong>{{ order.occurredError | booleanToYesNo }}</strong>
          </span>
          <span *ngIf="order.errorMessage"> - {{ order.errorMessage }}</span>
        </p>
      </div>
    </div>
  </div>

  <!-- <hr class="hidden-print" *ngIf="order.canCancel" />
	<gp-form-row *ngIf="order.canCancel">
		<gp-form-col cols="12">
			<gp-spinner-button bootstrapClass="warning" text="Cancelar" icon="close" pull="right"
				[loading]="canceling" loadingtext="Cancelando" (click)="cancelCardOrder()">
			</gp-spinner-button>
		</gp-form-col>
	</gp-form-row> -->
</gp-card>

<gp-card *ngIf="canBeRefundedOrApproved">
  <gp-form-row>
	<gp-form-col cols="12" [inputGroup]="false">
      <gp-spinner-button
        [pink]="true"
        text="Aprovar Pedido"
        pull="right"
        icon="check"
        [loading]="sending"
        loadingText="Aprovando"
		marginLeft="1em"
        (click)="approve()"
		*ngIf="canBeApproved">
      </gp-spinner-button>

	  <gp-spinner-button text="Estornar" icon="close" pull="right" [actionPrimary]="true"
        [loading]="sending" loadingtext="Estornando" (click)="showRefundModal()" *ngIf="canBeRefund">
	  </gp-spinner-button>
    </gp-form-col>
  </gp-form-row>
</gp-card>

<gp-form-row>
  <gp-form-col cols="12 6 6" *ngIf="order.transferSystemDate">
    <gp-card title="Última da Transferência">
      <div class="fixed-height">
        <gp-form-row>
          <gp-form-col>
            <label>Data de Transferência:</label>
            <span>{{ order.transferDate | date: 'dd/MM/yyyy' }}</span>
            <br />
            <label>Data no Sistema:</label>
            <span>{{ order.transferSystemDate | datetimezone: order.timezone }}</span>
          </gp-form-col>
        </gp-form-row>
        <gp-form-row>
          <gp-form-col>
            <a class="btn btn-primary" target="_blank" [href]="order.transferReceiptImageUrl">
              <i class="fa fa-download fa-2x"></i>
              <span class="left-p1">Ver Comprovante</span>
            </a>
          </gp-form-col>
        </gp-form-row>
      </div>
    </gp-card>
  </gp-form-col>

  <gp-form-col cols="12 6 6" *ngIf="canRegisterTransfer">
    <gp-card title="Adicionar Transferência">
      <div class="fixed-height">
        <gp-form-row>
          <gp-datepicker
            cols="12 6 6"
            [disableSince]="now"
            label="Data da Transferência"
            name="transferDate"
            [(ngModel)]="transfer.transferDate"
          ></gp-datepicker>

          <gp-form-col cols="12 6 6">
            <div class="top-p27">
              <gp-fileupload
                name="transferReceipt"
                #transferReceiptUploader
                (oncomplete)="onTransferReceiptComplete($event)"
                #gpFile
              ></gp-fileupload>
            </div>
          </gp-form-col>
          <gp-form-col cols="12 3 3">
            <gp-spinner-button
              type="button"
              *ngIf="gpFile.hasSelectedFile()"
              bootstrapClass="primary"
              text="Upload do banner"
              icon="send"
              loadingText="Salvando imagem..."
              [loading]="loadingUploading"
              (click)="sendFixedBanner()"
            ></gp-spinner-button>
          </gp-form-col>
        </gp-form-row>
        <gp-form-row>
          <gp-form-col cols="12 12 12">
            <gp-spinner-button
              type="button"
              [pink]="true"
              icon="send"
              text="Registrar Tranferência"
              [loading]="sending"
              (click)="registerTransfer()"
            >
            </gp-spinner-button>
          </gp-form-col>
        </gp-form-row>
      </div>
    </gp-card>
  </gp-form-col>
</gp-form-row>

<gp-card title="Última da Transferência" *ngIf="hasTransfersHistoric">
  <table class="table table-striped table-hover dataTable" role="grid" style="width:100%">
    <thead>
      <tr role="row">
        <th>Data no Sistema</th>
        <th>Data de Transferência</th>
        <th></th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let reg of order.transfersHistoric">
        <td>{{ reg.transferSystemDate | datetimezone: order.timezone }}</td>
        <td>{{ reg.transferDate | date: 'dd/MM/yyyy' }}</td>
        <td><a target="_blank" [href]="reg.transferReceiptImageUrl">Ver Comprovante</a></td>
      </tr>
    </tbody>
  </table>
</gp-card>

<bank-transfer-order-refund-modal (close)="onModalClose()" #bankTransferRefundModal>
</bank-transfer-order-refund-modal>

