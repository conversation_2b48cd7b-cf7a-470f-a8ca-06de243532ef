import { Injectable } from '@angular/core';
import { Observable } from 'rxjs/Observable';

import { ApiService } from '../../../core/api/api.service';
import { FormatHelper } from '../../../shared/formatters/format-helper';

@Injectable()
export class BankTransfersService {

  constructor(private _api: ApiService) { }

  getOrders(campaignId: string, userDocument: string, orderNumber: string,
    status: string, skip: number, limit: number): Observable<any[]> {
    const params: any = {};
    if (campaignId) params.campaignId = campaignId;
    if (userDocument && userDocument.length)
      params.userDocument = FormatHelper.removeMask(userDocument);
    if (orderNumber) params.orderNumber = orderNumber;
    if (status) params.status = status;
    params.skip = skip || 0;
    params.limit = limit || 20;
    return this._api.get('/api/banktransfer/orders', params);
  }

  getOrderById(orderId: string): Observable<any> {
    return this._api.get(`/api/banktransfer/orders/${orderId}`);
  }

  maskAsTransferred(orderId: string, transfer: any): any {
    return this._api.put(`/api/banktransfer/orders/${orderId}/transferred`, transfer, 20000);
  }

  markAsApproved(orderId: string): any {
    return this._api.put(`/api/banktransfer/orders/${orderId}/approval`, {}, 20000);
  }

  refundOrder(campaignId: string, orderId: string, refundReason: any): Observable<any> {
    return this._api.post(`/api/banktransfer/orders/ordernumber/${orderId}/refund`, {
      campaignId, refundReason: refundReason
    }, 30000);
  }

  exportOrders(campaignId: any, userDocument: any, orderNumber: any, status: any) {
    const params: any = {};
    if (campaignId) params.campaignId = campaignId;
    if (userDocument && userDocument.length)
      params.userDocument = FormatHelper.removeMask(userDocument);
    if (orderNumber) params.orderNumber = orderNumber;
    if (status) params.status = status;

    return this._api.get('/api/banktransfer/orders/csv', params);
  }
}
