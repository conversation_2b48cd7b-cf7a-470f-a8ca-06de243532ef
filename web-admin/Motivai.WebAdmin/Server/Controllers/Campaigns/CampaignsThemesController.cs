using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

using Motivai.SharedKernel.Domain.Model;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Api;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.SharedKernel.Helpers.Storage;
using Motivai.WebAdmin.Server.Helpers;
using Motivai.WebAdmin.Server.Models.Security;
using Motivai.WebAdmin.Server.Repositories;
using Motivai.WebAdmin.Server.Services.Security;

namespace Motivai.WebAdmin.Server.Controllers
{
    [Authorize]
    [Route("/api/campaigns/{encryptedId}")]
    public class CampaignsThemesController : Controller
    {
        private readonly ICryptography _cryptography;
        private readonly IStorageIntegrator _storage;
        private readonly CampaignRepository _campaignRepository;

        public CampaignsThemesController(ICryptography criptografia, IStorageIntegrator storage, CampaignRepository campaignRepository)
        {
            this._campaignRepository = campaignRepository;
            this._cryptography = criptografia;
            this._storage = storage;
        }

        private static Dictionary<string, string> CreateAllowedMediaTypesMap()
        {
            var mediaTypesAllowed = new Dictionary<string, string>();
            mediaTypesAllowed.Add("css", "text/css");
            mediaTypesAllowed.Add("json", "application/json");
            mediaTypesAllowed.Add("xml", "application/xml");
            // Images
            mediaTypesAllowed.Add("png", "image/png");
            mediaTypesAllowed.Add("ico", "image/ico");
            mediaTypesAllowed.Add("svg", "image/svg+xml");
            mediaTypesAllowed.Add("jpg", "image/jpeg");
            mediaTypesAllowed.Add("jpeg", "image/jpeg");
            mediaTypesAllowed.Add("gif", "image/gif");
            mediaTypesAllowed.Add("tiff", "image/tiff");
            // Fonts
            mediaTypesAllowed.Add("otf", "font/opentype");
            mediaTypesAllowed.Add("eot", "font/eot");
            mediaTypesAllowed.Add("sfnt", "font/sfnt");
            mediaTypesAllowed.Add("ttf", "font/ttf");
            mediaTypesAllowed.Add("woff", "font/woff");
            mediaTypesAllowed.Add("woff2", "font/woff2");
            return mediaTypesAllowed;
        }

        [HttpPost]
        [Route("cataloglayout")]
        [Authorization(PermissionCodes.CAMPAIGNS_EDIT)]
        public async Task<ApiReturn<bool>> UploadLayout(string encryptedId, List<IFormFile> layout)
        {
            Guid campaignId = ParserHelper.decryptGuid(_cryptography, encryptedId);
            if (campaignId == Guid.Empty)
            {
                return ApiReturn<bool>.FromError("ID da campanha inválido.");
            }
            if (_storage == null)
            {
                throw MotivaiException.ofValidation("Cliente storage indisponivel.");
            }
            if (layout == null || layout.Count == 0)
            {
                throw MotivaiException.ofValidation("Arquivo com layout é obrigatório.");
            }
            // Configurações do Upload
            string bucketName = ConfigurationHelper.GetValue("GCS_THEME_BUCKET_NAME");
            string folderFormat = ConfigurationHelper.GetValue("StorageFolders:Themes");
            var folderId = Guid.NewGuid();
            string campaignFolderTheme = String.Format(folderFormat, campaignId, folderId);

            var mediaTypesAllowed = CreateAllowedMediaTypesMap();

            try
            {
                var totalUploadedFiles = await _storage.UploadZipfile(bucketName, campaignFolderTheme, true, layout[0].OpenReadStream(), mediaTypesAllowed, false);
                if (totalUploadedFiles <= 0)
                {
                    return ApiReturn<bool>.FromError("Não foi possível efetuar uploado dos arquivos, por favor, verifique a integridade do arquivo zip e se o seu conteúdo contém extensões válidas para o tema.");
                }
            }
            catch (Exception ex)
            {
                await ExceptionLoggerMiddleware.HandleException(ex);
                if (ex is MotivaiException)
                    return ApiReturn<bool>.FromError(ex.Message);
                return ApiReturn<bool>.FromError("Não foi possível efetuar o upload do arquivo, por favor, tente novamente.");
            }
            return await ApiReturn<bool>.Execute(_campaignRepository.SetCampaignWithCustomLayout(campaignId, folderId));
        }

        [HttpPost]
        [Route("sitelayout")]
        [Authorization(PermissionCodes.CAMPAIGNS_EDIT)]
        public async Task<ApiReturn<bool>> UploadSiteLayout(string encryptedId, List<IFormFile> siteLayout)
        {
            Guid campaignId = ParserHelper.decryptGuid(_cryptography, encryptedId);
            if (campaignId == Guid.Empty)
            {
                return ApiReturn<bool>.FromError("ID da campanha inválido.");
            }
            LoggerFactory.GetLogger().Info("Cmp {} - Upload do tema do site.", campaignId);
            if (_storage == null)
            {
                throw MotivaiException.ofValidation("Cliente storage indisponivel.");
            }
            if (siteLayout == null || siteLayout.Count == 0)
            {
                throw MotivaiException.ofValidation("Arquivo com layout é obrigatório.");
            }
            // Configurações do Upload
            string bucketName = ConfigurationHelper.GetValue("GCS_THEME_BUCKET_NAME");
            string folderFormat = ConfigurationHelper.GetValue("StorageFolders:SiteThemes");
            var folderId = Guid.NewGuid();
            string campaignFolderTheme = String.Format(folderFormat, campaignId, folderId);
            Dictionary<string, string> mediaTypesAllowed = CreateAllowedMediaTypesMap();

            try
            {
                var totalUploadedFiles = await _storage.UploadZipfile(bucketName, campaignFolderTheme, true, siteLayout[0].OpenReadStream(), mediaTypesAllowed, false);
                if (totalUploadedFiles <= 0)
                {
                    return ApiReturn<bool>.FromError("Não foi possível efetuar uploado dos arquivos, por favor, verifique a integridade do arquivo zip e se o seu conteúdo contém extensões válidas para o tema.");
                }
            }
            catch (Exception ex)
            {
                await ExceptionLoggerMiddleware.HandleException(ex);
                if (ex is MotivaiException)
                    return ApiReturn<bool>.FromError(ex.Message);
                return ApiReturn<bool>.FromError("Não foi possível efetuar o upload do arquivo, por favor, tente novamente.");
            }
            return await ApiReturn<bool>.Execute(_campaignRepository.SetCampaignSiteWithCustomLayout(campaignId, folderId));
        }

        [HttpDelete]
        [Route("cataloglayout")]
        [Authorization(PermissionCodes.CAMPAIGNS_EDIT)]
        public async Task<ApiReturn<bool>> RemoveCurrentCatalogLayout(string encryptedId, List<IFormFile> layout)
        {
            Guid campaignId = ParserHelper.decryptGuid(_cryptography, encryptedId);
            if (campaignId == Guid.Empty)
            {
                return ApiReturn<bool>.FromError("ID da campanha inválido.");
            }
            return await ApiReturn<bool>.Execute(_campaignRepository.RemoveCurrentCatalogTheme(campaignId));
        }

        [HttpDelete]
        [Route("sitelayout")]
        [Authorization(PermissionCodes.CAMPAIGNS_EDIT)]
        public async Task<ApiReturn<bool>> RemoveCurrentSiteLayout(string encryptedId, List<IFormFile> layout)
        {
            Guid campaignId = ParserHelper.decryptGuid(_cryptography, encryptedId);
            if (campaignId == Guid.Empty)
            {
                return ApiReturn<bool>.FromError("ID da campanha inválido.");
            }
            return await ApiReturn<bool>.Execute(_campaignRepository.RemoveCurrentSiteTheme(campaignId));
        }
    }
}
