using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Model;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.WebAdmin.Server.Helpers;
using Motivai.WebAdmin.Server.Models.Campaign;
using Motivai.WebAdmin.Server.Models.Security;
using Motivai.WebAdmin.Server.Repositories;
using Motivai.WebAdmin.Server.Repositories.Campaigns;
using Motivai.WebAdmin.Server.Services.Security;

namespace Motivai.WebAdmin.Server.Controllers
{
    [Authorize]
    [Route("/api/campaigns")]
    public class CampaignsController : Controller
    {
        private readonly CampaignRepository _campaignRepository;
        private readonly CampaignUserManagementRepository _campaignUserManagementRepository;
        private readonly ICryptography _cryptography;

        public CampaignsController(ICryptography cryptography, CampaignRepository campaignRepository, CampaignUserManagementRepository campaignUserManagementRepository)
        {
            this._cryptography = cryptography;
            this._campaignRepository = campaignRepository;
            this._campaignUserManagementRepository = campaignUserManagementRepository;
        }

        [HttpGet]
        [Authorization(PermissionCodes.CAMPAIGNS_LIST)]
        public async Task<ApiReturn<List<CampaignSearchResult>>> GetCampaigns([FromQuery] string name = null,
                [FromQuery] string clientId = null, [FromQuery] string status = null,
                [FromQuery] CampaignType? type = null, [FromQuery] int? from = null, [FromQuery] int? size = null)
        {
            return await ApiReturn.Execute(_campaignRepository.GetCampaigns(name, clientId, type, status, from, size));
        }

        [HttpGet("clients")]
        public async Task<ApiReturn<List<dynamic>>> GetClients()
        {
            return await ApiReturn<List<dynamic>>.Execute(_campaignRepository.GetCampaignsClientsByBu());
        }

        [HttpGet("{id}")]
        [Authorization(PermissionCodes.CAMPAIGNS_BASIC_VIEW, PermissionCodes.CAMPAIGNS_FULL_VIEW)]
        public async Task<ApiReturn<Campaign>> GetCampaign(string id)
        {
            Guid campaignId = ParserHelper.decryptGuid(_cryptography, id);
            if (campaignId == Guid.Empty)
            {
                return ApiReturn<Campaign>.FromError("ID da campanha inválido.");
            }
            return await ApiReturn<Campaign>.Execute(_campaignRepository.GetCampaign(campaignId));
        }

        [HttpGet]
        [Route("{id}/data")]
        [Authorization(PermissionCodes.CAMPAIGNS_BASIC_VIEW, PermissionCodes.CAMPAIGNS_FULL_VIEW)]
        public async Task<ApiReturn<dynamic>> GetCampaignData(string id)
        {
            Guid campaignId = ParserHelper.decryptGuid(_cryptography, id);
            if (campaignId == Guid.Empty)
            {
                return ApiReturn<dynamic>.FromError("ID da campanha inválido.");
            }
            return await ApiReturn<dynamic>.Execute(_campaignRepository.GetCampaignPrincipalData(campaignId));
        }

        [HttpGet]
        [Route("{encryptedCampaignId}/url")]
        public async Task<ApiReturn<string>> GetCampaignUrl(string encryptedCampaignId)
        {
            Guid campaignId = ParserHelper.decryptGuid(_cryptography, encryptedCampaignId);
            if (campaignId == Guid.Empty)
            {
                return ApiReturn<string>.FromError("ID da campanha inválido.");
            }
            return await ApiReturn<string>.Execute(_campaignRepository.GetCampaignUrl(campaignId));
        }

        [HttpPost]
        [Authorization(PermissionCodes.CAMPAIGNS_INS)]
        public async Task<ApiReturn<Campaign>> CreateCampaign([FromBody] Campaign campaign)
        {
            return await ApiReturn<Campaign>.Execute(_campaignRepository.Create(campaign));
        }

        [HttpPut("{id}")]
        [Authorization(PermissionCodes.CAMPAIGNS_EDIT)]
        public async Task<ApiReturn<bool>> UpdateCampaign(string id, [FromBody] Campaign campaign)
        {
            Guid campaignId = ParserHelper.decryptGuid(_cryptography, id);
            if (campaignId == Guid.Empty)
            {
                return ApiReturn<bool>.FromError("ID da campanha inválido.");
            }
            campaign.Id = ParserHelper.decryptGuid(_cryptography, id).ToString();
            campaign.BuId = ParserHelper.decryptGuid(_cryptography, campaign.BuId).ToString();
            campaign.ClientId = ParserHelper.decryptGuid(_cryptography, campaign.ClientId).ToString();
            return await ApiReturn<bool>.Execute(_campaignRepository.Update(campaignId, campaign));
        }

        [HttpPut("{campaignId}/paymentgateways")]
        [Authorization(PermissionCodes.CAMPAIGNS_PAYMENT_GATEWAYS)]
        public async Task<ApiReturn<bool>> UpdatePaymentGateway(string campaignId, [FromBody] PaymentGatewaySettings settings)
        {
            return await ApiReturn<bool>.Execute(_campaignRepository.UpdatePaymentGateway(campaignId, settings));
        }

        [HttpGet("{campaignId}/paymentgateways")]
        [Authorization(PermissionCodes.CAMPAIGNS_PAYMENT_GATEWAYS)]
        public async Task<ApiReturn<List<PaymentGatewaySettings>>> GetPaymentGateways(string campaignId)
        {
            return await ApiReturn<List<PaymentGatewaySettings>>.Execute(_campaignRepository.GetPaymentGateways(campaignId));
        }

        [HttpGet("{campaignId}/paymentgateways/{company}")]
        [Authorization(PermissionCodes.CAMPAIGNS_PAYMENT_GATEWAYS)]
        public async Task<ApiReturn<PaymentGatewaySettings>> GetPaymentGateway(string campaignId, string company)
        {
            return await ApiReturn<PaymentGatewaySettings>.Execute(_campaignRepository.GetPaymentGatewayByCompany(campaignId, company));
        }

        [HttpDelete("{campaignId}/paymentgateways/{company}")]
        [Authorization(PermissionCodes.CAMPAIGNS_PAYMENT_GATEWAYS)]
        public async Task<ApiReturn<bool>> RemovePaymentGateway(string campaignId, string company)
        {
            return await ApiReturn<bool>.Execute(_campaignRepository.RemovePaymentGateway(campaignId, company));
        }

        [HttpGet("{campaignId}/participants")]
        [Authorization(PermissionCodes.CAMPAIGNS_PARTICIPANTS_GROUPS_VIEW)]
        public async Task<ApiReturn<List<dynamic>>> GetCampaignParticipants(string campaignId, [FromQuery] string term)
        {
            return await ApiReturn<List<dynamic>>.Execute(_campaignRepository.GetCampaignParticipants(campaignId, term));
        }

        [HttpGet("{campaignId}/participants/search")]
        // [Authorization(PermissionCodes.CAMPAIGNS_PARTICIPANTS_GROUPS_VIEW)]
        public async Task<ApiReturn<List<dynamic>>> GetUserManagement(string campaignId, [FromQuery] string document, [FromQuery] string login)
        {
            return await ApiReturn<List<dynamic>>.Execute(_campaignUserManagementRepository.GetUserManagement(campaignId, document, login));
        }

        [HttpGet("{campaignId}/info")]
        public async Task<ApiReturn<dynamic>> GetGeneralInfo(string campaignId)
        {
            return await ApiReturn<dynamic>.Execute(_campaignRepository.GetGeneralInfo(campaignId));
        }

        [HttpGet("{campaignId}/ordersimportsettings")]
        public async Task<ApiReturn<dynamic>> GetOrdersImportSettings(string campaignId)
        {
            return await ApiReturn<dynamic>.Execute(_campaignRepository.GetOrdersImportSettings(campaignId));
        }

        [HttpPut("{campaignId}/ordersimportsettings")]
        public async Task<ApiReturn<bool>> SaveOrdersImportSettings(string campaignId, [FromBody] dynamic settings)
        {
            return await ApiReturn<bool>.Execute(_campaignRepository.SaveOrdersImportSettings(campaignId, settings));
        }

        [HttpPut("{campaignId}/sla/orders")]
        public async Task<ApiReturn<bool>> SaveOrdersSla(string campaignId, [FromBody] dynamic sla)
        {
            return await ApiReturn<bool>.Execute(_campaignRepository.SaveOrdersSla(campaignId, sla));
        }

        [HttpGet("{campaignId}/sla/orders")]
        public async Task<ApiReturn<dynamic>> FindOrdersSla(string campaignId)
        {
            return await ApiReturn<dynamic>.Execute(_campaignRepository.FindOrdersSla(campaignId));
        }

        [HttpPut("{encryptedCampaignId}/products/fixedprices")]
        public async Task<ApiReturn<dynamic>> SaveProductsFixedPrice(string encryptedCampaignId, [FromBody] ProductFixedPrice fixedProductPrice)
        {
            return await ApiReturn<dynamic>.Execute(_campaignRepository.SaveProductsFixedPrice(encryptedCampaignId, fixedProductPrice));
        }

        [HttpGet("{encryptedCampaignId}/products/fixedprices")]
        public async Task<ApiReturn<List<dynamic>>> GetProductsFixedPrices(string encryptedCampaignId)
        {
            return await ApiReturn<List<dynamic>>.Execute(_campaignRepository.GetProductsFixedPrices(encryptedCampaignId));
        }

        [HttpPut]
        [Route("{encryptedCampaignId}/products/fixedprices/{encryptedId}")]
        public async Task<ApiReturn<bool>> DeleteProductFixedPriceById(string encryptedCampaignId, string encryptedId)
        {
            return await ApiReturn<bool>.Execute(_campaignRepository.DeleteProductFixedPriceById(encryptedCampaignId, encryptedId));
        }

        [HttpPut]
        [Route("{encryptedCampaignId}/mobile/settings")]
        public async Task<ApiReturn<bool>> UpdateCampaignMobileApplicationSettings(string encryptedCampaignId, [FromBody] dynamic campaignMobileApplicationSettings)
        {
            return await ApiReturn<bool>.Execute(_campaignRepository.UpdateCampaignMobileApplicationSettings(encryptedCampaignId, campaignMobileApplicationSettings));
        }

        [HttpGet]
        [Route("{encryptedCampaignId}/mobile/settings")]
        public async Task<ApiReturn<dynamic>> GetCampaignMobileApplicationSettingsByCampaign(string encryptedCampaignId)
        {
            return await ApiReturn<dynamic>.Execute(_campaignRepository.GetCampaignMobileApplicationSettingsByCampaign(encryptedCampaignId));
        }

        [HttpGet("{campaignId}/points/metadata/parametrization")]
        public async Task<ApiReturn<dynamic>> GetMetadataFieldsParametrizationByCampaignId(string campaignId)
        {
            return await ApiReturn.Execute(_campaignRepository.GetMetadataFieldsParametrizationByCampaignId(campaignId));
        }

        [HttpPost("{campaignId}/points/metadata/parametrization")]
        public async Task<ApiReturn<dynamic>> CreateMetadataFieldsParametrization(string campaignId, [FromBody] dynamic metadataFieldsParametrization)
        {
            return await ApiReturn.Execute(_campaignRepository.CreateMetadataFieldsParametrization(campaignId, metadataFieldsParametrization));
        }

        [HttpPut("{campaignId}/points/metadata/parametrization/{id}")]
        public async Task<ApiReturn<dynamic>> UpdateMetadataFieldsParametrizationById(string campaignId, string id, [FromBody] dynamic metadataFieldsParametrization)
        {
            return await ApiReturn.Execute(_campaignRepository.UpdateMetadataFieldsParametrizationById(campaignId, id, metadataFieldsParametrization));
        }

        [HttpGet("{campaignId}/processes/parametrizations/consultpersondata")]
        public async Task<ApiReturn<dynamic>> GetPersonDataConsultParametrizations(string campaignId)
        {
            return await ApiReturn.Execute(_campaignRepository.GetPersonDataConsultParametrizations(campaignId));
        }

        [HttpPut("{campaignId}/processes/parametrizations/consultpersondata")]
        public async Task<ApiReturn<dynamic>> UpdatePersonDataConsultParametrizations(string campaignId, [FromBody] dynamic processesParametrizatons)
        {
            return await ApiReturn.Execute(_campaignRepository.UpdatePersonDataConsultParametrizations(campaignId, processesParametrizatons));
        }
    }
}
