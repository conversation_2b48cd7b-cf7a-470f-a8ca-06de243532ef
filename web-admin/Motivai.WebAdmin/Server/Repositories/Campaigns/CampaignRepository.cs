using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.WebAdmin.Server.Models.Security;
using Motivai.SharedKernel.Domain.Entities.BusinessUnit;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Extensions;
using Motivai.SharedKernel.Helpers.Storage;
using Motivai.WebAdmin.Server.Extensions;
using Motivai.WebAdmin.Server.Helpers;
using Motivai.WebAdmin.Server.Models.Campaign;
using Motivai.WebAdmin.Server.Models.Campaign.Filters;
using Motivai.WebAdmin.Server.Models.Product;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Motivai.WebAdmin.Server.Repositories
{
	public class CampaignRepository
	{
		private readonly IMemoryCache _cache;
		private readonly ICryptography _cryptography;
		private readonly ManufacturerRepository _manufacturerRepository;
		private readonly CompanyRepository _companyRepository;
		private readonly CategoriesRepository _categoryRepository;
		private readonly ElasticSearchRepository _elasticSearchRepository;
		private readonly IHttpContextAccessor _context;

		private Motivai.SharedKernel.Domain.Entities.References.Security.OperationUser GetOperationUser() {
            return _context.HttpContext.User.GetUser();
        }

		public CampaignRepository(ICryptography cryptography, IMemoryCache cache,
			ManufacturerRepository manufacturerRepository, CompanyRepository companyRepository,
			CategoriesRepository categoryRepository, ElasticSearchRepository elasticSearchRepository,
			IHttpContextAccessor context)
		{
			this._cryptography = cryptography;
			this._cache = cache;
			this._manufacturerRepository = manufacturerRepository;
			this._companyRepository = companyRepository;
			this._categoryRepository = categoryRepository;
			this._elasticSearchRepository = elasticSearchRepository;
			this._context = context;
		}

		private Guid BuId()
		{
			return _context.HttpContext.User.GetBuId();
		}

		private Guid UserId()
		{
			return _context.HttpContext.User.GetUserId();
		}

		public async Task<List<CampaignSearchResult>> GetCampaigns(string name = null, string encryptedClientId = null,
			CampaignType? type = null, string status = null, int? from = null, int? size = null)
		{
			var request = HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns")
				.Bu(BuId())
				.Query("name", name)
				.Query("status", status)
				.Query("from", from)
				.Query("size", size);

			if (!string.IsNullOrEmpty(encryptedClientId))
			{
				Guid clientId = ParserHelper.decryptGuid(_cryptography, encryptedClientId);
				if (clientId != Guid.Empty)
				{
					request.Query("clientId", clientId);
				}
			}

			if (type != null && type.HasValue)
				request.Query("type", type.Value.ToString());

			var response = await request.AsyncGet()
				.GetApiReturn<List<CampaignSearchResult>>();

			var campaigns = response.GetReturnOrError();
			if (campaigns == null) return null;

			campaigns.ForEach(campaign =>
			{
				campaign.Id = _cryptography.Encrypt(campaign.Id);
				campaign.ClientId = _cryptography.Encrypt(campaign.ClientId);
				campaign.BuId = _cryptography.Encrypt(campaign.BuId);
			});

			return campaigns;
		}

		public async Task<string> GetCampaignUrl(Guid campaignId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("url")
				.AsyncGet()
				.GetApiReturn<string>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar a URL da campanha.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<List<CampaignData>> GetCampaignsByIds(List<Guid> campaignsIds)
		{
			var apiResponse = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns/search")
				.Bu(BuId())
				.Entity(new { campaignsIds })
				.AsyncPost()
				.GetApiReturn<List<CampaignData>>();
			if (apiResponse == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as campanhas.");
			return apiResponse.GetReturnOrError();
		}

		public async Task<List<Guid>> GetCampaignsClientsIdsByBu()
		{
			var apiResponse = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns/clientsids")
				.Query("buId", BuId())
				.AsyncGet()
				.GetApiReturn<List<Guid>>();
			if (apiResponse == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os clientes.");
			return apiResponse.GetReturnOrError();
		}

		public async Task<List<dynamic>> GetCampaignsClientsByBu()
		{
			var clientsIds = await GetCampaignsClientsIdsByBu();
			if (clientsIds == null || clientsIds.Count == 0) return null;
			return await _companyRepository.GetClientsByIds(clientsIds);
		}

		private async Task PrepareForEdit(Campaign campaign)
		{
			campaign.Id = _cryptography.Encrypt(campaign.Id);
			if (!string.IsNullOrEmpty(campaign.ClientId))
			{
				var client = await _companyRepository.GetCompanyInfo(Guid.Parse(campaign.ClientId));
				if (client != null)
				{
					campaign.ClientName = client.Name;
				}
			}
			campaign.BuId = _cryptography.Encrypt(campaign.BuId);
			campaign.ClientId = _cryptography.Encrypt(campaign.ClientId);
		}

		private async Task<Campaign> LoadCampaign(Guid campaignId)
		{
			var response = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId)
				.Bu(BuId())
				.AsyncGet()
				.GetApiReturn<Campaign>();
			return response.GetReturnOrError();
		}

		public async Task<Campaign> GetCampaign(Guid campaignId)
		{
			var campaign = await LoadCampaign(campaignId);
			if (campaign == null) return null;
			await PrepareForEdit(campaign);
			if (!_context.HttpContext.User.HasPermission(PermissionCodes.CAMPAIGNS_FULL_VIEW))
			{
				campaign.Parametrizations = null;
				campaign.IntegrationSettings = null;
				campaign.PageSettings = null;
				campaign.SitePageSettings = null;
			}
			return campaign;
		}

		public async Task<dynamic> GetCampaignPrincipalData(Guid campaignId)
		{
			Guid userBuId = BuId();
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("info")
				.Bu(userBuId)
				.AsyncGet()
				.GetApiReturn<CampaignInfo>();

			var campaign = apiReturn.GetReturnOrError();
			if (campaign == null) return null;
			return new
			{
				Id = _cryptography.Encrypt(campaign.Id),
				BuId = _cryptography.Encrypt(campaign.BuId),
				ClientId = _cryptography.Encrypt(campaign.ClientId),
				Type = campaign.Type,
				Modality = campaign.Modality,
				License = campaign.License,
				CoinType = campaign.CoinType,
				Name = campaign.Name,
				Gp = userBuId == BUConstants.ROOT_BU_ID
			};

		}

		public async Task<Campaign> Create(Campaign campaign)
		{
			if (campaign == null)
			{
				throw MotivaiException.ofValidation("Preencha os campos corretamente para prosseguir.");
			}
			campaign.Id = campaign.BuId = Guid.Empty.ToString();
			campaign.ClientId = ParserHelper.decryptGuid(_cryptography, campaign.ClientId).ToString();
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns")
				.Entity(campaign)
				.Bu(BuId())
				.AsyncPost()
				.GetApiReturn<Campaign>();
			if (apiReturn == null) return null;
			campaign = apiReturn.GetReturnOrError();
			if (campaign == null) return null;
			await PrepareForEdit(campaign);
			return campaign;
		}

		public async Task<bool> Update(Guid campaignId, Campaign campaign)
		{
			campaign.OperationUser = GetOperationUser();
			if (!_context.HttpContext.User.HasPermission(PermissionCodes.CAMPAIGNS_FULL_VIEW))
			{
				var dbCampaign = await LoadCampaign(campaignId);
				if (dbCampaign != null)
				{
					campaign.Parametrizations = dbCampaign.Parametrizations;
					campaign.IntegrationSettings = dbCampaign.IntegrationSettings;
					campaign.PageSettings = dbCampaign.PageSettings;
					campaign.SitePageSettings = dbCampaign.SitePageSettings;
				}
			}

			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId)
				.Entity(campaign)
				.Bu(BuId())
				.AsyncPut()
				.LogPayloadToLogger()
				.GetApiReturn<bool>();
			if (apiReturn == null) return false;
			var result = apiReturn.GetReturnOrError();
			if (result)
			{
				_cache.Remove("cmp_dt_" + campaign.Id);
			}

			return result;
		}

		public async Task<bool> SetCampaignWithCustomLayout(Guid campaignId, Guid folderId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("theme").Path(folderId)
				.Bu(BuId())
				.AsyncPut()
				.GetApiReturn<bool>();
			if (apiReturn == null) throw MotivaiException.of(ErrorType.ApiException, "Não foi possível atualizar o tema do catálogo.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> SetCampaignSiteWithCustomLayout(Guid campaignId, Guid folderId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("site/theme").Path(folderId)
				.Bu(BuId())
				.AsyncPut()
				.GetApiReturn<bool>();
			if (apiReturn == null) throw MotivaiException.of(ErrorType.ApiException, "Não foi possível atualizar o tema do site campanha.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> RemoveCurrentCatalogTheme(Guid campaignId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("theme")
				.Bu(BuId())
				.AsyncDelete()
				.GetApiReturn<bool>();
			if (apiReturn == null) throw MotivaiException.of(ErrorType.ApiException, "Não foi possível remover o tema atual do catálogo.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> RemoveCurrentSiteTheme(Guid campaignId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("site/theme")
				.Bu(BuId())
				.AsyncDelete()
				.GetApiReturn<bool>();
			if (apiReturn == null) throw MotivaiException.of(ErrorType.ApiException, "Não foi possível remover o tema atual do site campanha.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<List<PartnerSetting>> GetPartners(Guid campaignId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("partners")
				.AsyncGet()
				.GetApiReturn<List<PartnerSetting>>();
			var partners = apiReturn.GetReturnOrError();
			if (partners.IsNullOrEmpty()) return null;
			var partnersInfo = await _companyRepository.GetPartnersByIds(partners.Select(p => Guid.Parse(p.PartnerId)).ToList());
			if (!partnersInfo.IsNullOrEmpty())
			{
				partners.ForEach(partner =>
				{
					var partnerInfo = partnersInfo.FirstOrDefault(p => p.Id == partner.PartnerId);
					partner.Id = null;
					partner.PartnerId = _cryptography.Encrypt(partner.PartnerId);
					partner.PartnerName = partnerInfo != null ? partnerInfo.Trade : null;
					partner.EnableCustomPromotionalPrice = partnerInfo.Partner != null && partnerInfo.Partner.EnableCustomPromotionalPrice ? partnerInfo.Partner.EnableCustomPromotionalPrice : false;
				});
			}
			return partners;
		}


		//loadpartners()
		public async Task<List<dynamic>> GetPartnersData(Guid campaignId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("partners")
				.AsyncGet()
				.GetApiReturn<List<PartnerSetting>>();
			var partners = apiReturn.GetReturnOrError();
			if (partners == null) return null;
			var partnersData = new List<dynamic>();
			var partnersInfo = await _companyRepository.GetPartnersByIds(partners.Select(p => Guid.Parse(p.PartnerId)).ToList());
			if (partnersInfo == null) return partnersData;
			partners.ForEach(partnerSet =>
			{
				// var partner = await _companyRepository.GetPartnerData(Guid.Parse(partnerSet.PartnerId));
				var partner = partnersInfo.FirstOrDefault(p => p.Id == partnerSet.PartnerId);
				partnersData.Add(new
				{
					partnerId = _cryptography.Encrypt(partnerSet.PartnerId),
					partnerName = partner != null ? partner.Trade : ""
				});
			});
			return partnersData;
		}

		public async Task<PartnerSetting> GetPartnerSetting(Guid campaignId, Guid partnerId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("partners").Path(partnerId)
				.AsyncGet()
				.GetApiReturn<PartnerSetting>();
			var partner = apiReturn.GetReturnOrError();
			if (partner == null) return null;
			partner.Id = null;
			partner.PartnerId = _cryptography.Encrypt(partner.PartnerId.ToString());
			return partner;
		}

		public async Task<bool> SavePartnerSetting(Guid campaignId, PartnerSetting partnerSetting)
		{
			if (partnerSetting == null)
			{
				throw MotivaiException.ofValidation("Dados de configuração do parceiro são obrigatórios.");
			}
			if (!string.IsNullOrEmpty(partnerSetting.PartnerId))
			{
				partnerSetting.PartnerId = ParserHelper.decryptGuid(_cryptography, partnerSetting.PartnerId).ToString();
			}
			var httpClient = HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("partners");
			Motivai.SharedKernel.Domain.Http.HttpRequest request = null;
			if (string.IsNullOrEmpty(partnerSetting.Id))
			{
				partnerSetting.Id = Guid.Empty.ToString();
				request = httpClient.Entity(partnerSetting).AsyncPost();
			}
			else
			{
				request = httpClient.Path(partnerSetting.PartnerId)
					.Entity(partnerSetting).AsyncPut();
			}
			var apiReturn = await request.GetApiReturn<bool>();
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> UnlinkPartnerSetting(Guid campaignId, Guid partnerId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("partners").Path(partnerId)
				.AsyncDelete()
				.GetApiReturn<bool>();
			if (apiReturn == null) return false;
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> UpdateLinkedPartners(Guid campaignId, List<PartnerSetting> partners)
		{
			var response = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("partners/status")
				.Entity(partners)
				.AsyncPut().GetApiReturn<dynamic>();

			return response.GetReturnOrError();
		}

		// Filtros
		private async Task<dynamic> GetFilters(Guid campaignId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId.ToString()).Path("filters").Bu(BuId())
				.AsyncGet()
				.GetApiReturn<dynamic>();
			if (apiReturn == null) return null;

			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> GetFiltersByValue(Guid campaignId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId.ToString()).Path("filters/byvalue").Bu(BuId())
				.AsyncGet()
				.GetApiReturn<dynamic>();
			if (apiReturn == null) return null;

			return apiReturn.GetReturnOrError();
		}

		public async Task<List<dynamic>> GetFiltersByManufactures(Guid campaignId, int? skip, int? limit)
		{
			var request = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId.ToString()).Path("filters").Path("manufactures").Bu(BuId())
				.Query("skip", skip)
				.Query("limit", limit)
				.AsyncGet()
				.GetApiReturn<dynamic>();

			var filtersByManufacturers = request.GetReturnOrError();
			if (filtersByManufacturers == null) return null;

			var ByManufacturers = new List<dynamic>();
			if (filtersByManufacturers != null && filtersByManufacturers is JArray)
			{
				var manufacturers = (filtersByManufacturers as JArray).ToObject<List<dynamic>>();
				var partners = await _companyRepository.GetPartnersByIds(manufacturers
					.Where(m => !string.IsNullOrEmpty((string)m.partnerId))
					.Select(m => Guid.Parse((string)m.partnerId))
					.ToList());

				await manufacturers.ForEachAsync(async m =>
				{
					var manufacturerName = "";
					var partnerName = "";
					var manufacturer = await _manufacturerRepository.Get(Guid.Parse((string)m.manufacturerId));
					if (manufacturer != null)
					{
						manufacturerName = manufacturer.Name;
					}
					if (m.partnerId != null && partners != null)
					{
						var partner = partners.FirstOrDefault(p => p.Id == (string)m.partnerId);
						if (partner != null)
						{
							partnerName = partner.Name;
						}
					}
					ByManufacturers.Add(new
					{
						id = _cryptography.Encrypt((string)m.id),
						manufacturerId = _cryptography.Encrypt((string)m.manufacturerId),
						manufacturerName,
						partnerName,
						partnerId = m.partnerId != null ? _cryptography.Encrypt((string)m.partnerId) : null
					});
				});
			}
			return ByManufacturers;
		}

		public async Task<List<dynamic>> GetFiltersByPartners(Guid campaignId, int? skip, int? limit)
		{
			var request = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId.ToString()).Path("filters").Path("partners").Bu(BuId())
				.Query("skip", skip)
				.Query("limit", limit)
				.AsyncGet()
				.GetApiReturn<dynamic>();
			var filtersByPartners = request.GetReturnOrError();
			if (filtersByPartners == null) return null;

			var ByPartners = new List<dynamic>();
			if (filtersByPartners != null)
			{
				var partners = await _companyRepository.GetPartnersByIds((filtersByPartners as JArray).ToObject<List<Guid>>());
				if (partners != null)
				{
					partners.ForEach(partner =>
					{
						ByPartners.Add(new
						{
							id = _cryptography.Encrypt(partner.Id),
							name = partner.Trade
						});
					});
				}
			}
			return ByPartners;
		}

		public async Task<List<dynamic>> GetFiltersByBlockRules(Guid campaignId, int? skip, int? limit)
		{
			var request = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId.ToString()).Path("filters").Path("block/rules").Bu(BuId())
				.Query("skip", skip)
				.Query("limit", limit)
				.AsyncGet()
				.GetApiReturn<dynamic>();
			var filtersByRules = request.GetReturnOrError();
			if (filtersByRules == null) return null;

			var ByRules = new List<dynamic>();
			if (filtersByRules != null)
			{
				await ((JArray)filtersByRules).ToObject<List<dynamic>>().ForEachAsync(async rule =>
				{
					var departmentName = "";
					var categoryName = "Todas";
					var subcategoryName = "Todas";

					var category = await _categoryRepository.GetCategoryData(Guid.Parse((string)rule.departmentId));
					if (category != null)
						departmentName = category.Name;
					if (!string.IsNullOrEmpty((string)rule.categoryId))
					{
						category = await _categoryRepository.GetCategoryData(Guid.Parse((string)rule.categoryId));
						if (category != null)
							categoryName = category.Name;
					}
					if (!string.IsNullOrEmpty((string)rule.subcategoryId))
					{
						category = await _categoryRepository.GetCategoryData(Guid.Parse((string)rule.subcategoryId));
						if (category != null)
							subcategoryName = category.Name;
					}
					ByRules.Add(new
					{
						id = _cryptography.Encrypt((string)rule.id),
						departmentName,
						categoryName,
						subcategoryName
					});
				});
			}
			return ByRules;
		}

		public async Task<List<dynamic>> GetFiltersByAllowRules(Guid campaignId, int? skip, int? limit)
		{
			var request = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId.ToString()).Path("filters").Path("allow/rules").Bu(BuId())
				.Query("skip", skip)
				.Query("limit", limit)
				.AsyncGet()
				.GetApiReturn<dynamic>();
			var filtersByRules = request.GetReturnOrError();
			if (filtersByRules == null) return null;

			var ByRules = new List<dynamic>();
			if (filtersByRules != null)
			{
				await ((JArray)filtersByRules).ToObject<List<dynamic>>().ForEachAsync(async rule =>
				{
					var departmentName = "";
					var categoryName = "Todas";
					var subcategoryName = "Todas";

					var category = await _categoryRepository.GetCategoryData(Guid.Parse((string)rule.departmentId));
					if (category != null)
						departmentName = category.Name;
					if (!string.IsNullOrEmpty((string)rule.categoryId))
					{
						category = await _categoryRepository.GetCategoryData(Guid.Parse((string)rule.categoryId));
						if (category != null)
							categoryName = category.Name;
					}
					if (!string.IsNullOrEmpty((string)rule.subcategoryId))
					{
						category = await _categoryRepository.GetCategoryData(Guid.Parse((string)rule.subcategoryId));
						if (category != null)
							subcategoryName = category.Name;
					}
					ByRules.Add(new
					{
						id = _cryptography.Encrypt((string)rule.id),
						departmentName,
						categoryName,
						subcategoryName
					});
				});
			}
			return ByRules;
		}
		public async Task<List<dynamic>> GetFiltersByPartnerRules(Guid campaignId, int? skip, int? limit)
		{
			var request = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId.ToString()).Path("filters").Path("partner/rules").Bu(BuId())
				.Query("skip", skip)
				.Query("limit", limit)
				.AsyncGet()
				.GetApiReturn<dynamic>();
			var filtersByPartnerRules = request.GetReturnOrError();
			if (filtersByPartnerRules == null) return null;

			var ByPartnersSkus = new List<dynamic>();
			if (filtersByPartnerRules != null && filtersByPartnerRules is JArray)
			{
				var partnersSkus = (filtersByPartnerRules as JArray).ToObject<List<dynamic>>();
				var products = await _elasticSearchRepository.GetProductsBySkusIds(partnersSkus.Select(p => Guid.Parse((string)p.skuId)).ToList());
				partnersSkus.ForEach(p =>
				{
					var partnerName = "Não Encontrado";
					var productName = "Não Encontrado";
					var sku = "";
					if (products != null)
					{
						var product = products.FirstOrDefault(pr => pr.SkuId == (string)p.skuId);
						if (product != null)
						{
							partnerName = product.PartnerName;
							productName = product.Name;
							sku = product.SkuCode;
						}
					}
					ByPartnersSkus.Add(new
					{
						id = _cryptography.Encrypt((string)p.id),
						partnerName,
						productName,
						sku
					});
				});
			}
			return ByPartnersSkus;
		}

		public async Task<List<dynamic>> GetFiltersBySku(Guid campaignId, string sku, string productName, int? skip, int? limit)
		{
			var request = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId.ToString()).Path("filters").Path("sku").Bu(BuId())
				.Query("skip", skip)
				.Query("limit", limit)
				.AsyncGet()
				.GetApiReturn<dynamic>();
			var filtersBySku = request.GetReturnOrError();
			if (filtersBySku == null || filtersBySku.Count <= 0) return null;

			var WhitelistBySkus = new List<dynamic>();
			if (filtersBySku != null && filtersBySku is JArray)
			{
				var whiteListSkus = (filtersBySku as JArray).ToObject<List<dynamic>>();
				var products = await _elasticSearchRepository.GetProductsBySkusIds(whiteListSkus.Select(p => Guid.Parse((string)p.skuId)).ToList());
				if (!string.IsNullOrEmpty(sku) || !string.IsNullOrEmpty(productName))
				{
					ProductSkuModel product = products.FirstOrDefault(pr => pr.SkuCode == sku || pr.Name == productName);
					var skuId = whiteListSkus.First(s => s.skuId == product.SkuId);
					if (skuId != null && skuId.id != null) {
						WhitelistBySkus.Add(new
						{
							id = _cryptography.Encrypt((string)skuId.id),
							partnerName = product.PartnerName,
							productName = product.Name,
							sku = product.SkuCode
						});
					}
					return WhitelistBySkus;
				}

				whiteListSkus.ForEach(p =>
				{
					if (products != null)
					{
						ProductSkuModel product = products.FirstOrDefault(pr => pr.SkuId == (string)p.skuId);
						if (product != null)
						{
							WhitelistBySkus.Add(new
							{
								id = _cryptography.Encrypt((string)p.id),
								partnerName = product.PartnerName,
								productName = product.Name,
								sku = product.SkuCode
							});
						}
					}
				});
			}
			return WhitelistBySkus;
		}
		public async Task<List<dynamic>> GetFiltersByParticipantsGroups(Guid campaignId, int? skip, int? limit)
		{
			var request = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId.ToString()).Path("filters/participants/groups").Bu(BuId())
				.Query("skip", skip)
				.Query("limit", limit)
				.AsyncGet()
				.GetApiReturn<dynamic>();
			var filtersByParticipantsGroups = request.GetReturnOrError();
			if (filtersByParticipantsGroups == null) return null;

			var ParticipantsGroupsWhitelists = new List<dynamic>();
			if (filtersByParticipantsGroups != null && filtersByParticipantsGroups is JArray)
			{
				var participantsGroupsWhitelists = (filtersByParticipantsGroups as JArray).ToObject<List<FilterParticipantGroupWhitelist>>();
				participantsGroupsWhitelists.ForEach(group =>
				{
					group.ParticipantGroupId = _cryptography.Encrypt(group.ParticipantGroupId);
					if (group.Skus != null)
					{
						group.Skus.ForEach(sku =>
						{
							sku.PartnerId = _cryptography.Encrypt(sku.PartnerId);
							sku.ProductId = _cryptography.Encrypt(sku.ProductId);
							sku.SkuId = _cryptography.Encrypt(sku.SkuId);
						});
					}
					ParticipantsGroupsWhitelists.Add(group);
				});
			}

			return ParticipantsGroupsWhitelists;
		}

		public async Task<bool> SetFilterByValue(Guid campaignId, dynamic payload)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("filters/byvalue")
				.Entity(payload)
				.AsyncPut()
				.GetApiReturn<bool>();
			if (apiReturn == null) return false;
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> RemoveFilterByValue(Guid campaignId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("filters/byvalue")
				.AsyncDelete()
				.GetApiReturn<bool>();
			if (apiReturn == null) return false;
			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> AddManufacturerFilter(Guid campaignId, dynamic payload)
		{
			var filter = new
			{
				ManufacturerId = ParserHelper.decryptGuid(_cryptography, payload.manufacturerId.ToString()),
				PartnerId = string.IsNullOrEmpty((string)payload.partnerId) ? (Guid?)null : ParserHelper.decryptGuid(_cryptography, (string)payload.partnerId)
			};
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("filters/manufacturers")
				.Entity(filter)
				.AsyncPost()
				.GetApiReturn<Guid>();
			if (apiReturn == null) return null;
			var id = apiReturn.GetReturnOrError();

			var manufacturerName = "";
			var partnerName = "";
			var manufacturer = await _manufacturerRepository.Get(filter.ManufacturerId);
			if (manufacturer != null)
			{
				manufacturerName = manufacturer.Name;
			}
			if (filter.PartnerId != null && filter.PartnerId.HasValue)
			{
				var partner = await _companyRepository.GetPartnerData(filter.PartnerId.Value);
				if (partner != null)
				{
					partnerName = partner.Name;
				}
			}
			return new
			{
				id = _cryptography.Encrypt(id.ToString()),
				manufacturerId = payload.manufacturerId,
				manufacturerName,
				partnerId = payload.partnerId,
				partnerName
			};
		}

		public async Task<bool> RemoveManufacturerFilter(Guid campaignId, Guid filterId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("filters/manufacturers").Path(filterId)
				.AsyncDelete()
				.GetApiReturn<bool>();
			if (apiReturn == null) return false;
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> AddPartnerFilter(Guid campaignId, Guid partnerId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("filters/partners")
				.Entity(new { partnerId })
				.AsyncPost()
				.GetApiReturn<bool>();
			if (apiReturn == null) return false;
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> RemovePartnerFilter(Guid campaignId, Guid partnerId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("filters/partners").Path(partnerId)
				.AsyncDelete()
				.GetApiReturn<bool>();
			if (apiReturn == null) return false;
			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> AddBlockRuleFilter(Guid campaignId, dynamic payload)
		{
			Guid departmentId = ParserHelper.decryptGuid(_cryptography, (string)payload.departmentId);
			Guid? categoryId = null, subcategoryId = null;
			if (!string.IsNullOrEmpty((string)payload.categoryId))
			{
				categoryId = ParserHelper.decryptGuid(_cryptography, (string)payload.categoryId);
			}
			if (!string.IsNullOrEmpty((string)payload.subcategoryId))
			{
				subcategoryId = ParserHelper.decryptGuid(_cryptography, (string)payload.subcategoryId);
			}
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("filters/block/rules")
				.Entity(new
				{
					departmentId,
					categoryId,
					subcategoryId
				})
				.AsyncPost()
				.GetApiReturn<Guid>();
			if (apiReturn == null) return false;
			var id = apiReturn.GetReturnOrError();

			var departmentName = "";
			var categoryName = "Todas";
			var subcategoryName = "Todas";

			var category = await _categoryRepository.GetCategoryData(departmentId);
			if (category != null)
				departmentName = category.Name;
			if (categoryId != null && categoryId.HasValue)
			{
				category = await _categoryRepository.GetCategoryData(categoryId.Value);
				if (category != null)
					categoryName = category.Name;
			}
			if (subcategoryId != null && subcategoryId.HasValue)
			{
				category = await _categoryRepository.GetCategoryData(subcategoryId.Value);
				if (category != null)
					subcategoryName = category.Name;
			}
			return new
			{
				id = _cryptography.Encrypt(id.ToString()),
				departmentName,
				categoryName,
				subcategoryName
			};
		}

		public async Task<dynamic> AddAllowRuleFilter(Guid campaignId, dynamic payload)
		{
			Guid departmentId = ParserHelper.decryptGuid(_cryptography, (string)payload.departmentId);
			Guid? categoryId = null, subcategoryId = null;
			if (!string.IsNullOrEmpty((string)payload.categoryId))
			{
				categoryId = ParserHelper.decryptGuid(_cryptography, (string)payload.categoryId);
			}
			if (!string.IsNullOrEmpty((string)payload.subcategoryId))
			{
				subcategoryId = ParserHelper.decryptGuid(_cryptography, (string)payload.subcategoryId);
			}
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("filters/allow/rules")
				.Entity(new
				{
					departmentId,
					categoryId,
					subcategoryId
				})
				.AsyncPost()
				.GetApiReturn<Guid>();
			if (apiReturn == null) return false;
			var id = apiReturn.GetReturnOrError();

			var departmentName = "";
			var categoryName = "Todas";
			var subcategoryName = "Todas";

			var category = await _categoryRepository.GetCategoryData(departmentId);
			if (category != null)
				departmentName = category.Name;
			if (categoryId != null && categoryId.HasValue)
			{
				category = await _categoryRepository.GetCategoryData(categoryId.Value);
				if (category != null)
					categoryName = category.Name;
			}
			if (subcategoryId != null && subcategoryId.HasValue)
			{
				category = await _categoryRepository.GetCategoryData(subcategoryId.Value);
				if (category != null)
					subcategoryName = category.Name;
			}
			return new
			{
				id = _cryptography.Encrypt(id.ToString()),
				departmentName,
				categoryName,
				subcategoryName
			};
		}

		public async Task<bool> RemoveBlockRuleFilter(Guid campaignId, Guid ruleId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("filters/block/rules").Path(ruleId)
				.AsyncDelete()
				.GetApiReturn<bool>();
			if (apiReturn == null) return false;
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> RemoveAllowRuleFilter(Guid campaignId, Guid ruleId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("filters/allow/rules").Path(ruleId)
				.AsyncDelete()
				.GetApiReturn<bool>();
			if (apiReturn == null) return false;
			return apiReturn.GetReturnOrError();
		}

		private async Task<dynamic> LoadFilterData(dynamic createdFilter)
		{
			if (createdFilter == null) return null;
			var partnerName = "Não Encontrado";
			var productName = "Não Encontrado";
			var sku = "";
			var product = await _elasticSearchRepository.GetProductBySkuId((Guid)createdFilter.skuId);
			if (product != null)
			{
				partnerName = product.PartnerName;
				productName = product.Name;
				sku = product.SkuCode;
			}
			return new
			{
				id = _cryptography.Encrypt((string)createdFilter.id),
				partnerName,
				productName,
				sku
			};
		}

		public async Task<dynamic> AddPartnerSkuFilter(Guid campaignId, dynamic payload)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("filters/skus")
				.Entity(new
				{
					partnerId = ParserHelper.decryptGuid(_cryptography, (string)payload.partnerId),
					sku = payload.sku
				})
				.AsyncPost()
				.GetApiReturn<dynamic>();
			if (apiReturn == null) return null;
			var filter = apiReturn.GetReturnOrError();
			return await LoadFilterData(filter);
		}

		public async Task<bool> RemovePartnerSkuFilter(Guid campaignId, Guid filterId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("filters/skus").Path(filterId)
				.AsyncDelete()
				.GetApiReturn<bool>();
			if (apiReturn == null) return false;
			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> AddPartnerSkuToWhitelist(Guid campaignId, dynamic payload)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("filters/whitelist/skus")
				.Entity(new
				{
					partnerId = ParserHelper.decryptGuid(_cryptography, (string)payload.partnerId),
					sku = payload.sku
				})
				.AsyncPost()
				.GetApiReturn<dynamic>();
			if (apiReturn == null) return false;
			var filter = apiReturn.GetReturnOrError();
			return await LoadFilterData(filter);
		}

		public async Task<bool> RemovePartnerSkuToWhitelist(Guid campaignId, Guid filterId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("filters/whitelist/skus").Path(filterId)
				.AsyncDelete()
				.GetApiReturn<bool>();
			if (apiReturn == null) return false;
			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> SaveParticipantGroupWhitelist(Guid campaignId, FilterParticipantGroupWhitelist groupWhitelist)
		{
			if (groupWhitelist == null)
				throw MotivaiException.ofValidation("Informe o grupo de participante e seus SKUs.");

			groupWhitelist.ParticipantGroupId = _cryptography.Decrypt(groupWhitelist.ParticipantGroupId);
			if (groupWhitelist.Skus != null)
			{
				groupWhitelist.Skus.ForEach(sku =>
				{
					sku.PartnerId = _cryptography.Decrypt(sku.PartnerId);
					sku.ProductId = _cryptography.Decrypt(sku.ProductId);
					sku.SkuId = _cryptography.Decrypt(sku.SkuId);
				});
			}

			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("filters/participantsgroups")
				.Path(groupWhitelist.ParticipantGroupId).Path("whitelist")
				.Entity(groupWhitelist)
				.AsyncPost()
				.GetApiReturn<dynamic>();
			if (apiReturn == null) return false;
			return apiReturn.GetReturnOrError();
		}

		public async Task<List<dynamic>> GetDepartments(Guid campaignId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("marketing/departments/available")
				.Query("all", "true")
				.AsyncGet()
				.GetApiReturn<List<dynamic>>();
			if (apiReturn == null) return null;
			var departments = apiReturn.GetReturnOrError();
			if (departments == null) return null;
			return departments.Select(dep => (dynamic)new
			{
				id = _cryptography.Encrypt(dep.id.ToString()),
				name = dep.name
			}).ToList();
		}

		public async Task<CampaignMenu> GetDepartmentsOrder(Guid campaignId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("marketing/departments")
				.AsyncGet()
				.GetApiReturn<CampaignMenu>();
			if (apiReturn == null) return null;
			var campaignMenu = apiReturn.GetReturnOrError();
			if (campaignMenu == null) return null;
			if (!string.IsNullOrEmpty(campaignMenu.DepartmentId))
				campaignMenu.DepartmentId = _cryptography.Encrypt(campaignMenu.DepartmentId);
			campaignMenu.MenuOrder = campaignMenu.MenuOrder.Select(id => _cryptography.Encrypt(id.ToString())).ToList();

			if (campaignMenu.Segmentations != null) {
				campaignMenu.Segmentations.Encrypt(_cryptography);
			}

			return campaignMenu;
		}

		public async Task<bool> SaveDepartmentsOrder(Guid campaignId, CampaignMenu campaignMenu)
		{
			if (campaignMenu == null || campaignMenu.MenuOrder.IsNullOrEmpty())
			{
				throw MotivaiException.ofValidation("Ordem dos departamentos é obrigatória.");
			}
			if (campaignMenu.Segmentations != null) {
				campaignMenu.Segmentations.Validate(_cryptography);
			}

			if (!string.IsNullOrEmpty(campaignMenu.DepartmentId))
				campaignMenu.DepartmentId = _cryptography.Decrypt(campaignMenu.DepartmentId);
			else
				campaignMenu.DepartmentId = null;
			campaignMenu.MenuOrder = campaignMenu.MenuOrder.Select(id => _cryptography.Decrypt(id)).ToList();

			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("marketing/departments")
				.Entity(campaignMenu)
				.AsyncPost()
				.GetApiReturn<bool>();
			if (apiReturn == null) return false;
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> UpdateCatalogMenu(Guid campaignId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("marketing/departments/menu")
				.AsyncPut()
				.GetApiReturn<bool>();
			if (apiReturn == null) return false;
			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> GetFeaturedProductsByCampaign(Guid campaignId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("marketing/departments/products")
				.AsyncGet()
				.GetApiReturn<List<dynamic>>();
			if (apiReturn == null) return null;
			var skusByDeps = apiReturn.GetReturnOrError();
			if (skusByDeps == null) return null;

			List<ProductSkuModel> home = null;
			var departments = new List<dynamic>();

			await skusByDeps.ForEachAsync(async dep =>
			{
				var skus = new List<ProductSkuModel>();

				if (dep.skus != null)
				{
					skus = JsonConvert.DeserializeObject<List<ProductSkuModel>>(dep.skus.ToString());

					if (skus != null)
					{
						skus.ForEach(product =>
						{
							product.SkuId = _cryptography.Encrypt(product.SkuId);
							product.PartnerId = _cryptography.Encrypt(product.PartnerId);
						});
					}
				}

				if (dep.departmentId == Guid.Empty)
				{
					home = skus;
				}
				else
				{
					var departmentName = "";
					var category = await _categoryRepository.GetCategoryData(Guid.Parse((string)dep.departmentId));
					if (category != null)
						departmentName = category.Name;
					departments.Add(new
					{
						departmentId = _cryptography.Encrypt((string)dep.departmentId),
						departmentName,
						skus
					});
				}
			});

			return new
			{
				home,
				departments
			};
		}

		public async Task<dynamic> AddSkuToHome(Guid campaignId, dynamic payload)
		{
			if (payload == null)
				throw MotivaiException.ofValidation("Selecione um SKU válido.");

			dynamic partnerSku = new
			{
				partnerId = this._cryptography.Decrypt(payload.partnerId.ToString()),
				partnerName = payload.partnerName,
				skuId = this._cryptography.Decrypt(payload.skuId.ToString()),
				skuCode = payload.skuCode,
				skuName = payload.name,
				productId = this._cryptography.Decrypt(payload.id.ToString()),
				elasticSearchId = payload.elasticSearchId
			};

			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("marketing/departments/home/<USER>")
				.Entity(partnerSku)
				.AsyncPost()
				.GetApiReturn<bool>();
			if (apiReturn == null) return false;
			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> AddSkuToDepartment(Guid campaignId, Guid departmentId, dynamic payload)
		{

			if (payload == null)
				throw MotivaiException.ofValidation("Selecione um SKU válido.");

			dynamic partnerSku = new
			{
				partnerId = this._cryptography.Decrypt(payload.partnerId.ToString()),
				partnerName = payload.partnerName,
				skuId = this._cryptography.Decrypt(payload.skuId.ToString()),
				skuCode = payload.skuCode,
				skuName = payload.name,
				productId = this._cryptography.Decrypt(payload.id.ToString()),
				elasticSearchId = payload.elasticSearchId
			};

			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("marketing/departments").Path(departmentId).Path("products")
				.Entity(partnerSku)
				.AsyncPost()
				.GetApiReturn<bool>();
			if (apiReturn == null) return false;
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> RemoveSkuFromHome(Guid campaignId, Guid partnerId, Guid skuId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("marketing/departments/home/<USER>").Path(partnerId).Path("products").Path(skuId)
				.AsyncDelete()
				.GetApiReturn<bool>();
			if (apiReturn == null) return false;
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> RemoveSkuFromDepartment(Guid campaignId, Guid departmentId, Guid partnerId, Guid skuId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("marketing/departments").Path(departmentId).Path("partners").Path(partnerId).Path("products").Path(skuId)
				.AsyncDelete()
				.GetApiReturn<bool>();
			if (apiReturn == null) return false;
			return apiReturn.GetReturnOrError();
		}

		public async Task<List<dynamic>> GetMediaBoxesByCampaign(Guid campaignId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("marketing/mediaboxes")
				.AsyncGet()
				.GetApiReturn<List<dynamic>>();
			if (apiReturn == null) return null;
			var mediaBoxes = apiReturn.GetReturnOrError();
			if (mediaBoxes == null) return null;
			mediaBoxes.ForEach(mb => mb.id = _cryptography.Encrypt((string)mb.id));
			return mediaBoxes;
		}

		public async Task<dynamic> GetMediaBoxById(Guid campaignId, Guid mediaBoxId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("marketing/mediaboxes").Path(mediaBoxId)
				.AsyncGet()
				.GetApiReturn<dynamic>();
			if (apiReturn == null) return null;
			var mediaBox = apiReturn.GetReturnOrError();
			if (mediaBox == null) return null;

			if (mediaBox.contentType == "PRODUCT" && mediaBox.product != null)
			{
				mediaBox.product.partnerId = _cryptography.Encrypt((string)mediaBox.product.partnerId);
				mediaBox.product.productId = _cryptography.Encrypt((string)mediaBox.product.productId);
				mediaBox.product.skuId = _cryptography.Encrypt((string)mediaBox.product.skuId);
			}
			else
			{
				mediaBox.product = null;
			}

			List<string> departments = null;
			if (mediaBox.departments != null && mediaBox.departments is JArray)
			{
				departments = (mediaBox.departments as JArray).ToObject<List<string>>().Select(id => _cryptography.Encrypt(id)).ToList();
			}

			List<string> participantsGroups = null;
			if (mediaBox.participantsGroups != null && mediaBox.participantsGroups is JArray)
			{
				participantsGroups = (mediaBox.participantsGroups as JArray).ToObject<List<string>>().Select(id => _cryptography.Encrypt(id)).ToList();
			}

			return new
			{
				id = _cryptography.Encrypt((string)mediaBox.id),
				mediaBox.active,
				mediaBox.contentType,
				mediaBox.name,
				mediaBox.position,
				mediaBox.imageUrl,
				mediaBox.mediumImageUrl,
				mediaBox.smallImageUrl,
				mediaBox.link,
				mediaBox.comunication,
				mediaBox.product,
				mediaBox.startDate,
				mediaBox.endDate,
				mediaBox.visibleOnCatalog,
				mediaBox.visibleOnSite,
				mediaBox.visibleOnAppCashback,
				mediaBox.sitePages,
				participantsGroups,
				// targetAudiences,
				departments
			};
		}

		public async Task<dynamic> SaveMediaBox(Guid campaignId, dynamic payload)
		{
			var departments = new List<string>();
			if (payload.departments != null && payload.departments is JArray)
			{
				(payload.departments as JArray).ToObject<List<string>>().ForEach(encryptedId =>
				{
					departments.Add(_cryptography.Decrypt(encryptedId));
				});
			}

			List<string> participantsGroups = null;
			if (payload.participantsGroups != null && payload.participantsGroups is JArray)
			{
				participantsGroups = (payload.participantsGroups as JArray).ToObject<List<string>>()
					.Select(id => _cryptography.Decrypt(id)).ToList();
			}

			if (payload.contentType == "PRODUCT" && payload.product != null)
			{
				payload.product.partnerId = _cryptography.Decrypt((string)payload.product.partnerId);
				payload.product.productId = _cryptography.Decrypt((string)payload.product.productId);
				payload.product.skuId = _cryptography.Decrypt((string)payload.product.skuId);
			}
			else
			{
				payload.product = null;
			}

			bool isEdit = payload.id != null;

			var httpClient = HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("marketing/mediaboxes")
				.Entity(new
				{
					id = payload.id != null ? payload.id : Guid.Empty,
					payload.contentType,
					payload.name,
					payload.position,
					payload.link,
					payload.product,
					payload.startDate,
					payload.endDate,
					payload.comunication,
					payload.visibleOnCatalog,
					payload.visibleOnSite,
					payload.visibleOnAppCashback,
					payload.sitePages,
					payload.openInNewTab,
					active = payload.active == true,
					participantsGroups,
					departments
				});
			if (isEdit)
			{
				httpClient.Path(payload.id.ToString());
				var apiReturn = await httpClient.AsyncPut().GetApiReturn<bool>();
				return apiReturn == null ? false : apiReturn.GetReturnOrError();
			}
			else
			{
				var apiReturn = await httpClient.AsyncPost().GetApiReturn<string>();
				if (apiReturn == null) return null;
				var mediaBoxId = apiReturn.GetReturnOrError();
				if (mediaBoxId == null) return null;
				return _cryptography.Encrypt(mediaBoxId);
			}
		}

		public async Task<string> UpdateMediaBoxImage(Guid campaignId, Guid mediaBoxId, string imageUrl)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("marketing/mediaboxes").Path(mediaBoxId).Path("imageurl")
				.Entity(new
				{
					imageUrl
				})
				.AsyncPut()
				.GetApiReturn<bool>();
			if (apiReturn != null && apiReturn.GetReturnOrError())
			{
				return imageUrl;
			}
			return null;
		}
		public async Task<string> UpdateMediaBoxImageTablet(Guid campaignId, Guid mediaBoxId, string imageUrl)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("marketing/mediaboxes").Path(mediaBoxId).Path("images/medium/url")
				.Entity(new
				{
					imageUrl
				})
				.AsyncPut()
				.GetApiReturn<bool>();
			if (apiReturn != null && apiReturn.GetReturnOrError())
			{
				return imageUrl;
			}
			return null;
		}

		public async Task<string> UpdateMediaBoxImageMobile(Guid campaignId, Guid mediaBoxId, string imageUrl)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("marketing/mediaboxes").Path(mediaBoxId).Path("images/small/url")
				.Entity(new
				{
					imageUrl
				})
				.AsyncPut()
				.GetApiReturn<bool>();
			if (apiReturn != null && apiReturn.GetReturnOrError())
			{
				return imageUrl;
			}
			return null;
		}

		public async Task<bool> RemoveMediaBox(Guid campaignId, Guid mediaBoxId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("marketing/mediaboxes").Path(mediaBoxId)
				.AsyncDelete()
				.GetApiReturn<bool>();
			if (apiReturn == null) return false;
			return apiReturn.GetReturnOrError();
		}

		public async Task<List<dynamic>> GetSpecialShopsByCampaign(Guid campaignId, bool onlyForCatalogMenu)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("marketing/specialshops")
				.Query("onlyForCatalogMenu", onlyForCatalogMenu.ToString())
				.AsyncGet()
				.GetApiReturn<List<dynamic>>();
			if (apiReturn == null) return null;
			var specialShops = apiReturn.GetReturnOrError();
			if (specialShops == null) return null;
			specialShops.ForEach(sp => sp.id = _cryptography.Encrypt((string)sp.id));
			return specialShops;
		}

		public async Task<dynamic> GetSpecialShopsById(Guid campaignId, Guid specialShopId, bool onlyForCatalogMenu)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("marketing/specialshops").Path(specialShopId)
				.Query("onlyForCatalogMenu", onlyForCatalogMenu.ToString())
				.AsyncGet()
				.GetApiReturn<dynamic>();
			if (apiReturn == null) return null;
			var specialShop = apiReturn.GetReturnOrError();
			if (specialShop == null) return null;
			List<ProductSkuModel> skus = null;
			if (specialShop.skus != null && specialShop.skus is JArray)
			{
				var skusIds = (specialShop.skus as JArray).ToObject<List<Guid>>();
				skus = await _elasticSearchRepository.GetProductsBySkusIds(skusIds);
				if (skus != null)
				{
					skus.ForEach(product =>
					{
						product.SkuId = _cryptography.Encrypt(product.SkuId);
					});
				}
			}

			List<string> participantsGroups = null;
			if (specialShop.participantsGroups != null && specialShop.participantsGroups is JArray)
			{
				participantsGroups = (specialShop.participantsGroups as JArray).ToObject<List<string>>()
					.Select(id => _cryptography.Encrypt(id)).ToList();
			}
			return new
			{
				id = _cryptography.Encrypt((string)specialShop.id),
				specialShop.name,
				specialShop.active,
				specialShop.startDate,
				specialShop.endDate,
				specialShop.pageLinkUrl,
				specialShop.pageContent,
				specialShop.viewParametrization,
				specialShop.principalBannerImageUrl,
				specialShop.linkBannerImageUrl,
				specialShop.bannerSmallImageUrl,
				participantsGroups,
				// targetAudiences,
				skus
			};
		}

		public async Task<dynamic> SaveSpecialShop(Guid campaignId, dynamic specialShop)
		{
			bool isEdit = specialShop.id != null;
			var skus = new List<string>();
			if (specialShop.skus != null)
			{
				(specialShop.skus as JArray).ToObject<List<string>>().ForEach(encryptedId =>
				{
					skus.Add(_cryptography.Decrypt(encryptedId));
				});
			}

			List<string> participantsGroups = null;
			if (specialShop.participantsGroups != null && specialShop.participantsGroups is JArray)
			{
				participantsGroups = (specialShop.participantsGroups as JArray).ToObject<List<string>>()
					.Select(id => _cryptography.Decrypt(id)).ToList();
			}
			var httpClient = HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("marketing/specialshops")
				.Entity(new
				{
					id = specialShop.id != null ? specialShop.id : Guid.Empty,
					specialShop.name,
					specialShop.startDate,
					specialShop.endDate,
					specialShop.active,
					specialShop.pageLinkUrl,
					specialShop.pageContent,
					specialShop.viewParametrization,
					skus,
					participantsGroups
				});
			if (isEdit)
			{
				httpClient.Path(specialShop.id.ToString());
				var apiReturn = await httpClient.AsyncPut().GetApiReturn<bool>();
				return apiReturn == null ? false : apiReturn.GetReturnOrError();
			}
			else
			{
				var apiReturn = await httpClient.AsyncPost().GetApiReturn<string>();
				if (apiReturn == null) return null;
				var specialShopId = apiReturn.GetReturnOrError();
				return specialShopId == null ? null : _cryptography.Encrypt(specialShopId);
			}
		}

		public async Task<bool> UpdateSpecialShopPrincipalBanner(Guid campaignId, Guid specialShopId, string imageUrl)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("marketing/specialshops").Path(specialShopId).Path("principalbannerimage")
				.Entity(new
				{
					imageUrl
				})
				.AsyncPut()
				.GetApiReturn<bool>();
			if (apiReturn == null) return false;
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> UpdateSpecialShopLinkBanner(Guid campaignId, Guid specialShopId, string imageUrl)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("marketing/specialshops").Path(specialShopId).Path("linkbannerimage")
				.Entity(new
				{
					imageUrl
				})
				.AsyncPut()
				.GetApiReturn<bool>();
			if (apiReturn == null) return false;
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> UpdateSpecialShopSmallBanner(Guid campaignId, Guid specialShopId, string imageUrl)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("marketing/specialshops").Path(specialShopId).Path("smallbannerimage")
				.Entity(new
				{
					imageUrl
				})
				.AsyncPut()
				.GetApiReturn<bool>();
			if (apiReturn == null) return false;
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> DeleteSpecialShop(Guid campaignId, Guid specialShopId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("marketing/specialshops").Path(specialShopId)
				.AsyncDelete()
				.GetApiReturn<bool>();
			if (apiReturn == null) return false;
			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> GetTransactionalNotificationByType(Guid campaignId, string type)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("marketing/transactionalnotifications").Path(type)
				.AsyncGet()
				.GetApiReturn<dynamic>();

			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> SaveTransactionalNotificationByType(Guid campaignId, string type, dynamic notification)
		{
			if (notification.sms != null)
				notification.sms.activatedBy = UserId();
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("marketing/transactionalnotifications").Path(type)
				.Entity(notification)
				.AsyncPut()
				.GetApiReturn<bool>();
			if (apiReturn == null) return false;
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> UpdateNotificationEmailHeaderImage(Guid campaignId, string type, string imageUrl)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("marketing/transactionalnotifications").Path(type).Path("email/headerimage")
				.Entity(new { imageUrl })
				.AsyncPut()
				.GetApiReturn<bool>();
			if (apiReturn == null) return false;
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> UpdatePaymentGateway(string campaignId, PaymentGatewaySettings settings)
		{
			var response = await HttpClient
				.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(_cryptography.Decrypt(campaignId)).Path("paymentgateways")
				.Entity(settings)
				.AsyncPut()
				.GetApiReturn<dynamic>();

			var success = response.GetReturnOrError();
			if (success == null) return false;

			return success;
		}

		public async Task<List<PaymentGatewaySettings>> GetPaymentGateways(string campaignId)
		{

			var response = await HttpClient
				.Create(MotivaiApi.Campaigns)
				.Path("campaigns")
				.Path(_cryptography.Decrypt(campaignId))
				.Path("paymentgateways")
				.AsyncGet()
				.GetApiReturn<dynamic>();

			var payments = response.GetReturnOrError();
			if (payments == null) return null;

			var paymentsReturn = new List<PaymentGatewaySettings>();
			(payments as IEnumerable<dynamic>)
			.ToList()
				.ForEach(payment =>
				{
					paymentsReturn.Add(new PaymentGatewaySettings()
					{
						AdditionalFee = payment.additionalFee,
						AntiFraudTransactionCurrencyValue = payment.antiFraudTransactionCurrencyValue,
						AntiFraudFee = payment.antiFraudFee,
						AntiFraudFeeType = payment.antiFraudFeeType,
						Batch = payment.batch,
						ChangeConversionFactor = payment.changeConversionFactor,
						ConversionFactor = payment.conversionFactor,
						EnableAntiFraud = payment.enableAntiFraud,
						EnableFreeBatch = payment.enableFreeBatch,
						EnableInstallment = payment.enableInstallment,
						GatewayCompany = payment.gatewayCompany,
						IsMaster = payment.isMaster,
						TransactionCurrencyValue = payment.transactionCurrencyValue,
						MaxInstallNumber = payment.maxInstallNumber,
						MerchantKey = payment.merchantKey,
						MinInstallCurrencyValue = payment.minInstallCurrencyValue,
						PrivateKey = payment.privateKey,
						PublicKey = payment.publicKey,
						AnticipationFee = payment.anticipationFee,
						GovernmentFee = payment.governmentFee,
						BankTransferFee = payment.bankTransferFee
					});
				});

			return paymentsReturn;
		}

		public async Task<bool> RemovePaymentGateway(string campaignId, string company)
		{

			var response = await HttpClient
				.Create(MotivaiApi.Campaigns)
				.Path("campaigns")
				.Path(_cryptography.Decrypt(campaignId))
				.Path("paymentgateways")
				.Path(company)
				.AsyncDelete()
				.GetApiReturn<dynamic>();

			var success = response.GetReturnOrError();
			if (success == null) return false;

			return success;
		}

		public async Task<PaymentGatewaySettings> GetPaymentGatewayByCompany(string campaignId, string company)
		{

			var response = await HttpClient
				.Create(MotivaiApi.Campaigns)
				.Path("campaigns")
				.Path(_cryptography.Decrypt(campaignId))
				.Path("paymentgateways")
				.Path(company)
				.AsyncGet()
				.GetApiReturn<dynamic>();

			var payment = response.GetReturnOrError();
			if (payment == null) return null;

			var fees = new List<KeyValuePair<int, decimal>>();
			if (payment.installmentsFee != null)
			{
				foreach (var p in payment.installmentsFee)
				{
					fees.Add(new KeyValuePair<int, decimal>(int.Parse(p.key.ToString()), decimal.Parse(p.value.ToString())));
				}
			}

			return new PaymentGatewaySettings()
			{
				AdditionalFee = payment.additionalFee,
				AntiFraudTransactionCurrencyValue = payment.antiFraudTransactionCurrencyValue,
				AntiFraudFee = payment.antiFraudFee,
				AntiFraudFeeType = payment.antiFraudFeeType,
				Batch = payment.batch,
				ChangeConversionFactor = payment.changeConversionFactor,
				ConversionFactor = payment.conversionFactor,
				EnableAntiFraud = payment.enableAntiFraud,
				EnableFreeBatch = payment.enableFreeBatch,
				EnableInstallment = payment.enableInstallment,
				GatewayCompany = payment.gatewayCompany,
				IsMaster = payment.isMaster,
				TransactionCurrencyValue = payment.transactionCurrencyValue,
				MaxInstallNumber = payment.maxInstallNumber,
				MerchantKey = payment.merchantKey,
				MinInstallCurrencyValue = payment.minInstallCurrencyValue,
				InstallmentsFee = fees,
				PrivateKey = payment.privateKey,
				PublicKey = payment.publicKey,
				AnticipationFee = payment.anticipationFee,
				GovernmentFee = payment.governmentFee,
				BankTransferFee = payment.bankTransferFee
			};
		}

		public async Task<List<dynamic>> GetCampaignParticipants(string campaignId, string term)
		{
			var request = HttpClient.Create(MotivaiApi.Users)
				.Path("campaigns").Path(_cryptography.Decrypt(campaignId)).Path("participants")
				.Bu(BuId());

			if (!string.IsNullOrEmpty(term))
			{
				if (Cpf.IsCpf(term))
				{
					request.Query("cpf", term);

				}
				else if (Cnpj.IsCnpj(term))
				{
					request.Query("cnpj", term);

				}
				else
				{
					request.Query("name", term);
				}
			}

			var response = await request.AsyncGet().GetApiReturn<List<dynamic>>();
			var users = response.GetReturnOrError();
			if (users == null) return null;

			var list = new List<dynamic>();
			users.ForEach(u =>
			{
				list.Add(new
				{
					participantId = _cryptography.Encrypt(u.participantId.ToString()),
					userId = _cryptography.Encrypt(u.userId.ToString()),
					name = u.name,
					document = u.document
				});
			});

			return list;
		}

		public async Task<List<dynamic>> GetShippings(Guid campaignId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("shippings")
				.Bu(BuId())
				.AsyncGet()
				.GetApiReturn<List<dynamic>>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os fretes da campanha.");
			var shippings = apiReturn.GetReturnOrError();
			if (shippings == null) return null;
			shippings.ForEach(ship =>
			{
				if (ship.id != null && !string.IsNullOrEmpty((string)ship.id))
					ship.id = _cryptography.Encrypt((string)ship.id);
			});
			return shippings;
		}

		public async Task<dynamic> GetShippingById(Guid campaignId, Guid shippingId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("shippings").Path(shippingId)
				.Bu(BuId())
				.AsyncGet()
				.GetApiReturn<dynamic>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o frete da campanha.");
			var shipping = apiReturn.GetReturnOrError();
			if (shipping == null) return null;
			if (shipping.id != null && !string.IsNullOrEmpty((string)shipping.id))
			{
				shipping.id = _cryptography.Encrypt((string)shipping.id);
			}
			var skusFilters = new List<dynamic>();
			var partnersFilters = new List<dynamic>();
			var categoriesFilters = new List<dynamic>();

			if (shipping.skusFilters != null && shipping.skusFilters is JArray && (shipping.skusFilters as JArray).Count > 0)
			{
				var skusIds = (shipping.skusFilters as JArray).ToObject<List<dynamic>>().Select(f => (Guid)f.skuId).ToList();
				var products = await _elasticSearchRepository.GetProductsBySkusIds(skusIds);

				(shipping.skusFilters as JArray).ToObject<List<dynamic>>().ForEach(filter =>
				{
					if (filter == null) return;
					dynamic product = null;
					if (products != null)
						product = products.FirstOrDefault(p => p != null && p.SkuId == ((string)filter.skuId));
					skusFilters.Add(new
					{
						id = _cryptography.Encrypt((string)filter.id),
						partnerId = _cryptography.Encrypt((string)filter.partnerId),
						skuId = _cryptography.Encrypt((string)filter.skuId),
						partnerName = product == null ? null : product.PartnerName,
						productName = product == null ? null : product.Name,
						skuCode = product == null ? null : product.SkuCode,
					});
				});
			}
			if (shipping.partnersFilters != null && shipping.partnersFilters is JArray && (shipping.partnersFilters as JArray).Count > 0)
			{
				var partnersFound = await _companyRepository.GetPartnersByIds((shipping.partnersFilters as JArray).ToObject<List<Guid>>());
				if (partnersFilters != null && partnersFound != null)
				{
					partnersFound.ForEach(partner =>
					{
						partnersFilters.Add(new
						{
							partnerId = _cryptography.Encrypt(partner.Id),
							partnerName = partner.Trade
						});
					});
				}
			}
			if (shipping.categoriesFilters != null)
			{
				await (shipping.categoriesFilters as JArray).ToObject<List<dynamic>>().ForEachAsync(async filter =>
				{
					var departmentName = "";
					var categoryName = "Todas";
					var subcategoryName = "Todas";

					var category = await _categoryRepository.GetCategoryData(Guid.Parse((string)filter.departmentId));
					if (category != null)
						departmentName = category.Name;
					if (!string.IsNullOrEmpty((string)filter.categoryId))
					{
						category = await _categoryRepository.GetCategoryData(Guid.Parse((string)filter.categoryId));
						if (category != null)
							categoryName = category.Name;
					}
					if (!string.IsNullOrEmpty((string)filter.subcategoryId))
					{
						category = await _categoryRepository.GetCategoryData(Guid.Parse((string)filter.subcategoryId));
						if (category != null)
							subcategoryName = category.Name;
					}
					categoriesFilters.Add(new
					{
						id = _cryptography.Encrypt((string)filter.id),
						departmentName,
						categoryName,
						subcategoryName
					});
				});
			}
			return new
			{
				id = shipping.id,
				description = shipping.description,
				active = shipping.active,
				type = shipping.type,
				fixedCostType = shipping.fixedCostType,
				fixedCostValue = shipping.fixedCostValue,
				embeddableInProductPrice = shipping.embeddableInProductPrice,
				correiosIntegration = shipping.correiosIntegration,
				sourceCep = shipping.sourceCep,
				applyAdditionalValue = shipping.applyAdditionalValue,
				additionalType = shipping.additionalType,
				additionalValue = shipping.additionalValue,
				applyFeeValue = shipping.applyFeeValue,
				feeValue = shipping.feeValue,
				feeValueType = shipping.feeValueType,
				skusFilters = skusFilters,
				partnersFilters = partnersFilters,
				categoriesFilters = categoriesFilters
			};
		}

		public async Task<dynamic> GetShippingFilters(Guid campaignId, Guid shippingId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("shippings").Path(shippingId).Path("filters")
				.Bu(BuId())
				.AsyncGet()
				.GetApiReturn<dynamic>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o frete da campanha.");
			var shipping = apiReturn.GetReturnOrError();
			if (shipping == null) return null;
			if (shipping.id != null && !string.IsNullOrEmpty((string)shipping.id))
			{
				shipping.id = _cryptography.Encrypt((string)shipping.id);
			}
			var skusFilters = new List<dynamic>();
			var partnersFilters = new List<dynamic>();
			var categoriesFilters = new List<dynamic>();
			if (shipping.skusFilters != null)
			{
				var skusIds = (shipping.skusFilters as JArray).ToObject<List<dynamic>>().Select(f => (Guid)f.skuId).ToList();
				var products = await _elasticSearchRepository.GetProductsBySkusIds(skusIds);

				(shipping.skusFilters as JArray).ToObject<List<dynamic>>().ForEach(filter =>
				{
					if (filter == null) return;
					dynamic product = null;
					if (products != null)
						product = products.FirstOrDefault(p => p != null && p.SkuId == ((string)filter.skuId));
					skusFilters.Add(new
					{
						id = _cryptography.Encrypt((string)filter.id),
						partnerId = _cryptography.Encrypt((string)filter.partnerId),
						skuId = _cryptography.Encrypt((string)filter.skuId),
						partnerName = product == null ? null : product.PartnerName,
						productName = product == null ? null : product.Name,
						skuCode = product == null ? null : product.SkuCode,
					});
				});
			}
			if (shipping.partnersFilters != null)
			{
				var partnersFound = await _companyRepository.GetPartnersByIds((shipping.partnersFilters as JArray).ToObject<List<Guid>>());
				if (partnersFilters != null)
				{
					partnersFound.ForEach(partner =>
					{
						partnersFilters.Add(new
						{
							partnerId = _cryptography.Encrypt(partner.Id),
							partnerName = partner.Trade
						});
					});
				}
			}
			if (shipping.categoriesFilters != null)
			{
				await (shipping.categoriesFilters as JArray).ToObject<List<dynamic>>().ForEachAsync(async filter =>
				{
					var departmentName = "";
					var categoryName = "Todas";
					var subcategoryName = "Todas";

					var category = await _categoryRepository.GetCategoryData(Guid.Parse((string)filter.departmentId));
					if (category != null)
						departmentName = category.Name;
					if (!string.IsNullOrEmpty((string)filter.categoryId))
					{
						category = await _categoryRepository.GetCategoryData(Guid.Parse((string)filter.categoryId));
						if (category != null)
							categoryName = category.Name;
					}
					if (!string.IsNullOrEmpty((string)filter.subcategoryId))
					{
						category = await _categoryRepository.GetCategoryData(Guid.Parse((string)filter.subcategoryId));
						if (category != null)
							subcategoryName = category.Name;
					}
					categoriesFilters.Add(new
					{
						id = _cryptography.Encrypt((string)filter.id),
						departmentName,
						categoryName,
						subcategoryName
					});
				});
			}
			return new
			{
				id = shipping.id,
				description = shipping.description,
				active = shipping.active,
				type = shipping.type,
				fixedCostValue = shipping.fixedCostValue,
				embeddableInProductPrice = shipping.embeddableInProductPrice,
				correiosIntegration = shipping.correiosIntegration,
				sourceCep = shipping.sourceCep,
				applyAdditionalValue = shipping.applyAdditionalValue,
				additionalType = shipping.additionalType,
				additionalValue = shipping.additionalValue,
				skusFilters = skusFilters,
				partnersFilters = partnersFilters,
				categoriesFilters = categoriesFilters
			};
		}

		public async Task<string> CreateShipping(Guid campaignId, dynamic payload)
		{
			if (payload == null)
				throw MotivaiException.ofValidation("Preencha os dados para prosseguir.");
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("shippings")
				.Bu(BuId())
				.Entity(payload)
				.AsyncPost()
				.GetApiReturn<Guid>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível cadastrar o frete na campanha.");
			var shippingId = apiReturn.GetReturnOrError();
			if (shippingId == Guid.Empty)
				return null;
			return _cryptography.Encrypt(shippingId.ToString());
		}

		public async Task<string> SaveShipping(Guid campaignId, Guid shippingId, dynamic payload)
		{
			if (payload == null)
				throw MotivaiException.ofValidation("Preencha os dados para prosseguir.");
			payload.id = shippingId;
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("shippings").Path(shippingId)
				.Bu(BuId())
				.Entity(payload)
				.AsyncPut()
				.GetApiReturn<Guid>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível salvar o frete na campanha.");
			var result = apiReturn.GetReturnOrError();
			if (result == Guid.Empty)
				return null;
			return _cryptography.Encrypt(shippingId.ToString());
		}

		public async Task<bool> AddPartnerSkuToShippingFilter(Guid campaignId, Guid shippingId, dynamic payload)
		{
			if (payload == null)
				throw MotivaiException.ofValidation("Preencha os dados para prosseguir.");
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("shippings").Path(shippingId).Path("filters/skus")
				.Bu(BuId())
				.Entity(new
				{
					PartnerId = ParserHelper.decryptGuid(_cryptography, (string)payload.partnerId),
					SkuId = ParserHelper.decryptGuid(_cryptography, (string)payload.skuId)
				})
				.AsyncPost()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível adicionar o SKU no filtro do frete na campanha.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> RemovePartnerSkuFromShippingFilter(Guid campaignId, Guid shippingId, Guid filterId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("shippings").Path(shippingId).Path("filters/skus").Path(filterId)
				.Bu(BuId())
				.AsyncDelete()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível remover o SKU do filtro do frete na campanha.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> AddPartnerToShippingFilter(Guid campaignId, Guid shippingId, dynamic payload)
		{
			if (payload == null)
				throw MotivaiException.ofValidation("Preencha os dados para prosseguir.");
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("shippings").Path(shippingId).Path("filters/partners")
				.Bu(BuId())
				.Entity(new
				{
					PartnerId = ParserHelper.decryptGuid(_cryptography, (string)payload.partnerId)
				})
				.AsyncPost()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível adicionar o parceiro no filtro do frete na campanha.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> RemovePartnerFromShippingFilter(Guid campaignId, Guid shippingId, Guid partnerId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("shippings").Path(shippingId).Path("filters/partners").Path(partnerId)
				.Bu(BuId())
				.AsyncDelete()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível remover o parceiro do filtro do frete na campanha.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> AddClassificationToShippingFilter(Guid campaignId, Guid shippingId, dynamic payload)
		{
			if (payload == null)
				throw MotivaiException.ofValidation("Preencha os dados para prosseguir.");
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("shippings").Path(shippingId).Path("filters/classifications")
				.Bu(BuId())
				.Entity(new
				{
					DepartmentId = ParserHelper.decryptGuid(_cryptography, (string)payload.departmentId),
					CategoryId = string.IsNullOrEmpty((string)payload.categoryId) ? null :
						(Guid?)ParserHelper.decryptGuid(_cryptography, (string)payload.categoryId),
					SubcategoryId = string.IsNullOrEmpty((string)payload.subcategoryId) ? null :
						(Guid?)ParserHelper.decryptGuid(_cryptography, (string)payload.subcategoryId),
				})
				.AsyncPost()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível adicionar o parceiro no filtro do frete na campanha.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> RemoveClassificationFromShippingFilter(Guid campaignId, Guid shippingId, Guid classificationId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("shippings").Path(shippingId).Path("filters/classifications").Path(classificationId)
				.Bu(BuId())
				.AsyncDelete()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível remover a classificação do filtro do frete na campanha.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> GetGeneralInfo(string campaignId)
		{
			var decryptedCampaignId = _cryptography.Decrypt(campaignId);
			var response = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(decryptedCampaignId).Path("info")
				.Bu(BuId())
				.AsyncGet()
				.GetApiReturn<CampaignInfo>();

			var info = response.GetReturnOrError();
			if (info == null) return null;

			return new
			{
				info.Name,
				info.Type,
				info.Modality,
				info.License,
				info.CoinType,
				info.CampaignSiteUrl,
				info.Url,
				info.PointsConversionFactor,
				info.ExpirationDate,
				info.ExpirationPeriod,
				info.PointsExpirationType
			};
		}

		public async Task<dynamic> GetOrdersImportSettings(string campaignId)
		{
			var decryptedCampaignId = _cryptography.Decrypt(campaignId);
			var response = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(decryptedCampaignId).Path("ordersimportsettings")
				.Bu(BuId())
				.AsyncGet()
				.GetApiReturn<dynamic>();

			return response.GetReturnOrError();
		}

		public async Task<bool> SaveOrdersImportSettings(string campaignId, dynamic settings)
		{
			var decryptedCampaignId = _cryptography.Decrypt(campaignId);
			var response = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(decryptedCampaignId).Path("ordersimportsettings")
				.Bu(BuId())
				.Entity(settings)
				.AsyncPut()
				.GetApiReturn<dynamic>();

			return response.GetReturnOrError();
		}

		public async Task<bool> SaveOrdersSla(string encryptedCampaignId, dynamic sla)
		{
			var campaignId = _cryptography.Decrypt(encryptedCampaignId);
			var response = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("sla/orders")
				.Entity(sla)
				.AsyncPut()
				.GetApiReturn<dynamic>();

			return response.GetReturnOrError();
		}

		public async Task<dynamic> FindOrdersSla(string encryptedCampaignId)
		{
			var campaignId = _cryptography.Decrypt(encryptedCampaignId);
			var response = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("sla/orders")
				.AsyncGet()
				.GetApiReturn<dynamic>();

			return response.GetReturnOrError();
		}

		public async Task<dynamic> FindInstitutionalMechanic(string encryptedCampaignId)
		{
			var campaignId = _cryptography.Decrypt(encryptedCampaignId);
			var response = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("institutional/mechanic")
				.AsyncGet().GetApiReturn<dynamic>();
			return response.GetReturnOrError();
		}

		public async Task<bool> UpdateInstitutionalMechanic(string encryptedCampaignId, dynamic mechanic)
		{
			var campaignId = _cryptography.Decrypt(encryptedCampaignId);
			var response = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("institutional/mechanic")
				.Entity(mechanic)
				.AsyncPut().GetApiReturn<bool>();
			var updated = response.GetReturnOrError();
			if (updated == null) return false;
			return updated;
		}

		public async Task<dynamic> FindInstitutionalHowToParticipate(string encryptedCampaignId)
		{
			var campaignId = _cryptography.Decrypt(encryptedCampaignId);
			var response = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("institutional/howtoparticipate")
				.AsyncGet().GetApiReturn<dynamic>();
			return response.GetReturnOrError();
		}

		public async Task<bool> UpdateInstitutionalHowToParticipate(string encryptedCampaignId, dynamic howToParticipate)
		{
			var campaignId = _cryptography.Decrypt(encryptedCampaignId);
			var response = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("institutional/howtoparticipate")
				.Entity(howToParticipate)
				.AsyncPut().GetApiReturn<bool>();
			var updated = response.GetReturnOrError();
			if (updated == null) return false;
			return updated;
		}

		public async Task<dynamic> SaveProductsFixedPrice(string encryptedCampaignId, ProductFixedPrice fixedProductPrice)
		{
			var campaignId = _cryptography.Decrypt(encryptedCampaignId);

			fixedProductPrice.PartnerId = _cryptography.Decrypt(fixedProductPrice.PartnerId.ToString());
			fixedProductPrice.ProductId = _cryptography.Decrypt(fixedProductPrice.ProductId.ToString());
			fixedProductPrice.SkuId = _cryptography.Decrypt(fixedProductPrice.SkuId.ToString());

			var response = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("products/fixedprices")
				.Entity(fixedProductPrice)
				.AsyncPut().GetApiReturn<List<ProductFixedPrice>>();

			var updated = response.GetReturnOrError();
			if (updated == null) return false;
			return updated;
		}

		public async Task<List<dynamic>> GetProductsFixedPrices(string encryptedCampaignId)
		{
			var campaignId = _cryptography.Decrypt(encryptedCampaignId);

			var request = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("products/fixedprices")
				.AsyncGet()
				.GetApiReturn<List<ProductFixedPrice>>();

			var fixedPrices = request.GetReturnOrError();
			if (fixedPrices == null || fixedPrices.Count == 0)
				return null;

			var skus = await _elasticSearchRepository.GetProductsBySkusIds(fixedPrices.Select(p => Guid.Parse(p.SkuId)).ToList());

			return fixedPrices.Select(p =>
			{
				var partnerName = "Não Encontrado";
				var productName = "Não Encontrado";
				var sku = "";
				if (skus != null)
				{
					var product = skus.FirstOrDefault(pr => pr.SkuId == p.SkuId);
					if (product != null)
					{
						partnerName = product.PartnerName;
						productName = product.Name;
						sku = product.SkuCode;
					}
				}
				return (dynamic)new
				{
					Id = _cryptography.Encrypt(p.Id.ToString()),
					PartnerId = _cryptography.Encrypt(p.PartnerId.ToString()),
					ProductId = _cryptography.Encrypt(p.ProductId.ToString()),
					SkuId = _cryptography.Encrypt(p.SkuId.ToString()),
					partnerName,
					productName,
					sku,
					p.FixedSalePrice
				};
			}).ToList();
		}

		public async Task<bool> DeleteProductFixedPriceById(string encryptedCampaignId, string encryptedId)
		{
			var campaignId = _cryptography.Decrypt(encryptedCampaignId);
			var id = _cryptography.Decrypt(encryptedId);
			var response = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("products/fixedprices").Path(id)
				.AsyncPut().GetApiReturn<bool>();

			return response.GetReturnOrError();

		}

		public async Task<bool> UpdateCampaignMobileApplicationSettings(string encryptedCampaignId, dynamic campaignMobileApplicationSettings)
		{
			var campaignId = _cryptography.Decrypt(encryptedCampaignId);

			var response = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("mobile/settings")
				.Entity(campaignMobileApplicationSettings)
				.AsyncPut().GetApiReturn<bool>();

			return response.GetReturnOrError();

		}
		public async Task<dynamic> GetCampaignMobileApplicationSettingsByCampaign(string encryptedCampaignId)
		{
			var campaignId = _cryptography.Decrypt(encryptedCampaignId);

			var response = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("mobile/settings")
				.AsyncGet().GetApiReturn<dynamic>();

			return response.GetReturnOrError();

		}

		public async Task<bool> SaveSocialMedias(Guid campaignId, dynamic payload)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId)
				.Path("marketing/socialmedias")
				.Entity(payload)
				.AsyncPut()
				.GetApiReturn<bool>();

			if (apiReturn == null) return false;
			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> GetSocialMedias(Guid campaignId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId)
				.Path("marketing/socialmedias")
				.AsyncGet()
				.GetApiReturn<dynamic>();

			if (apiReturn == null) return false;
			return apiReturn.GetReturnOrError();
		}

		public async Task<List<ResumedCampaignDynamicRanking>> FindResumed(string encryptedCampaignId, int? skip = 0, int? limit = 100)
		{
			var campaignId = _cryptography.Decrypt(encryptedCampaignId);
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("participantsrankings/resumed")
				.Query("skip", skip)
				.Query("limit", limit)
				.AsyncGet()
				.GetApiReturn<List<ResumedCampaignDynamicRanking>>();

			if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os rankings.");

            var rankings = apiReturn.GetReturnOrError();
            if (rankings == null) return null;

			rankings.ForEach(f => f.Encrypt(_cryptography));
			return rankings;
		}

		public async Task<List<CampaignParticipantRankingParametrization>> GetCampaignParticipantRankingsSettings(string campaignId, bool onlyActive = false)
		{
			campaignId = _cryptography.Decrypt(campaignId);

			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("participants/dynamicrankings/settings")
				.Query("onlyActive", onlyActive)
				.AsyncGet()
				.GetApiReturn<List<CampaignParticipantRankingParametrization>>();

			if (apiReturn == null) return null;

			var response = apiReturn.GetReturnOrError();

			response.ForEach(ranking =>
			{
				ranking.Id = _cryptography.Encrypt(ranking.Id);
				ranking.CampaignId = _cryptography.Encrypt(ranking.CampaignId);

				if (!ranking.ParticipantsGroups.IsNullOrEmpty())
				{
					for (int i = 0; i < ranking.ParticipantsGroups.Count; i++)
					{
						if (string.IsNullOrEmpty(ranking.ParticipantsGroups[i]))
							continue;

						ranking.ParticipantsGroups[i] = this._cryptography.Encrypt(ranking.ParticipantsGroups[i]);
					}
				}
			});

			return response;
		}

		public async Task<bool> SaveCampaignParticipantRankingSettings(string campaignId, CampaignParticipantRankingParametrization rankingParametrization)
		{
			campaignId = _cryptography.Decrypt(campaignId);
			rankingParametrization.CampaignId = campaignId;

			if (!rankingParametrization.ParticipantsGroups.IsNullOrEmpty())
			{
				for (int i = 0; i < rankingParametrization.ParticipantsGroups.Count; i++)
				{
					if (string.IsNullOrEmpty(rankingParametrization.ParticipantsGroups[i]))
						continue;

					rankingParametrization.ParticipantsGroups[i] = this._cryptography.Decrypt(rankingParametrization.ParticipantsGroups[i]);
				}
			}

			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId)
				.Path("participants/dynamicrankings/settings")
				.Entity(rankingParametrization)
				.AsyncPost()
				.GetApiReturn<bool>();

			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> UpdateCampaignParticipantRankingSettingsById(string campaignId, string rankingParametrizationId, CampaignParticipantRankingParametrization rankingParametrization)
		{
			campaignId = _cryptography.Decrypt(campaignId);
			rankingParametrizationId = _cryptography.Decrypt(rankingParametrizationId);

			rankingParametrization.CampaignId = campaignId;
			rankingParametrization.Id = rankingParametrizationId;

			if (!rankingParametrization.ParticipantsGroups.IsNullOrEmpty())
			{
				for (int i = 0; i < rankingParametrization.ParticipantsGroups.Count; i++)
				{
					if (string.IsNullOrEmpty(rankingParametrization.ParticipantsGroups[i]))
						continue;

					rankingParametrization.ParticipantsGroups[i] = this._cryptography.Decrypt(rankingParametrization.ParticipantsGroups[i]);
				}
			}

			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId)
				.Path("participants/dynamicrankings/settings").Path(rankingParametrizationId)
				.Entity(rankingParametrization)
				.AsyncPut()
				.GetApiReturn<bool>();

			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> InactiveCampaignParticipantRankingSettingsById(string campaignId, string rankingParametrizationId)
		{
			campaignId = _cryptography.Decrypt(campaignId);
			rankingParametrizationId = _cryptography.Decrypt(rankingParametrizationId);

			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId)
				.Path("participants/dynamicrankings/settings")
				.Path(rankingParametrizationId).Path("inactive")
				.AsyncPut()
				.GetApiReturn<bool>();

			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> GetMetadataFieldsParametrizationByCampaignId(string campaignId)
		{
			campaignId = _cryptography.Decrypt(campaignId);

			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId)
				.Path("points/metadata/parametrization")
				.AsyncGet()
				.GetApiReturn<dynamic>();

			var response = apiReturn.GetReturnOrError();

			if (response != null && response.id != null)
			{
				response.id = _cryptography.Encrypt((string)response.id);
			}

			return response;

		}

		public async Task<dynamic> UpdateMetadataFieldsParametrizationById(string campaignId, string id, dynamic metadataFieldsParametrization)
		{
			campaignId = _cryptography.Decrypt(campaignId);
			id = _cryptography.Decrypt(id);

			metadataFieldsParametrization.id = id;
			metadataFieldsParametrization.campaignId = campaignId;

			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId)
				.Path("points/metadata/parametrization")
				.Path(id)
				.Entity(metadataFieldsParametrization)
				.AsyncPut()
				.GetApiReturn<dynamic>();

			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> CreateMetadataFieldsParametrization(string campaignId, dynamic metadataFieldsParametrization)
		{
			campaignId = _cryptography.Decrypt(campaignId);
			metadataFieldsParametrization.campaignId = campaignId;
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId)
				.Path("points/metadata/parametrization")
				.Entity(metadataFieldsParametrization)
				.AsyncPost()
				.GetApiReturn<dynamic>();

			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> GetPersonDataConsultParametrizations(string campaignId)
		{
			campaignId = _cryptography.Decrypt(campaignId);
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId)
				.Path("processes/parametrizations/consultpersondata")
				.AsyncGet()
				.GetApiReturn<dynamic>();
			if (apiReturn.HasNullReturn())
				return null;
			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> UpdatePersonDataConsultParametrizations(string campaignId, dynamic processesParametrizatons)
		{
			campaignId = _cryptography.Decrypt(campaignId);
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId)
				.Path("processes/parametrizations/consultpersondata")
				.Entity(processesParametrizatons)
				.AsyncPut()
				.GetApiReturn<dynamic>();
			if (apiReturn.HasNullReturn())
				return null;
			return apiReturn.GetReturnOrError();
		}

		public async Task<decimal> GetPointsConversionFactor(string campaignId)
        {
			campaignId = _cryptography.Decrypt(campaignId);
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId).Path("pointsconversionfactor")
                .AsyncGet()
                .GetApiReturn<decimal>();
            if (apiReturn == null)
            {
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o fator de conversão da campanha.");
            }
            return apiReturn.GetReturnOrError();
        }

	}
}

