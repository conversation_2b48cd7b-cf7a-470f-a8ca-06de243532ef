using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.WebAdmin.Server.Extensions;
using Motivai.WebAdmin.Server.Models.Login;
using Motivai.WebAdmin.Server.Models.Security;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;

namespace Motivai.WebAdmin.Server.Repositories
{
	public class UserAdministratorRepository
	{
		private readonly ICryptography _cryptography;
		private readonly IHttpContextAccessor _context;

		public UserAdministratorRepository(ICryptography cryptography, IHttpContextAccessor context)
		{
			this._cryptography = cryptography;
			this._context = context;
		}

		private Guid BuId()
		{
			return this._context.HttpContext.User.GetBuId();
		}

		public async Task<string> Authenticate(UserAdministrationAuthenticationModel userModel, LocationInfo locationInfo)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("administrators/authenticate")
				.Entity(new
				{
					login = userModel.Login,
					password = userModel.Password,
					locationInfo
				})
				.AsyncPost()
				.GetApiReturn<string>();
			return apiReturn.GetReturnOrError();
		}

		public async Task<UserAdministrator> AuthenticateWithoutMfa(UserAdministrationAuthenticationModel userModel, LocationInfo locationInfo)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("administrators/authentication/withoutmfa/validate")
				.Entity(new
				{
					login = userModel.Login,
					password = userModel.Password,
					locationInfo
				})
				.AsyncPost()
				.GetApiReturn<dynamic>();
			if (apiReturn == null) return null;
			var user = apiReturn.GetReturnOrError();
			return new UserAdministrator()
			{
				Id = user.id,
				RoleId = user.roleId,
				BuId = user.buId,
				RegionId = user.regionId,
				Name = user.name,
				Email = user.email,
				Login = user.login,
				ChangePassword = user.changePassword,
				LayoutType = user.layoutType,
				StartComponent = user.startComponent
			};
		}

		public async Task<UserAdministrator> ValidateMfa(UserAdministrationMfaValidationModel mfaValidationModel, LocationInfo locationInfo)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("administrators/authentication/mfa/validate")
				.Entity(new
				{
					sessionId = mfaValidationModel.SessionId,
					securityCode = mfaValidationModel.SecurityCode,
					locationInfo
				})
				.AsyncPost()
				.GetApiReturn<dynamic>();
			if (apiReturn == null) return null;
			var user = apiReturn.GetReturnOrError();
			return new UserAdministrator()
			{
				Id = user.id,
				RoleId = user.roleId,
				BuId = user.buId,
				RegionId = user.regionId,
				Name = user.name,
				Email = user.email,
				Login = user.login,
				ChangePassword = user.changePassword,
				LayoutType = user.layoutType,
				StartComponent = user.startComponent
			};
		}

		public async Task<bool> UpdatePassword(Guid userId, ChangePasswordModel model, UserAdministrationAction actionLog)
		{
			if (model == null)
				throw MotivaiException.ofValidation("Preencha os campos corretamente.");
			model.OperationUser = actionLog.OperationUser;
			model.LocationInfo = actionLog.LocationInfo;

			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("administrators").Path(userId).Path("password")
				.Bu(BuId())
				.Entity(model)
				.AsyncPut()
				.GetApiReturn<bool>();
			return apiReturn.GetReturnOrError();
		}

		public async Task<List<UserAdministrator>> GetAdministrators(string name, string email, string login, int? skip, int? limit)
		{
			var response = await HttpClient.Create(MotivaiApi.Users)
				.Path("administrators")
				.Bu(BuId())
				.Query("name", name)
				.Query("email", email)
				.Query("login", login)
				.Query("skip", skip)
				.Query("limit", limit)
				.AsyncGet()
				.GetApiReturn<List<UserAdministrator>>();

			var users = response.GetReturnOrError();
			if (users == null) return null;
			users.ForEach(user =>
			{
				user.Id = _cryptography.Encrypt(user.Id);
				user.RoleId = _cryptography.Encrypt(user.RoleId);
				user.BuId = null;
			});
			return users;
		}

		public async Task<UserAdministrator> GetAdministrator(string encryptedId)
		{
			if (!Guid.TryParse(_cryptography.Decrypt(encryptedId), out Guid userId))
			{
				throw MotivaiException.ofValidation("ID do usuário inválido.");
			}
			var response = await HttpClient
				.Create(MotivaiApi.Users)
				.Path("administrators").Path(userId)
				.Bu(BuId())
				.AsyncGet()
				.GetApiReturn<UserAdministrator>();
			var user = response.GetReturnOrError();
			if (user == null) return null;

			user.Id = _cryptography.Encrypt(user.Id);
			user.RoleId = _cryptography.Encrypt(user.RoleId);
			user.BuId = _cryptography.Encrypt(user.BuId);
			user.RegionId = !string.IsNullOrEmpty(user.RegionId) ? _cryptography.Encrypt(user.RegionId) : "";
			return user;
		}

		public async Task<UserAdministrator> GetLoggedAdministrator()
		{
			var response = await HttpClient.Create(MotivaiApi.Users)
				.Path("administrators").Path(_context.HttpContext.User.GetUserId())
				.RootBu()
				.AsyncGet()
				.GetApiReturn<UserAdministrator>();

			return response.GetReturnOrError();
		}

		public async Task<dynamic> Save(UserAdministrator user, OperationUser operationUser, LocationInfo locationInfo)
		{
			user.OperationUser = operationUser;
			user.LocationInfo = locationInfo;

			if (!string.IsNullOrEmpty(user.RoleId))
			{
				if (!Guid.TryParse(_cryptography.Decrypt(user.RoleId), out Guid roleId))
				{
					throw MotivaiException.ofValidation("ID do perfil inválido.");
				}
				user.RoleId = roleId.ToString();
			}
			else
			{
				user.RoleId = Guid.Empty.ToString();
			}

			if (!string.IsNullOrEmpty(user.BuId))
			{
				if (!Guid.TryParse(_cryptography.Decrypt(user.BuId), out Guid buId))
				{
					throw MotivaiException.ofValidation("ID da BU inválido.");
				}
				user.BuId = buId.ToString();
			}
			else
			{
				user.BuId = Guid.Empty.ToString();
			}

			if (!string.IsNullOrEmpty(user.RegionId))
			{
				if (!Guid.TryParse(_cryptography.Decrypt(user.RegionId), out Guid regionId))
				{
					throw MotivaiException.ofValidation("ID da Region inválido.");
				}
				user.RegionId = regionId.ToString();
			}
			else
			{
				user.RegionId = Guid.Empty.ToString();
			}

			var httpClient = HttpClient.Create(MotivaiApi.Users)
				.Path("administrators")
				.Bu(BuId());

			Motivai.SharedKernel.Domain.Http.HttpRequest request;
			if (string.IsNullOrWhiteSpace(user.Id))
			{
				user.Id = null;
				request = httpClient.Entity(user)
					.AsyncPost();
			}
			else
			{
				request = httpClient.Path(user.Id)
					.Entity(user)
					.AsyncPut();
			}

			var apiReturn = await request.LogPayloadToConsole()
				.GetApiReturn<dynamic>();
			return apiReturn.GetReturnOrError();
		}

		public async Task<string> ResetPassword(Guid userId, OperationUser operationUser, LocationInfo locationInfo)
		{
			LoggerFactory.GetLogger().Info("{} - Resetando senha: {}",
					userId, JsonConvert.SerializeObject(operationUser));

			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("administrators").Path(userId).Path("password/reset")
				.Bu(BuId())
				.Entity(new {
					operationUser,
					locationInfo
				})
				.AsyncPost()
				.GetApiReturn<string>();
			return apiReturn.GetReturnOrError();
		}
	}
}
