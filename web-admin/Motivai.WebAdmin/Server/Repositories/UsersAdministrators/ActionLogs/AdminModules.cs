namespace Motivai.WebAdmin.Server.Repositories.UsersAdministrators.Logs
{
	public static class AdminModules
	{
		public const string CALLCENTER = "Backoffice - Call Center";
		public const string REPORTS = "Relatórios";
		public const string PREPAID_CARDS = "Cartões Pré-pagos";
		public const string BANK = "Transferências Bancárias";
		public const string CAMPAIGN = "Campanha";
		public const string CAMPAIGN_PAYMENT_METHOD = "Campanha Formas de Pagamento";
		public const string CAMPAIGN_CATALOG = "Campanha - Catálogo";
		public const string CAMPAIGN_DYNAMIC_FORM = "Campanha - Formulário Dinâmico";
		public const string CAMPAIGN_RAFFLE = "Campanha - Sorteio";
		public const string CAMPAIGN_CASHBACK = "Campanha - Cashback";
		public const string MARKET_PLACE = "Marketplace";
	}

	public static class AdminModulesFeatures
	{
		#region Relatórios

		public const string REPORTS_USERS_ACCESSES_LOG = "Acessos de Participantes";
		public const string REPORTS_USERS_REGISTRATIONS = "Registros de Participantes";
		public const string REPORTS_EXPIRED_POINTS = "Pontos Expirados";
		public const string REPORTS_EXPIRING_POINTS = "Pontos a Expirar";
		public const string REPORTS_USERS_AVAILABLE_BALANCES = "Saldos Disponíveis de Participantes";
		public const string REPORTS_USERS_BLOCKED_BALANCES = "Saldos Bloqueados de Participantes";
		public const string REPORTS_ORDERS = "Pedidos";
		public const string REPORTS_USERS_TERMS_ACCEPTANCE = "Aceites de Regulamento";
		public const string REPORTS_USERS_CREDITS = "Créditos";
		public const string REPORTS_USERS_TRANSACTIONAL_DAILY_MOVEMENTS = "Transacional Diário";
		public const string REPORTS_USERS_TRANSACTIONAL_MONTHLY_MOVEMENTS = "Transacional Mensal";
		public const string REPORTS_PARTICIPANT_PRE_REGISTER = "Pré-Cadastro dos Participantes";
		public const string REPORTS_PARTICIPANT_MANAGEMENT = "Gestão de Participantes";

		#endregion

		#region Cartões Pré-pagos

		public const string PREPAID_CARDS_ORDERS = "Pedidos";
		public const string PREPAID_CARDS_ORDERS_BATCHES = "Lotes de Pedidos";
		public const string PREPAID_CARDS_ORDERS_BALANCE = "Consulta de Saldo";
		public const string PREPAID_CARDS_TAX = "Taxas";
		#endregion

		#region Transferências

		public const string BANK_TRANSFER_ORDERS = "Pedidos";

		#endregion

		#region Campanha

		public const string CAMPAIGN_ORDERS = "Pedidos de Produtos e Vales";
		public const string CAMPAIGN_SCHEDULED_PAYMENTS = "Pagamentos Agendados";
		public const string CAMPAIGN_PAYMENTS_ORDERS_OF_ACCOUNT_RECHARGE = "Pedidos de Pague Contas e Recargas";
		public const string CAMPAIGN_RAFFLE_TICKETS_VIEW = "Vizualização dos Cupons";
		public const string CAMPAIGN_CASHBACK_PURCHASE_RECEIPTS = "NF Recebidas";
		public const string CAMPAIGN_DYNAMIC_FORM_FILLED = "Formulários Preenchidos";

		#endregion

		#region Configurações

		public const string PLATFORM_GENERAL_SETTIGNS_RISK_ASSESSMENT = "Análise de Risco";
		public const string PLATFORM_GENERAL_SETTIGNS_PAYMENT_METHOD = "Formas de Pagamento";


		#endregion
	}
}
