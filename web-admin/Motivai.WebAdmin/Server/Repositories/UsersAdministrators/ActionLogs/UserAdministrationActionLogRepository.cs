using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Logger;

namespace Motivai.WebAdmin.Server.Repositories.UsersAdministrators.Logs
{
	public class UserAdministrationActionLogRepository
	{
		public async Task<bool> ReportActionLog(UserActionOperationLog actionLog)
		{
			LoggerFactory.GetLogger().Info("Admin - Log de Acesso - Module {} - Feature {} - Acao {} - User {}",
				actionLog.Module, actionLog.Feature, actionLog.AccessDate, actionLog.OperationUser?.UserId);
			try
			{
				var response = await HttpClient.Create(MotivaiApi.Users)
					.Path("administrators/actions/logs")
					.Entity(actionLog)
					.AsyncPost()
					.GetApiReturn<bool>();
				return response.GetReturnOrError();
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "User Admin Logs", "Erro durante o registro de log do admin.");
				return false;
			}
		}
	}

	public class UserActionOperationLogBuilder
	{
		private readonly UserActionOperationLog operationLog;
		private Dictionary<string, string> metadata = new Dictionary<string, string>();

		public UserActionOperationLogBuilder()
		{
			operationLog = new UserActionOperationLog()
			{
				AccessDate = DateTime.UtcNow,
			};
		}

		public UserActionOperationLogBuilder ForFeature(string module, string feature)
		{
			operationLog.Module = module;
			operationLog.Feature = feature;
			return this;
		}

		public UserActionOperationLogBuilder WithUserOperation(OperationUser operationUser, LocationInfo locationInfo)
		{
			operationLog.OperationUser = operationUser;
			operationLog.LocationInfo = locationInfo;
			return this;
		}

		public UserActionOperationLogBuilder WithAction(string action)
		{
			operationLog.Action = action;
			return this;
		}

		public UserActionOperationLogBuilder WithCampaign(string campaignId)
		{
			if (string.IsNullOrEmpty(campaignId))
			{
				return this;
			}
			operationLog.CampaignId = Guid.Parse(campaignId);
			return this;
		}

		public UserActionOperationLogBuilder WithCampaign(Guid campaignId)
		{
			operationLog.CampaignId = campaignId;
			return this;
		}

		public UserActionOperationLogBuilder WithMetadata(string key, bool value)
		{
			metadata.Add(key, value.ToString());
			return this;
		}

		public UserActionOperationLogBuilder WithMetadata(string key, Guid value)
		{
			metadata.Add(key, value.ToString());
			return this;
		}

		public UserActionOperationLogBuilder WithMetadata(string key, string value)
		{
			metadata.Add(key, value);
			return this;
		}

		public UserActionOperationLogBuilder WithMetadata(Dictionary<string, string> metadata)
		{
			this.metadata = metadata;
			return this;
		}

		public UserActionOperationLogBuilder WithMetadata(string key, dynamic value)
		{
			if (value == null) {
				value = " ";
			}

			metadata.Add(key, value.ToString());
			return this;
		}


		public UserActionOperationLogBuilder WithMetadata(string key, DateTime? value)
		{
			metadata.Add(key, value?.ToString());
			return this;
		}

		public UserActionOperationLogBuilder WithMetadata(string key, int? value)
		{
			metadata.Add(key, value?.ToString());
			return this;
		}

		public UserActionOperationLog Build()
		{
			operationLog.Metadata = metadata;
			return operationLog;
		}
	}
}
