#!/usr/bin/env bash
set -e errexit

if [ -z "$(node --version | grep v10)" ]; then
	echo "Versão NodeJS inválida; Utilize NodeJS v10 para deploy"
	exit 1
fi

cd Motivai.WebAdmin

# export GOOGLE_APPLICATION_CREDENTIALS=""
export ASPNETCORE_ENVIRONMENT=Local
export ENV_GP=Local
export ENV=Development
export GOOGLE_APPLICATION_CREDENTIALS=""
export LOGIN_RPA_ALLOWED_IP="127.0.0.1,*******"

export ELASTICSEARCH_URI="https://fa72e69c0e924782a75db7e11fd5f26d.us-east-1.aws.found.io:443"
export ELASTICSEARCH_API_KEY="cVY1T2U1Y0J0eEpBQ216cWd5X1E6RGkwVUF3MzU0SE5mNlNTZGxTZ29qZw=="

export AWS_X_API_KEY=""
export GOOGLE_RECAPTCHA_PRIVATE_API_KEY="6Ld1yb8qAAAAAA7cLHq7UicxOQ8ztWjfC21dT5LM"
export GCS_URL="https://storage.googleapis.com"
export GCS_BUCKET_NAME="files-prod-motivai"
# export GOOGLE_APPLICATION_CREDENTIALS="$HOME/projects/motivai/plataforma/gcp-service-account.json"

export VIMEO_API_URL="https://api.vimeo.com"
export VIMEO_API_TOKEN="ce74dea0bb199ee939a2508e5ce61db0"

export ThemesUrl="https://styles.motivai.com.br"
export API_IMAGES="https://images.motivai.com.br"
export API_BUSINESS_UNIT="http://127.0.0.1:8001/api/v1/namespaces/prod/services/api-business-units:http/proxy"
export API_GENERAL_SETTINGS="http://127.0.0.1:8001/api/v1/namespaces/prod/services/api-general-settings:http/proxy"
export API_CORREIOS="http://127.0.0.1:8001/api/v1/namespaces/prod/services/api-correios:http/proxy"

export API_COMPANIES="http://127.0.0.1:8001/api/v1/namespaces/prod/services/api-companies:http/proxy"
export API_CAMPAIGNS="http://127.0.0.1:8001/api/v1/namespaces/prod/services/api-campaigns:http/proxy"
export API_TRANSACTIONS="http://127.0.0.1:8001/api/v1/namespaces/prod/services/api-transactions:http/proxy"
export API_USERS="http://127.0.0.1:8001/api/v1/namespaces/prod/services/api-users:http/proxy"
export API_ORDERS="http://127.0.0.1:8001/api/v1/namespaces/prod/services/api-orders:http/proxy"
export API_PRICES="http://httpbin.org/get"
export API_BANK_TRANSFERS_ORDERS="http://httpbin.org/get"
export API_CLIENTS_INTEGRATIONS="http://httpbin.org/get"
export API_NOTIFICATIONS="http://127.0.0.1:8001/api/v1/namespaces/prod/services/api-notifications:http/proxy"
export EMAILS_SERVICE="http://127.0.0.1:3000"
export API_DOWNLOADER_SERVICE="https://downloader.motivai.com.br"
export API_ELASTICSEARCH="http://127.0.0.1:8001/api/v1/namespaces/prod/services/api-elasticsearch:http/proxy"
export API_CATALOGS="http://127.0.0.1:8001/api/v1/namespaces/prod/services/api-catalogs:http/proxy"
export API_PRODUCTS="http://127.0.0.1:8001/api/v1/namespaces/prod/services/api-products:http/proxy"
export API_CAMPAIGN_SITE="http://127.0.0.1:8001/api/v1/namespaces/prod/services/api-campaign-site:http/proxy"
export API_CAMPAIGNS_SITE_FEATURES="http://127.0.0.1:8001/api/v1/namespaces/prod/services/api-campaigns-sites-features:http/proxy"
export API_RAFFLES_TICKETS_PARTICIPANTS_LINK="http://httpbin.org/get"

export API_LEARNING_MANAGEMENT_SYSTEM="http://127.0.0.1:8001/api/v1/namespaces/prod/services/api-learning-management-system:http/proxy"
export API_LEARNING_MANAGEMENT_SYSTEM_EVENTS_QUEUER="https://dx46z2zs13.execute-api.us-east-1.amazonaws.com/prod"
export API_LEARNING_MANAGEMENT_SYSTEM_CERTIFICATE_QUEUER="https://b5jooyqgff.execute-api.us-east-1.amazonaws.com/prod"

export API_PAYMENTS_GATEWAY="http://127.0.0.1:8001/api/v1/namespaces/prod/services/api-payments-gateway:http/proxy"

dotnet watch run
